# نظام نقاط البيع القطري - تطبيق سطح المكتب
# Qatar POS System - Desktop Application

## 🎯 نظرة عامة - Overview

تم تحويل نظام نقاط البيع القطري إلى تطبيق سطح مكتب يعمل بدون الحاجة لخادم خارجي. التطبيق يجمع بين سهولة استخدام واجهة سطح المكتب وقوة نظام Flask.

## 📁 الملفات المُنشأة - Created Files

### ملفات التطبيق الأساسية:
- `desktop_app.py` - التطبيق الرئيسي مع واجهة متقدمة
- `simple_desktop.py` - إصدار مبسط للاختبار
- `desktop_config.py` - إعدادات التطبيق المكتبي
- `test_desktop.py` - اختبار المكونات

### ملفات البناء والتوزيع:
- `build_exe.py` - سكريپت بناء ملف EXE
- `build.bat` - ملف batch لتشغيل البناء
- `requirements_desktop.txt` - متطلبات التطبيق المكتبي

### ملفات التشغيل:
- `run_desktop.bat` - تشغيل التطبيق الكامل
- `run_simple.bat` - تشغيل الإصدار المبسط
- `install.bat` - مثبت التطبيق (يُنشأ بعد البناء)
- `uninstall.bat` - إلغاء تثبيت التطبيق

### ملفات التوثيق:
- `DESKTOP_GUIDE.md` - دليل شامل للتطبيق المكتبي
- `DESKTOP_README.md` - هذا الملف
- `README.txt` - تعليمات للمستخدم النهائي

## 🚀 التشغيل السريع - Quick Start

### 1. التشغيل المباشر (للتطوير):

```bash
# تشغيل الإصدار المبسط
python simple_desktop.py

# أو تشغيل الإصدار الكامل
python desktop_app.py
```

### 2. استخدام ملفات Batch:

```cmd
# للإصدار المبسط
run_simple.bat

# للإصدار الكامل
run_desktop.bat
```

## 🔧 بناء ملف EXE

### الطريقة التلقائية:

```bash
# تشغيل سكريپت البناء
python build_exe.py

# أو استخدام batch
build.bat
```

### الطريقة اليدوية:

```bash
# تثبيت PyInstaller
pip install pyinstaller

# بناء التطبيق
pyinstaller --onefile --windowed --icon=static/images/logo.ico desktop_app.py
```

## 📋 المتطلبات - Requirements

### للتطوير:
- Python 3.8+
- Flask وملحقاته
- tkinter (مدمج مع Python)
- PyInstaller (للبناء)

### للاستخدام النهائي:
- Windows 10+ (64-bit)
- 4 GB RAM
- 500 MB مساحة فارغة

## 🎮 كيفية الاستخدام - How to Use

### 1. بدء التطبيق:
- شغل أحد ملفات التشغيل
- ستظهر نافذة التحكم

### 2. تشغيل النظام:
- انقر "بدء الخادم"
- انتظر حتى تصبح الحالة "يعمل"
- انقر "فتح المتصفح"

### 3. استخدام النظام:
- ستفتح صفحة تسجيل الدخول
- استخدم: admin / admin123
- استخدم النظام كالمعتاد

### 4. إغلاق التطبيق:
- أغلق المتصفح
- انقر "إيقاف الخادم"
- أغلق نافذة التحكم

## 🔍 استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة:

#### 1. "tkinter غير متوفر"
```bash
# تأكد من تثبيت Python مع tkinter
# أعد تثبيت Python مع تفعيل "tcl/tk and IDLE"
```

#### 2. "المنفذ مستخدم"
- التطبيق سيبحث عن منفذ متاح تلقائياً
- أو أغلق التطبيقات الأخرى التي تستخدم المنفذ 2626

#### 3. "خطأ في قاعدة البيانات"
```bash
# احذف ملف قاعدة البيانات وأعد التشغيل
del qatar_pos.db
```

#### 4. "فشل في فتح المتصفح"
- انسخ الرابط من النافذة والصقه في المتصفح يدوياً
- تأكد من وجود متصفح افتراضي

## 🎨 المميزات - Features

### واجهة سطح المكتب:
✅ تحكم كامل في الخادم (بدء/إيقاف)
✅ فتح المتصفح تلقائياً
✅ عرض حالة النظام
✅ سجل مفصل للأحداث
✅ دعم اللغة العربية والإنجليزية

### النظام الأساسي:
✅ جميع ميزات نظام نقاط البيع
✅ قاعدة بيانات محلية
✅ لا يحتاج اتصال إنترنت
✅ أمان وخصوصية عالية

## 📦 التوزيع - Distribution

### إنشاء حزمة التوزيع:

1. **بناء التطبيق:**
   ```bash
   python build_exe.py
   ```

2. **النتائج:**
   - `dist/QatarPOS.exe` - الملف التنفيذي
   - `install.bat` - مثبت التطبيق
   - `uninstall.bat` - إلغاء التثبيت
   - `README.txt` - تعليمات المستخدم

3. **التوزيع:**
   - اضغط مجلد `dist` مع ملفات التثبيت
   - وزع الملف المضغوط

## 🔄 التحديثات - Updates

### تحديث التطبيق:
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. أعد بناء التطبيق بالكود الجديد
3. استبدل الملف التنفيذي
4. شغل التطبيق للتحقق من التوافق

### تحديث قاعدة البيانات:
```bash
# في حالة تغيير هيكل قاعدة البيانات
flask db upgrade
```

## 🛡️ الأمان - Security

### اعتبارات الأمان:
- قاعدة البيانات محلية ومحمية
- لا يتم إرسال بيانات خارجياً
- الخادم يعمل محلياً فقط
- كلمات مرور مشفرة

### نصائح الأمان:
- غير كلمة مرور المدير الافتراضية
- أنشئ نسخ احتياطية دورية
- احتفظ بالتطبيق محدثاً
- لا تشارك ملف قاعدة البيانات

## 📞 الدعم - Support

### الحصول على المساعدة:
- راجع `DESKTOP_GUIDE.md` للدليل الشامل
- فحص ملفات السجل للأخطاء
- تواصل مع فريق الدعم

### الإبلاغ عن المشاكل:
عند الإبلاغ عن مشكلة، أرفق:
- وصف المشكلة
- رسائل الخطأ
- معلومات النظام
- خطوات إعادة الإنتاج

## 📈 التطوير المستقبلي

### ميزات مخططة:
- [ ] واجهة أكثر تطوراً
- [ ] نظام تحديث تلقائي
- [ ] دعم أنظمة تشغيل أخرى
- [ ] تكامل مع الطابعات
- [ ] تصدير البيانات المتقدم

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**© 2024 Qatar POS System. جميع الحقوق محفوظة.**

**للدعم الفني:** <EMAIL>
