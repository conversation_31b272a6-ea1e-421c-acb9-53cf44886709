{% extends "base.html" %}

{% block title %}
{{ 'تقرير المنتجات بطيئة الحركة' if language == 'ar' else 'Slow Moving Products Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-speedometer"></i>
                        {{ 'تقرير المنتجات بطيئة الحركة' if language == 'ar' else 'Slow Moving Products Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">{{ 'القسم' if language == 'ar' else 'Category' }}</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">{{ 'جميع الأقسام' if language == 'ar' else 'All Categories' }}</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {{ 'selected' if selected_category == category.id }}>
                                            {{ category.name_ar if language == 'ar' else category.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{{ 'فترة التحليل (أيام)' if language == 'ar' else 'Analysis Period (Days)' }}</label>
                                    <select name="days" class="form-select">
                                        <option value="30" {{ 'selected' if days == 30 }}>30 {{ 'يوم' if language == 'ar' else 'Days' }}</option>
                                        <option value="60" {{ 'selected' if days == 60 }}>60 {{ 'يوم' if language == 'ar' else 'Days' }}</option>
                                        <option value="90" {{ 'selected' if days == 90 }}>90 {{ 'يوم' if language == 'ar' else 'Days' }}</option>
                                        <option value="180" {{ 'selected' if days == 180 }}>180 {{ 'يوم' if language == 'ar' else 'Days' }}</option>
                                        <option value="365" {{ 'selected' if days == 365 }}>365 {{ 'يوم' if language == 'ar' else 'Days' }}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{{ 'الحد الأدنى للمبيعات' if language == 'ar' else 'Minimum Sales' }}</label>
                                    <input type="number" name="min_sales" class="form-control" value="{{ min_sales or 0 }}" min="0">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel"></i>
                                            {{ 'تطبيق الفلتر' if language == 'ar' else 'Apply Filter' }}
                                        </button>
                                        <a href="{{ url_for('reports.slow_moving') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'منتجات بطيئة الحركة' if language == 'ar' else 'Slow Moving Products' }}</h6>
                                            <h3 class="mb-0">{{ products|length }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-speedometer fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة المخزون المتأثر' if language == 'ar' else 'Affected Stock Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_stock_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-currency-dollar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'منتجات بدون مبيعات' if language == 'ar' else 'Zero Sales Products' }}</h6>
                                            <h3 class="mb-0">{{ zero_sales_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-x-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'فترة التحليل' if language == 'ar' else 'Analysis Period' }}</h6>
                                            <h3 class="mb-0">{{ days }} {{ 'يوم' if language == 'ar' else 'Days' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-calendar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slow Moving Products Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    {% if not selected_category %}
                                    <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                    {% endif %}
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'المبيعات في الفترة' if language == 'ar' else 'Sales in Period' }}</th>
                                    <th>{{ 'معدل الدوران' if language == 'ar' else 'Turnover Rate' }}</th>
                                    <th>{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</th>
                                    <th>{{ 'آخر بيع' if language == 'ar' else 'Last Sale' }}</th>
                                    <th>{{ 'التوصية' if language == 'ar' else 'Recommendation' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        <code>{{ product.barcode or 'N/A' }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                        {% if product.sku %}
                                        <br><small class="text-muted">{{ product.sku }}</small>
                                        {% endif %}
                                    </td>
                                    {% if not selected_category %}
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-secondary">
                                            {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        <span class="badge bg-info">{{ product.current_stock }}</span>
                                    </td>
                                    <td>
                                        {% set sales_qty = product.sales_quantity or 0 %}
                                        {% if sales_qty == 0 %}
                                        <span class="badge bg-danger">0</span>
                                        {% elif sales_qty <= min_sales %}
                                        <span class="badge bg-warning">{{ sales_qty }}</span>
                                        {% else %}
                                        <span class="badge bg-success">{{ sales_qty }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.current_stock > 0 %}
                                        {% set turnover_rate = (sales_qty / product.current_stock * 100) %}
                                        <span class="{% if turnover_rate < 10 %}text-danger{% elif turnover_rate < 50 %}text-warning{% else %}text-success{% endif %}">
                                            {{ "%.1f"|format(turnover_rate) }}%
                                        </span>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set stock_value = product.current_stock * (product.cost_price or 0) %}
                                        <strong>{{ "%.2f"|format(stock_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ 'غير متوفر' if language == 'ar' else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        {% if sales_qty == 0 %}
                                        <span class="badge bg-danger">{{ 'مراجعة عاجلة' if language == 'ar' else 'Urgent Review' }}</span>
                                        {% elif sales_qty <= min_sales %}
                                        <span class="badge bg-warning">{{ 'تخفيض السعر' if language == 'ar' else 'Price Reduction' }}</span>
                                        {% else %}
                                        <span class="badge bg-info">{{ 'مراقبة' if language == 'ar' else 'Monitor' }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if not products %}
                    <div class="text-center py-5">
                        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">{{ 'ممتاز! لا توجد منتجات بطيئة الحركة' if language == 'ar' else 'Great! No slow moving products found' }}</h4>
                        <p class="text-muted">{{ 'جميع المنتجات تتحرك بشكل جيد في الفترة المحددة' if language == 'ar' else 'All products are moving well in the specified period' }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
