{% extends "base.html" %}

{% block title %}
{{ 'تقرير تقييم المخزون' if language == 'ar' else 'Stock Valuation Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-calculator"></i>
                        {{ 'تقرير تقييم المخزون' if language == 'ar' else 'Stock Valuation Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'القسم' if language == 'ar' else 'Category' }}</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">{{ 'جميع الأقسام' if language == 'ar' else 'All Categories' }}</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {{ 'selected' if selected_category == category.id }}>
                                            {{ category.name_ar if language == 'ar' else category.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'طريقة التقييم' if language == 'ar' else 'Valuation Method' }}</label>
                                    <select name="valuation_method" class="form-select">
                                        <option value="cost" {{ 'selected' if valuation_method == 'cost' }}>
                                            {{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}
                                        </option>
                                        <option value="selling" {{ 'selected' if valuation_method == 'selling' }}>
                                            {{ 'سعر البيع' if language == 'ar' else 'Selling Price' }}
                                        </option>
                                        <option value="average" {{ 'selected' if valuation_method == 'average' }}>
                                            {{ 'المتوسط' if language == 'ar' else 'Average' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel"></i>
                                            {{ 'تطبيق الفلتر' if language == 'ar' else 'Apply Filter' }}
                                        </button>
                                        <a href="{{ url_for('reports.stock_valuation') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                                            <h3 class="mb-0">{{ products|length }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-box-seam fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي الكمية' if language == 'ar' else 'Total Quantity' }}</h6>
                                            <h3 class="mb-0">{{ total_quantity }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-boxes fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-currency-dollar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'متوسط القيمة' if language == 'ar' else 'Average Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(average_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-graph-up fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Valuation Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    {% if not selected_category %}
                                    <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                    {% endif %}
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</th>
                                    <th>{{ 'سعر البيع' if language == 'ar' else 'Selling Price' }}</th>
                                    <th>{{ 'سعر التقييم' if language == 'ar' else 'Valuation Price' }}</th>
                                    <th>{{ 'إجمالي القيمة' if language == 'ar' else 'Total Value' }}</th>
                                    <th>{{ 'النسبة المئوية' if language == 'ar' else 'Percentage' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        <code>{{ product.barcode or 'N/A' }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                        {% if product.sku %}
                                        <br><small class="text-muted">{{ product.sku }}</small>
                                        {% endif %}
                                    </td>
                                    {% if not selected_category %}
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-secondary">
                                            {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ product.current_stock }}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>{{ "%.2f"|format(product.selling_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>
                                        {% if valuation_method == 'cost' %}
                                        <strong>{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                        {% elif valuation_method == 'selling' %}
                                        <strong>{{ "%.2f"|format(product.selling_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                        {% else %}
                                        <strong>{{ "%.2f"|format((product.cost_price or 0 + product.selling_price) / 2) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if valuation_method == 'cost' %}
                                        {% set item_value = (product.cost_price or 0) * product.current_stock %}
                                        {% elif valuation_method == 'selling' %}
                                        {% set item_value = product.selling_price * product.current_stock %}
                                        {% else %}
                                        {% set item_value = ((product.cost_price or 0) + product.selling_price) / 2 * product.current_stock %}
                                        {% endif %}
                                        <strong>{{ "%.2f"|format(item_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        {% set percentage = (item_value / total_value * 100) if total_value > 0 else 0 %}
                                        <span class="badge bg-info">{{ "%.1f"|format(percentage) }}%</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
