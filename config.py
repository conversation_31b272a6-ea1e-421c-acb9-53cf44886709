"""
Configuration settings for Qatar POS System
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'qatar-pos-secret-key-2024'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///qatar_pos.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload configuration
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # 8-hour work day
    
    # Internationalization
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Qatar'
    
    # Qatar-specific settings
    CURRENCY = 'QAR'
    CURRENCY_SYMBOL = 'ر.ق'
    TAX_RATE = 0.0  # Qatar currently has 0% VAT for most items
    
    # Business settings
    BUSINESS_NAME_AR = 'نظام نقاط البيع'
    BUSINESS_NAME_EN = 'Point of Sale System'
    BUSINESS_ADDRESS_AR = 'الدوحة، قطر'
    BUSINESS_ADDRESS_EN = 'Doha, Qatar'
    
    # Qatar Tax Authority settings
    TAX_NUMBER = os.environ.get('TAX_NUMBER') or ''
    COMMERCIAL_REGISTRATION = os.environ.get('COMMERCIAL_REGISTRATION') or ''
    
    # Working days (Sunday to Thursday in Qatar)
    WORKING_DAYS = [0, 1, 2, 3, 4]  # Monday=0, Sunday=6
    
    # Pagination
    ITEMS_PER_PAGE = 20
    
    # File extensions
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SQLALCHEMY_ECHO = True

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SQLALCHEMY_ECHO = False

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
