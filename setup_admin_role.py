#!/usr/bin/env python3
"""
Admin Role Setup and Configuration - Qatar POS System
Complete setup and management of admin role with full permissions
"""

from app import create_app
from models.user import User
from extensions import db
from werkzeug.security import generate_password_hash
from datetime import datetime

def setup_admin_role():
    """Setup and configure admin role with full permissions"""
    app = create_app()
    
    with app.app_context():
        print("🔧 إعداد وضبط دور المدير (Admin)")
        print("=" * 50)
        
        # Check current admin users
        admin_users = User.query.filter_by(role='admin').all()
        
        print(f"📊 عدد المديرين الحاليين: {len(admin_users)}")
        
        for admin in admin_users:
            print(f"   👤 {admin.username} - {admin.email}")
            print(f"      نشط: {admin.is_active}")
            print(f"      آخر دخول: {admin.last_login or 'لم يسجل دخول'}")
        
        # Ensure we have at least one admin
        if not admin_users:
            print("\n⚠️ لا يوجد مديرين في النظام - سيتم إنشاء مدير افتراضي")
            create_default_admin()
        else:
            print(f"\n✅ يوجد {len(admin_users)} مدير في النظام")
        
        # Test admin permissions
        print("\n🧪 اختبار صلاحيات المدير...")
        test_admin_permissions()
        
        # Display admin capabilities
        print("\n📋 صلاحيات دور المدير:")
        display_admin_permissions()
        
        return True

def create_default_admin():
    """Create default admin user if none exists"""
    try:
        admin = User(
            username='admin',
            email='<EMAIL>',
            first_name_ar='المدير',
            first_name_en='Admin',
            last_name_ar='العام',
            last_name_en='User',
            role='admin',
            is_active=True,
            phone='+974 5555 0000'
        )
        admin.set_password('admin123')
        
        db.session.add(admin)
        db.session.commit()
        
        print("✅ تم إنشاء مدير افتراضي:")
        print(f"   👤 اسم المستخدم: admin")
        print(f"   🔑 كلمة المرور: admin123")
        print(f"   📧 البريد: <EMAIL>")
        
        return admin
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدير: {e}")
        db.session.rollback()
        return None

def test_admin_permissions():
    """Test admin permissions for all system modules"""
    admin_user = User.query.filter_by(role='admin').first()
    
    if not admin_user:
        print("❌ لا يوجد مدير للاختبار")
        return False
    
    # List of all permissions to test
    permissions_to_test = [
        'sales', 'sales_read', 'sales_write',
        'products', 'products_read', 'products_write',
        'inventory', 'inventory_read', 'inventory_write', 'inventory_adjust',
        'customers', 'customers_read', 'customers_write',
        'suppliers', 'suppliers_read', 'suppliers_write',
        'users', 'users_read', 'users_write',
        'reports', 'reports_read', 'reports_write',
        'settings', 'settings_read', 'settings_write',
        'pos', 'barcode', 'barcode_read', 'barcode_write'
    ]
    
    print(f"🧪 اختبار صلاحيات المستخدم: {admin_user.username}")
    
    all_passed = True
    for permission in permissions_to_test:
        has_permission = admin_user.has_permission(permission)
        status = "✅" if has_permission else "❌"
        print(f"   {status} {permission}")
        
        if not has_permission:
            all_passed = False
    
    if all_passed:
        print("\n🎉 جميع الصلاحيات تعمل بشكل صحيح!")
    else:
        print("\n⚠️ بعض الصلاحيات لا تعمل - يجب مراجعة الكود")
    
    return all_passed

def display_admin_permissions():
    """Display detailed admin permissions"""
    
    admin_capabilities = {
        "📊 إدارة المبيعات": [
            "عرض جميع المبيعات",
            "إنشاء مبيعات جديدة",
            "تعديل المبيعات",
            "حذف المبيعات",
            "استرجاع المبيعات",
            "طباعة الفواتير"
        ],
        "📦 إدارة المنتجات": [
            "عرض جميع المنتجات",
            "إضافة منتجات جديدة",
            "تعديل المنتجات",
            "حذف المنتجات",
            "إدارة الفئات",
            "إدارة الباركود"
        ],
        "📋 إدارة المخزون": [
            "عرض حالة المخزون",
            "تعديل كميات المخزون",
            "حركات المخزون",
            "تسوية المخزون",
            "تقارير المخزون",
            "تنبيهات المخزون"
        ],
        "👥 إدارة العملاء": [
            "عرض جميع العملاء",
            "إضافة عملاء جدد",
            "تعديل بيانات العملاء",
            "حذف العملاء",
            "تقارير العملاء"
        ],
        "🏢 إدارة الموردين": [
            "عرض جميع الموردين",
            "إضافة موردين جدد",
            "تعديل بيانات الموردين",
            "حذف الموردين",
            "تقارير الموردين"
        ],
        "👤 إدارة المستخدمين": [
            "عرض جميع المستخدمين",
            "إضافة مستخدمين جدد",
            "تعديل بيانات المستخدمين",
            "حذف المستخدمين",
            "إدارة الأدوار والصلاحيات",
            "إعادة تعيين كلمات المرور"
        ],
        "📈 التقارير والتحليلات": [
            "تقارير المبيعات",
            "تقارير المخزون",
            "تقارير العملاء",
            "تقارير الموردين",
            "التقارير المالية",
            "تصدير التقارير"
        ],
        "⚙️ إعدادات النظام": [
            "إعدادات الشركة",
            "إعدادات نقاط البيع",
            "إعدادات الضرائب",
            "إعدادات الطباعة",
            "إعدادات الأمان",
            "النسخ الاحتياطي"
        ],
        "🛒 نقاط البيع": [
            "الوصول لواجهة نقاط البيع",
            "معالجة المبيعات",
            "إدارة السلة",
            "تطبيق الخصومات",
            "طرق الدفع المختلفة"
        ],
        "📊 الباركود": [
            "إنشاء باركود للمنتجات",
            "طباعة ملصقات الباركود",
            "قراءة الباركود",
            "إدارة أنواع الباركود"
        ]
    }
    
    for category, permissions in admin_capabilities.items():
        print(f"\n{category}:")
        for permission in permissions:
            print(f"   ✅ {permission}")

def create_additional_admin():
    """Create additional admin user"""
    print("\n➕ إنشاء مدير إضافي")
    print("=" * 30)
    
    username = input("اسم المستخدم: ").strip()
    if not username:
        print("❌ اسم المستخدم مطلوب")
        return None
    
    # Check if username exists
    existing_user = User.query.filter_by(username=username).first()
    if existing_user:
        print(f"❌ اسم المستخدم '{username}' موجود بالفعل")
        return None
    
    email = input("البريد الإلكتروني: ").strip()
    if not email:
        print("❌ البريد الإلكتروني مطلوب")
        return None
    
    # Check if email exists
    existing_email = User.query.filter_by(email=email).first()
    if existing_email:
        print(f"❌ البريد الإلكتروني '{email}' موجود بالفعل")
        return None
    
    password = input("كلمة المرور: ").strip()
    if not password:
        print("❌ كلمة المرور مطلوبة")
        return None
    
    first_name_ar = input("الاسم الأول (عربي): ").strip() or "مدير"
    last_name_ar = input("اسم العائلة (عربي): ").strip() or "النظام"
    first_name_en = input("الاسم الأول (إنجليزي): ").strip() or "System"
    last_name_en = input("اسم العائلة (إنجليزي): ").strip() or "Admin"
    phone = input("رقم الهاتف (اختياري): ").strip()
    
    try:
        admin = User(
            username=username,
            email=email,
            first_name_ar=first_name_ar,
            first_name_en=first_name_en,
            last_name_ar=last_name_ar,
            last_name_en=last_name_en,
            role='admin',
            is_active=True,
            phone=phone if phone else None
        )
        admin.set_password(password)
        
        db.session.add(admin)
        db.session.commit()
        
        print("\n✅ تم إنشاء المدير الجديد بنجاح:")
        print(f"   👤 اسم المستخدم: {username}")
        print(f"   📧 البريد: {email}")
        print(f"   🎭 الدور: admin")
        
        return admin
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدير: {e}")
        db.session.rollback()
        return None

def update_admin_password():
    """Update admin password"""
    print("\n🔑 تحديث كلمة مرور المدير")
    print("=" * 30)
    
    username = input("اسم المستخدم للمدير: ").strip()
    if not username:
        print("❌ اسم المستخدم مطلوب")
        return False
    
    admin = User.query.filter_by(username=username, role='admin').first()
    if not admin:
        print(f"❌ لم يتم العثور على مدير باسم '{username}'")
        return False
    
    new_password = input("كلمة المرور الجديدة: ").strip()
    if not new_password:
        print("❌ كلمة المرور الجديدة مطلوبة")
        return False
    
    try:
        admin.set_password(new_password)
        db.session.commit()
        
        print(f"✅ تم تحديث كلمة مرور المدير '{username}' بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث كلمة المرور: {e}")
        db.session.rollback()
        return False

def admin_management_menu():
    """Interactive admin management menu"""
    app = create_app()
    
    with app.app_context():
        while True:
            print("\n" + "=" * 60)
            print("🔧 إدارة المديرين - نظام نقاط البيع القطري")
            print("=" * 60)
            print("1. عرض المديرين الحاليين")
            print("2. اختبار صلاحيات المدير")
            print("3. إنشاء مدير جديد")
            print("4. تحديث كلمة مرور مدير")
            print("5. عرض صلاحيات المدير")
            print("0. خروج")
            print("=" * 60)
            
            choice = input("اختر رقم العملية: ").strip()
            
            if choice == '1':
                # Show current admins
                admins = User.query.filter_by(role='admin').all()
                print(f"\n👥 المديرون الحاليون ({len(admins)}):")
                for admin in admins:
                    print(f"   👤 {admin.username} - {admin.email}")
                    print(f"      الاسم: {admin.get_full_name('ar')}")
                    print(f"      نشط: {admin.is_active}")
                    print(f"      آخر دخول: {admin.last_login or 'لم يسجل دخول'}")
                    print()
            
            elif choice == '2':
                test_admin_permissions()
            
            elif choice == '3':
                create_additional_admin()
            
            elif choice == '4':
                update_admin_password()
            
            elif choice == '5':
                display_admin_permissions()
            
            elif choice == '0':
                print("👋 وداعاً!")
                break
            
            else:
                print("❌ اختيار غير صحيح")

if __name__ == '__main__':
    print("🚀 إعداد وإدارة دور المدير - نظام نقاط البيع القطري")
    print("=" * 70)
    
    try:
        # Setup admin role
        setup_admin_role()
        
        # Ask if user wants interactive menu
        print("\n" + "=" * 50)
        interactive = input("هل تريد فتح قائمة الإدارة التفاعلية؟ (y/n): ").strip().lower()
        
        if interactive in ['y', 'yes', 'نعم', 'ن']:
            admin_management_menu()
        else:
            print("✅ تم إعداد دور المدير بنجاح!")
            print("🔗 يمكنك الوصول للنظام على: http://localhost:2626")
            print("👤 تسجيل الدخول باستخدام: admin / admin123")
        
    except Exception as e:
        print(f"💥 خطأ في إعداد دور المدير: {e}")
        import traceback
        traceback.print_exc()
