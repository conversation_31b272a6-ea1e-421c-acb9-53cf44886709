{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        إدارة الموردين
    {% else %}
        Suppliers Management
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            إدارة الموردين
                        {% else %}
                            Suppliers Management
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('suppliers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            {% if language == 'ar' %}إضافة مورد جديد{% else %}Add New Supplier{% endif %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="GET" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           value="{{ search or '' }}" 
                                           placeholder="{% if language == 'ar' %}البحث في الموردين...{% else %}Search suppliers...{% endif %}">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Suppliers Table -->
                    {% if suppliers.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{% if language == 'ar' %}كود المورد{% else %}Supplier Code{% endif %}</th>
                                    <th>{% if language == 'ar' %}اسم الشركة{% else %}Company Name{% endif %}</th>
                                    <th>{% if language == 'ar' %}الهاتف{% else %}Phone{% endif %}</th>
                                    <th>{% if language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}</th>
                                    <th>{% if language == 'ar' %}المدينة{% else %}City{% endif %}</th>
                                    <th>{% if language == 'ar' %}الحالة{% else %}Status{% endif %}</th>
                                    <th>{% if language == 'ar' %}الإجراءات{% else %}Actions{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers.items %}
                                <tr>
                                    <td>{{ supplier.supplier_code }}</td>
                                    <td>{{ supplier.get_company_name(language) }}</td>
                                    <td>{{ supplier.phone or '-' }}</td>
                                    <td>{{ supplier.email or '-' }}</td>
                                    <td>{{ supplier.get_city(language) or '-' }}</td>
                                    <td>
                                        {% if supplier.is_active %}
                                            <span class="badge badge-success">
                                                {% if language == 'ar' %}نشط{% else %}Active{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="badge badge-danger">
                                                {% if language == 'ar' %}غير نشط{% else %}Inactive{% endif %}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('suppliers.view', supplier_id=supplier.id) }}" 
                                               class="btn btn-sm btn-info" title="{% if language == 'ar' %}عرض{% else %}View{% endif %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" 
                                               class="btn btn-sm btn-warning" title="{% if language == 'ar' %}تعديل{% else %}Edit{% endif %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if suppliers.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if suppliers.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.prev_num, search=search) }}">
                                        {% if language == 'ar' %}السابق{% else %}Previous{% endif %}
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in suppliers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != suppliers.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('suppliers.index', page=page_num, search=search) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if suppliers.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.next_num, search=search) }}">
                                        {% if language == 'ar' %}التالي{% else %}Next{% endif %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">
                            {% if search %}
                                {% if language == 'ar' %}
                                    لم يتم العثور على موردين مطابقين للبحث
                                {% else %}
                                    No suppliers found matching your search
                                {% endif %}
                            {% else %}
                                {% if language == 'ar' %}
                                    لا توجد موردين مسجلين بعد
                                {% else %}
                                    No suppliers registered yet
                                {% endif %}
                            {% endif %}
                        </h5>
                        {% if not search %}
                        <a href="{{ url_for('suppliers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            {% if language == 'ar' %}إضافة أول مورد{% else %}Add First Supplier{% endif %}
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
