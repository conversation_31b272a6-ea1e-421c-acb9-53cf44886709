"""
مسارات سلة المشتريات - نظام نقاط البيع القطري
Cart Routes - Qatar POS System
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from flask_login import login_required, current_user
from models.product import Product
from models.customer import Customer
from models.sale import Sale, SaleItem
from models.category import Category
from extensions import db
from utils.helpers import get_user_language
from decimal import Decimal
import json
from datetime import datetime

cart_bp = Blueprint('cart', __name__)

@cart_bp.route('/')
@login_required
def index():
    """صفحة سلة المشتريات الرئيسية"""
    language = get_user_language()
    
    # جلب العملاء الحديثين
    recent_customers = Customer.query.order_by(Customer.created_at.desc()).limit(10).all()
    
    # جلب الفئات للفلترة
    categories = Category.query.filter_by(is_active=True).all()
    
    # جلب المنتجات
    products = Product.query.filter_by(is_active=True).limit(50).all()
    
    return render_template('cart/index.html',
                         language=language,
                         recent_customers=recent_customers,
                         categories=categories,
                         products=products)

@cart_bp.route('/api/products')
@login_required
def api_products():
    """API لجلب المنتجات مع الفلترة"""
    try:
        # معاملات البحث
        search = request.args.get('search', '').strip()
        category_id = request.args.get('category_id', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # بناء الاستعلام
        query = Product.query.filter_by(is_active=True)

        # فلترة بالبحث
        if search:
            query = query.filter(
                db.or_(
                    Product.name_ar.contains(search),
                    Product.name_en.contains(search),
                    Product.sku.contains(search),
                    Product.barcode.contains(search)
                )
            )

        # فلترة بالفئة
        if category_id:
            query = query.filter_by(category_id=category_id)

        # ترقيم الصفحات
        products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # تحويل البيانات
        language = get_user_language()
        products_data = []

        for product in products.items:
            try:
                # Get product name safely
                if language == 'ar':
                    product_name = getattr(product, 'name_ar', None) or getattr(product, 'name_en', None) or 'منتج بدون اسم'
                else:
                    product_name = getattr(product, 'name_en', None) or getattr(product, 'name_ar', None) or 'Unnamed Product'

                # Get category name safely
                category_name = ''
                if hasattr(product, 'category') and product.category:
                    if language == 'ar':
                        category_name = getattr(product.category, 'name_ar', None) or getattr(product.category, 'name_en', None) or ''
                    else:
                        category_name = getattr(product.category, 'name_en', None) or getattr(product.category, 'name_ar', None) or ''

                products_data.append({
                    'id': product.id,
                    'name': product_name,
                    'sku': getattr(product, 'sku', '') or '',
                    'barcode': getattr(product, 'barcode', '') or '',
                    'price': float(getattr(product, 'selling_price', 0) or 0),
                    'cost': float(getattr(product, 'cost_price', 0) or 0),
                    'stock': getattr(product, 'current_stock', 0) or 0,
                    'image': getattr(product, 'image_url', None) or '/static/images/no-image.png',
                    'category': category_name,
                    'unit': getattr(product, 'unit', None) or ('قطعة' if language == 'ar' else 'piece')
                })
            except Exception as product_error:
                # Skip problematic products
                continue

        return jsonify({
            'success': True,
            'products': products_data,
            'pagination': {
                'page': products.page,
                'pages': products.pages,
                'per_page': products.per_page,
                'total': products.total,
                'has_next': products.has_next,
                'has_prev': products.has_prev
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المنتجات: {str(e)}'
        }), 500

@cart_bp.route('/api/product/<int:product_id>')
@login_required
def api_product_details(product_id):
    """API لجلب تفاصيل منتج محدد"""
    try:
        product = Product.query.get_or_404(product_id)
        language = get_user_language()
        
        # Get product name safely
        if language == 'ar':
            product_name = product.name_ar or product.name_en or 'منتج بدون اسم'
        else:
            product_name = product.name_en or product.name_ar or 'Unnamed Product'

        # Get category name safely
        category_name = ''
        if product.category:
            if language == 'ar':
                category_name = product.category.name_ar or product.category.name_en or ''
            else:
                category_name = product.category.name_en or product.category.name_ar or ''

        # Get description safely
        if language == 'ar':
            description = product.description_ar or product.description_en or ''
        else:
            description = product.description_en or product.description_ar or ''

        return jsonify({
            'success': True,
            'product': {
                'id': product.id,
                'name': product_name,
                'sku': product.sku or '',
                'barcode': product.barcode or '',
                'price': float(product.selling_price or 0),
                'cost': float(product.cost_price or 0),
                'stock': product.current_stock or 0,
                'image': product.image_url if product.image_url else '/static/images/no-image.png',
                'category': category_name,
                'unit': product.unit or ('قطعة' if language == 'ar' else 'piece'),
                'description': description
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب تفاصيل المنتج: {str(e)}'
        }), 500

@cart_bp.route('/api/search')
@login_required
def api_search():
    """API للبحث السريع في المنتجات"""
    try:
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'success': True, 'products': []})
        
        # البحث في المنتجات
        products = Product.query.filter(
            Product.is_active == True,
            db.or_(
                Product.name_ar.contains(query),
                Product.name_en.contains(query),
                Product.sku.contains(query),
                Product.barcode.contains(query)
            )
        ).limit(10).all()
        
        language = get_user_language()
        results = []
        
        for product in products:
            # Get product name safely
            if language == 'ar':
                product_name = product.name_ar or product.name_en or 'منتج بدون اسم'
            else:
                product_name = product.name_en or product.name_ar or 'Unnamed Product'

            results.append({
                'id': product.id,
                'name': product_name,
                'sku': product.sku or '',
                'barcode': product.barcode or '',
                'price': float(product.selling_price or 0),
                'stock': product.current_stock or 0,
                'image': product.image_url if product.image_url else '/static/images/no-image.png'
            })
        
        return jsonify({
            'success': True,
            'products': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        }), 500

@cart_bp.route('/api/barcode/<barcode>')
@login_required
def api_search_barcode(barcode):
    """API للبحث بالباركود"""
    try:
        product = Product.query.filter_by(
            barcode=barcode,
            is_active=True
        ).first()
        
        if not product:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على منتج بهذا الباركود'
            }), 404
        
        language = get_user_language()
        
        # Get product name safely
        if language == 'ar':
            product_name = product.name_ar or product.name_en or 'منتج بدون اسم'
        else:
            product_name = product.name_en or product.name_ar or 'Unnamed Product'

        return jsonify({
            'success': True,
            'product': {
                'id': product.id,
                'name': product_name,
                'sku': product.sku or '',
                'barcode': product.barcode or '',
                'price': float(product.selling_price or 0),
                'stock': product.current_stock or 0,
                'image': product.image_url if product.image_url else '/static/images/no-image.png'
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث بالباركود: {str(e)}'
        }), 500

@cart_bp.route('/api/customers')
@login_required
def api_customers():
    """API لجلب العملاء"""
    try:
        search = request.args.get('search', '').strip()
        
        query = Customer.query.filter_by(is_active=True)
        
        if search:
            query = query.filter(
                db.or_(
                    Customer.name_ar.contains(search),
                    Customer.name_en.contains(search),
                    Customer.phone.contains(search),
                    Customer.email.contains(search)
                )
            )
        
        customers = query.limit(20).all()
        language = get_user_language()
        
        customers_data = []
        for customer in customers:
            # Get customer name safely
            if language == 'ar':
                customer_name = customer.name_ar or customer.name_en or 'عميل بدون اسم'
            else:
                customer_name = customer.name_en or customer.name_ar or 'Unnamed Customer'

            customers_data.append({
                'id': customer.id,
                'name': customer_name,
                'phone': customer.phone or '',
                'email': customer.email or '',
                'address': customer.address or ''
            })
        
        return jsonify({
            'success': True,
            'customers': customers_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب العملاء: {str(e)}'
        }), 500

@cart_bp.route('/api/categories')
@login_required
def api_categories():
    """API لجلب الفئات"""
    try:
        categories = Category.query.filter_by(is_active=True).all()
        language = get_user_language()
        
        categories_data = []
        for category in categories:
            # Get category name safely
            if language == 'ar':
                category_name = category.name_ar or category.name_en or 'فئة بدون اسم'
            else:
                category_name = category.name_en or category.name_ar or 'Unnamed Category'

            categories_data.append({
                'id': category.id,
                'name': category_name
            })
        
        return jsonify({
            'success': True,
            'categories': categories_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الفئات: {str(e)}'
        }), 500
