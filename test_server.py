#!/usr/bin/env python3
"""
Qatar POS System - Minimal Test Server
Just to verify the system is working
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>نظام نقاط البيع القطري - اختبار</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 40px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container { 
                background: white; 
                padding: 40px; 
                border-radius: 15px; 
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                max-width: 600px;
                width: 100%;
            }
            h1 { 
                color: #333; 
                margin-bottom: 20px;
                font-size: 2.5em;
            }
            .success { 
                color: #28a745; 
                font-size: 1.5em;
                margin: 20px 0;
            }
            .info { 
                color: #17a2b8; 
                margin: 15px 0;
                font-size: 1.1em;
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .feature {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                border-left: 4px solid #007bff;
            }
            .feature h3 {
                color: #333;
                margin-bottom: 10px;
            }
            .links {
                margin-top: 30px;
            }
            .links a {
                display: inline-block;
                margin: 10px;
                padding: 12px 24px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 8px;
                transition: background 0.3s;
            }
            .links a:hover {
                background: #0056b3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🇶🇦 نظام نقاط البيع القطري</h1>
            <h2>Qatar POS System</h2>
            
            <div class="success">✅ النظام يعمل بنجاح!</div>
            <div class="success">✅ System is working successfully!</div>
            
            <div class="info">📍 الخادم: http://localhost:5000</div>
            <div class="info">📍 Server: http://localhost:5000</div>
            
            <div class="features">
                <div class="feature">
                    <h3>🌐 متعدد اللغات</h3>
                    <p>Arabic & English</p>
                </div>
                <div class="feature">
                    <h3>💰 الريال القطري</h3>
                    <p>QAR Support</p>
                </div>
                <div class="feature">
                    <h3>🏢 للشركات القطرية</h3>
                    <p>Qatar Business Ready</p>
                </div>
                <div class="feature">
                    <h3>🆔 الهوية القطرية</h3>
                    <p>Qatar ID Support</p>
                </div>
            </div>
            
            <div class="links">
                <a href="/test">صفحة الاختبار / Test Page</a>
                <a href="/health">فحص النظام / Health</a>
                <a href="/simple">النظام البسيط / Simple System</a>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return '''
    <h1>🧪 Test Page / صفحة الاختبار</h1>
    <p>✅ Flask is working!</p>
    <p>✅ فلاسك يعمل بنجاح!</p>
    <p>🇶🇦 Qatar POS System Test</p>
    <p><a href="/">← Back to Home / العودة للرئيسية</a></p>
    '''

@app.route('/health')
def health():
    return {
        'status': 'ok',
        'message': 'Qatar POS System is healthy',
        'message_ar': 'نظام نقاط البيع القطري يعمل بصحة جيدة',
        'version': '1.0.0',
        'server': 'Flask Development Server'
    }

@app.route('/simple')
def simple():
    return '''
    <div style="font-family: Arial; padding: 40px; text-align: center;">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System - Simple Mode</h2>
        <hr>
        <h3>✅ النظام جاهز للعمل!</h3>
        <h3>✅ System Ready to Use!</h3>
        <p>هذا اختبار بسيط للتأكد من عمل النظام</p>
        <p>This is a simple test to verify the system is working</p>
        <hr>
        <p><strong>Next Steps / الخطوات التالية:</strong></p>
        <ol style="text-align: right; display: inline-block;">
            <li>تشغيل النظام الكامل / Run full system: <code>python run.py</code></li>
            <li>أو النظام البسيط / Or simple system: <code>python simple_run.py</code></li>
            <li>فتح المتصفح / Open browser: <code>http://localhost:5000</code></li>
        </ol>
        <p><a href="/">← العودة / Back</a></p>
    </div>
    '''

if __name__ == '__main__':
    print("🚀 Starting Qatar POS Test Server...")
    print("🇶🇦 تشغيل خادم اختبار نظام نقاط البيع القطري")
    print("=" * 50)
    print("📍 URL: http://localhost:5000")
    print("🧪 Test: http://localhost:5000/test")
    print("💚 Health: http://localhost:5000/health")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("💡 Try running: pip install flask")
        input("Press Enter to exit...")
