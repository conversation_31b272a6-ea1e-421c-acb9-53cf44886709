#!/usr/bin/env python3
"""
Qatar POS System - Run Now
The simplest way to run the system
"""

print("🇶🇦 Qatar POS System - Starting...")
print("نظام نقاط البيع القطري - جاري التشغيل...")

# Try different methods to run the server
import sys
import os

def try_flask():
    """Try to run with Flask"""
    try:
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/')
        def home():
            return '''
            <h1 style="text-align: center; color: #333; font-family: Arial;">
                🇶🇦 نظام نقاط البيع القطري<br>
                Qatar POS System<br>
                <span style="color: #28a745;">✅ يعمل بنجاح! Working!</span>
            </h1>
            <div style="text-align: center; margin: 20px;">
                <a href="/test" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">Test Page</a>
            </div>
            '''
        
        @app.route('/test')
        def test():
            return '<h1 style="text-align: center; color: #28a745;">✅ Flask Test Successful!</h1><p style="text-align: center;"><a href="/">Back</a></p>'
        
        print("✅ Flask found - starting Flask server...")
        print("📍 URL: http://localhost:5000")
        app.run(host='0.0.0.0', port=5000, debug=True)
        return True
        
    except ImportError:
        print("⚠️ Flask not found")
        return False
    except Exception as e:
        print(f"❌ Flask error: {e}")
        return False

def try_simple_server():
    """Try simple HTTP server"""
    try:
        import http.server
        import socketserver
        
        class Handler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    html = '''
                    <!DOCTYPE html>
                    <html>
                    <head><meta charset="UTF-8"><title>Qatar POS</title></head>
                    <body style="font-family: Arial; text-align: center; padding: 50px;">
                        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
                        <h2>Qatar POS System</h2>
                        <div style="color: #28a745; font-size: 1.5em; margin: 20px;">
                            ✅ النظام يعمل بنجاح!<br>
                            ✅ System Working!
                        </div>
                        <p>Simple HTTP Server - No Flask needed!</p>
                        <a href="/test" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none;">Test</a>
                    </body>
                    </html>
                    '''
                    self.wfile.write(html.encode('utf-8'))
                elif self.path == '/test':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    self.wfile.write(b'<h1 style="text-align: center; color: #28a745;">Test OK!</h1><p style="text-align: center;"><a href="/">Back</a></p>')
                else:
                    self.send_response(404)
                    self.end_headers()
        
        print("✅ Starting simple HTTP server...")
        print("📍 URL: http://localhost:5000")
        with socketserver.TCPServer(("", 5000), Handler) as httpd:
            httpd.serve_forever()
        return True
        
    except Exception as e:
        print(f"❌ Simple server error: {e}")
        return False

def main():
    print("🔍 Trying to start server...")
    
    # Try Flask first
    if try_flask():
        return
    
    # Try simple server
    print("🔄 Trying simple HTTP server...")
    if try_simple_server():
        return
    
    # If all fails
    print("❌ Could not start server")
    print("💡 Try: pip install flask")
    print("💡 Or run: python emergency_server.py")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Try: python emergency_server.py")
