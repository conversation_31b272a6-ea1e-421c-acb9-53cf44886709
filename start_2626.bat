@echo off
chcp 65001 >nul
title نظام نقاط البيع القطري - المنفذ 2626

echo.
echo ========================================
echo 🇶🇦 نظام نقاط البيع القطري
echo    Qatar POS System - Port 2626
echo ========================================
echo.

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

echo 🚀 Starting Qatar POS System on port 2626...
echo.
echo 📍 Server will be available at: http://127.0.0.1:2626
echo 🔑 Default login: admin / admin123
echo 🌐 Test page: http://127.0.0.1:2626/test
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Try different server files in order of preference
echo 📍 Trying custom port 2626 server...
python server_2626.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying main application...
python app.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying simple run...
python simple_run.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying test server...
python test_server.py
if not errorlevel 1 goto success

echo.
echo ❌ All servers failed to start
echo.
echo 💡 Solutions:
echo 1. Install Flask: pip install flask
echo 2. Check port 2626 is free
echo 3. Run as administrator
echo.
pause
exit /b 1

:success
echo.
echo 🎉 Server started successfully!
echo 🌐 Open: http://127.0.0.1:2626
pause
