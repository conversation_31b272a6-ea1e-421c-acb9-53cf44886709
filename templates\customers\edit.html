{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        تعديل العميل
    {% else %}
        Edit Customer
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            تعديل العميل: {{ customer.get_display_name(language) }}
                        {% else %}
                            Edit Customer: {{ customer.get_display_name(language) }}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('customers.view', customer_id=customer.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <form method="POST" class="card-body">
                    <!-- Customer Type -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="customer_type">
                                    {% if language == 'ar' %}نوع العميل{% else %}Customer Type{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="customer_type" name="customer_type" required>
                                    <option value="individual" {% if customer.customer_type == 'individual' %}selected{% endif %}>
                                        {% if language == 'ar' %}فرد{% else %}Individual{% endif %}
                                    </option>
                                    <option value="company" {% if customer.customer_type == 'company' %}selected{% endif %}>
                                        {% if language == 'ar' %}شركة{% else %}Company{% endif %}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_active">
                                    {% if language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                           {% if customer.is_active %}checked{% endif %}>
                                    <label class="custom-control-label" for="is_active">
                                        {% if language == 'ar' %}نشط{% else %}Active{% endif %}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Individual Fields -->
                    <div id="individual-fields" style="display: {% if customer.customer_type == 'individual' %}block{% else %}none{% endif %};">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name_ar">
                                        {% if language == 'ar' %}الاسم الأول (عربي){% else %}First Name (Arabic){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="first_name_ar" name="first_name_ar" 
                                           value="{{ customer.first_name_ar or '' }}" dir="rtl">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name_en">
                                        {% if language == 'ar' %}الاسم الأول (إنجليزي){% else %}First Name (English){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="first_name_en" name="first_name_en" 
                                           value="{{ customer.first_name_en or '' }}" dir="ltr">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name_ar">
                                        {% if language == 'ar' %}اسم العائلة (عربي){% else %}Last Name (Arabic){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="last_name_ar" name="last_name_ar" 
                                           value="{{ customer.last_name_ar or '' }}" dir="rtl">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name_en">
                                        {% if language == 'ar' %}اسم العائلة (إنجليزي){% else %}Last Name (English){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="last_name_en" name="last_name_en" 
                                           value="{{ customer.last_name_en or '' }}" dir="ltr">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Company Fields -->
                    <div id="company-fields" style="display: {% if customer.customer_type == 'company' %}block{% else %}none{% endif %};">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name_ar">
                                        {% if language == 'ar' %}اسم الشركة (عربي){% else %}Company Name (Arabic){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="company_name_ar" name="company_name_ar" 
                                           value="{{ customer.company_name_ar or '' }}" dir="rtl">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name_en">
                                        {% if language == 'ar' %}اسم الشركة (إنجليزي){% else %}Company Name (English){% endif %}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                           value="{{ customer.company_name_en or '' }}" dir="ltr">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="commercial_registration">
                                        {% if language == 'ar' %}السجل التجاري{% else %}Commercial Registration{% endif %}
                                    </label>
                                    <input type="text" class="form-control" id="commercial_registration" name="commercial_registration" 
                                           value="{{ customer.commercial_registration or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tax_number">
                                        {% if language == 'ar' %}الرقم الضريبي{% else %}Tax Number{% endif %}
                                    </label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                           value="{{ customer.tax_number or '' }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <h5 class="mt-4">
                        {% if language == 'ar' %}معلومات الاتصال{% else %}Contact Information{% endif %}
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">
                                    {% if language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ customer.phone or '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">
                                    {% if language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ customer.email or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <h5 class="mt-4">
                        {% if language == 'ar' %}معلومات العنوان{% else %}Address Information{% endif %}
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="address_ar">
                                    {% if language == 'ar' %}العنوان (عربي){% else %}Address (Arabic){% endif %}
                                </label>
                                <textarea class="form-control" id="address_ar" name="address_ar" rows="3" dir="rtl">{{ customer.address_ar or '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="address_en">
                                    {% if language == 'ar' %}العنوان (إنجليزي){% else %}Address (English){% endif %}
                                </label>
                                <textarea class="form-control" id="address_en" name="address_en" rows="3" dir="ltr">{{ customer.address_en or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="city_ar">
                                    {% if language == 'ar' %}المدينة (عربي){% else %}City (Arabic){% endif %}
                                </label>
                                <input type="text" class="form-control" id="city_ar" name="city_ar" 
                                       value="{{ customer.city_ar or '' }}" dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="city_en">
                                    {% if language == 'ar' %}المدينة (إنجليزي){% else %}City (English){% endif %}
                                </label>
                                <input type="text" class="form-control" id="city_en" name="city_en" 
                                       value="{{ customer.city_en or '' }}" dir="ltr">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="postal_code">
                                    {% if language == 'ar' %}الرمز البريدي{% else %}Postal Code{% endif %}
                                </label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ customer.postal_code or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Qatar ID -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="qatar_id">
                                    {% if language == 'ar' %}رقم الهوية القطرية{% else %}Qatar ID{% endif %}
                                </label>
                                <input type="text" class="form-control" id="qatar_id" name="qatar_id" 
                                       value="{{ customer.qatar_id or '' }}" maxlength="11" 
                                       placeholder="{% if language == 'ar' %}مثال: 12345678901{% else %}Example: 12345678901{% endif %}">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if language == 'ar' %}حفظ التغييرات{% else %}Save Changes{% endif %}
                            </button>
                            <a href="{{ url_for('customers.view', customer_id=customer.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('customer_type').addEventListener('change', function() {
    const customerType = this.value;
    const individualFields = document.getElementById('individual-fields');
    const companyFields = document.getElementById('company-fields');
    
    if (customerType === 'individual') {
        individualFields.style.display = 'block';
        companyFields.style.display = 'none';
        
        // Make individual fields required
        document.getElementById('first_name_ar').required = true;
        document.getElementById('first_name_en').required = true;
        document.getElementById('last_name_ar').required = true;
        document.getElementById('last_name_en').required = true;
        
        // Remove company field requirements
        document.getElementById('company_name_ar').required = false;
        document.getElementById('company_name_en').required = false;
    } else {
        individualFields.style.display = 'none';
        companyFields.style.display = 'block';
        
        // Remove individual field requirements
        document.getElementById('first_name_ar').required = false;
        document.getElementById('first_name_en').required = false;
        document.getElementById('last_name_ar').required = false;
        document.getElementById('last_name_en').required = false;
        
        // Make company fields required
        document.getElementById('company_name_ar').required = true;
        document.getElementById('company_name_en').required = true;
    }
});
</script>
{% endblock %}
