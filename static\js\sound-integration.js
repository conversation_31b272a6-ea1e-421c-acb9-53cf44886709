/**
 * Qatar POS System - Sound Integration
 * Integrates sounds with system events and UI interactions
 */

class SoundIntegration {
    constructor() {
        this.settings = {};
        this.init();
    }

    async init() {
        // Wait for sound manager to be ready
        if (typeof window.soundManager === 'undefined') {
            setTimeout(() => this.init(), 100);
            return;
        }

        // Load sound settings from server
        await this.loadSettings();
        
        // Apply settings to sound manager
        this.applySettings();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('Sound Integration initialized');
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/settings/sounds');
            if (response.ok) {
                const data = await response.json();
                this.settings = data;
            }
        } catch (error) {
            console.warn('Failed to load sound settings:', error);
            // Use default settings
            this.settings = {
                sounds_enabled: { value: true },
                sound_volume: { value: 0.5 },
                sale_sound_enabled: { value: true },
                error_sound_enabled: { value: true },
                notification_sound_enabled: { value: true },
                button_click_sound_enabled: { value: false },
                payment_sound_enabled: { value: true },
                low_stock_sound_enabled: { value: true }
            };
        }
    }

    applySettings() {
        if (window.soundManager) {
            const enabled = this.getSetting('sounds_enabled', true);
            const volume = this.getSetting('sound_volume', 0.5);
            
            window.soundManager.setEnabled(enabled);
            window.soundManager.setVolume(volume);
        }
    }

    getSetting(key, defaultValue) {
        return this.settings[key] ? this.settings[key].value : defaultValue;
    }

    setupEventListeners() {
        // Sale completion sounds
        document.addEventListener('saleCompleted', () => {
            if (this.getSetting('sale_sound_enabled', true)) {
                window.soundManager.playSale();
            }
        });

        // Payment received sounds
        document.addEventListener('paymentReceived', () => {
            if (this.getSetting('payment_sound_enabled', true)) {
                window.soundManager.playPayment();
            }
        });

        // Error sounds
        document.addEventListener('errorOccurred', () => {
            if (this.getSetting('error_sound_enabled', true)) {
                window.soundManager.playError();
            }
        });

        // Success sounds
        document.addEventListener('operationSuccess', () => {
            if (this.getSetting('sale_sound_enabled', true)) {
                window.soundManager.playSuccess();
            }
        });

        // Notification sounds
        document.addEventListener('notificationShown', () => {
            if (this.getSetting('notification_sound_enabled', true)) {
                window.soundManager.playNotification();
            }
        });

        // Low stock alerts
        document.addEventListener('lowStockAlert', () => {
            if (this.getSetting('low_stock_sound_enabled', true)) {
                window.soundManager.playLowStock();
            }
        });

        // Button click sounds (if enabled)
        if (this.getSetting('button_click_sound_enabled', false)) {
            document.addEventListener('click', (e) => {
                if (e.target.matches('button, .btn, input[type="submit"], input[type="button"]')) {
                    window.soundManager.playClick();
                }
            });
        }

        // Form submission sounds
        document.addEventListener('submit', (e) => {
            // Play success sound for form submissions
            setTimeout(() => {
                if (this.getSetting('sale_sound_enabled', true)) {
                    window.soundManager.playSuccess();
                }
            }, 100);
        });

        // AJAX success/error handling
        $(document).ajaxSuccess(() => {
            if (this.getSetting('sale_sound_enabled', true)) {
                window.soundManager.playSuccess();
            }
        });

        $(document).ajaxError(() => {
            if (this.getSetting('error_sound_enabled', true)) {
                window.soundManager.playError();
            }
        });
    }

    // Manual sound triggers for specific operations
    playOperationSound(operation) {
        switch (operation) {
            case 'sale':
                if (this.getSetting('sale_sound_enabled', true)) {
                    window.soundManager.playSale();
                }
                break;
            case 'payment':
                if (this.getSetting('payment_sound_enabled', true)) {
                    window.soundManager.playPayment();
                }
                break;
            case 'error':
                if (this.getSetting('error_sound_enabled', true)) {
                    window.soundManager.playError();
                }
                break;
            case 'success':
                if (this.getSetting('sale_sound_enabled', true)) {
                    window.soundManager.playSuccess();
                }
                break;
            case 'notification':
                if (this.getSetting('notification_sound_enabled', true)) {
                    window.soundManager.playNotification();
                }
                break;
            case 'warning':
                if (this.getSetting('error_sound_enabled', true)) {
                    window.soundManager.playWarning();
                }
                break;
            case 'lowStock':
                if (this.getSetting('low_stock_sound_enabled', true)) {
                    window.soundManager.playLowStock();
                }
                break;
        }
    }

    // Update settings
    async updateSettings(newSettings) {
        try {
            const response = await fetch('/api/settings/sounds', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(newSettings)
            });

            if (response.ok) {
                // Reload settings
                await this.loadSettings();
                this.applySettings();
                return true;
            }
        } catch (error) {
            console.error('Failed to update sound settings:', error);
        }
        return false;
    }

    // Test all sounds
    testAllSounds() {
        if (window.soundManager) {
            window.soundManager.testSounds();
        }
    }
}

// Initialize sound integration when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.soundIntegration = new SoundIntegration();
});

// Helper functions for easy access
window.playSound = function(soundName) {
    if (window.soundIntegration) {
        window.soundIntegration.playOperationSound(soundName);
    }
};

window.testSounds = function() {
    if (window.soundIntegration) {
        window.soundIntegration.testAllSounds();
    }
};
