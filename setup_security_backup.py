#!/usr/bin/env python3
"""
Setup script for security and backup settings
Qatar POS System
"""

import sys
import os
sys.path.append('.')

from app import create_app
from extensions import db
from models.setting import Setting
from utils.backup_manager import backup_manager
from utils.security_manager import security_manager

def setup_security_settings():
    """Setup default security settings"""
    
    print("🔒 Setting up security settings...")
    
    app = create_app()
    
    with app.app_context():
        # Security settings to create
        security_settings = [
            {
                'key': 'force_password_change',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'إجبار تغيير كلمة المرور',
                'description_en': 'Force password change'
            },
            {
                'key': 'session_timeout',
                'value': '480',
                'value_type': 'integer',
                'category': 'security',
                'description_ar': 'مدة انتهاء الجلسة بالدقائق',
                'description_en': 'Session timeout in minutes'
            },
            {
                'key': 'two_factor_enabled',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'المصادقة الثنائية',
                'description_en': 'Two-factor authentication'
            },
            {
                'key': 'max_login_attempts',
                'value': '5',
                'value_type': 'integer',
                'category': 'security',
                'description_ar': 'عدد محاولات تسجيل الدخول',
                'description_en': 'Maximum login attempts'
            },
            {
                'key': 'min_password_length',
                'value': '8',
                'value_type': 'integer',
                'category': 'security',
                'description_ar': 'الحد الأدنى لطول كلمة المرور',
                'description_en': 'Minimum password length'
            },
            {
                'key': 'require_uppercase',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'يتطلب أحرف كبيرة',
                'description_en': 'Require uppercase letters'
            },
            {
                'key': 'require_numbers',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'يتطلب أرقام',
                'description_en': 'Require numbers'
            },
            {
                'key': 'require_special_chars',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'يتطلب رموز خاصة',
                'description_en': 'Require special characters'
            },
            {
                'key': 'password_expiry_days',
                'value': '0',
                'value_type': 'integer',
                'category': 'security',
                'description_ar': 'انتهاء صلاحية كلمة المرور بالأيام',
                'description_en': 'Password expiry in days'
            },
            {
                'key': 'enable_audit_log',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'تفعيل سجل المراجعة',
                'description_en': 'Enable audit log'
            },
            {
                'key': 'enable_ip_whitelist',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'تفعيل القائمة البيضاء للـ IP',
                'description_en': 'Enable IP whitelist'
            },
            {
                'key': 'enable_ssl_only',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'HTTPS فقط',
                'description_en': 'HTTPS only'
            },
            {
                'key': 'enable_rate_limiting',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'تحديد معدل الطلبات',
                'description_en': 'Enable rate limiting'
            },
            {
                'key': 'enable_data_encryption',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'تشفير البيانات الحساسة',
                'description_en': 'Encrypt sensitive data'
            },
            {
                'key': 'enable_backup_encryption',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'تشفير النسخ الاحتياطية',
                'description_en': 'Encrypt backups'
            },
            {
                'key': 'log_retention_months',
                'value': '6',
                'value_type': 'integer',
                'category': 'security',
                'description_ar': 'مدة الاحتفاظ بالسجلات بالأشهر',
                'description_en': 'Log retention period in months'
            },
            {
                'key': 'enable_gdpr_compliance',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'security',
                'description_ar': 'الامتثال لحماية البيانات',
                'description_en': 'GDPR compliance'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for setting_data in security_settings:
            # Check if setting already exists
            existing = Setting.query.filter_by(key=setting_data['key']).first()
            
            if existing:
                # Update existing setting
                existing.value = setting_data['value']
                existing.value_type = setting_data['value_type']
                existing.category = setting_data['category']
                existing.description_ar = setting_data['description_ar']
                existing.description_en = setting_data['description_en']
                updated_count += 1
                print(f"✅ Updated: {setting_data['key']}")
            else:
                # Create new setting
                new_setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    value_type=setting_data['value_type'],
                    category=setting_data['category'],
                    description_ar=setting_data['description_ar'],
                    description_en=setting_data['description_en']
                )
                db.session.add(new_setting)
                created_count += 1
                print(f"🆕 Created: {setting_data['key']}")
        
        # Commit all changes
        try:
            db.session.commit()
            print(f"\n✅ Successfully setup security settings!")
            print(f"📊 Created: {created_count} settings")
            print(f"🔄 Updated: {updated_count} settings")
            return True
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error setting up security settings: {str(e)}")
            return False

def setup_backup_settings():
    """Setup default backup settings"""
    
    print("\n💾 Setting up backup settings...")
    
    app = create_app()
    
    with app.app_context():
        # Backup settings to create
        backup_settings = [
            {
                'key': 'auto_backup_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'backup',
                'description_ar': 'تفعيل النسخ الاحتياطي التلقائي',
                'description_en': 'Enable automated backup'
            },
            {
                'key': 'backup_frequency',
                'value': 'daily',
                'value_type': 'string',
                'category': 'backup',
                'description_ar': 'تكرار النسخ الاحتياطي',
                'description_en': 'Backup frequency'
            },
            {
                'key': 'backup_time',
                'value': '02:00',
                'value_type': 'string',
                'category': 'backup',
                'description_ar': 'وقت النسخ الاحتياطي',
                'description_en': 'Backup time'
            },
            {
                'key': 'backup_retention_days',
                'value': '30',
                'value_type': 'integer',
                'category': 'backup',
                'description_ar': 'مدة الاحتفاظ بالنسخ الاحتياطية بالأيام',
                'description_en': 'Backup retention period in days'
            },
            {
                'key': 'backup_compression',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'backup',
                'description_ar': 'ضغط النسخ الاحتياطية',
                'description_en': 'Compress backups'
            },
            {
                'key': 'backup_cloud_storage',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'backup',
                'description_ar': 'رفع للتخزين السحابي',
                'description_en': 'Upload to cloud storage'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for setting_data in backup_settings:
            # Check if setting already exists
            existing = Setting.query.filter_by(key=setting_data['key']).first()
            
            if existing:
                # Update existing setting
                existing.value = setting_data['value']
                existing.value_type = setting_data['value_type']
                existing.category = setting_data['category']
                existing.description_ar = setting_data['description_ar']
                existing.description_en = setting_data['description_en']
                updated_count += 1
                print(f"✅ Updated: {setting_data['key']}")
            else:
                # Create new setting
                new_setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    value_type=setting_data['value_type'],
                    category=setting_data['category'],
                    description_ar=setting_data['description_ar'],
                    description_en=setting_data['description_en']
                )
                db.session.add(new_setting)
                created_count += 1
                print(f"🆕 Created: {setting_data['key']}")
        
        # Commit all changes
        try:
            db.session.commit()
            print(f"\n✅ Successfully setup backup settings!")
            print(f"📊 Created: {created_count} settings")
            print(f"🔄 Updated: {updated_count} settings")
            return True
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error setting up backup settings: {str(e)}")
            return False

def test_security_system():
    """Test the security system"""
    
    print("\n🧪 Testing security system...")
    
    app = create_app()
    
    with app.app_context():
        # Test password validation
        test_password = "TestPass123!"
        settings = {
            'min_password_length': '8',
            'require_uppercase': 'true',
            'require_numbers': 'true',
            'require_special_chars': 'false'
        }
        
        is_valid, errors = security_manager.validate_password_policy(test_password, settings)
        print(f"🔐 Password validation: {'✅ PASS' if is_valid else '❌ FAIL'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # Test CSRF token generation
        try:
            token = security_manager.generate_secure_token()
            print(f"🎫 CSRF token generation: {'✅ PASS' if token else '❌ FAIL'}")
        except Exception as e:
            print(f"🎫 CSRF token generation: ❌ FAIL - {str(e)}")
        
        # Test audit logging
        try:
            security_manager.log_security_event('test_event', {'test': True})
            print(f"📝 Audit logging: ✅ PASS")
        except Exception as e:
            print(f"📝 Audit logging: ❌ FAIL - {str(e)}")

def test_backup_system():
    """Test the backup system"""
    
    print("\n🧪 Testing backup system...")
    
    # Test backup directory creation
    if os.path.exists(backup_manager.backup_dir):
        print(f"📁 Backup directory: ✅ PASS")
    else:
        print(f"📁 Backup directory: ❌ FAIL")
    
    # Test encryption key
    if backup_manager.encryption_key:
        print(f"🔑 Encryption key: ✅ PASS")
    else:
        print(f"🔑 Encryption key: ❌ FAIL")
    
    # Test backup creation (small test)
    try:
        # This would create a test backup in production
        print(f"💾 Backup creation: ✅ READY (not tested to avoid creating files)")
    except Exception as e:
        print(f"💾 Backup creation: ❌ FAIL - {str(e)}")

def create_initial_backup():
    """Create initial backup after setup"""
    
    print("\n💾 Creating initial backup...")
    
    try:
        success, result = backup_manager.create_backup(
            backup_type='full',
            compress=True,
            encrypt=True
        )
        
        if success:
            print(f"✅ Initial backup created: {result}")
            return True
        else:
            print(f"❌ Initial backup failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Initial backup error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Qatar POS Security & Backup Setup")
    print("=" * 50)
    
    # Setup security settings
    security_success = setup_security_settings()
    
    # Setup backup settings
    backup_success = setup_backup_settings()
    
    if security_success and backup_success:
        # Test systems
        test_security_system()
        test_backup_system()
        
        # Create initial backup
        print("\n" + "=" * 50)
        backup_created = create_initial_backup()
        
        print(f"\n🎉 Setup completed successfully!")
        print(f"💡 You can now access:")
        print(f"   🔒 Security settings: http://localhost:2626/settings#security")
        print(f"   💾 Backup settings: http://localhost:2626/settings#backup")
        
        if backup_created:
            print(f"   📦 Initial backup created and ready!")
        
    else:
        print(f"\n❌ Setup failed!")
        sys.exit(1)
