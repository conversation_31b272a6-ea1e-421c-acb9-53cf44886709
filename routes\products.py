"""
Product management routes for Qatar POS System
Handles product CRUD operations, categories, and inventory
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
from models.product import Product
from models.category import Category
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language, save_uploaded_file, generate_barcode

products_bp = Blueprint('products', __name__)

@products_bp.route('/')
@login_required
@permission_required('products_read')
def index():
    """List all products"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)
    status_filter = request.args.get('status', '')
    stock_filter = request.args.get('stock', '')
    
    query = Product.query
    
    # Apply filters
    if search:
        query = query.filter(
            (Product.name_ar.contains(search)) |
            (Product.name_en.contains(search)) |
            (Product.sku.contains(search)) |
            (Product.barcode.contains(search))
        )
    
    if category_id:
        query = query.filter(Product.category_id == category_id)
    
    if status_filter == 'active':
        query = query.filter(Product.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(Product.is_active == False)
    
    if stock_filter == 'low':
        query = query.filter(Product.current_stock <= Product.minimum_stock)
    elif stock_filter == 'out':
        query = query.filter(Product.current_stock <= 0)
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get categories for filter dropdown
    categories = Category.query.filter_by(is_active=True).all()
    
    return render_template('products/index.html',
                         products=products,
                         categories=categories,
                         search=search,
                         category_id=category_id,
                         status_filter=status_filter,
                         stock_filter=stock_filter,
                         language=language)

@products_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('products_write')
def create():
    """Create new product"""
    language = get_user_language()
    
    if request.method == 'POST':
        # Get form data
        sku = request.form.get('sku', '').strip()
        barcode = request.form.get('barcode', '').strip()
        name_ar = request.form.get('name_ar', '').strip()
        name_en = request.form.get('name_en', '').strip()
        description_ar = request.form.get('description_ar', '').strip()
        description_en = request.form.get('description_en', '').strip()
        category_id = request.form.get('category_id', type=int)
        cost_price = request.form.get('cost_price', type=float)
        selling_price = request.form.get('selling_price', type=float)
        discount_percentage = request.form.get('discount_percentage', 0, type=float)
        current_stock = request.form.get('current_stock', 0, type=int)
        minimum_stock = request.form.get('minimum_stock', 5, type=int)
        maximum_stock = request.form.get('maximum_stock', 1000, type=int)
        unit_of_measure = request.form.get('unit_of_measure', 'piece')
        weight = request.form.get('weight', type=float)
        dimensions = request.form.get('dimensions', '').strip()
        is_taxable = bool(request.form.get('is_taxable'))
        track_inventory = bool(request.form.get('track_inventory'))
        
        # Validation
        errors = []
        
        if not sku:
            errors.append('رمز المنتج مطلوب' if language == 'ar' else 'SKU is required')
        elif Product.query.filter_by(sku=sku).first():
            errors.append('رمز المنتج موجود بالفعل' if language == 'ar' else 'SKU already exists')
        
        if barcode and Product.query.filter_by(barcode=barcode).first():
            errors.append('الباركود موجود بالفعل' if language == 'ar' else 'Barcode already exists')
        
        if not name_ar or not name_en:
            errors.append('اسم المنتج مطلوب بالعربية والإنجليزية' if language == 'ar' 
                         else 'Product name is required in both Arabic and English')
        
        if not category_id:
            errors.append('فئة المنتج مطلوبة' if language == 'ar' else 'Product category is required')
        
        if cost_price is None or cost_price < 0:
            errors.append('سعر التكلفة يجب أن يكون رقم موجب' if language == 'ar' 
                         else 'Cost price must be a positive number')
        
        if selling_price is None or selling_price < 0:
            errors.append('سعر البيع يجب أن يكون رقم موجب' if language == 'ar' 
                         else 'Selling price must be a positive number')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('products/create.html', categories=categories, language=language)
        
        # Generate barcode if not provided
        if not barcode:
            barcode = generate_barcode()
        
        # Handle image upload
        image_filename = None
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename:
                upload_folder = os.path.join('static', 'uploads', 'products')
                image_filename = save_uploaded_file(file, upload_folder)
        
        # Create product
        try:
            product = Product(
                sku=sku,
                barcode=barcode,
                name_ar=name_ar,
                name_en=name_en,
                description_ar=description_ar,
                description_en=description_en,
                category_id=category_id,
                cost_price=cost_price,
                selling_price=selling_price,
                discount_percentage=discount_percentage,
                current_stock=current_stock,
                minimum_stock=minimum_stock,
                maximum_stock=maximum_stock,
                unit_of_measure=unit_of_measure,
                weight=weight,
                dimensions=dimensions,
                image_filename=image_filename,
                is_taxable=is_taxable,
                track_inventory=track_inventory
            )
            
            db.session.add(product)
            db.session.commit()
            
            flash('تم إنشاء المنتج بنجاح' if language == 'ar' 
                  else 'Product created successfully', 'success')
            return redirect(url_for('products.view', product_id=product.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المنتج' if language == 'ar' 
                  else 'Error creating product', 'error')
    
    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/create.html', categories=categories, language=language)

@products_bp.route('/<int:product_id>')
@login_required
@permission_required('products_read')
def view(product_id):
    """View product details"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)
    
    return render_template('products/view.html', product=product, language=language)

@products_bp.route('/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('products_write')
def edit(product_id):
    """Edit product"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)

    if request.method == 'POST':
        # Get form data
        name_ar = request.form.get('name_ar', '').strip()
        name_en = request.form.get('name_en', '').strip()
        description_ar = request.form.get('description_ar', '').strip()
        description_en = request.form.get('description_en', '').strip()
        category_id = request.form.get('category_id', type=int)
        cost_price = request.form.get('cost_price', type=float)
        selling_price = request.form.get('selling_price', type=float)
        discount_percentage = request.form.get('discount_percentage', 0, type=float)
        current_stock = request.form.get('current_stock', type=int)
        minimum_stock = request.form.get('minimum_stock', type=int)
        maximum_stock = request.form.get('maximum_stock', type=int)
        unit_of_measure = request.form.get('unit_of_measure', 'piece')
        weight = request.form.get('weight', type=float)
        dimensions = request.form.get('dimensions', '').strip()
        is_taxable = bool(request.form.get('is_taxable'))
        track_inventory = bool(request.form.get('track_inventory'))
        is_active = bool(request.form.get('is_active'))

        # Validation
        errors = []

        if not name_ar or not name_en:
            errors.append('اسم المنتج مطلوب بالعربية والإنجليزية' if language == 'ar'
                         else 'Product name is required in both Arabic and English')

        if not category_id:
            errors.append('فئة المنتج مطلوبة' if language == 'ar' else 'Product category is required')

        if cost_price is None or cost_price < 0:
            errors.append('سعر التكلفة يجب أن يكون رقم موجب' if language == 'ar'
                         else 'Cost price must be a positive number')

        if selling_price is None or selling_price < 0:
            errors.append('سعر البيع يجب أن يكون رقم موجب' if language == 'ar'
                         else 'Selling price must be a positive number')

        if errors:
            for error in errors:
                flash(error, 'error')
            categories = Category.query.filter_by(is_active=True).all()
            return render_template('products/edit.html', product=product, categories=categories, language=language)

        # Handle image upload
        image_filename = product.image_filename
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename:
                upload_folder = os.path.join('static', 'uploads', 'products')
                new_image = save_uploaded_file(file, upload_folder)
                if new_image:
                    image_filename = new_image

        # Update product
        try:
            product.name_ar = name_ar
            product.name_en = name_en
            product.description_ar = description_ar
            product.description_en = description_en
            product.category_id = category_id
            product.cost_price = cost_price
            product.selling_price = selling_price
            product.discount_percentage = discount_percentage
            product.minimum_stock = minimum_stock
            product.maximum_stock = maximum_stock
            product.unit_of_measure = unit_of_measure
            product.weight = weight
            product.dimensions = dimensions
            product.image_filename = image_filename
            product.is_taxable = is_taxable
            product.track_inventory = track_inventory
            product.is_active = is_active

            # Update stock if changed and tracking inventory
            if track_inventory and current_stock != product.current_stock:
                from models.inventory import InventoryTransaction
                InventoryTransaction.create_transaction(
                    product=product,
                    quantity_change=current_stock - product.current_stock,
                    transaction_type='adjustment',
                    user_id=current_user.id,
                    notes='Stock updated via product edit'
                )

            db.session.commit()

            flash('تم تحديث المنتج بنجاح' if language == 'ar'
                  else 'Product updated successfully', 'success')
            return redirect(url_for('products.view', product_id=product.id))

        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث المنتج' if language == 'ar'
                  else 'Error updating product', 'error')

    categories = Category.query.filter_by(is_active=True).all()
    return render_template('products/edit.html', product=product, categories=categories, language=language)

@products_bp.route('/api/search')
@login_required
@permission_required('products_read')
def api_search():
    """API endpoint for product search (for POS interface)"""
    query = request.args.get('q', '')
    category_id = request.args.get('category', type=int)
    limit = request.args.get('limit', 50, type=int)  # Increased default limit for POS

    # Build base query - always start with active products
    base_query = Product.query.filter(Product.is_active == True)

    # Apply search filter if query provided and not empty
    if query and query.strip():
        base_query = base_query.filter(
            (Product.name_ar.contains(query)) |
            (Product.name_en.contains(query)) |
            (Product.sku.contains(query)) |
            (Product.barcode.contains(query))
        )

    # Apply category filter if provided
    if category_id:
        base_query = base_query.filter(Product.category_id == category_id)

    # Order by name and limit results
    products = base_query.order_by(Product.name_en).limit(limit).all()

    language = get_user_language()
    return jsonify([product.to_dict(language) for product in products])

@products_bp.route('/api/barcode/<barcode>')
@login_required
@permission_required('products_read')
def api_get_by_barcode(barcode):
    """API endpoint to get product by barcode"""
    product = Product.query.filter_by(barcode=barcode, is_active=True).first()
    
    if not product:
        return jsonify({'error': 'Product not found'}), 404
    
    language = get_user_language()
    return jsonify(product.to_dict(language))

# Categories routes
@products_bp.route('/categories')
@login_required
@permission_required('products_read')
def categories():
    """List all categories"""
    language = get_user_language()
    # Show all categories (active and inactive) for management
    categories = Category.query.order_by(Category.created_at.desc()).all()

    return render_template('products/categories.html', categories=categories, language=language)

@products_bp.route('/categories/create', methods=['GET', 'POST'])
@login_required
@permission_required('products_write')
def create_category():
    """Create new category"""
    language = get_user_language()
    
    if request.method == 'POST':
        name_ar = request.form.get('name_ar', '').strip()
        name_en = request.form.get('name_en', '').strip()
        description_ar = request.form.get('description_ar', '').strip()
        description_en = request.form.get('description_en', '').strip()
        parent_id = request.form.get('parent_id', type=int)
        
        if not name_ar or not name_en:
            flash('اسم الفئة مطلوب بالعربية والإنجليزية' if language == 'ar' 
                  else 'Category name is required in both Arabic and English', 'error')
            return render_template('products/create_category.html', language=language)
        
        try:
            category = Category(
                name_ar=name_ar,
                name_en=name_en,
                description_ar=description_ar,
                description_en=description_en,
                parent_id=parent_id if parent_id else None
            )
            
            db.session.add(category)
            db.session.commit()
            
            flash('تم إنشاء الفئة بنجاح' if language == 'ar' 
                  else 'Category created successfully', 'success')
            return redirect(url_for('products.categories'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء الفئة' if language == 'ar' 
                  else 'Error creating category', 'error')
    
    # Get parent categories for dropdown
    parent_categories = Category.query.filter_by(parent_id=None, is_active=True).all()
    
    return render_template('products/create_category.html',
                         parent_categories=parent_categories,
                         language=language)

@products_bp.route('/categories/<int:category_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('products_write')
def edit_category(category_id):
    """Edit category"""
    language = get_user_language()
    category = Category.query.get_or_404(category_id)

    if request.method == 'POST':
        name_ar = request.form.get('name_ar', '').strip()
        name_en = request.form.get('name_en', '').strip()
        description_ar = request.form.get('description_ar', '').strip()
        description_en = request.form.get('description_en', '').strip()
        parent_id = request.form.get('parent_id', type=int)
        is_active = bool(request.form.get('is_active'))

        if not name_ar or not name_en:
            flash('اسم الفئة مطلوب بالعربية والإنجليزية' if language == 'ar'
                  else 'Category name is required in both Arabic and English', 'error')
            return render_template('products/edit_category.html', category=category, language=language)

        # Prevent setting parent to self or child
        if parent_id == category.id:
            flash('لا يمكن جعل الفئة أب لنفسها' if language == 'ar'
                  else 'Category cannot be parent of itself', 'error')
            return render_template('products/edit_category.html', category=category, language=language)

        try:
            category.name_ar = name_ar
            category.name_en = name_en
            category.description_ar = description_ar
            category.description_en = description_en
            category.parent_id = parent_id if parent_id else None
            category.is_active = is_active

            db.session.commit()

            flash('تم تحديث الفئة بنجاح' if language == 'ar'
                  else 'Category updated successfully', 'success')
            return redirect(url_for('products.categories'))

        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الفئة' if language == 'ar'
                  else 'Error updating category', 'error')

    # Get parent categories (excluding self and children)
    parent_categories = Category.query.filter(
        Category.id != category.id,
        Category.parent_id != category.id,
        Category.is_active == True
    ).all()

    return render_template('products/edit_category.html',
                         category=category,
                         parent_categories=parent_categories,
                         language=language)

@products_bp.route('/categories/<int:category_id>/toggle-status', methods=['POST'])
@login_required
@permission_required('products_write')
def toggle_category_status(category_id):
    """Toggle category active status"""
    language = get_user_language()
    category = Category.query.get_or_404(category_id)

    try:
        category.is_active = not category.is_active
        db.session.commit()

        status_text = 'تم تفعيل' if category.is_active else 'تم تعطيل'
        status_text_en = 'activated' if category.is_active else 'deactivated'

        flash(f'{status_text} الفئة بنجاح' if language == 'ar'
              else f'Category {status_text_en} successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تغيير حالة الفئة' if language == 'ar'
              else 'Error changing category status', 'error')

    return redirect(url_for('products.categories'))
