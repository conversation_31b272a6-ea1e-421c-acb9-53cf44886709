#!/usr/bin/env python3
"""
اختبار إنشاء العميل
Test customer creation functionality
"""

from app import create_app
from extensions import db
from models.customer import Customer
from utils.helpers import validate_email, validate_qatar_id

def test_customer_model():
    """اختبار نموذج العميل"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار نموذج العميل")
        print("=" * 40)
        
        # اختبار generate_customer_code
        try:
            code = Customer.generate_customer_code()
            print(f"  ✅ generate_customer_code(): {code}")
        except Exception as e:
            print(f"  ❌ generate_customer_code(): خطأ - {e}")
        
        # اختبار إنشاء عميل فرد
        try:
            customer = Customer(
                customer_code='TEST001',
                customer_type='individual',
                first_name_ar='أحمد',
                first_name_en='Ahmed',
                last_name_ar='محمد',
                last_name_en='Mohammed',
                phone='+974-1234-5678',
                email='<EMAIL>'
            )
            print(f"  ✅ إنشاء عميل فرد: {customer.get_display_name('ar')}")
        except Exception as e:
            print(f"  ❌ إنشاء عميل فرد: خطأ - {e}")
        
        # اختبار إنشاء عميل شركة
        try:
            customer = Customer(
                customer_code='TEST002',
                customer_type='company',
                company_name_ar='شركة الاختبار',
                company_name_en='Test Company',
                phone='+974-8765-4321',
                email='<EMAIL>'
            )
            print(f"  ✅ إنشاء عميل شركة: {customer.get_display_name('ar')}")
        except Exception as e:
            print(f"  ❌ إنشاء عميل شركة: خطأ - {e}")

def test_validation_functions():
    """اختبار دوال التحقق"""
    print("\n🔍 اختبار دوال التحقق")
    print("=" * 40)
    
    # اختبار validate_email
    test_emails = [
        ('<EMAIL>', True),
        ('invalid-email', False),
        ('<EMAIL>', True),
        ('', False),
        (None, False)
    ]
    
    for email, expected in test_emails:
        try:
            result = validate_email(email)
            status = "✅" if result == expected else "❌"
            print(f"  {status} validate_email('{email}'): {result}")
        except Exception as e:
            print(f"  ❌ validate_email('{email}'): خطأ - {e}")
    
    # اختبار validate_qatar_id
    test_qatar_ids = [
        ('12345678901', True),  # 11 digits
        ('1234567890', False),  # 10 digits
        ('123456789012', False),  # 12 digits
        ('1234567890a', False),  # contains letter
        ('', False),
        (None, False)
    ]
    
    for qatar_id, expected in test_qatar_ids:
        try:
            result = validate_qatar_id(qatar_id)
            status = "✅" if result == expected else "❌"
            print(f"  {status} validate_qatar_id('{qatar_id}'): {result}")
        except Exception as e:
            print(f"  ❌ validate_qatar_id('{qatar_id}'): خطأ - {e}")

def test_customer_creation_with_db():
    """اختبار إنشاء العميل مع قاعدة البيانات"""
    app = create_app()
    
    with app.app_context():
        print("\n💾 اختبار إنشاء العميل مع قاعدة البيانات")
        print("=" * 40)
        
        try:
            # إنشاء عميل تجريبي
            customer = Customer(
                customer_code=Customer.generate_customer_code(),
                customer_type='individual',
                first_name_ar='عميل',
                first_name_en='Customer',
                last_name_ar='تجريبي',
                last_name_en='Test',
                phone='+974-5555-5555',
                email='<EMAIL>',
                address_ar='الدوحة، قطر',
                address_en='Doha, Qatar',
                city_ar='الدوحة',
                city_en='Doha'
            )
            
            # محاولة الحفظ
            db.session.add(customer)
            db.session.commit()
            
            print(f"  ✅ تم إنشاء العميل بنجاح: {customer.customer_code}")
            print(f"      الاسم: {customer.get_display_name('ar')}")
            print(f"      الهاتف: {customer.phone}")
            print(f"      البريد: {customer.email}")
            
            # حذف العميل التجريبي
            db.session.delete(customer)
            db.session.commit()
            print(f"  ✅ تم حذف العميل التجريبي")
            
        except Exception as e:
            db.session.rollback()
            print(f"  ❌ خطأ في إنشاء العميل: {e}")
            print(f"      نوع الخطأ: {type(e).__name__}")
            import traceback
            print(f"      التفاصيل: {traceback.format_exc()}")

def test_duplicate_customer_code():
    """اختبار تكرار رمز العميل"""
    app = create_app()
    
    with app.app_context():
        print("\n🔄 اختبار تكرار رمز العميل")
        print("=" * 40)
        
        # إنشاء عدة رموز والتأكد من عدم تكرارها
        codes = set()
        for i in range(10):
            try:
                code = Customer.generate_customer_code()
                if code in codes:
                    print(f"  ❌ رمز مكرر: {code}")
                else:
                    codes.add(code)
                    print(f"  ✅ رمز جديد: {code}")
            except Exception as e:
                print(f"  ❌ خطأ في إنشاء الرمز {i+1}: {e}")
        
        print(f"  📊 تم إنشاء {len(codes)} رمز فريد من أصل 10")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار إنشاء العميل")
    print("=" * 60)
    
    test_customer_model()
    test_validation_functions()
    test_customer_creation_with_db()
    test_duplicate_customer_code()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار!")
    print("🎯 إذا ظهرت أخطاء، فهذا سيساعد في تشخيص مشكلة إنشاء العميل")
