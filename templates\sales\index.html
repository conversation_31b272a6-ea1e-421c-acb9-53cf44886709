{% extends "base.html" %}

{% block title %}
{{ 'إدارة المبيعات - نظام نقاط البيع القطري' if language == 'ar' else 'Sales Management - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-cart"></i>
                {{ 'إدارة المبيعات' if language == 'ar' else 'Sales Management' }}
            </h1>
            {% if current_user.has_permission('sales') %}
            <a href="{{ url_for('sales.pos') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'بيع جديد' if language == 'ar' else 'New Sale' }}
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{{ 'البحث' if language == 'ar' else 'Search' }}</label>
                <input type="text" class="form-control" name="search" value="{{ search }}" 
                       placeholder="{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice number' }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'الحالة' if language == 'ar' else 'Status' }}</label>
                <select class="form-select" name="status">
                    <option value="">{{ 'جميع الحالات' if language == 'ar' else 'All Status' }}</option>
                    <option value="completed" {{ 'selected' if status_filter == 'completed' }}>
                        {{ 'مكتملة' if language == 'ar' else 'Completed' }}
                    </option>
                    <option value="pending" {{ 'selected' if status_filter == 'pending' }}>
                        {{ 'معلقة' if language == 'ar' else 'Pending' }}
                    </option>
                    <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>
                        {{ 'ملغية' if language == 'ar' else 'Cancelled' }}
                    </option>
                    <option value="refunded" {{ 'selected' if status_filter == 'refunded' }}>
                        {{ 'مسترجعة' if language == 'ar' else 'Refunded' }}
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        {{ 'بحث' if language == 'ar' else 'Search' }}
                    </button>
                    <a href="{{ url_for('sales.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Sales Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set total_sales = sales.items | sum(attribute='total_amount') %}
                            {{ '{:,.2f} ر.ق'.format(total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(total_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'متوسط المعاملة' if language == 'ar' else 'Average Transaction' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set avg_sale = (total_sales / sales.total) if sales.total > 0 else 0 %}
                            {{ '{:,.2f} ر.ق'.format(avg_sale) if language == 'ar' else 'QAR {:,.2f}'.format(avg_sale) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'معاملات معلقة' if language == 'ar' else 'Pending Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales.items | selectattr('status', 'equalto', 'pending') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            {{ 'قائمة المبيعات' if language == 'ar' else 'Sales List' }}
            <span class="badge bg-primary">{{ sales.total }}</span>
        </h6>
    </div>
    <div class="card-body p-0">
        {% if sales.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice #' }}</th>
                        <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                        <th>{{ 'البائع' if language == 'ar' else 'Seller' }}</th>
                        <th>{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                        <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment' }}</th>
                        <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                        <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                        <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales.items %}
                    <tr>
                        <td>
                            <strong>{{ sale.sale_number }}</strong>
                            <br>
                            <small class="text-muted">{{ sale.items.count() }} {{ 'صنف' if language == 'ar' else 'items' }}</small>
                        </td>
                        <td>
                            {% if sale.customer %}
                            <div>
                                <strong>{{ sale.customer.get_display_name(language) }}</strong>
                                {% if sale.customer.phone %}
                                <br><small class="text-muted">{{ sale.customer.phone }}</small>
                                {% endif %}
                            </div>
                            {% else %}
                            <span class="text-muted">{{ 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.seller.get_full_name(language) }}</td>
                        <td>
                            <strong>{{ '{:,.2f} ر.ق'.format(sale.total_amount) if language == 'ar' else 'QAR {:,.2f}'.format(sale.total_amount) }}</strong>
                            {% if sale.discount_amount > 0 %}
                            <br><small class="text-success">{{ 'خصم:' if language == 'ar' else 'Discount:' }} {{ '{:,.2f} ر.ق'.format(sale.discount_amount) if language == 'ar' else 'QAR {:,.2f}'.format(sale.discount_amount) }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.payment_method == 'cash' %}
                            <span class="badge bg-success">{{ 'نقدي' if language == 'ar' else 'Cash' }}</span>
                            {% elif sale.payment_method == 'card' %}
                            <span class="badge bg-primary">{{ 'بطاقة' if language == 'ar' else 'Card' }}</span>
                            {% elif sale.payment_method == 'bank_transfer' %}
                            <span class="badge bg-info">{{ 'تحويل' if language == 'ar' else 'Transfer' }}</span>
                            {% else %}
                            <span class="badge bg-warning">{{ 'آجل' if language == 'ar' else 'Credit' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.status == 'completed' %}
                            <span class="badge bg-success">{{ 'مكتملة' if language == 'ar' else 'Completed' }}</span>
                            {% elif sale.status == 'pending' %}
                            <span class="badge bg-warning">{{ 'معلقة' if language == 'ar' else 'Pending' }}</span>
                            {% elif sale.status == 'cancelled' %}
                            <span class="badge bg-danger">{{ 'ملغية' if language == 'ar' else 'Cancelled' }}</span>
                            {% elif sale.status == 'refunded' %}
                            <span class="badge bg-secondary">{{ 'مسترجعة' if language == 'ar' else 'Refunded' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>{{ sale.sale_date.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ sale.sale_date.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('sales.view', sale_id=sale.id) }}" 
                                   class="btn btn-outline-primary" title="{{ 'عرض' if language == 'ar' else 'View' }}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('sales.invoice', sale_id=sale.id) }}" 
                                   class="btn btn-outline-info" title="{{ 'الفاتورة' if language == 'ar' else 'Invoice' }}">
                                    <i class="bi bi-file-text"></i>
                                </a>
                                <a href="{{ url_for('sales.print_invoice', sale_id=sale.id) }}" 
                                   class="btn btn-outline-secondary" target="_blank" title="{{ 'طباعة' if language == 'ar' else 'Print' }}">
                                    <i class="bi bi-printer"></i>
                                </a>
                                {% if sale.status == 'completed' and current_user.has_permission('sales') %}
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="showRefundModal({{ sale.id }})" title="{{ 'استرجاع' if language == 'ar' else 'Refund' }}">
                                    <i class="bi bi-arrow-return-left"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if sales.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Sales pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if sales.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.index', page=sales.prev_num, search=search, status=status_filter, date_from=date_from, date_to=date_to) }}">
                            {{ 'السابق' if language == 'ar' else 'Previous' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in sales.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != sales.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('sales.index', page=page_num, search=search, status=status_filter, date_from=date_from, date_to=date_to) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if sales.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('sales.index', page=sales.next_num, search=search, status=status_filter, date_from=date_from, date_to=date_to) }}">
                            {{ 'التالي' if language == 'ar' else 'Next' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-cart display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد مبيعات' if language == 'ar' else 'No sales found' }}</h5>
            <p class="text-muted">
                {% if search or status_filter or date_from or date_to %}
                {{ 'جرب تغيير معايير البحث' if language == 'ar' else 'Try changing your search criteria' }}
                {% else %}
                {{ 'ابدأ بإجراء بيع جديد' if language == 'ar' else 'Start by making a new sale' }}
                {% endif %}
            </p>
            {% if current_user.has_permission('sales') and not (search or status_filter or date_from or date_to) %}
            <a href="{{ url_for('sales.pos') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'بيع جديد' if language == 'ar' else 'New Sale' }}
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'استرجاع المبيعة' if language == 'ar' else 'Refund Sale' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="refundForm" method="POST">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        {{ 'تحذير: هذا الإجراء لا يمكن التراجع عنه' if language == 'ar' else 'Warning: This action cannot be undone' }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'سبب الاسترجاع' if language == 'ar' else 'Refund Reason' }}</label>
                        <select class="form-select" name="refund_reason" required>
                            <option value="">{{ 'اختر السبب' if language == 'ar' else 'Select reason' }}</option>
                            <option value="customer_request">{{ 'طلب العميل' if language == 'ar' else 'Customer Request' }}</option>
                            <option value="defective_product">{{ 'منتج معيب' if language == 'ar' else 'Defective Product' }}</option>
                            <option value="wrong_item">{{ 'صنف خاطئ' if language == 'ar' else 'Wrong Item' }}</option>
                            <option value="pricing_error">{{ 'خطأ في السعر' if language == 'ar' else 'Pricing Error' }}</option>
                            <option value="other">{{ 'أخرى' if language == 'ar' else 'Other' }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'ملاحظات إضافية' if language == 'ar' else 'Additional Notes' }}</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        {{ 'تأكيد الاسترجاع' if language == 'ar' else 'Confirm Refund' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showRefundModal(saleId) {
    const form = document.getElementById('refundForm');
    form.action = `/sales/${saleId}/refund`;
    
    const modal = new bootstrap.Modal(document.getElementById('refundModal'));
    modal.show();
}

// Auto-submit form on filter change
document.querySelectorAll('select[name="status"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}
