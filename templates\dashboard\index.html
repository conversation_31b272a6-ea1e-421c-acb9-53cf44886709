{% extends "base.html" %}

{% block title %}
{{ 'لوحة التحكم - نظام نقاط البيع القطري' if language == 'ar' else 'Dashboard - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-speedometer2"></i>
            {{ 'لوحة التحكم' if language == 'ar' else 'Dashboard' }}
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'مبيعات اليوم' if language == 'ar' else "Today's Sales" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(stats.today_sales) if language == 'ar' else 'QAR {:,.2f}'.format(stats.today_sales) }}
                        </div>
                        {% if stats.sales_growth != 0 %}
                        <div class="text-xs">
                            <span class="text-{{ 'success' if stats.sales_growth > 0 else 'danger' }}">
                                <i class="bi bi-arrow-{{ 'up' if stats.sales_growth > 0 else 'down' }}"></i>
                                {{ '{:.1f}%'.format(abs(stats.sales_growth)) }}
                            </span>
                            {{ 'من الأمس' if language == 'ar' else 'from yesterday' }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'معاملات اليوم' if language == 'ar' else "Today's Transactions" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.today_transactions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock Items' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.low_stock_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Tables Row -->
<div class="row">
    <!-- Sales Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'مخطط المبيعات' if language == 'ar' else 'Sales Chart' }}
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in">
                        <a class="dropdown-item" href="#" onclick="loadSalesChart('week')">
                            {{ 'أسبوع' if language == 'ar' else 'Week' }}
                        </a>
                        <a class="dropdown-item" href="#" onclick="loadSalesChart('month')">
                            {{ 'شهر' if language == 'ar' else 'Month' }}
                        </a>
                        <a class="dropdown-item" href="#" onclick="loadSalesChart('year')">
                            {{ 'سنة' if language == 'ar' else 'Year' }}
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="salesChart" width="100%" height="40"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Sales -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'المبيعات الأخيرة' if language == 'ar' else 'Recent Sales' }}
                </h6>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                    {% for sale in recent_sales %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <div class="icon-circle bg-primary">
                                <i class="bi bi-receipt text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ sale.sale_date.strftime('%H:%M') }}</div>
                            <div class="font-weight-bold">{{ sale.sale_number }}</div>
                            <div class="text-xs text-gray-500">
                                {{ sale.customer.get_display_name(language) if sale.customer else ('عميل عادي' if language == 'ar' else 'Walk-in Customer') }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-weight-bold">
                                {{ '{:,.2f} ر.ق'.format(sale.total_amount) if language == 'ar' else 'QAR {:,.2f}'.format(sale.total_amount) }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">
                        {{ 'لا توجد مبيعات حديثة' if language == 'ar' else 'No recent sales' }}
                    </p>
                {% endif %}
                <div class="text-center">
                    <a href="{{ url_for('sales.index') }}" class="btn btn-primary btn-sm">
                        {{ 'عرض جميع المبيعات' if language == 'ar' else 'View All Sales' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock and Top Products Row -->
<div class="row">
    <!-- Low Stock Products -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    {{ 'منتجات بمخزون منخفض' if language == 'ar' else 'Low Stock Products' }}
                </h6>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'الحد الأدنى' if language == 'ar' else 'Min Stock' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('products.view', product_id=product.id) }}">
                                            {{ product.get_name(language) }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if product.is_out_of_stock() else 'warning' }}">
                                            {{ product.current_stock }}
                                        </span>
                                    </td>
                                    <td>{{ product.minimum_stock }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">
                        {{ 'جميع المنتجات لديها مخزون كافي' if language == 'ar' else 'All products have sufficient stock' }}
                    </p>
                {% endif %}
                <div class="text-center">
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-warning btn-sm">
                        {{ 'إدارة المخزون' if language == 'ar' else 'Manage Inventory' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Selling Products -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    {{ 'المنتجات الأكثر مبيعاً' if language == 'ar' else 'Top Selling Products' }}
                </h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                    <th>{{ 'الكمية المباعة' if language == 'ar' else 'Sold Qty' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product, total_sold in top_products %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('products.view', product_id=product.id) }}">
                                            {{ product.get_name(language) }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ total_sold }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">
                        {{ 'لا توجد بيانات مبيعات' if language == 'ar' else 'No sales data available' }}
                    </p>
                {% endif %}
                <div class="text-center">
                    <a href="{{ url_for('reports.products_report') }}" class="btn btn-success btn-sm">
                        {{ 'تقرير المنتجات' if language == 'ar' else 'Products Report' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Chart
let salesChart;

function loadSalesChart(period = 'week') {
    fetch(`{{ url_for('dashboard.api_sales_chart') }}?period=${period}`)
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('salesChart').getContext('2d');
            
            if (salesChart) {
                salesChart.destroy();
            }
            
            salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: '{{ "المبيعات" if language == "ar" else "Sales" }}',
                        data: data.sales,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '{{ "ر.ق " if language == "ar" else "QAR " }}' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => console.error('Error loading sales chart:', error));
}

// Load initial chart
document.addEventListener('DOMContentLoaded', function() {
    loadSalesChart();
});
</script>
{% endblock %}
