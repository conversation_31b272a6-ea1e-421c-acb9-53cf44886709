@echo off
chcp 65001 >nul
title تثبيت نظام نقاط البيع القطري - Qatar POS Installation

echo.
echo ========================================
echo 🇶🇦 نظام نقاط البيع القطري
echo    Qatar POS System Installation
echo ========================================
echo.

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from:
    echo https://python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found: 
python --version

echo.
echo 📦 Installing required packages...
echo.

echo Installing Flask...
python -m pip install Flask==2.3.3
if errorlevel 1 goto error

echo Installing Flask-SQLAlchemy...
python -m pip install Flask-SQLAlchemy==3.0.5
if errorlevel 1 goto error

echo Installing Flask-Login...
python -m pip install Flask-Login==0.6.3
if errorlevel 1 goto error

echo Installing Flask-Migrate...
python -m pip install Flask-Migrate==4.0.5
if errorlevel 1 goto error

echo Installing Flask-WTF...
python -m pip install Flask-WTF==1.1.1
if errorlevel 1 goto error

echo Installing python-dotenv...
python -m pip install python-dotenv==1.0.0
if errorlevel 1 goto error

echo Installing Jinja2...
python -m pip install Jinja2==3.1.2
if errorlevel 1 goto error

echo.
echo 📁 Creating necessary directories...
if not exist "static\css" mkdir "static\css"
if not exist "static\js" mkdir "static\js"
if not exist "static\images" mkdir "static\images"
if not exist "uploads" mkdir "uploads"
if not exist "logs" mkdir "logs"
if not exist "backups" mkdir "backups"

echo.
echo ⚙️ Creating environment file...
if not exist ".env" (
    echo # Qatar POS System - Development Environment > .env
    echo SECRET_KEY=dev-secret-key-change-in-production >> .env
    echo FLASK_ENV=development >> .env
    echo FLASK_DEBUG=1 >> .env
    echo DATABASE_URL=sqlite:///qatarpos.db >> .env
    echo DEFAULT_LANGUAGE=ar >> .env
    echo DEFAULT_CURRENCY=QAR >> .env
    echo ✅ Created .env file
) else (
    echo ✅ .env file already exists
)

echo.
echo 🧪 Testing installation...
python -c "import flask; print('✅ Flask:', flask.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ Flask test failed
    goto error
)

echo.
echo ========================================
echo 🎉 Installation completed successfully!
echo ========================================
echo.
echo 📋 Next steps:
echo.
echo 1. Test the system:
echo    python test_server.py
echo.
echo 2. Run simple version:
echo    python simple_run.py
echo.
echo 3. Run full system:
echo    python run.py
echo.
echo 4. Or use the start script:
echo    start.bat
echo.
echo 🌐 Then open: http://localhost:5000
echo.
echo 🇶🇦 مرحباً بك في نظام نقاط البيع القطري!
echo ========================================
pause
exit /b 0

:error
echo.
echo ❌ Installation failed!
echo.
echo 💡 Try these solutions:
echo 1. Update pip: python -m pip install --upgrade pip
echo 2. Check internet connection
echo 3. Run as administrator
echo 4. Install packages manually:
echo    pip install flask flask-sqlalchemy flask-login
echo.
pause
exit /b 1
