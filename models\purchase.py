"""
Purchase Order models for Qatar POS System
Manages supplier orders and inventory receiving
"""

from datetime import datetime
from extensions import db

class PurchaseOrder(db.Model):
    """Purchase order from suppliers"""
    
    __tablename__ = 'purchase_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Order identification
    order_number = db.Column(db.String(20), unique=True, nullable=False, index=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    
    # Order details
    order_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expected_delivery_date = db.Column(db.DateTime)
    actual_delivery_date = db.Column(db.DateTime)
    
    # Amounts
    subtotal = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    shipping_cost = db.Column(db.Numeric(10, 2), default=0)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    
    # Status
    status = db.Column(db.Enum('draft', 'sent', 'confirmed', 'partial_received', 
                              'completed', 'cancelled', name='po_statuses'),
                      nullable=False, default='draft')
    
    # User who created the order
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Notes and references
    notes = db.Column(db.Text)
    supplier_reference = db.Column(db.String(100))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    creator = db.relationship('User', backref='purchase_orders_created')
    items = db.relationship('PurchaseOrderItem', backref='purchase_order', 
                           cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """Calculate order totals from items"""
        self.subtotal = sum(item.total_cost for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_cost
        return self.total_amount
    
    def add_item(self, product, quantity, unit_cost):
        """Add item to purchase order"""
        # Check if item already exists
        existing_item = self.items.filter_by(product_id=product.id).first()
        if existing_item:
            existing_item.quantity += quantity
            existing_item.calculate_total()
        else:
            item = PurchaseOrderItem(
                purchase_order_id=self.id,
                product_id=product.id,
                quantity=quantity,
                unit_cost=unit_cost
            )
            item.calculate_total()
            db.session.add(item)
        
        self.calculate_totals()
        return True
    
    def remove_item(self, product_id):
        """Remove item from purchase order"""
        item = self.items.filter_by(product_id=product_id).first()
        if item:
            db.session.delete(item)
            self.calculate_totals()
            return True
        return False
    
    def receive_items(self, received_items, user_id):
        """Process received items and update inventory"""
        from models.inventory import InventoryTransaction
        
        all_received = True
        
        for item_data in received_items:
            item = self.items.filter_by(product_id=item_data['product_id']).first()
            if item:
                received_qty = item_data['received_quantity']
                item.received_quantity += received_qty
                
                # Create inventory transaction
                InventoryTransaction.create_transaction(
                    product=item.product,
                    quantity_change=received_qty,
                    transaction_type='purchase',
                    user_id=user_id,
                    reference_type='purchase_order',
                    reference_id=self.id,
                    reference_number=self.order_number,
                    unit_cost=item.unit_cost,
                    notes=f"Received from PO {self.order_number}"
                )
                
                # Update product cost price
                item.product.cost_price = item.unit_cost
                
                if item.received_quantity < item.quantity:
                    all_received = False
        
        # Update order status
        if all_received:
            self.status = 'completed'
            self.actual_delivery_date = datetime.utcnow()
        else:
            self.status = 'partial_received'
        
        return True
    
    def to_dict(self, language='ar'):
        """Convert purchase order to dictionary"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'supplier_name': self.supplier.get_company_name(language),
            'order_date': self.order_date.isoformat(),
            'expected_delivery_date': self.expected_delivery_date.isoformat() if self.expected_delivery_date else None,
            'actual_delivery_date': self.actual_delivery_date.isoformat() if self.actual_delivery_date else None,
            'subtotal': float(self.subtotal),
            'tax_amount': float(self.tax_amount),
            'shipping_cost': float(self.shipping_cost),
            'total_amount': float(self.total_amount),
            'status': self.status,
            'creator_name': self.creator.get_full_name(language),
            'items_count': len(self.items),
            'notes': self.notes,
            'supplier_reference': self.supplier_reference,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def generate_order_number():
        """Generate unique order number"""
        from datetime import datetime
        today = datetime.now()
        prefix = f"PO{today.strftime('%Y%m%d')}"
        
        last_order = PurchaseOrder.query.filter(
            PurchaseOrder.order_number.like(f"{prefix}%")
        ).order_by(PurchaseOrder.order_number.desc()).first()
        
        if last_order:
            last_number = int(last_order.order_number[-3:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:03d}"
    
    def __repr__(self):
        return f'<PurchaseOrder {self.order_number}>'


class PurchaseOrderItem(db.Model):
    """Individual items in a purchase order"""
    
    __tablename__ = 'purchase_order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Quantities
    quantity = db.Column(db.Integer, nullable=False, default=1)
    received_quantity = db.Column(db.Integer, nullable=False, default=0)
    
    # Pricing
    unit_cost = db.Column(db.Numeric(10, 3), nullable=False)
    total_cost = db.Column(db.Numeric(12, 2), nullable=False)
    
    # Notes for this specific item
    notes = db.Column(db.Text)
    
    # Relationships
    product = db.relationship('Product')
    
    def calculate_total(self):
        """Calculate total cost for this item"""
        self.total_cost = self.quantity * self.unit_cost
        return self.total_cost
    
    def get_remaining_quantity(self):
        """Get quantity still to be received"""
        return self.quantity - self.received_quantity
    
    def is_fully_received(self):
        """Check if item is fully received"""
        return self.received_quantity >= self.quantity
    
    def to_dict(self, language='ar'):
        """Convert purchase order item to dictionary"""
        return {
            'id': self.id,
            'product_name': self.product.get_name(language),
            'product_sku': self.product.sku,
            'quantity': self.quantity,
            'received_quantity': self.received_quantity,
            'remaining_quantity': self.get_remaining_quantity(),
            'unit_cost': float(self.unit_cost),
            'total_cost': float(self.total_cost),
            'is_fully_received': self.is_fully_received(),
            'notes': self.notes
        }
    
    def __repr__(self):
        return f'<PurchaseOrderItem {self.product.sku} x {self.quantity}>'
