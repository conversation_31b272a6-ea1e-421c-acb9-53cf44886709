"""
إضافة حقل رسوم التوصيل إلى جدول المبيعات
Add delivery fee field to sales table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extensions import db
from sqlalchemy import text

def upgrade():
    """إضافة حقل رسوم التوصيل"""
    try:
        # إضافة حقل delivery_fee إلى جدول sales
        with db.engine.connect() as connection:
            connection.execute(text("""
                ALTER TABLE sales
                ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00
            """))
            connection.commit()

        print("✅ تم إضافة حقل رسوم التوصيل بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة حقل رسوم التوصيل: {e}")
        return False

def downgrade():
    """إزالة حقل رسوم التوصيل"""
    try:
        # إزالة حقل delivery_fee من جدول sales
        with db.engine.connect() as connection:
            connection.execute(text("""
                ALTER TABLE sales
                DROP COLUMN delivery_fee
            """))
            connection.commit()

        print("✅ تم إزالة حقل رسوم التوصيل بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في إزالة حقل رسوم التوصيل: {e}")
        return False

if __name__ == '__main__':
    from app import create_app
    
    app = create_app()
    with app.app_context():
        print("🔄 تطبيق هجرة رسوم التوصيل...")
        if upgrade():
            print("🎉 تم تطبيق الهجرة بنجاح!")
        else:
            print("❌ فشل في تطبيق الهجرة!")
