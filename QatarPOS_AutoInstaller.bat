@echo off
chcp 65001 >nul
title Qatar POS System - Auto Installer

:: تشغيل كمدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ========================================
    echo    تحتاج صلاحيات المدير للتثبيت
    echo    Administrator privileges required
    echo ========================================
    echo.
    echo جاري إعادة التشغيل كمدير...
    echo Restarting as administrator...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

cls
echo ========================================
echo    Qatar POS System - Auto Installer
echo    نظام نقاط البيع القطري - مثبت تلقائي
echo ========================================
echo.
echo 🚀 مرحباً بك في مثبت نظام نقاط البيع القطري
echo 🚀 Welcome to Qatar POS System Installer
echo.

:: التحقق من وجود الملف التنفيذي
if not exist "dist\QatarPOS.exe" (
    echo ❌ خطأ: ملف QatarPOS.exe غير موجود في مجلد dist
    echo ❌ Error: QatarPOS.exe not found in dist folder
    echo.
    echo يرجى التأكد من وجود الملف في: dist\QatarPOS.exe
    echo Please ensure the file exists at: dist\QatarPOS.exe
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف التطبيق
echo ✅ Application file found
echo.

:: إنشاء مجلد التثبيت
set INSTALL_DIR=C:\QatarPOS
echo 📁 إنشاء مجلد التثبيت: %INSTALL_DIR%
echo 📁 Creating installation directory: %INSTALL_DIR%

if exist "%INSTALL_DIR%" (
    echo ⚠️ المجلد موجود مسبقاً، سيتم الاستبدال
    echo ⚠️ Directory exists, will be replaced
    rmdir /S /Q "%INSTALL_DIR%" >nul 2>&1
)

mkdir "%INSTALL_DIR%" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء مجلد التثبيت
    echo ❌ Failed to create installation directory
    pause
    exit /b 1
)

echo ✅ تم إنشاء مجلد التثبيت بنجاح
echo ✅ Installation directory created successfully
echo.

:: نسخ الملف التنفيذي
echo 📋 نسخ ملف التطبيق...
echo 📋 Copying application file...
copy "dist\QatarPOS.exe" "%INSTALL_DIR%\" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في نسخ الملف التنفيذي
    echo ❌ Failed to copy executable file
    pause
    exit /b 1
)

echo ✅ تم نسخ الملف التنفيذي بنجاح
echo ✅ Executable file copied successfully
echo.

:: إنشاء ملف إلغاء التثبيت
echo 🗑️ إنشاء أداة إلغاء التثبيت...
echo 🗑️ Creating uninstaller...

(
echo @echo off
echo chcp 65001 ^>nul
echo title Qatar POS System - Uninstaller
echo.
echo ========================================
echo    Qatar POS System - Uninstaller
echo    نظام نقاط البيع القطري - إلغاء التثبيت
echo ========================================
echo.
echo هل أنت متأكد من إلغاء تثبيت Qatar POS System؟
echo Are you sure you want to uninstall Qatar POS System?
echo.
echo اضغط Y للمتابعة أو أي مفتاح آخر للإلغاء
echo Press Y to continue or any other key to cancel
echo.
set /p choice=
if /i "%%choice%%"=="Y" ^(
    echo.
    echo 🗑️ جاري إلغاء التثبيت...
    echo 🗑️ Uninstalling...
    echo.
    
    echo حذف الملفات...
    echo Deleting files...
    if exist "C:\QatarPOS" rmdir /S /Q "C:\QatarPOS"
    
    echo حذف الاختصارات...
    echo Deleting shortcuts...
    if exist "%%USERPROFILE%%\Desktop\Qatar POS.lnk" del "%%USERPROFILE%%\Desktop\Qatar POS.lnk"
    if exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS" rmdir /S /Q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS"
    
    echo.
    echo ✅ تم إلغاء التثبيت بنجاح!
    echo ✅ Uninstallation completed successfully!
^) else ^(
    echo.
    echo ⏹️ تم إلغاء العملية
    echo ⏹️ Operation cancelled
^)
echo.
pause
) > "%INSTALL_DIR%\Uninstall.bat"

echo ✅ تم إنشاء أداة إلغاء التثبيت
echo ✅ Uninstaller created
echo.

:: إنشاء اختصار على سطح المكتب
echo 🖥️ إنشاء اختصار على سطح المكتب...
echo 🖥️ Creating desktop shortcut...

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System - نظام نقاط البيع القطري'; $Shortcut.Save()"

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء اختصار سطح المكتب
    echo ✅ Desktop shortcut created
) else (
    echo ⚠️ فشل في إنشاء اختصار سطح المكتب
    echo ⚠️ Failed to create desktop shortcut
)
echo.

:: إنشاء اختصار في قائمة البداية
echo 📋 إنشاء اختصار في قائمة البداية...
echo 📋 Creating Start Menu shortcut...

set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Qatar POS
mkdir "%START_MENU_DIR%" >nul 2>&1

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System - نظام نقاط البيع القطري'; $Shortcut.Save()"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Uninstall Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'إلغاء تثبيت نظام نقاط البيع القطري'; $Shortcut.Save()"

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء اختصارات قائمة البداية
    echo ✅ Start Menu shortcuts created
) else (
    echo ⚠️ فشل في إنشاء اختصارات قائمة البداية
    echo ⚠️ Failed to create Start Menu shortcuts
)
echo.

:: إضافة إلى البرامج المثبتة (اختياري)
echo 📝 تسجيل التطبيق في النظام...
echo 📝 Registering application in system...

reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayName" /t REG_SZ /d "Qatar POS System" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "Publisher" /t REG_SZ /d "Qatar POS System" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul 2>&1

echo ✅ تم تسجيل التطبيق في النظام
echo ✅ Application registered in system
echo.

:: إنشاء ملف معلومات التثبيت
echo 📄 إنشاء ملف معلومات التثبيت...
echo 📄 Creating installation info file...

(
echo Qatar POS System - Installation Information
echo نظام نقاط البيع القطري - معلومات التثبيت
echo ==========================================
echo.
echo Installation Date: %date% %time%
echo تاريخ التثبيت: %date% %time%
echo.
echo Installation Path: %INSTALL_DIR%
echo مسار التثبيت: %INSTALL_DIR%
echo.
echo Version: 1.0.0
echo الإصدار: 1.0.0
echo.
echo Support: <EMAIL>
echo الدعم الفني: <EMAIL>
echo.
echo To uninstall: Run Uninstall.bat or use Windows Add/Remove Programs
echo لإلغاء التثبيت: شغل Uninstall.bat أو استخدم إضافة/إزالة البرامج
) > "%INSTALL_DIR%\Installation_Info.txt"

echo ✅ تم إنشاء ملف معلومات التثبيت
echo ✅ Installation info file created
echo.

:: التثبيت مكتمل
echo ========================================
echo 🎉 تم التثبيت بنجاح!
echo 🎉 Installation completed successfully!
echo ========================================
echo.
echo 📍 مسار التطبيق: %INSTALL_DIR%\QatarPOS.exe
echo 📍 Application path: %INSTALL_DIR%\QatarPOS.exe
echo.
echo 🖥️ تم إنشاء اختصار على سطح المكتب
echo 🖥️ Desktop shortcut created
echo.
echo 📋 تم إنشاء اختصارات في قائمة البداية
echo 📋 Start Menu shortcuts created
echo.
echo 🗑️ لإلغاء التثبيت: شغل %INSTALL_DIR%\Uninstall.bat
echo 🗑️ To uninstall: Run %INSTALL_DIR%\Uninstall.bat
echo.
echo هل تريد تشغيل التطبيق الآن؟
echo Do you want to run the application now?
echo.
set /p run_choice=اضغط Y لتشغيل التطبيق أو أي مفتاح آخر للخروج (Press Y to run or any key to exit): 

if /i "%run_choice%"=="Y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    echo 🚀 Starting application...
    start "" "%INSTALL_DIR%\QatarPOS.exe"
) else (
    echo.
    echo 👋 يمكنك تشغيل التطبيق من اختصار سطح المكتب
    echo 👋 You can run the application from the desktop shortcut
)

echo.
echo شكراً لاستخدام نظام نقاط البيع القطري!
echo Thank you for using Qatar POS System!
echo.
pause
