{% extends "base.html" %}

{% block title %}
{{ 'إدارة الدفع عند الاستلام - نظام نقاط البيع القطري' if language == 'ar' else 'COD Management - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.cod-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.cod-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
}

.status-pending_delivery {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-out_for_delivery {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #74c0fc;
}

.status-delivered {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-payment_collected {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-failed_delivery {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cod-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.cod-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

.delivery-info {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.cod-summary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.cod-summary .stat-item {
    text-align: center;
}

.cod-summary .stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.cod-summary .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.filter-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.notes-section {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.timeline-item {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background-color: #007bff;
}

.timeline-item.completed::before {
    background-color: #28a745;
}

.timeline-item.failed::before {
    background-color: #dc3545;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-truck"></i>
                    {{ 'إدارة الدفع عند الاستلام' if language == 'ar' else 'COD Management' }}
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('main.dashboard') }}">
                                {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('sales.index') }}">
                                {{ 'المبيعات' if language == 'ar' else 'Sales' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            {{ 'إدارة COD' if language == 'ar' else 'COD Management' }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- COD Summary -->
    <div class="row">
        <div class="col-12">
            <div class="cod-summary">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number" id="total_cod_orders">{{ cod_stats.total or 0 }}</span>
                            <span class="stat-label">{{ 'إجمالي طلبات COD' if language == 'ar' else 'Total COD Orders' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number" id="pending_deliveries">{{ cod_stats.pending or 0 }}</span>
                            <span class="stat-label">{{ 'في انتظار التوصيل' if language == 'ar' else 'Pending Delivery' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number" id="out_for_delivery">{{ cod_stats.out_for_delivery or 0 }}</span>
                            <span class="stat-label">{{ 'في طريق التوصيل' if language == 'ar' else 'Out for Delivery' }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number text-warning" id="total_amount">{{ '%.2f ر.ق'|format(cod_stats.total_amount or 0) if language == 'ar' else 'QAR %.2f'|format(cod_stats.total_amount or 0) }}</span>
                            <span class="stat-label">{{ 'إجمالي المبلغ' if language == 'ar' else 'Total Amount' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row">
        <div class="col-12">
            <div class="filter-section">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">{{ 'حالة COD' if language == 'ar' else 'COD Status' }}</label>
                        <select name="cod_status" class="form-select">
                            <option value="">{{ 'جميع الحالات' if language == 'ar' else 'All Statuses' }}</option>
                            <option value="pending_delivery" {{ 'selected' if request.args.get('cod_status') == 'pending_delivery' else '' }}>
                                {{ 'في انتظار التوصيل' if language == 'ar' else 'Pending Delivery' }}
                            </option>
                            <option value="out_for_delivery" {{ 'selected' if request.args.get('cod_status') == 'out_for_delivery' else '' }}>
                                {{ 'في طريق التوصيل' if language == 'ar' else 'Out for Delivery' }}
                            </option>
                            <option value="delivered" {{ 'selected' if request.args.get('cod_status') == 'delivered' else '' }}>
                                {{ 'تم التوصيل' if language == 'ar' else 'Delivered' }}
                            </option>
                            <option value="payment_collected" {{ 'selected' if request.args.get('cod_status') == 'payment_collected' else '' }}>
                                {{ 'تم تحصيل المبلغ' if language == 'ar' else 'Payment Collected' }}
                            </option>
                            <option value="failed_delivery" {{ 'selected' if request.args.get('cod_status') == 'failed_delivery' else '' }}>
                                {{ 'فشل التوصيل' if language == 'ar' else 'Failed Delivery' }}
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                        <input type="date" name="from_date" class="form-control" value="{{ request.args.get('from_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                        <input type="date" name="to_date" class="form-control" value="{{ request.args.get('to_date', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ 'البحث' if language == 'ar' else 'Search' }}</label>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="{{ 'رقم الفاتورة أو اسم العميل' if language == 'ar' else 'Invoice number or customer name' }}"
                                   value="{{ request.args.get('search', '') }}">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- COD Orders List -->
    <div class="row">
        <div class="col-12">
            {% if cod_orders %}
                {% for sale in cod_orders %}
                <div class="cod-card card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-receipt"></i>
                                        {{ sale.sale_number }}
                                    </h5>
                                    <span class="status-badge status-{{ sale.cod_status }}">
                                        {{ sale.get_cod_status_display(language) }}
                                    </span>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-sm-6">
                                        <small class="text-muted">{{ 'العميل:' if language == 'ar' else 'Customer:' }}</small>
                                        <div>{{ sale.customer.get_display_name(language) if sale.customer else 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</div>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">{{ 'المبلغ:' if language == 'ar' else 'Amount:' }}</small>
                                        <div class="fw-bold text-primary">
                                            {{ '%.2f ر.ق'|format(sale.total_amount) if language == 'ar' else 'QAR %.2f'|format(sale.total_amount) }}
                                        </div>
                                    </div>
                                </div>

                                {% if sale.delivery_address %}
                                <div class="delivery-info">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <small class="text-muted">{{ 'عنوان التوصيل:' if language == 'ar' else 'Delivery Address:' }}</small>
                                            <div>{{ sale.delivery_address }}</div>
                                        </div>
                                        {% if sale.delivery_phone %}
                                        <div class="col-sm-6">
                                            <small class="text-muted">{{ 'هاتف التوصيل:' if language == 'ar' else 'Delivery Phone:' }}</small>
                                            <div>{{ sale.delivery_phone }}</div>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% if sale.delivery_notes %}
                                    <div class="mt-2">
                                        <small class="text-muted">{{ 'ملاحظات التوصيل:' if language == 'ar' else 'Delivery Notes:' }}</small>
                                        <div>{{ sale.delivery_notes }}</div>
                                    </div>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <div class="text-end">
                                    <small class="text-muted d-block">{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</small>
                                    
                                    <div class="cod-actions mt-3">
                                        {% if sale.cod_status == 'pending_delivery' %}
                                            <button class="btn btn-info btn-sm" onclick="updateCODStatus({{ sale.id }}, 'out_for_delivery')">
                                                <i class="bi bi-truck"></i>
                                                {{ 'في طريق التوصيل' if language == 'ar' else 'Out for Delivery' }}
                                            </button>
                                        {% endif %}
                                        
                                        {% if sale.cod_status == 'out_for_delivery' %}
                                            <button class="btn btn-success btn-sm" onclick="updateCODStatus({{ sale.id }}, 'delivered')">
                                                <i class="bi bi-check-circle"></i>
                                                {{ 'تم التوصيل' if language == 'ar' else 'Delivered' }}
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="updateCODStatus({{ sale.id }}, 'failed_delivery')">
                                                <i class="bi bi-x-circle"></i>
                                                {{ 'فشل التوصيل' if language == 'ar' else 'Failed' }}
                                            </button>
                                        {% endif %}
                                        
                                        {% if sale.cod_status == 'delivered' %}
                                            <button class="btn btn-primary btn-sm" onclick="updateCODStatus({{ sale.id }}, 'payment_collected')">
                                                <i class="bi bi-cash"></i>
                                                {{ 'تم تحصيل المبلغ' if language == 'ar' else 'Payment Collected' }}
                                            </button>
                                        {% endif %}
                                        
                                        {% if sale.cod_status == 'failed_delivery' %}
                                            <button class="btn btn-info btn-sm" onclick="updateCODStatus({{ sale.id }}, 'pending_delivery')">
                                                <i class="bi bi-arrow-clockwise"></i>
                                                {{ 'إعادة المحاولة' if language == 'ar' else 'Retry' }}
                                            </button>
                                        {% endif %}
                                        
                                        <button class="btn btn-outline-secondary btn-sm" onclick="viewSaleDetails({{ sale.id }})">
                                            <i class="bi bi-eye"></i>
                                            {{ 'عرض' if language == 'ar' else 'View' }}
                                        </button>
                                        
                                        <button class="btn btn-outline-primary btn-sm" onclick="addCODNote({{ sale.id }})">
                                            <i class="bi bi-chat-dots"></i>
                                            {{ 'ملاحظة' if language == 'ar' else 'Note' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if sale.notes %}
                        <div class="notes-section mt-3">
                            <small class="text-muted">{{ 'الملاحظات:' if language == 'ar' else 'Notes:' }}</small>
                            <div class="mt-1">{{ sale.notes|nl2br }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
                
                <!-- Pagination -->
                {% if pagination %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales.cod_management', page=pagination.prev_num, **request.args) }}">
                                {{ 'السابق' if language == 'ar' else 'Previous' }}
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('sales.cod_management', page=page_num, **request.args) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales.cod_management', page=pagination.next_num, **request.args) }}">
                                {{ 'التالي' if language == 'ar' else 'Next' }}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-truck display-1 text-muted"></i>
                    <h4 class="mt-3 text-muted">
                        {{ 'لا توجد طلبات دفع عند الاستلام' if language == 'ar' else 'No COD orders found' }}
                    </h4>
                    <p class="text-muted">
                        {{ 'لم يتم العثور على أي طلبات تطابق معايير البحث' if language == 'ar' else 'No orders match the search criteria' }}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- COD Status Update Modal -->
<div class="modal fade" id="codStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{ 'تحديث حالة الدفع عند الاستلام' if language == 'ar' else 'Update COD Status' }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="codStatusForm">
                    <input type="hidden" id="saleId" name="sale_id">
                    <input type="hidden" id="newStatus" name="new_status">
                    
                    <div class="mb-3">
                        <label class="form-label">{{ 'ملاحظات (اختياري):' if language == 'ar' else 'Notes (optional):' }}</label>
                        <textarea class="form-control" id="statusNotes" name="notes" rows="3"
                                  placeholder="{{ 'أضف ملاحظات حول تحديث الحالة' if language == 'ar' else 'Add notes about the status update' }}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmCODStatusUpdate()">
                    {{ 'تحديث الحالة' if language == 'ar' else 'Update Status' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Note Modal -->
<div class="modal fade" id="addNoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{ 'إضافة ملاحظة' if language == 'ar' else 'Add Note' }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addNoteForm">
                    <input type="hidden" id="noteSaleId" name="sale_id">
                    
                    <div class="mb-3">
                        <label class="form-label">{{ 'الملاحظة:' if language == 'ar' else 'Note:' }}</label>
                        <textarea class="form-control" id="noteText" name="note" rows="4" required
                                  placeholder="{{ 'أدخل الملاحظة هنا' if language == 'ar' else 'Enter note here' }}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmAddNote()">
                    {{ 'إضافة الملاحظة' if language == 'ar' else 'Add Note' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Update COD status
function updateCODStatus(saleId, newStatus) {
    document.getElementById('saleId').value = saleId;
    document.getElementById('newStatus').value = newStatus;
    document.getElementById('statusNotes').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('codStatusModal'));
    modal.show();
}

function confirmCODStatusUpdate() {
    const formData = new FormData(document.getElementById('codStatusForm'));
    
    fetch('/api/sales/cod/update-status', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || '{{ "حدث خطأ أثناء تحديث الحالة" if language == "ar" else "Error updating status" }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ "حدث خطأ في الاتصال" if language == "ar" else "Connection error" }}');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('codStatusModal')).hide();
}

// Add note
function addCODNote(saleId) {
    document.getElementById('noteSaleId').value = saleId;
    document.getElementById('noteText').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('addNoteModal'));
    modal.show();
}

function confirmAddNote() {
    const formData = new FormData(document.getElementById('addNoteForm'));
    
    fetch('/api/sales/cod/add-note', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || '{{ "حدث خطأ أثناء إضافة الملاحظة" if language == "ar" else "Error adding note" }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ "حدث خطأ في الاتصال" if language == "ar" else "Connection error" }}');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('addNoteModal')).hide();
}

// View sale details
function viewSaleDetails(saleId) {
    window.open(`/sales/view/${saleId}`, '_blank');
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if no modals are open
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
