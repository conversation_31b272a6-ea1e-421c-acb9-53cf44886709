{% extends "base.html" %}

{% block title %}{{ 'إنشاء تعديل مخزون' if language == 'ar' else 'Create Stock Adjustment' }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-gear"></i>
                {{ 'إنشاء تعديل مخزون' if language == 'ar' else 'Create Stock Adjustment' }}
            </h1>
            <a href="{{ url_for('inventory.adjustments') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>
</div>

<form method="POST" id="adjustmentForm">
    <div class="row">
        <!-- Adjustment Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'تفاصيل التعديل' if language == 'ar' else 'Adjustment Details' }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reason" class="form-label">{{ 'سبب التعديل' if language == 'ar' else 'Reason' }} *</label>
                                <select name="reason" id="reason" class="form-select" required>
                                    <option value="">{{ 'اختر السبب' if language == 'ar' else 'Select Reason' }}</option>
                                    <option value="physical_count">{{ 'جرد فعلي' if language == 'ar' else 'Physical Count' }}</option>
                                    <option value="damage">{{ 'تلف' if language == 'ar' else 'Damage' }}</option>
                                    <option value="theft">{{ 'سرقة' if language == 'ar' else 'Theft' }}</option>
                                    <option value="expired">{{ 'منتهي الصلاحية' if language == 'ar' else 'Expired' }}</option>
                                    <option value="system_error">{{ 'خطأ نظام' if language == 'ar' else 'System Error' }}</option>
                                    <option value="other">{{ 'أخرى' if language == 'ar' else 'Other' }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="adjustment_date" class="form-label">{{ 'تاريخ التعديل' if language == 'ar' else 'Adjustment Date' }} *</label>
                                <input type="datetime-local" name="adjustment_date" id="adjustment_date"
                                       class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ 'ملاحظات' if language == 'ar' else 'Notes' }}</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="{{ 'أدخل أي ملاحظات إضافية' if language == 'ar' else 'Enter any additional notes' }}"></textarea>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ 'المنتجات' if language == 'ar' else 'Products' }}</h5>
                    <button type="button" class="btn btn-primary btn-sm" onclick="addProduct()">
                        <i class="bi bi-plus"></i>
                        {{ 'إضافة منتج' if language == 'ar' else 'Add Product' }}
                    </button>
                </div>
                <div class="card-body">
                    <div id="products-container">
                        <!-- Products will be added here dynamically -->
                    </div>
                    
                    <div id="no-products" class="text-center py-4 text-muted">
                        <i class="bi bi-box display-4"></i>
                        <p class="mt-2">{{ 'لم يتم إضافة منتجات بعد' if language == 'ar' else 'No products added yet' }}</p>
                        <button type="button" class="btn btn-outline-primary" onclick="addProduct()">
                            <i class="bi bi-plus"></i>
                            {{ 'إضافة منتج' if language == 'ar' else 'Add Product' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'ملخص التعديل' if language == 'ar' else 'Adjustment Summary' }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ 'عدد المنتجات:' if language == 'ar' else 'Products Count:' }}</span>
                        <span id="products-count">0</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ 'إجمالي التغيير:' if language == 'ar' else 'Total Change:' }}</span>
                        <span id="total-change">0</span>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>{{ 'قيمة التغيير:' if language == 'ar' else 'Value Change:' }}</span>
                        <span id="value-change">0.00 {{ 'ر.ق' if language == 'ar' else 'QAR' }}</span>
                    </div>
                    <hr>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success" id="save-draft">
                            <i class="bi bi-save"></i>
                            {{ 'حفظ كمسودة' if language == 'ar' else 'Save as Draft' }}
                        </button>
                        <button type="button" class="btn btn-primary" onclick="submitAndApprove()">
                            <i class="bi bi-check-circle"></i>
                            {{ 'حفظ واعتماد' if language == 'ar' else 'Save & Approve' }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">{{ 'مساعدة' if language == 'ar' else 'Help' }}</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        {{ 'تعديل المخزون يسمح لك بتصحيح كميات المنتجات في النظام. تأكد من إدخال الكميات الصحيحة والسبب المناسب.' if language == 'ar' 
                           else 'Stock adjustment allows you to correct product quantities in the system. Make sure to enter correct quantities and appropriate reason.' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Product Selection Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'اختيار منتج' if language == 'ar' else 'Select Product' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" id="product-search" class="form-control" 
                           placeholder="{{ 'البحث عن منتج...' if language == 'ar' else 'Search for product...' }}">
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                <th>{{ 'الرمز' if language == 'ar' else 'SKU' }}</th>
                                <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                <th>{{ 'الإجراء' if language == 'ar' else 'Action' }}</th>
                            </tr>
                        </thead>
                        <tbody id="products-list">
                            {% for product in products %}
                            <tr>
                                <td>{{ product.get_name(language) }}</td>
                                <td><code>{{ product.sku }}</code></td>
                                <td>{{ product.current_stock }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" 
                                            onclick="selectProduct({{ product.id }}, '{{ product.get_name(language) }}', '{{ product.sku }}', {{ product.current_stock }}, {{ product.cost_price }})">
                                        {{ 'اختيار' if language == 'ar' else 'Select' }}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let productIndex = 0;
let selectedProducts = new Set();

function addProduct() {
    $('#productModal').modal('show');
}

function selectProduct(productId, productName, sku, currentStock, costPrice) {
    if (selectedProducts.has(productId)) {
        alert('{{ "هذا المنتج مضاف بالفعل" if language == "ar" else "This product is already added" }}');
        return;
    }
    
    selectedProducts.add(productId);
    
    const productHtml = `
        <div class="card mb-3" id="product-${productIndex}">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <strong>${productName}</strong><br>
                        <small class="text-muted">${sku}</small>
                        <input type="hidden" name="products[${productIndex}][product_id]" value="${productId}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</label>
                        <input type="number" class="form-control" value="${currentStock}" readonly>
                        <input type="hidden" name="products[${productIndex}][old_quantity]" value="${currentStock}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">{{ 'المخزون الجديد' if language == 'ar' else 'New Stock' }}</label>
                        <input type="number" name="products[${productIndex}][new_quantity]" 
                               class="form-control new-quantity" value="${currentStock}" 
                               min="0" step="1" onchange="calculateChange(${productIndex}, ${currentStock}, ${costPrice})">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">{{ 'التغيير' if language == 'ar' else 'Change' }}</label>
                        <input type="text" class="form-control quantity-change" readonly value="0">
                        <input type="hidden" name="products[${productIndex}][unit_cost]" value="${costPrice}">
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                onclick="removeProduct(${productIndex}, ${productId})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <input type="text" name="products[${productIndex}][notes]" 
                               class="form-control form-control-sm" 
                               placeholder="{{ 'ملاحظات للمنتج (اختياري)' if language == 'ar' else 'Product notes (optional)' }}">
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#products-container').append(productHtml);
    $('#no-products').hide();
    productIndex++;
    updateSummary();
    $('#productModal').modal('hide');
}

function removeProduct(index, productId) {
    selectedProducts.delete(productId);
    $(`#product-${index}`).remove();
    updateSummary();
    
    if ($('#products-container .card').length === 0) {
        $('#no-products').show();
    }
}

function calculateChange(index, oldQuantity, costPrice) {
    const newQuantity = parseFloat($(`input[name="products[${index}][new_quantity]"]`).val()) || 0;
    const change = newQuantity - oldQuantity;
    
    $(`#product-${index} .quantity-change`).val(change);
    updateSummary();
}

function updateSummary() {
    let totalProducts = $('#products-container .card').length;
    let totalChange = 0;
    let valueChange = 0;
    
    $('#products-container .card').each(function() {
        const change = parseFloat($(this).find('.quantity-change').val()) || 0;
        const costPrice = parseFloat($(this).find('input[name*="[unit_cost]"]').val()) || 0;
        
        totalChange += change;
        valueChange += change * costPrice;
    });
    
    $('#products-count').text(totalProducts);
    $('#total-change').text(totalChange);
    $('#value-change').text(valueChange.toFixed(2) + ' {{ "ر.ق" if language == "ar" else "QAR" }}');
}

function submitAndApprove() {
    $('<input>').attr({
        type: 'hidden',
        name: 'approve',
        value: 'true'
    }).appendTo('#adjustmentForm');
    
    $('#adjustmentForm').submit();
}

// Product search functionality
$('#product-search').on('input', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('#products-list tr').each(function() {
        const productName = $(this).find('td:first').text().toLowerCase();
        const sku = $(this).find('code').text().toLowerCase();
        
        if (productName.includes(searchTerm) || sku.includes(searchTerm)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
});

// Set current date/time
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    document.getElementById('adjustment_date').value = localDateTime;
});
</script>
{% endblock %}
