"""
Build Script for Qatar POS System Desktop Application
سكريبت بناء تطبيق نقاط البيع القطري لسطح المكتب
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_spec_file():
    """إنشاء ملف المواصفات لـ PyInstaller"""
    
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# تحديد الملفات والمجلدات المطلوبة
added_files = [
    ('templates', 'templates'),
    ('static', 'static'),
    ('migrations', 'migrations'),
    ('config.py', '.'),
    ('extensions.py', '.'),
    ('models', 'models'),
    ('routes', 'routes'),
    ('utils', 'utils'),
    ('requirements.txt', '.'),
]

# تحديد الوحدات المخفية
hidden_imports = [
    'flask',
    'flask_sqlalchemy',
    'flask_login',
    'flask_migrate',
    'flask_babel',
    'flask_wtf',
    'wtforms',
    'sqlalchemy',
    'reportlab',
    'weasyprint',
    'barcode',
    'qrcode',
    'PIL',
    'openpyxl',
    'xlsxwriter',
    'requests',
    'dateutil',
    'dotenv',
    'tkinter',
    'threading',
    'webbrowser',
    'socket',
    'json',
    'datetime',
    'time',
    'os',
    'sys',
    'subprocess',
]

a = Analysis(
    ['desktop_app.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='QatarPOS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/images/logo.ico' if os.path.exists('static/images/logo.ico') else None,
)
'''
    
    with open('qatar_pos.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✅ تم إنشاء ملف المواصفات: qatar_pos.spec")

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 جاري تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements_desktop.txt'
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 جاري بناء الملف التنفيذي...")
    
    try:
        # إنشاء ملف المواصفات
        create_spec_file()
        
        # تشغيل PyInstaller
        subprocess.check_call([
            sys.executable, '-m', 'PyInstaller', 
            '--clean', 
            'qatar_pos.spec'
        ])
        
        print("✅ تم بناء الملف التنفيذي بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء الملف التنفيذي: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت التثبيت"""
    
    installer_content = '''@echo off
echo ========================================
echo    Qatar POS System - نظام نقاط البيع القطري
echo    Desktop Installation - تثبيت سطح المكتب
echo ========================================
echo.

echo جاري إنشاء مجلد التطبيق...
if not exist "C:\\QatarPOS" mkdir "C:\\QatarPOS"

echo جاري نسخ الملفات...
xcopy /E /I /Y "dist\\QatarPOS.exe" "C:\\QatarPOS\\"

echo جاري إنشاء اختصار على سطح المكتب...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Qatar POS.lnk'); $Shortcut.TargetPath = 'C:\\QatarPOS\\QatarPOS.exe'; $Shortcut.WorkingDirectory = 'C:\\QatarPOS'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()"

echo جاري إنشاء اختصار في قائمة البداية...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Qatar POS" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Qatar POS"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Qatar POS\\Qatar POS.lnk'); $Shortcut.TargetPath = 'C:\\QatarPOS\\QatarPOS.exe'; $Shortcut.WorkingDirectory = 'C:\\QatarPOS'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()"

echo.
echo ✅ تم تثبيت التطبيق بنجاح!
echo 📍 مسار التطبيق: C:\\QatarPOS\\QatarPOS.exe
echo 🖥️ تم إنشاء اختصار على سطح المكتب
echo 📋 تم إنشاء اختصار في قائمة البداية
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''
    
    with open('install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content.strip())
    
    print("✅ تم إنشاء سكريبت التثبيت: install.bat")

def create_uninstaller_script():
    """إنشاء سكريپت إلغاء التثبيت"""
    
    uninstaller_content = '''@echo off
echo ========================================
echo    Qatar POS System - نظام نقاط البيع القطري
echo    Uninstallation - إلغاء التثبيت
echo ========================================
echo.

echo هل أنت متأكد من إلغاء تثبيت Qatar POS System؟
echo Press Y to continue or any other key to cancel...
pause >nul

echo جاري حذف ملفات التطبيق...
if exist "C:\\QatarPOS" rmdir /S /Q "C:\\QatarPOS"

echo جاري حذف الاختصارات...
if exist "%USERPROFILE%\\Desktop\\Qatar POS.lnk" del "%USERPROFILE%\\Desktop\\Qatar POS.lnk"
if exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Qatar POS" rmdir /S /Q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Qatar POS"

echo.
echo ✅ تم إلغاء تثبيت التطبيق بنجاح!
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''
    
    with open('uninstall.bat', 'w', encoding='utf-8') as f:
        f.write(uninstaller_content.strip())
    
    print("✅ تم إنشاء سكريپت إلغاء التثبيت: uninstall.bat")

def create_readme():
    """إنشاء ملف README"""
    
    readme_content = '''# Qatar POS System - Desktop Application
# نظام نقاط البيع القطري - تطبيق سطح المكتب

## التثبيت - Installation

### الطريقة الأولى: التثبيت التلقائي
1. قم بتشغيل ملف `install.bat` كمدير
2. اتبع التعليمات على الشاشة
3. سيتم إنشاء اختصارات على سطح المكتب وقائمة البداية

### الطريقة الثانية: التثبيت اليدوي
1. انسخ ملف `QatarPOS.exe` إلى المجلد المطلوب
2. قم بتشغيل الملف مباشرة

## الاستخدام - Usage

1. قم بتشغيل التطبيق من الاختصار أو الملف مباشرة
2. سيتم بدء الخادم المحلي تلقائياً
3. انقر على "فتح المتصفح" أو اذهب إلى http://127.0.0.1:2626
4. استخدم النظام كما هو معتاد

## المميزات - Features

✅ واجهة سطح مكتب سهلة الاستخدام
✅ بدء وإيقاف الخادم من الواجهة
✅ فتح المتصفح تلقائياً
✅ سجل النظام المفصل
✅ دعم اللغة العربية والإنجليزية
✅ لا يحتاج تثبيت Python منفصل

## المتطلبات - Requirements

- Windows 10 أو أحدث
- 4 GB RAM كحد أدنى
- 500 MB مساحة فارغة على القرص الصلب
- اتصال بالإنترنت (للتحديثات)

## الدعم الفني - Support

للدعم الفني والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +974 1234 5678

## إلغاء التثبيت - Uninstallation

قم بتشغيل ملف `uninstall.bat` كمدير لإلغاء التثبيت بالكامل

---

© 2024 Qatar POS System. All rights reserved.
'''
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print("✅ تم إنشاء ملف README.txt")

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 جاري تنظيف الملفات المؤقتة...")
    
    # حذف المجلدات المؤقتة
    temp_dirs = ['build', '__pycache__']
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"🗑️ تم حذف: {temp_dir}")
    
    # حذف الملفات المؤقتة
    temp_files = ['qatar_pos.spec']
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"🗑️ تم حذف: {temp_file}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية بناء تطبيق Qatar POS لسطح المكتب")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['desktop_app.py', 'app.py', 'config.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # بناء الملف التنفيذي
    if not build_executable():
        return False
    
    # إنشاء ملفات التثبيت
    create_installer_script()
    create_uninstaller_script()
    create_readme()
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 تم بناء التطبيق بنجاح!")
    print("📁 الملف التنفيذي: dist/QatarPOS.exe")
    print("💾 سكريپت التثبيت: install.bat")
    print("🗑️ سكريپت إلغاء التثبيت: uninstall.bat")
    print("📖 ملف التعليمات: README.txt")
    print("\n✨ يمكنك الآن توزيع التطبيق!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في بناء التطبيق")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        sys.exit(1)
