@echo off
chcp 65001 >nul
title Qatar POS System - Desktop Application

echo ========================================
echo    Qatar POS System
echo    نظام نقاط البيع القطري
echo    Desktop Application - تطبيق سطح المكتب
echo ========================================
echo.

echo 🚀 بدء تشغيل التطبيق...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "desktop_app.py" (
    echo ❌ ملف desktop_app.py غير موجود
    pause
    exit /b 1
)

if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

echo ✅ تم العثور على جميع الملفات المطلوبة
echo.

echo 📦 التحقق من المتطلبات...
pip show flask >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Flask غير مثبت، جاري التثبيت...
    pip install flask flask-sqlalchemy flask-login flask-migrate flask-babel flask-wtf
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🖥️ تشغيل تطبيق سطح المكتب...
echo.
echo 💡 نصائح:
echo    - سيتم بدء الخادم تلقائياً
echo    - انقر "فتح المتصفح" للوصول للنظام
echo    - استخدم Ctrl+C لإيقاف التطبيق
echo.

python desktop_app.py

echo.
echo 👋 تم إغلاق التطبيق
pause
