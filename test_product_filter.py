#!/usr/bin/env python3
"""
اختبار فلترة المنتجات حسب الفئة
Test product filtering by category
"""

from app import create_app
from extensions import db
from models.product import Product
from models.category import Category

def test_product_filtering():
    """اختبار فلترة المنتجات"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار فلترة المنتجات حسب الفئة")
        print("=" * 50)
        
        # عرض جميع الفئات
        categories = Category.query.filter_by(is_active=True).all()
        print(f"📂 الفئات المتاحة ({len(categories)}):")
        for cat in categories:
            print(f"  - ID: {cat.id}, الاسم: {cat.name_ar} ({cat.name_en})")
        
        print()
        
        # عرض جميع المنتجات
        all_products = Product.query.all()
        print(f"📦 جميع المنتجات ({len(all_products)}):")
        for prod in all_products:
            category_name = prod.category.name_ar if prod.category else 'بدون فئة'
            print(f"  - {prod.name_ar} (فئة: {category_name}, ID: {prod.category_id})")
        
        print()
        
        # اختبار الفلترة لكل فئة
        for category in categories:
            filtered_products = Product.query.filter_by(category_id=category.id).all()
            print(f"🔍 المنتجات في فئة '{category.name_ar}' (ID: {category.id}):")
            if filtered_products:
                for prod in filtered_products:
                    print(f"  ✅ {prod.name_ar} (SKU: {prod.sku})")
            else:
                print("  ❌ لا توجد منتجات في هذه الفئة")
            print()
        
        # اختبار الفلترة مع الحالة النشطة
        print("🔍 اختبار الفلترة مع الحالة النشطة:")
        active_products = Product.query.filter_by(is_active=True).all()
        print(f"  ✅ المنتجات النشطة: {len(active_products)}")
        
        # اختبار فلترة مركبة (فئة + حالة نشطة)
        for category in categories:
            filtered_active = Product.query.filter_by(
                category_id=category.id, 
                is_active=True
            ).all()
            print(f"  📂 فئة '{category.name_ar}' + نشط: {len(filtered_active)} منتج")

if __name__ == '__main__':
    test_product_filtering()
