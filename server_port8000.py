#!/usr/bin/env python3
"""
Qatar POS System - Server on Port 8000
Alternative port to avoid conflicts
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import sys

def open_browser():
    """Open browser after server starts"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
        print("🌐 Browser opened automatically")
    except:
        print("🌐 Please open browser manually: http://localhost:8000")

class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 60px;
            border-radius: 30px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            max-width: 700px;
            width: 90%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 4em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.4);
        }
        h2 {
            font-size: 2.5em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .success {
            background: rgba(40, 167, 69, 0.4);
            border: 3px solid rgba(40, 167, 69, 0.7);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
            font-size: 1.5em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .info {
            background: rgba(23, 162, 184, 0.3);
            border: 2px solid rgba(23, 162, 184, 0.6);
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            font-size: 1.1em;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.2);
        }
        .status {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(40, 167, 69, 0.9);
            padding: 15px 25px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1em;
            animation: bounce 3s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .port-info {
            background: rgba(255, 193, 7, 0.3);
            border: 2px solid rgba(255, 193, 7, 0.6);
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="status">🟢 ONLINE</div>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        
        <div class="success">
            🎉 النظام يعمل بنجاح!<br>
            🎉 System Running Successfully!
        </div>
        
        <div class="port-info">
            🔧 تم حل مشكلة المنفذ!<br>
            🔧 Port conflict resolved!<br>
            📍 New URL: http://localhost:8000
        </div>
        
        <div class="info">
            <strong>🌐 معلومات الخادم / Server Information:</strong><br>
            📍 URL: http://localhost:8000<br>
            🔧 Engine: Python HTTP Server<br>
            🐍 Python: ''' + sys.version.split()[0] + '''<br>
            🔌 Port: 8000 (Alternative Port)<br>
            ⏰ Status: Fully Operational
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🌐 متعدد اللغات</h3>
                <p>Arabic & English Support</p>
            </div>
            <div class="feature">
                <h3>💰 الريال القطري</h3>
                <p>QAR Currency Support</p>
            </div>
            <div class="feature">
                <h3>🏢 للشركات القطرية</h3>
                <p>Qatar Business Ready</p>
            </div>
            <div class="feature">
                <h3>🆔 الهوية القطرية</h3>
                <p>Qatar ID Support</p>
            </div>
            <div class="feature">
                <h3>📅 أسبوع العمل</h3>
                <p>6-Day Work Week</p>
            </div>
            <div class="feature">
                <h3>🕌 إغلاق الجمعة</h3>
                <p>Friday Closure</p>
            </div>
        </div>
        
        <div style="margin-top: 50px; font-size: 1.3em;">
            <p><strong>🎊 مشكلة المنفذ 5000 تم حلها!</strong></p>
            <p><strong>🎊 Port 5000 conflict resolved!</strong></p>
            <p><strong>✅ النظام يعمل على المنفذ 8000</strong></p>
            <p><strong>✅ System running on port 8000</strong></p>
        </div>
        
        <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.7;">
            <p>صُنع بـ ❤️ في قطر | Made with ❤️ in Qatar</p>
        </div>
    </div>
</body>
</html>'''
        
        self.wfile.write(html.encode('utf-8'))

def main():
    print("🇶🇦 Qatar POS System - Port 8000 Server")
    print("نظام نقاط البيع القطري - خادم المنفذ 8000")
    print("=" * 60)
    print("🔧 Using alternative port 8000 to avoid conflicts")
    print("🔧 استخدام المنفذ البديل 8000 لتجنب التعارض")
    print("=" * 60)
    print("📍 Server URL: http://localhost:8000")
    print("🌐 Opening browser automatically...")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", 8000), QatarPOSHandler) as httpd:
            print("✅ Server started successfully on port 8000!")
            print("✅ تم تشغيل الخادم بنجاح على المنفذ 8000!")
            print("🎉 SUCCESS! Open http://localhost:8000")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Thank you!")
        print("👋 تم إيقاف الخادم. شكراً!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Try a different port or check permissions")

if __name__ == '__main__':
    main()
