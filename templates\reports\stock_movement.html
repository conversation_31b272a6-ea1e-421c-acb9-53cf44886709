{% extends "base.html" %}

{% block title %}
{{ 'تقرير حركة المخزون' if language == 'ar' else 'Stock Movement Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block extra_css %}
<style>
.movement-in {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.movement-out {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.movement-adjust {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.movement-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.timeline-item {
    border-left: 3px solid #dee2e6;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #6c757d;
}

.timeline-item.movement-in::before {
    background-color: #28a745;
}

.timeline-item.movement-out::before {
    background-color: #dc3545;
}

.timeline-item.movement-adjust::before {
    background-color: #ffc107;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-arrow-left-right"></i>
                        {{ 'تقرير حركة المخزون' if language == 'ar' else 'Stock Movement Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">{{ 'القسم' if language == 'ar' else 'Category' }}</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">{{ 'جميع الأقسام' if language == 'ar' else 'All Categories' }}</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {{ 'selected' if selected_category == category.id }}>
                                            {{ category.name_ar if language == 'ar' else category.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">{{ 'المنتج' if language == 'ar' else 'Product' }}</label>
                                    <select name="product_id" class="form-select">
                                        <option value="">{{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}</option>
                                        {% for product in products %}
                                        <option value="{{ product.id }}" {{ 'selected' if selected_product == product.id }}>
                                            {{ product.name_ar if language == 'ar' else product.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">{{ 'نوع الحركة' if language == 'ar' else 'Movement Type' }}</label>
                                    <select name="movement_type" class="form-select">
                                        <option value="">{{ 'جميع الأنواع' if language == 'ar' else 'All Types' }}</option>
                                        <option value="sale" {{ 'selected' if selected_movement_type == 'sale' }}>
                                            {{ 'بيع' if language == 'ar' else 'Sale' }}
                                        </option>
                                        <option value="purchase" {{ 'selected' if selected_movement_type == 'purchase' }}>
                                            {{ 'شراء' if language == 'ar' else 'Purchase' }}
                                        </option>
                                        <option value="adjustment" {{ 'selected' if selected_movement_type == 'adjustment' }}>
                                            {{ 'تعديل' if language == 'ar' else 'Adjustment' }}
                                        </option>
                                        <option value="return" {{ 'selected' if selected_movement_type == 'return' }}>
                                            {{ 'إرجاع' if language == 'ar' else 'Return' }}
                                        </option>
                                        <option value="damage" {{ 'selected' if selected_movement_type == 'damage' }}>
                                            {{ 'تلف' if language == 'ar' else 'Damage' }}
                                        </option>
                                        <option value="transfer" {{ 'selected' if selected_movement_type == 'transfer' }}>
                                            {{ 'نقل' if language == 'ar' else 'Transfer' }}
                                        </option>
                                        <option value="initial" {{ 'selected' if selected_movement_type == 'initial' }}>
                                            {{ 'رصيد ابتدائي' if language == 'ar' else 'Initial Stock' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel"></i>
                                            {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                                        </button>
                                        <a href="{{ url_for('reports.stock_movement') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي الحركات' if language == 'ar' else 'Total Movements' }}</h6>
                                            <h3 class="mb-0">{{ total_movements }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-arrow-left-right fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة الداخل' if language == 'ar' else 'Value In' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_value_in) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-arrow-down-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة الخارج' if language == 'ar' else 'Value Out' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_value_out) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-arrow-up-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% for movement_type, count in movement_stats.items() %}
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">
                                                {% if movement_type == 'sale' %}{{ 'مبيعات' if language == 'ar' else 'Sales' }}
                                                {% elif movement_type == 'purchase' %}{{ 'مشتريات' if language == 'ar' else 'Purchases' }}
                                                {% elif movement_type == 'adjustment' %}{{ 'تعديلات' if language == 'ar' else 'Adjustments' }}
                                                {% elif movement_type == 'return' %}{{ 'إرجاعات' if language == 'ar' else 'Returns' }}
                                                {% elif movement_type == 'damage' %}{{ 'تلف' if language == 'ar' else 'Damage' }}
                                                {% elif movement_type == 'transfer' %}{{ 'نقل' if language == 'ar' else 'Transfer' }}
                                                {% elif movement_type == 'initial' %}{{ 'رصيد ابتدائي' if language == 'ar' else 'Initial' }}
                                                {% endif %}
                                            </h6>
                                            <h3 class="mb-0">{{ count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            {% if movement_type == 'sale' %}<i class="bi bi-cart-dash fs-1"></i>
                                            {% elif movement_type == 'purchase' %}<i class="bi bi-cart-plus fs-1"></i>
                                            {% elif movement_type == 'adjustment' %}<i class="bi bi-tools fs-1"></i>
                                            {% elif movement_type == 'return' %}<i class="bi bi-arrow-return-left fs-1"></i>
                                            {% elif movement_type == 'damage' %}<i class="bi bi-exclamation-triangle fs-1"></i>
                                            {% elif movement_type == 'transfer' %}<i class="bi bi-arrow-left-right fs-1"></i>
                                            {% elif movement_type == 'initial' %}<i class="bi bi-plus-circle fs-1"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Movements Timeline -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="bi bi-clock-history"></i>
                                {{ 'سجل حركات المخزون' if language == 'ar' else 'Stock Movement Log' }}
                                <span class="badge bg-secondary ms-2">{{ movements|length }}</span>
                            </h5>
                            
                            {% if movements %}
                            <div class="timeline">
                                {% for movement in movements %}
                                <div class="timeline-item movement-{{ 'in' if movement.quantity_change > 0 else 'out' if movement.quantity_change < 0 else 'adjust' }}">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-2">
                                                    <div class="d-flex align-items-center">
                                                        {% if movement.transaction_type == 'sale' %}
                                                        <i class="bi bi-cart-dash movement-icon text-danger"></i>
                                                        <span class="badge bg-danger">{{ 'بيع' if language == 'ar' else 'Sale' }}</span>
                                                        {% elif movement.transaction_type == 'purchase' %}
                                                        <i class="bi bi-cart-plus movement-icon text-success"></i>
                                                        <span class="badge bg-success">{{ 'شراء' if language == 'ar' else 'Purchase' }}</span>
                                                        {% elif movement.transaction_type == 'adjustment' %}
                                                        <i class="bi bi-tools movement-icon text-warning"></i>
                                                        <span class="badge bg-warning">{{ 'تعديل' if language == 'ar' else 'Adjustment' }}</span>
                                                        {% elif movement.transaction_type == 'return' %}
                                                        <i class="bi bi-arrow-return-left movement-icon text-info"></i>
                                                        <span class="badge bg-info">{{ 'إرجاع' if language == 'ar' else 'Return' }}</span>
                                                        {% elif movement.transaction_type == 'damage' %}
                                                        <i class="bi bi-exclamation-triangle movement-icon text-danger"></i>
                                                        <span class="badge bg-danger">{{ 'تلف' if language == 'ar' else 'Damage' }}</span>
                                                        {% elif movement.transaction_type == 'transfer' %}
                                                        <i class="bi bi-arrow-left-right movement-icon text-primary"></i>
                                                        <span class="badge bg-primary">{{ 'نقل' if language == 'ar' else 'Transfer' }}</span>
                                                        {% elif movement.transaction_type == 'initial' %}
                                                        <i class="bi bi-plus-circle movement-icon text-secondary"></i>
                                                        <span class="badge bg-secondary">{{ 'رصيد ابتدائي' if language == 'ar' else 'Initial' }}</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>{{ movement.product.name_ar if language == 'ar' else movement.product.name_en }}</strong>
                                                    {% if movement.product.sku %}
                                                    <br><small class="text-muted">{{ movement.product.sku }}</small>
                                                    {% endif %}
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="fw-bold">{{ movement.old_quantity }}</div>
                                                        <small class="text-muted">{{ 'قبل' if language == 'ar' else 'Before' }}</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-1">
                                                    <div class="text-center">
                                                        {% if movement.quantity_change > 0 %}
                                                        <i class="bi bi-arrow-right text-success fs-4"></i>
                                                        <div class="text-success fw-bold">+{{ movement.quantity_change }}</div>
                                                        {% else %}
                                                        <i class="bi bi-arrow-right text-danger fs-4"></i>
                                                        <div class="text-danger fw-bold">{{ movement.quantity_change }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="fw-bold">{{ movement.new_quantity }}</div>
                                                        <small class="text-muted">{{ 'بعد' if language == 'ar' else 'After' }}</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-end">
                                                        <div class="fw-bold">{{ "%.2f"|format(movement.total_cost or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</div>
                                                        <small class="text-muted">{{ movement.transaction_date.strftime('%Y-%m-%d %H:%M') }}</small>
                                                        {% if movement.reference_number %}
                                                        <br><small class="text-muted">{{ movement.reference_number }}</small>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% if movement.notes %}
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <small class="text-muted">
                                                        <i class="bi bi-chat-left-text"></i>
                                                        {{ movement.notes }}
                                                    </small>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                                <h4 class="mt-3 text-muted">{{ 'لا توجد حركات مخزون' if language == 'ar' else 'No stock movements found' }}</h4>
                                <p class="text-muted">{{ 'جرب تغيير الفلاتر للعثور على حركات المخزون' if language == 'ar' else 'Try adjusting the filters to find stock movements' }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
