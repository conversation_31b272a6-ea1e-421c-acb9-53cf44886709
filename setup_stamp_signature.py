#!/usr/bin/env python3
"""
Setup script to add stamp and signature settings to Qatar POS System
"""

from app import create_app
from models.setting import Setting
from extensions import db

def setup_stamp_signature_settings():
    """Add stamp and signature settings to the database"""
    app = create_app()
    
    with app.app_context():
        print("🏢 إضافة إعدادات الختم والتوقيع...")
        print("=" * 50)
        
        # New stamp and signature settings
        new_settings = [
            # Company Stamp Settings
            {
                'key': 'company_stamp',
                'value': '',
                'category': 'company',
                'description_ar': 'ختم الشركة',
                'description_en': 'Company Stamp'
            },
            {
                'key': 'stamp_width',
                'value': '120',
                'value_type': 'integer',
                'category': 'company',
                'description_ar': 'عرض الختم (بكسل)',
                'description_en': 'Stamp Width (pixels)'
            },
            {
                'key': 'stamp_height',
                'value': '120',
                'value_type': 'integer',
                'category': 'company',
                'description_ar': 'ارتفاع الختم (بكسل)',
                'description_en': 'Stamp Height (pixels)'
            },
            
            # Manager Signature Settings
            {
                'key': 'manager_signature',
                'value': '',
                'category': 'company',
                'description_ar': 'توقيع المسؤول',
                'description_en': 'Manager Signature'
            },
            {
                'key': 'signature_width',
                'value': '200',
                'value_type': 'integer',
                'category': 'company',
                'description_ar': 'عرض التوقيع (بكسل)',
                'description_en': 'Signature Width (pixels)'
            },
            {
                'key': 'signature_height',
                'value': '80',
                'value_type': 'integer',
                'category': 'company',
                'description_ar': 'ارتفاع التوقيع (بكسل)',
                'description_en': 'Signature Height (pixels)'
            },
            {
                'key': 'manager_name',
                'value': '',
                'category': 'company',
                'description_ar': 'اسم المسؤول',
                'description_en': 'Manager Name'
            },
            {
                'key': 'manager_title',
                'value': 'المدير العام',
                'category': 'company',
                'description_ar': 'منصب المسؤول',
                'description_en': 'Manager Title'
            }
        ]
        
        added_count = 0
        updated_count = 0
        
        for setting_data in new_settings:
            existing = Setting.query.filter_by(key=setting_data['key']).first()
            
            if not existing:
                # Create new setting
                setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    value_type=setting_data.get('value_type', 'string'),
                    category=setting_data['category'],
                    description_ar=setting_data.get('description_ar'),
                    description_en=setting_data.get('description_en'),
                    is_public=setting_data.get('is_public', False)
                )
                db.session.add(setting)
                print(f"✅ تم إضافة إعداد جديد: {setting_data['key']}")
                added_count += 1
            else:
                # Update existing setting if needed
                if not existing.description_ar and setting_data.get('description_ar'):
                    existing.description_ar = setting_data['description_ar']
                    existing.description_en = setting_data.get('description_en')
                    print(f"🔄 تم تحديث إعداد موجود: {setting_data['key']}")
                    updated_count += 1
                else:
                    print(f"⏭️ إعداد موجود بالفعل: {setting_data['key']}")
        
        try:
            db.session.commit()
            print(f"\n🎉 تم بنجاح!")
            print(f"   📝 إعدادات جديدة: {added_count}")
            print(f"   🔄 إعدادات محدثة: {updated_count}")
            
            # Display current company settings
            print(f"\n📋 إعدادات الشركة الحالية:")
            company_settings = Setting.get_category_settings('company')
            for setting in company_settings:
                if setting.key in ['company_stamp', 'manager_signature', 'stamp_width', 'stamp_height', 
                                 'signature_width', 'signature_height', 'manager_name', 'manager_title']:
                    print(f"   {setting.key}: {setting.get_value()}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ الإعدادات: {e}")
            return False
        
        return True

if __name__ == '__main__':
    success = setup_stamp_signature_settings()
    if success:
        print("\n✅ تم إعداد إعدادات الختم والتوقيع بنجاح!")
        print("يمكنك الآن الذهاب إلى إعدادات الشركة لرفع الختم والتوقيع.")
    else:
        print("\n❌ فشل في إعداد الإعدادات!")
