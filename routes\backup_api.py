"""
Backup API Routes for Qatar POS System
Handles backup creation, restoration, and management
"""

from flask import Blueprint, request, jsonify, send_file, current_app
from flask_login import login_required
from utils.decorators import permission_required
from utils.backup_manager import backup_manager
from utils.security_manager import security_manager
from models.setting import Setting
import os
import tempfile
import logging

backup_api_bp = Blueprint('backup_api', __name__, url_prefix='/api/backup')
logger = logging.getLogger(__name__)

@backup_api_bp.route('/create', methods=['POST'])
@login_required
@permission_required('admin')
def create_backup():
    """Create a new backup"""
    try:
        data = request.get_json() or {}
        backup_type = data.get('type', 'full')
        manual = data.get('manual', False)
        
        # Get backup settings
        compress = Setting.get_setting('backup_compression', 'true') == 'true'
        encrypt = Setting.get_setting('enable_backup_encryption', 'true') == 'true'
        
        # Log the backup creation
        security_manager.log_security_event('backup_created', {
            'type': backup_type,
            'manual': manual,
            'compress': compress,
            'encrypt': encrypt
        })
        
        # Create the backup
        success, result = backup_manager.create_backup(
            backup_type=backup_type,
            compress=compress,
            encrypt=encrypt
        )
        
        if success:
            # Send notification if enabled
            if Setting.get_setting('backup_notifications_enabled') == 'true':
                # This would integrate with the notification system
                pass
            
            return jsonify({
                'success': True,
                'message': 'Backup created successfully',
                'backup_path': result,
                'type': backup_type
            })
        else:
            return jsonify({
                'success': False,
                'error': result
            }), 500
            
    except Exception as e:
        logger.error(f"Backup creation failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/restore', methods=['POST'])
@login_required
@permission_required('admin')
def restore_backup():
    """Restore from a backup file"""
    try:
        if 'backup_file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No backup file provided'
            }), 400
        
        file = request.files['backup_file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Validate file
        allowed_extensions = ['zip', 'encrypted', 'db', 'sql']
        is_valid, validation_message = security_manager.validate_file_upload(
            file, allowed_extensions, max_size_mb=500
        )
        
        if not is_valid:
            return jsonify({
                'success': False,
                'error': validation_message
            }), 400
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Log the restore attempt
            security_manager.log_security_event('backup_restore_started', {
                'filename': file.filename,
                'file_size': os.path.getsize(temp_path)
            })
            
            # Perform restore
            success, result = backup_manager.restore_backup(temp_path)
            
            if success:
                security_manager.log_security_event('backup_restore_completed', {
                    'filename': file.filename
                })
                
                return jsonify({
                    'success': True,
                    'message': result
                })
            else:
                security_manager.log_security_event('backup_restore_failed', {
                    'filename': file.filename,
                    'error': result
                })
                
                return jsonify({
                    'success': False,
                    'error': result
                }), 500
                
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            
    except Exception as e:
        logger.error(f"Backup restore failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/history', methods=['GET'])
@login_required
@permission_required('admin')
def get_backup_history():
    """Get backup history"""
    try:
        backups = backup_manager.get_backup_history()
        
        return jsonify({
            'success': True,
            'backups': backups
        })
        
    except Exception as e:
        logger.error(f"Failed to get backup history: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/download/<backup_id>', methods=['GET'])
@login_required
@permission_required('admin')
def download_backup(backup_id):
    """Download a backup file"""
    try:
        backup_path = os.path.join(backup_manager.backup_dir, backup_id)
        
        if not os.path.exists(backup_path):
            return jsonify({
                'success': False,
                'error': 'Backup not found'
            }), 404
        
        # Log the download
        security_manager.log_security_event('backup_downloaded', {
            'backup_id': backup_id
        })
        
        return send_file(
            backup_path,
            as_attachment=True,
            download_name=backup_id
        )
        
    except Exception as e:
        logger.error(f"Backup download failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/delete/<backup_id>', methods=['DELETE'])
@login_required
@permission_required('admin')
def delete_backup(backup_id):
    """Delete a backup file"""
    try:
        success, result = backup_manager.delete_backup(backup_id)
        
        if success:
            # Log the deletion
            security_manager.log_security_event('backup_deleted', {
                'backup_id': backup_id
            })
            
            return jsonify({
                'success': True,
                'message': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result
            }), 500
            
    except Exception as e:
        logger.error(f"Backup deletion failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/cleanup', methods=['POST'])
@login_required
@permission_required('admin')
def cleanup_old_backups():
    """Clean up old backups based on retention policy"""
    try:
        retention_days = int(Setting.get_setting('backup_retention_days', '30'))
        
        success, result = backup_manager.cleanup_old_backups(retention_days)
        
        if success:
            # Log the cleanup
            security_manager.log_security_event('backup_cleanup', {
                'retention_days': retention_days,
                'result': result
            })
            
            return jsonify({
                'success': True,
                'message': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result
            }), 500
            
    except Exception as e:
        logger.error(f"Backup cleanup failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/schedule', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def manage_backup_schedule():
    """Manage automated backup schedule"""
    if request.method == 'GET':
        # Get current schedule settings
        settings = {
            'auto_backup_enabled': Setting.get_setting('auto_backup_enabled'),
            'backup_frequency': Setting.get_setting('backup_frequency'),
            'backup_time': Setting.get_setting('backup_time'),
            'backup_retention_days': Setting.get_setting('backup_retention_days'),
            'backup_compression': Setting.get_setting('backup_compression'),
            'backup_cloud_storage': Setting.get_setting('backup_cloud_storage')
        }
        
        return jsonify({
            'success': True,
            'settings': settings
        })
    
    elif request.method == 'POST':
        try:
            data = request.get_json() or {}
            
            # Update schedule settings
            settings_to_update = [
                'auto_backup_enabled',
                'backup_frequency', 
                'backup_time',
                'backup_retention_days',
                'backup_compression',
                'backup_cloud_storage'
            ]
            
            for setting_key in settings_to_update:
                if setting_key in data:
                    Setting.set_setting(setting_key, str(data[setting_key]))
            
            # Log the schedule update
            security_manager.log_security_event('backup_schedule_updated', data)
            
            return jsonify({
                'success': True,
                'message': 'Backup schedule updated successfully'
            })
            
        except Exception as e:
            logger.error(f"Failed to update backup schedule: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@backup_api_bp.route('/status', methods=['GET'])
@login_required
@permission_required('admin')
def get_backup_status():
    """Get backup system status"""
    try:
        # Get backup statistics
        backups = backup_manager.get_backup_history()
        
        total_backups = len(backups)
        total_size = sum(backup['size'] for backup in backups)
        
        # Get last backup info
        last_backup = backups[0] if backups else None
        
        # Check if automated backup is enabled
        auto_enabled = Setting.get_setting('auto_backup_enabled') == 'true'
        
        # Get backup directory size
        backup_dir_size = 0
        if os.path.exists(backup_manager.backup_dir):
            for dirpath, dirnames, filenames in os.walk(backup_manager.backup_dir):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    backup_dir_size += os.path.getsize(filepath)
        
        status = {
            'total_backups': total_backups,
            'total_size': total_size,
            'backup_dir_size': backup_dir_size,
            'last_backup': last_backup,
            'auto_backup_enabled': auto_enabled,
            'backup_frequency': Setting.get_setting('backup_frequency'),
            'next_backup_time': Setting.get_setting('backup_time'),
            'retention_days': int(Setting.get_setting('backup_retention_days', '30')),
            'encryption_enabled': Setting.get_setting('enable_backup_encryption') == 'true',
            'compression_enabled': Setting.get_setting('backup_compression') == 'true'
        }
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"Failed to get backup status: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@backup_api_bp.route('/test', methods=['POST'])
@login_required
@permission_required('admin')
def test_backup_system():
    """Test backup system functionality"""
    try:
        test_results = []
        
        # Test 1: Check backup directory
        if os.path.exists(backup_manager.backup_dir) and os.access(backup_manager.backup_dir, os.W_OK):
            test_results.append({'test': 'Backup Directory', 'status': 'passed', 'message': 'Directory accessible'})
        else:
            test_results.append({'test': 'Backup Directory', 'status': 'failed', 'message': 'Directory not accessible'})
        
        # Test 2: Check encryption key
        if backup_manager.encryption_key:
            test_results.append({'test': 'Encryption Key', 'status': 'passed', 'message': 'Key available'})
        else:
            test_results.append({'test': 'Encryption Key', 'status': 'failed', 'message': 'Key not found'})
        
        # Test 3: Check database connection
        try:
            from extensions import db
            db.engine.execute('SELECT 1')
            test_results.append({'test': 'Database Connection', 'status': 'passed', 'message': 'Connection successful'})
        except Exception as e:
            test_results.append({'test': 'Database Connection', 'status': 'failed', 'message': str(e)})
        
        # Test 4: Check disk space
        import shutil
        total, used, free = shutil.disk_usage(backup_manager.backup_dir)
        free_gb = free // (1024**3)
        if free_gb > 1:  # At least 1GB free
            test_results.append({'test': 'Disk Space', 'status': 'passed', 'message': f'{free_gb}GB available'})
        else:
            test_results.append({'test': 'Disk Space', 'status': 'warning', 'message': f'Only {free_gb}GB available'})
        
        # Calculate overall status
        failed_tests = [t for t in test_results if t['status'] == 'failed']
        overall_status = 'failed' if failed_tests else 'passed'
        
        return jsonify({
            'success': True,
            'overall_status': overall_status,
            'test_results': test_results
        })
        
    except Exception as e:
        logger.error(f"Backup system test failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
