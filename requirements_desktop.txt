# Qatar POS System - Desktop Application Requirements
# متطلبات تطبيق نقاط البيع القطري - إصدار سطح المكتب

# Core Flask and Web Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-Migrate==4.0.5
Flask-Babel==3.1.0
Flask-WTF==1.1.1
WTForms==3.0.1

# Database
SQLAlchemy==2.0.21

# HTTP Requests
requests==2.31.0

# PDF Generation
reportlab==4.0.4
weasyprint==59.0

# Barcode Generation
python-barcode==0.15.1
qrcode==7.4.2
Pillow==10.0.1

# Excel Support
openpyxl==3.1.2
xlsxwriter==3.1.9

# Date and Time
python-dateutil==2.8.2

# Utilities
python-dotenv==1.0.0
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
Werkzeug==2.3.7

# Desktop Application
PyInstaller==5.13.2
auto-py-to-exe==2.40.0

# Additional GUI Libraries (optional)
tkinter-tooltip==2.1.0

# Development Tools (optional)
pytest==7.4.2
pytest-flask==1.2.0
