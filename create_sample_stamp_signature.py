#!/usr/bin/env python3
"""
Create sample stamp and signature images for Qatar POS System
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_stamp():
    """Create a sample company stamp"""
    # Create a circular stamp image
    size = 120
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw outer circle
    draw.ellipse([5, 5, size-5, size-5], outline=(139, 21, 56), width=3)
    
    # Draw inner circle
    draw.ellipse([15, 15, size-15, size-15], outline=(139, 21, 56), width=2)
    
    # Try to use a font, fallback to default if not available
    try:
        font_large = ImageFont.truetype("arial.ttf", 14)
        font_small = ImageFont.truetype("arial.ttf", 10)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Add company name (Arabic)
    company_text = "شركة نقاط البيع القطرية"
    
    # Calculate text position for center alignment
    bbox = draw.textbbox((0, 0), company_text, font=font_small)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    
    # Draw company name
    draw.text((text_x, 25), company_text, fill=(139, 21, 56), font=font_small)
    
    # Add "QATAR" text
    qatar_text = "QATAR"
    bbox = draw.textbbox((0, 0), qatar_text, font=font_large)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    
    draw.text((text_x, 50), qatar_text, fill=(139, 21, 56), font=font_large)
    
    # Add year
    year_text = "2024"
    bbox = draw.textbbox((0, 0), year_text, font=font_small)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    
    draw.text((text_x, 80), year_text, fill=(139, 21, 56), font=font_small)
    
    # Save the stamp
    stamp_path = 'static/uploads/stamps/sample_company_stamp.png'
    os.makedirs(os.path.dirname(stamp_path), exist_ok=True)
    img.save(stamp_path, 'PNG')
    
    print(f"✅ تم إنشاء ختم الشركة النموذجي: {stamp_path}")
    return 'sample_company_stamp.png'

def create_sample_signature():
    """Create a sample manager signature"""
    # Create signature image
    width, height = 200, 80
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        font_small = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Draw signature-like text
    signature_text = "Ahmed Al-Mansouri"
    
    # Calculate text position
    bbox = draw.textbbox((0, 0), signature_text, font=font)
    text_width = bbox[2] - bbox[0]
    text_x = (width - text_width) // 2
    
    # Draw signature
    draw.text((text_x, 20), signature_text, fill=(0, 0, 139), font=font)
    
    # Add title
    title_text = "General Manager"
    bbox = draw.textbbox((0, 0), title_text, font=font_small)
    text_width = bbox[2] - bbox[0]
    text_x = (width - text_width) // 2
    
    draw.text((text_x, 50), title_text, fill=(100, 100, 100), font=font_small)
    
    # Add a simple underline
    draw.line([(20, 45), (width-20, 45)], fill=(0, 0, 139), width=1)
    
    # Save the signature
    signature_path = 'static/uploads/signatures/sample_manager_signature.png'
    os.makedirs(os.path.dirname(signature_path), exist_ok=True)
    img.save(signature_path, 'PNG')
    
    print(f"✅ تم إنشاء توقيع المسؤول النموذجي: {signature_path}")
    return 'sample_manager_signature.png'

def update_settings_with_samples():
    """Update settings with sample files"""
    from app import create_app
    from models.setting import Setting
    
    app = create_app()
    with app.app_context():
        # Create sample files
        stamp_filename = create_sample_stamp()
        signature_filename = create_sample_signature()
        
        # Update settings
        Setting.set_setting('company_stamp', stamp_filename, category='company')
        Setting.set_setting('manager_signature', signature_filename, category='company')
        Setting.set_setting('manager_name', 'أحمد المنصوري', category='company')
        Setting.set_setting('manager_title', 'المدير العام', category='company')
        
        print("✅ تم تحديث الإعدادات بالملفات النموذجية")

if __name__ == '__main__':
    print("🎨 إنشاء ختم وتوقيع نموذجيين...")
    print("=" * 50)
    
    try:
        update_settings_with_samples()
        print("\n🎉 تم إنشاء الختم والتوقيع النموذجيين بنجاح!")
        print("يمكنك الآن رؤيتهما في إعدادات الشركة.")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملفات النموذجية: {e}")
