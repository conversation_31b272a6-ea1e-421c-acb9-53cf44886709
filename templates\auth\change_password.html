{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        تغيير كلمة المرور
    {% else %}
        Change Password
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            تغيير كلمة المرور
                        {% else %}
                            Change Password
                        {% endif %}
                    </h3>
                </div>
                
                <form method="POST" class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <div class="form-group">
                        <label for="current_password">
                            {% if language == 'ar' %}كلمة المرور الحالية{% else %}Current Password{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>

                    <div class="form-group">
                        <label for="new_password">
                            {% if language == 'ar' %}كلمة المرور الجديدة{% else %}New Password{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        <small class="form-text text-muted">
                            {% if language == 'ar' %}يجب أن تكون 6 أحرف على الأقل{% else %}Must be at least 6 characters{% endif %}
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">
                            {% if language == 'ar' %}تأكيد كلمة المرور الجديدة{% else %}Confirm New Password{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if language == 'ar' %}تغيير كلمة المرور{% else %}Change Password{% endif %}
                        </button>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('{% if language == "ar" %}كلمات المرور غير متطابقة{% else %}Passwords do not match{% endif %}');
    } else {
        this.setCustomValidity('');
    }
});

// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength');
    
    if (password.length < 6) {
        this.setCustomValidity('{% if language == "ar" %}كلمة المرور قصيرة جداً{% else %}Password too short{% endif %}');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
