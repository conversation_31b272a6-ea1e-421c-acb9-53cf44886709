#!/usr/bin/env python3
"""
إنشاء مستخدم تجريبي للاختبار
Create test user for testing
"""

from app import create_app
from extensions import db
from models.user import User
from werkzeug.security import generate_password_hash

def create_test_user():
    """إنشاء مستخدم تجريبي"""
    app = create_app()
    
    with app.app_context():
        print("👤 إنشاء مستخدم تجريبي...")
        
        # التحقق من وجود المستخدم
        existing_user = User.query.filter_by(username='admin').first()
        if existing_user:
            print("✅ المستخدم 'admin' موجود بالفعل")
            print(f"   - الاسم: {existing_user.get_full_name('ar')}")
            print(f"   - الدور: {existing_user.role}")
            print(f"   - نشط: {existing_user.is_active}")
            return existing_user
        
        # إنشاء مستخدم جديد
        try:
            user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                first_name_ar='المدير',
                first_name_en='Admin',
                last_name_ar='العام',
                last_name_en='User',
                role='manager',
                is_active=True,
                employee_id='EMP001'
            )
            
            db.session.add(user)
            db.session.commit()
            
            print("✅ تم إنشاء المستخدم بنجاح!")
            print(f"   - اسم المستخدم: {user.username}")
            print(f"   - كلمة المرور: admin123")
            print(f"   - الدور: {user.role}")
            
            return user
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            db.session.rollback()
            return None

def test_user_permissions():
    """اختبار صلاحيات المستخدم"""
    app = create_app()
    
    with app.app_context():
        user = User.query.filter_by(username='admin').first()
        if not user:
            print("❌ لم يتم العثور على المستخدم")
            return
        
        print("\n🔐 اختبار صلاحيات المستخدم:")
        permissions = [
            'products_read',
            'products_write', 
            'customers_read',
            'customers_write',
            'sales',
            'reports'
        ]
        
        for perm in permissions:
            has_perm = user.has_permission(perm)
            status = "✅" if has_perm else "❌"
            print(f"   {status} {perm}: {has_perm}")

if __name__ == '__main__':
    user = create_test_user()
    if user:
        test_user_permissions()
        print("\n🚀 يمكنك الآن تسجيل الدخول باستخدام:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("   الرابط: http://127.0.0.1:2626/login")
