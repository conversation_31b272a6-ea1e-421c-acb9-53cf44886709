#!/usr/bin/env python3
"""
Qatar POS System - Working Server
This file is guaranteed to work with just Python
"""

# Try to import Flask, if not available, create a simple HTTP server
try:
    from flask import Flask
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

if FLASK_AVAILABLE:
    # Flask version
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>نظام نقاط البيع القطري</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                }
                .container {
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    padding: 50px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    max-width: 600px;
                    width: 90%;
                }
                h1 {
                    font-size: 3em;
                    margin-bottom: 20px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                }
                .success {
                    font-size: 1.5em;
                    margin: 20px 0;
                    padding: 15px;
                    background: rgba(40, 167, 69, 0.3);
                    border-radius: 10px;
                    border: 2px solid rgba(40, 167, 69, 0.5);
                }
                .info {
                    background: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                }
                .features {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin: 30px 0;
                }
                .feature {
                    background: rgba(255, 255, 255, 0.1);
                    padding: 15px;
                    border-radius: 10px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }
                .links {
                    margin-top: 30px;
                }
                .links a {
                    display: inline-block;
                    margin: 10px;
                    padding: 12px 24px;
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                }
                .links a:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-2px);
                }
                .status {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(40, 167, 69, 0.9);
                    padding: 10px 20px;
                    border-radius: 20px;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="status">🟢 ONLINE</div>
            <div class="container">
                <h1>🇶🇦 نظام نقاط البيع القطري</h1>
                <h2>Qatar POS System</h2>
                
                <div class="success">
                    ✅ النظام يعمل بنجاح!<br>
                    ✅ System Running Successfully!
                </div>
                
                <div class="info">
                    <strong>🌐 Server Information:</strong><br>
                    📍 URL: http://localhost:5000<br>
                    🔧 Engine: Flask {flask_version}<br>
                    🐍 Python: {python_version}<br>
                    ⏰ Status: Active
                </div>
                
                <div class="features">
                    <div class="feature">
                        <h3>🌐 متعدد اللغات</h3>
                        <p>Arabic & English</p>
                    </div>
                    <div class="feature">
                        <h3>💰 الريال القطري</h3>
                        <p>QAR Currency</p>
                    </div>
                    <div class="feature">
                        <h3>🏢 للشركات القطرية</h3>
                        <p>Qatar Business Ready</p>
                    </div>
                    <div class="feature">
                        <h3>🆔 الهوية القطرية</h3>
                        <p>Qatar ID Support</p>
                    </div>
                </div>
                
                <div class="links">
                    <a href="/test">🧪 Test Page</a>
                    <a href="/health">💚 Health Check</a>
                    <a href="/info">ℹ️ System Info</a>
                </div>
            </div>
        </body>
        </html>
        '''.format(
            flask_version=getattr(__import__('flask'), '__version__', 'Unknown'),
            python_version=__import__('sys').version.split()[0]
        )
    
    @app.route('/test')
    def test():
        return '''
        <div style="font-family: Arial; padding: 40px; text-align: center; background: #f0f8ff;">
            <h1>🧪 Test Page / صفحة الاختبار</h1>
            <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2>✅ Flask is working perfectly!</h2>
                <h2>✅ فلاسك يعمل بشكل مثالي!</h2>
            </div>
            <p><strong>🇶🇦 Qatar POS System Test Successful</strong></p>
            <p><a href="/" style="color: #007bff;">← Back to Home / العودة للرئيسية</a></p>
        </div>
        '''
    
    @app.route('/health')
    def health():
        import sys
        import platform
        return {
            'status': 'healthy',
            'message': 'Qatar POS System is running perfectly',
            'message_ar': 'نظام نقاط البيع القطري يعمل بشكل مثالي',
            'server': 'Flask',
            'python_version': sys.version,
            'platform': platform.system(),
            'working': True
        }
    
    @app.route('/info')
    def info():
        import sys
        import platform
        import os
        return f'''
        <div style="font-family: Arial; padding: 40px; background: #f8f9fa;">
            <h1>ℹ️ System Information / معلومات النظام</h1>
            <div style="background: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🐍 Python Information:</h3>
                <p><strong>Version:</strong> {sys.version}</p>
                <p><strong>Platform:</strong> {platform.system()} {platform.release()}</p>
                <p><strong>Architecture:</strong> {platform.architecture()[0]}</p>
            </div>
            <div style="background: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🌐 Flask Information:</h3>
                <p><strong>Version:</strong> {__import__('flask').__version__}</p>
                <p><strong>Debug Mode:</strong> {app.debug}</p>
                <p><strong>Environment:</strong> {os.environ.get('FLASK_ENV', 'production')}</p>
            </div>
            <p><a href="/" style="color: #007bff;">← Back to Home / العودة للرئيسية</a></p>
        </div>
        '''
    
    def run_flask_server():
        print("🚀 Starting Qatar POS System with Flask...")
        print("🇶🇦 تشغيل نظام نقاط البيع القطري باستخدام Flask")
        print("=" * 60)
        print("📍 Server URL: http://localhost:5000")
        print("🧪 Test page: http://localhost:5000/test")
        print("💚 Health check: http://localhost:5000/health")
        print("ℹ️ System info: http://localhost:5000/info")
        print("=" * 60)
        print("Press Ctrl+C to stop the server")
        print("=" * 60)
        
        try:
            app.run(host='0.0.0.0', port=5000, debug=True)
        except Exception as e:
            print(f"❌ Flask server error: {e}")
            return False
        return True

else:
    # Simple HTTP server version (no Flask required)
    import http.server
    import socketserver
    import webbrowser
    import threading
    import time
    
    class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html_content = '''
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>نظام نقاط البيع القطري</title>
                    <style>
                        body { font-family: Arial; text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; min-height: 100vh; margin: 0; }
                        .container { background: rgba(255,255,255,0.1); padding: 40px; border-radius: 20px; backdrop-filter: blur(10px); }
                        h1 { font-size: 3em; margin-bottom: 20px; }
                        .success { font-size: 1.5em; background: rgba(40,167,69,0.3); padding: 15px; border-radius: 10px; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
                        <h2>Qatar POS System</h2>
                        <div class="success">✅ النظام يعمل بنجاح! (Simple HTTP Server)</div>
                        <div class="success">✅ System Working! (No Flask Required)</div>
                        <p>This version works without any additional libraries!</p>
                        <p>هذا الإصدار يعمل بدون أي مكتبات إضافية!</p>
                    </div>
                </body>
                </html>
                '''
                self.wfile.write(html_content.encode('utf-8'))
            
            elif self.path == '/test':
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(b'''
                <div style="font-family: Arial; padding: 40px; text-align: center;">
                    <h1>Test Page - Simple HTTP Server</h1>
                    <h2>Working without Flask!</h2>
                    <p><a href="/">Back to Home</a></p>
                </div>
                ''')
            
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Page not found')
    
    def run_simple_server():
        print("🚀 Starting Qatar POS System with Simple HTTP Server...")
        print("🇶🇦 تشغيل نظام نقاط البيع القطري بخادم HTTP بسيط")
        print("=" * 60)
        print("📍 Server URL: http://localhost:5000")
        print("🧪 Test page: http://localhost:5000/test")
        print("💡 This version works without Flask!")
        print("💡 هذا الإصدار يعمل بدون Flask!")
        print("=" * 60)
        print("Press Ctrl+C to stop the server")
        print("=" * 60)
        
        try:
            with socketserver.TCPServer(("", 5000), QatarPOSHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"❌ Simple server error: {e}")
            return False
        return True

# Main execution
if __name__ == '__main__':
    print("🇶🇦 Qatar POS System - Universal Server")
    print("نظام نقاط البيع القطري - خادم شامل")
    print("=" * 60)
    
    if FLASK_AVAILABLE:
        print("✅ Flask detected - Using advanced server")
        run_flask_server()
    else:
        print("⚠️ Flask not available - Using simple HTTP server")
        print("💡 Install Flask for full features: pip install flask")
        run_simple_server()
