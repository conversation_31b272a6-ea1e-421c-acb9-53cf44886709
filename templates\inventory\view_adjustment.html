{% extends "base.html" %}

{% block title %}{{ 'عرض تعديل المخزون' if language == 'ar' else 'View Stock Adjustment' }} - {{ adjustment.adjustment_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-gear"></i>
                {{ 'تعديل المخزون' if language == 'ar' else 'Stock Adjustment' }} - {{ adjustment.adjustment_number }}
            </h1>
            <div>
                <a href="{{ url_for('inventory.adjustments') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة' if language == 'ar' else 'Back' }}
                </a>
                {% if adjustment.status == 'approved' %}
                <button onclick="window.print()" class="btn btn-outline-primary">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة' if language == 'ar' else 'Print' }}
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Adjustment Details -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ 'تفاصيل التعديل' if language == 'ar' else 'Adjustment Details' }}</h5>
                {% if adjustment.status == 'draft' %}
                    <span class="badge bg-warning">{{ 'مسودة' if language == 'ar' else 'Draft' }}</span>
                {% elif adjustment.status == 'approved' %}
                    <span class="badge bg-success">{{ 'معتمد' if language == 'ar' else 'Approved' }}</span>
                {% elif adjustment.status == 'cancelled' %}
                    <span class="badge bg-danger">{{ 'ملغي' if language == 'ar' else 'Cancelled' }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{{ 'رقم التعديل:' if language == 'ar' else 'Adjustment Number:' }}</strong></td>
                                <td>{{ adjustment.adjustment_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>{{ 'التاريخ:' if language == 'ar' else 'Date:' }}</strong></td>
                                <td>{{ adjustment.adjustment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>{{ 'السبب:' if language == 'ar' else 'Reason:' }}</strong></td>
                                <td>
                                    {% if adjustment.reason == 'physical_count' %}
                                        {{ 'جرد فعلي' if language == 'ar' else 'Physical Count' }}
                                    {% elif adjustment.reason == 'damage' %}
                                        {{ 'تلف' if language == 'ar' else 'Damage' }}
                                    {% elif adjustment.reason == 'theft' %}
                                        {{ 'سرقة' if language == 'ar' else 'Theft' }}
                                    {% elif adjustment.reason == 'expired' %}
                                        {{ 'منتهي الصلاحية' if language == 'ar' else 'Expired' }}
                                    {% elif adjustment.reason == 'system_error' %}
                                        {{ 'خطأ نظام' if language == 'ar' else 'System Error' }}
                                    {% else %}
                                        {{ 'أخرى' if language == 'ar' else 'Other' }}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{{ 'المستخدم:' if language == 'ar' else 'User:' }}</strong></td>
                                <td>{{ adjustment.user.get_full_name(language) }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if adjustment.status == 'approved' %}
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{{ 'معتمد بواسطة:' if language == 'ar' else 'Approved By:' }}</strong></td>
                                <td>{{ adjustment.approver.get_full_name(language) if adjustment.approver else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>{{ 'تاريخ الاعتماد:' if language == 'ar' else 'Approved Date:' }}</strong></td>
                                <td>{{ adjustment.approved_at.strftime('%Y-%m-%d %H:%M') if adjustment.approved_at else '-' }}</td>
                            </tr>
                        </table>
                        {% endif %}
                    </div>
                </div>
                
                {% if adjustment.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>{{ 'الملاحظات:' if language == 'ar' else 'Notes:' }}</strong>
                        <p class="mt-2">{{ adjustment.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Adjustment Items -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">{{ 'عناصر التعديل' if language == 'ar' else 'Adjustment Items' }}</h5>
            </div>
            <div class="card-body">
                {% if adjustment.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                <th>{{ 'الرمز' if language == 'ar' else 'SKU' }}</th>
                                <th>{{ 'المخزون السابق' if language == 'ar' else 'Old Stock' }}</th>
                                <th>{{ 'المخزون الجديد' if language == 'ar' else 'New Stock' }}</th>
                                <th>{{ 'التغيير' if language == 'ar' else 'Change' }}</th>
                                <th>{{ 'سعر الوحدة' if language == 'ar' else 'Unit Cost' }}</th>
                                <th>{{ 'قيمة التغيير' if language == 'ar' else 'Value Change' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in adjustment.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if item.product.image_filename %}
                                        <img src="{{ url_for('static', filename='uploads/products/' + item.product.image_filename) }}" 
                                             alt="{{ item.product.get_name(language) }}" 
                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="bi bi-box text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <strong>{{ item.product.get_name(language) }}</strong>
                                            {% if item.notes %}
                                            <br><small class="text-muted">{{ item.notes }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td><code>{{ item.product.sku }}</code></td>
                                <td>{{ item.old_quantity }}</td>
                                <td>{{ item.new_quantity }}</td>
                                <td>
                                    <span class="{% if item.quantity_change > 0 %}text-success{% elif item.quantity_change < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {% if item.quantity_change > 0 %}+{% endif %}{{ item.quantity_change }}
                                    </span>
                                </td>
                                <td>{{ "%.3f"|format(item.unit_cost) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                <td>
                                    <span class="{% if item.value_change > 0 %}text-success{% elif item.value_change < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {% if item.value_change > 0 %}+{% endif %}{{ "%.2f"|format(item.value_change) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="4">{{ 'الإجمالي:' if language == 'ar' else 'Total:' }}</th>
                                <th>
                                    {% set total_quantity_change = adjustment.items|sum(attribute='quantity_change') %}
                                    <span class="{% if total_quantity_change > 0 %}text-success{% elif total_quantity_change < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {% if total_quantity_change > 0 %}+{% endif %}{{ total_quantity_change }}
                                    </span>
                                </th>
                                <th>-</th>
                                <th>
                                    {% set total_value_change = adjustment.calculate_total_value() %}
                                    <span class="{% if total_value_change > 0 %}text-success{% elif total_value_change < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        {% if total_value_change > 0 %}+{% endif %}{{ "%.2f"|format(total_value_change) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                                    </span>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-box display-4 text-muted"></i>
                    <p class="mt-2 text-muted">{{ 'لا توجد عناصر في هذا التعديل' if language == 'ar' else 'No items in this adjustment' }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Actions Sidebar -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</h5>
            </div>
            <div class="card-body">
                {% if adjustment.status == 'draft' and current_user.has_permission('inventory') %}
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="approveAdjustment()">
                        <i class="bi bi-check-circle"></i>
                        {{ 'اعتماد التعديل' if language == 'ar' else 'Approve Adjustment' }}
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="cancelAdjustment()">
                        <i class="bi bi-x-circle"></i>
                        {{ 'إلغاء التعديل' if language == 'ar' else 'Cancel Adjustment' }}
                    </button>
                </div>
                {% elif adjustment.status == 'approved' %}
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i>
                    {{ 'تم اعتماد هذا التعديل' if language == 'ar' else 'This adjustment has been approved' }}
                </div>
                {% elif adjustment.status == 'cancelled' %}
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i>
                    {{ 'تم إلغاء هذا التعديل' if language == 'ar' else 'This adjustment has been cancelled' }}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Summary Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">{{ 'ملخص' if language == 'ar' else 'Summary' }}</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ 'عدد المنتجات:' if language == 'ar' else 'Products Count:' }}</span>
                    <span>{{ adjustment.items|length }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ 'إجمالي التغيير:' if language == 'ar' else 'Total Change:' }}</span>
                    <span>{{ adjustment.items|sum(attribute='quantity_change') }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>{{ 'قيمة التغيير:' if language == 'ar' else 'Value Change:' }}</span>
                    <span>{{ "%.2f"|format(adjustment.calculate_total_value()) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</span>
                </div>
            </div>
        </div>

        <!-- History Card -->
        {% if adjustment.status != 'draft' %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">{{ 'التاريخ' if language == 'ar' else 'History' }}</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ 'تم الإنشاء' if language == 'ar' else 'Created' }}</h6>
                            <p class="timeline-text">{{ adjustment.adjustment_date.strftime('%Y-%m-%d %H:%M') }}</p>
                            <small class="text-muted">{{ adjustment.user.get_full_name(language) }}</small>
                        </div>
                    </div>
                    
                    {% if adjustment.status == 'approved' and adjustment.approved_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ 'تم الاعتماد' if language == 'ar' else 'Approved' }}</h6>
                            <p class="timeline-text">{{ adjustment.approved_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            <small class="text-muted">{{ adjustment.approver.get_full_name(language) if adjustment.approver else '-' }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function approveAdjustment() {
    if (confirm('{{ "هل أنت متأكد من اعتماد هذا التعديل؟ لا يمكن التراجع عن هذا الإجراء." if language == "ar" else "Are you sure you want to approve this adjustment? This action cannot be undone." }}')) {
        fetch(`/inventory/adjustments/{{ adjustment.id }}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || '{{ "حدث خطأ أثناء اعتماد التعديل" if language == "ar" else "Error approving adjustment" }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ "حدث خطأ أثناء اعتماد التعديل" if language == "ar" else "Error approving adjustment" }}');
        });
    }
}

function cancelAdjustment() {
    if (confirm('{{ "هل أنت متأكد من إلغاء هذا التعديل؟" if language == "ar" else "Are you sure you want to cancel this adjustment?" }}')) {
        fetch(`/inventory/adjustments/{{ adjustment.id }}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || '{{ "حدث خطأ أثناء إلغاء التعديل" if language == "ar" else "Error cancelling adjustment" }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ "حدث خطأ أثناء إلغاء التعديل" if language == "ar" else "Error cancelling adjustment" }}');
        });
    }
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.8rem;
    margin-bottom: 2px;
}
</style>
{% endblock %}
