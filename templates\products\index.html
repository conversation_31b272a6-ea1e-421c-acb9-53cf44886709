{% extends "base.html" %}

{% block title %}
{{ 'إدارة المنتجات - نظام نقاط البيع القطري' if language == 'ar' else 'Product Management - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-box"></i>
                {{ 'إدارة المنتجات' if language == 'ar' else 'Product Management' }}
            </h1>
            {% if current_user.has_permission('products_write') %}
            <div>
                <a href="{{ url_for('products.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'إضافة منتج جديد' if language == 'ar' else 'Add New Product' }}
                </a>
                <a href="{{ url_for('products.create_category') }}" class="btn btn-outline-primary">
                    <i class="bi bi-tags"></i>
                    {{ 'إضافة فئة' if language == 'ar' else 'Add Category' }}
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{{ 'البحث' if language == 'ar' else 'Search' }}</label>
                <div class="search-box">
                    <input type="text" class="form-control" name="search" value="{{ search }}" 
                           placeholder="{{ 'اسم المنتج، الرمز، أو الباركود' if language == 'ar' else 'Product name, SKU, or barcode' }}">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'الفئة' if language == 'ar' else 'Category' }}</label>
                <select class="form-select" name="category">
                    <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {{ 'selected' if category.id == category_id }}>
                        {{ category.get_name(language) }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'الحالة' if language == 'ar' else 'Status' }}</label>
                <select class="form-select" name="status">
                    <option value="">{{ 'جميع الحالات' if language == 'ar' else 'All Status' }}</option>
                    <option value="active" {{ 'selected' if status_filter == 'active' }}>
                        {{ 'نشط' if language == 'ar' else 'Active' }}
                    </option>
                    <option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>
                        {{ 'غير نشط' if language == 'ar' else 'Inactive' }}
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'المخزون' if language == 'ar' else 'Stock' }}</label>
                <select class="form-select" name="stock">
                    <option value="">{{ 'جميع المستويات' if language == 'ar' else 'All Levels' }}</option>
                    <option value="low" {{ 'selected' if stock_filter == 'low' }}>
                        {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                    </option>
                    <option value="out" {{ 'selected' if stock_filter == 'out' }}>
                        {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        {{ 'بحث' if language == 'ar' else 'Search' }}
                    </button>
                    <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            {{ 'قائمة المنتجات' if language == 'ar' else 'Products List' }}
            <span class="badge bg-primary">{{ products.total }}</span>
        </h6>
    </div>
    <div class="card-body p-0">
        {% if products.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{{ 'الصورة' if language == 'ar' else 'Image' }}</th>
                        <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                        <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                        <th>{{ 'السعر' if language == 'ar' else 'Price' }}</th>
                        <th>{{ 'المخزون' if language == 'ar' else 'Stock' }}</th>
                        <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                        <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products.items %}
                    <tr>
                        <td>
                            {% if product.image_filename %}
                            <img src="{{ url_for('static', filename='uploads/products/' + product.image_filename) }}" 
                                 alt="{{ product.get_name(language) }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="bi bi-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ product.get_name(language) }}</strong>
                                <br>
                                <small class="text-muted">
                                    {{ 'الرمز:' if language == 'ar' else 'SKU:' }} {{ product.sku }}
                                    {% if product.barcode %}
                                    | {{ 'الباركود:' if language == 'ar' else 'Barcode:' }} {{ product.barcode }}
                                    {% endif %}
                                </small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ product.category.get_name(language) if product.category else '-' }}</span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ '{:,.2f} ر.ق'.format(product.get_final_price()) if language == 'ar' else 'QAR {:,.2f}'.format(product.get_final_price()) }}</strong>
                                {% if product.discount_percentage > 0 %}
                                <br>
                                <small class="text-muted text-decoration-line-through">
                                    {{ '{:,.2f} ر.ق'.format(product.selling_price) if language == 'ar' else 'QAR {:,.2f}'.format(product.selling_price) }}
                                </small>
                                <span class="badge bg-warning">{{ product.discount_percentage }}% {{ 'خصم' if language == 'ar' else 'OFF' }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if product.track_inventory %}
                                {% if product.is_out_of_stock() %}
                                <span class="badge bg-danger">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</span>
                                {% elif product.is_low_stock() %}
                                <span class="badge bg-warning">{{ product.current_stock }} {{ 'منخفض' if language == 'ar' else 'Low' }}</span>
                                {% else %}
                                <span class="badge bg-success">{{ product.current_stock }}</span>
                                {% endif %}
                            {% else %}
                            <span class="badge bg-secondary">{{ 'غير محدود' if language == 'ar' else 'Unlimited' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_active %}
                            <span class="badge bg-success">{{ 'نشط' if language == 'ar' else 'Active' }}</span>
                            {% else %}
                            <span class="badge bg-danger">{{ 'غير نشط' if language == 'ar' else 'Inactive' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('products.view', product_id=product.id) }}" 
                                   class="btn btn-outline-primary" title="{{ 'عرض' if language == 'ar' else 'View' }}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.has_permission('products_write') %}
                                <a href="{{ url_for('products.edit', product_id=product.id) }}" 
                                   class="btn btn-outline-warning" title="{{ 'تعديل' if language == 'ar' else 'Edit' }}">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Products pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('products.index', page=products.prev_num, search=search, category=category_id, status=status_filter, stock=stock_filter) }}">
                            {{ 'السابق' if language == 'ar' else 'Previous' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('products.index', page=page_num, search=search, category=category_id, status=status_filter, stock=stock_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('products.index', page=products.next_num, search=search, category=category_id, status=status_filter, stock=stock_filter) }}">
                            {{ 'التالي' if language == 'ar' else 'Next' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-box display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد منتجات' if language == 'ar' else 'No products found' }}</h5>
            <p class="text-muted">
                {% if search or category_id or status_filter or stock_filter %}
                {{ 'جرب تغيير معايير البحث' if language == 'ar' else 'Try changing your search criteria' }}
                {% else %}
                {{ 'ابدأ بإضافة منتج جديد' if language == 'ar' else 'Start by adding a new product' }}
                {% endif %}
            </p>
            {% if current_user.has_permission('products_write') and not (search or category_id or status_filter or stock_filter) %}
            <a href="{{ url_for('products.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة منتج جديد' if language == 'ar' else 'Add New Product' }}
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on filter change
document.querySelectorAll('select[name="category"], select[name="status"], select[name="stock"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Search input with debounce
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
{% endblock %}
