"""
Customer model for Qatar POS System
Supports bilingual customer information and purchase history
"""

from datetime import datetime
from extensions import db

class Customer(db.Model):
    """Customer model with bilingual support"""
    
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Customer identification
    customer_code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    
    # Personal information
    first_name_ar = db.Column(db.String(100))
    first_name_en = db.Column(db.String(100))
    last_name_ar = db.Column(db.String(100))
    last_name_en = db.Column(db.String(100))
    
    # Contact information
    phone = db.Column(db.String(20), index=True)
    email = db.Column(db.String(120), index=True)
    
    # Address information
    address_ar = db.Column(db.Text)
    address_en = db.Column(db.Text)
    city_ar = db.Column(db.String(100))
    city_en = db.Column(db.String(100))
    postal_code = db.Column(db.String(10))
    
    # Qatar-specific information
    qatar_id = db.Column(db.String(11), unique=True, index=True)  # Qatar ID
    company_name_ar = db.Column(db.String(200))
    company_name_en = db.Column(db.String(200))
    commercial_registration = db.Column(db.String(20))
    tax_number = db.Column(db.String(20))
    
    # Customer type and status
    customer_type = db.Column(db.Enum('individual', 'company', name='customer_types'), 
                             default='individual', nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Credit information
    credit_limit = db.Column(db.Numeric(10, 2), default=0)
    current_balance = db.Column(db.Numeric(10, 2), default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    last_purchase_date = db.Column(db.DateTime)
    
    # Relationships
    sales = db.relationship('Sale', backref='customer', lazy='dynamic')
    
    def get_full_name(self, language='ar'):
        """Get customer full name in specified language"""
        if language == 'ar':
            first = self.first_name_ar or ''
            last = self.last_name_ar or ''
        else:
            first = self.first_name_en or ''
            last = self.last_name_en or ''
        
        return f"{first} {last}".strip()
    
    def get_display_name(self, language='ar'):
        """Get display name (company name for companies, full name for individuals)"""
        if self.customer_type == 'company':
            return self.company_name_ar if language == 'ar' else self.company_name_en
        return self.get_full_name(language)
    
    def get_address(self, language='ar'):
        """Get address in specified language"""
        return self.address_ar if language == 'ar' else self.address_en
    
    def get_city(self, language='ar'):
        """Get city in specified language"""
        return self.city_ar if language == 'ar' else self.city_en
    
    def get_total_purchases(self):
        """Get total purchase amount"""
        from models.sale import Sale
        total = db.session.query(db.func.sum(Sale.total_amount)).filter_by(
            customer_id=self.id
        ).scalar()
        return total or 0
    
    def get_purchase_count(self):
        """Get total number of purchases"""
        return self.sales.count()
    
    def get_average_purchase(self):
        """Get average purchase amount"""
        total = self.get_total_purchases()
        count = self.get_purchase_count()
        return float(total) / float(count) if count > 0 else 0
    
    def update_last_purchase(self):
        """Update last purchase date"""
        self.last_purchase_date = datetime.utcnow()
    
    def can_purchase(self, amount):
        """Check if customer can make purchase within credit limit"""
        if self.credit_limit <= 0:
            return True  # No credit limit
        return (self.current_balance + amount) <= self.credit_limit
    
    def add_to_balance(self, amount):
        """Add amount to customer balance"""
        self.current_balance += amount
    
    def to_dict(self, language='ar'):
        """Convert customer to dictionary"""
        return {
            'id': self.id,
            'customer_code': self.customer_code,
            'display_name': self.get_display_name(language),
            'full_name': self.get_full_name(language),
            'phone': self.phone,
            'email': self.email,
            'address': self.get_address(language),
            'city': self.get_city(language),
            'customer_type': self.customer_type,
            'qatar_id': self.qatar_id,
            'commercial_registration': self.commercial_registration,
            'tax_number': self.tax_number,
            'credit_limit': float(self.credit_limit),
            'current_balance': float(self.current_balance),
            'total_purchases': float(self.get_total_purchases()),
            'purchase_count': self.get_purchase_count(),
            'average_purchase': float(self.get_average_purchase()),
            'last_purchase_date': self.last_purchase_date.isoformat() if self.last_purchase_date else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def generate_customer_code():
        """Generate unique customer code"""
        import random
        import string
        
        while True:
            code = 'C' + ''.join(random.choices(string.digits, k=6))
            if not Customer.query.filter_by(customer_code=code).first():
                return code
    
    def __repr__(self):
        return f'<Customer {self.customer_code}: {self.get_display_name("en")}>'
