{% extends "base.html" %}

{% block title %}
{{ 'تقرير المبيعات حسب طريقة الدفع - نظام نقاط البيع القطري' if language == 'ar' else 'Sales by Payment Method Report - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-credit-card"></i>
                {{ 'تقرير المبيعات حسب طريقة الدفع' if language == 'ar' else 'Sales by Payment Method Report' }}
            </h1>
            <div>
                <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                    <i class="bi bi-file-pdf"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="bi bi-file-excel"></i>
                    Excel
                </button>
                <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للتقارير' if language == 'ar' else 'Back to Reports' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'تصفية التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                        <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                        <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                                {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(total_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'إجمالي المعاملات' if language == 'ar' else 'Total Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,}'.format(total_transactions) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    {{ 'توزيع المبيعات حسب طريقة الدفع' if language == 'ar' else 'Sales Distribution by Payment Method' }}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="paymentPieChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {{ 'مقارنة طرق الدفع' if language == 'ar' else 'Payment Methods Comparison' }}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="paymentBarChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'تفاصيل المبيعات حسب طريقة الدفع' if language == 'ar' else 'Sales Details by Payment Method' }}
                </h6>
            </div>
            <div class="card-body">
                {% if payment_sales %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</th>
                                <th>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</th>
                                <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</th>
                                <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Avg Transaction' }}</th>
                                <th>{{ 'النسبة المئوية' if language == 'ar' else 'Percentage' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payment_sales %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ payment.method_name }}</span>
                                </td>
                                <td>{{ '{:,.2f} ر.ق'.format(payment.total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(payment.total_sales) }}</td>
                                <td>{{ '{:,}'.format(payment.transaction_count) }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(payment.avg_transaction) if language == 'ar' else 'QAR {:,.2f}'.format(payment.avg_transaction) }}</td>
                                <td>
                                    {% set percentage = (payment.total_sales / total_sales * 100) if total_sales > 0 else 0 %}
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" role="progressbar" style="width: {{ percentage }}%">
                                            {{ '{:.1f}%'.format(percentage) }}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-credit-card-2-front display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'لا توجد بيانات للفترة المحددة' if language == 'ar' else 'No data found for the selected period' }}</h5>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Payment Methods Pie Chart
const pieCtx = document.getElementById('paymentPieChart').getContext('2d');
const paymentPieChart = new Chart(pieCtx, {
    type: 'pie',
    data: {
        labels: [
            {% for payment in payment_sales %}
            '{{ payment.method_name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for payment in payment_sales %}
                {{ payment.total_sales }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{{ 'توزيع المبيعات' if language == 'ar' else 'Sales Distribution' }}'
            },
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Payment Methods Bar Chart
const barCtx = document.getElementById('paymentBarChart').getContext('2d');
const paymentBarChart = new Chart(barCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for payment in payment_sales %}
            '{{ payment.method_name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ 'المبيعات' if language == 'ar' else 'Sales' }}',
            data: [
                {% for payment in payment_sales %}
                {{ payment.total_sales }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{{ 'مقارنة طرق الدفع' if language == 'ar' else 'Payment Methods Comparison' }}'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    window.open(`{{ url_for('reports.sales_by_payment') }}?${params.toString()}`);
}
</script>
{% endblock %}
