{% extends "base.html" %}

{% block title %}{{ 'إعدادات الشركة' if language == 'ar' else 'Company Settings' }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-building"></i>
                {{ 'إعدادات الشركة' if language == 'ar' else 'Company Settings' }}
            </h1>
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'معلومات الشركة الأساسية' if language == 'ar' else 'Basic Company Information' }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name_ar" class="form-label">{{ 'اسم الشركة (عربي)' if language == 'ar' else 'Company Name (Arabic)' }} *</label>
                            <input type="text" class="form-control" id="company_name_ar" name="company_name_ar" 
                                   value="{{ settings.get('company_name_ar', '') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_name_en" class="form-label">{{ 'اسم الشركة (إنجليزي)' if language == 'ar' else 'Company Name (English)' }} *</label>
                            <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                   value="{{ settings.get('company_name_en', '') }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_cr_number" class="form-label">{{ 'رقم السجل التجاري' if language == 'ar' else 'Commercial Registration Number' }}</label>
                            <input type="text" class="form-control" id="company_cr_number" name="company_cr_number" 
                                   value="{{ settings.get('company_cr_number', '') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_tax_number" class="form-label">{{ 'الرقم الضريبي' if language == 'ar' else 'Tax Number' }}</label>
                            <input type="text" class="form-control" id="company_tax_number" name="company_tax_number" 
                                   value="{{ settings.get('company_tax_number', '') }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_phone" class="form-label">{{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}</label>
                            <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                   value="{{ settings.get('company_phone', '') }}" placeholder="+974 XXXX XXXX">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_email" class="form-label">{{ 'البريد الإلكتروني' if language == 'ar' else 'Email Address' }}</label>
                            <input type="email" class="form-control" id="company_email" name="company_email" 
                                   value="{{ settings.get('company_email', '') }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_address_ar" class="form-label">{{ 'العنوان (عربي)' if language == 'ar' else 'Address (Arabic)' }}</label>
                            <textarea class="form-control" id="company_address_ar" name="company_address_ar" rows="3">{{ settings.get('company_address_ar', '') }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_address_en" class="form-label">{{ 'العنوان (إنجليزي)' if language == 'ar' else 'Address (English)' }}</label>
                            <textarea class="form-control" id="company_address_en" name="company_address_en" rows="3">{{ settings.get('company_address_en', '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo Upload Section -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-image"></i>
                        {{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="logo_width" class="form-label">{{ 'عرض الشعار (بكسل)' if language == 'ar' else 'Logo Width (pixels)' }}</label>
                            <input type="number" class="form-control" id="logo_width" name="logo_width"
                                   value="{{ settings.get('logo_width', '150') }}" min="50" max="300">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="logo_height" class="form-label">{{ 'ارتفاع الشعار (بكسل)' if language == 'ar' else 'Logo Height (pixels)' }}</label>
                            <input type="number" class="form-control" id="logo_height" name="logo_height"
                                   value="{{ settings.get('logo_height', '80') }}" min="30" max="200">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="company_logo" class="form-label">{{ 'رفع شعار جديد' if language == 'ar' else 'Upload New Logo' }}</label>
                        <input type="file" class="form-control" id="company_logo" name="company_logo"
                               accept="image/*" onchange="previewLogo(this)">
                        <div class="form-text">
                            {{ 'الصيغ المدعومة: JPG, PNG, GIF. الحد الأقصى: 2MB' if language == 'ar' else 'Supported formats: JPG, PNG, GIF. Max size: 2MB' }}
                        </div>
                    </div>

                    <!-- Current Logo Display -->
                    {% if settings.get('company_logo') %}
                    <div class="current-logo mb-3">
                        <label class="form-label">{{ 'الشعار الحالي' if language == 'ar' else 'Current Logo' }}</label>
                        <div class="border rounded p-2 text-center bg-light">
                            <img src="{{ url_for('static', filename='uploads/logos/' + settings.get('company_logo')) }}"
                                 alt="Company Logo"
                                 style="max-width: {{ settings.get('logo_width', '150') }}px; max-height: {{ settings.get('logo_height', '80') }}px;"
                                 id="current-logo">
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeLogo()">
                                <i class="bi bi-trash"></i>
                                {{ 'حذف الشعار' if language == 'ar' else 'Remove Logo' }}
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Logo Preview -->
                    <div id="logo-preview" class="mb-3" style="display: none;">
                        <label class="form-label">{{ 'معاينة الشعار الجديد' if language == 'ar' else 'New Logo Preview' }}</label>
                        <div class="border rounded p-2 text-center bg-light">
                            <img id="preview-image" alt="Logo Preview"
                                 style="max-width: 150px; max-height: 80px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Company Stamp Section -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-award"></i>
                        {{ 'ختم الشركة' if language == 'ar' else 'Company Stamp' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stamp_width" class="form-label">{{ 'عرض الختم (بكسل)' if language == 'ar' else 'Stamp Width (pixels)' }}</label>
                            <input type="number" class="form-control" id="stamp_width" name="stamp_width"
                                   value="{{ settings.get('stamp_width', '120') }}" min="80" max="200">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stamp_height" class="form-label">{{ 'ارتفاع الختم (بكسل)' if language == 'ar' else 'Stamp Height (pixels)' }}</label>
                            <input type="number" class="form-control" id="stamp_height" name="stamp_height"
                                   value="{{ settings.get('stamp_height', '120') }}" min="80" max="200">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="company_stamp" class="form-label">{{ 'رفع ختم جديد' if language == 'ar' else 'Upload New Stamp' }}</label>
                        <input type="file" class="form-control" id="company_stamp" name="company_stamp"
                               accept="image/*" onchange="previewStamp(this)">
                        <div class="form-text">
                            {{ 'الصيغ المدعومة: JPG, PNG, GIF. الحد الأقصى: 2MB. يفضل صورة دائرية شفافة' if language == 'ar' else 'Supported formats: JPG, PNG, GIF. Max size: 2MB. Circular transparent image preferred' }}
                        </div>
                    </div>

                    <!-- Current Stamp Display -->
                    {% if settings.get('company_stamp') %}
                    <div class="current-stamp mb-3">
                        <label class="form-label">{{ 'الختم الحالي' if language == 'ar' else 'Current Stamp' }}</label>
                        <div class="border rounded p-2 text-center bg-light">
                            <img src="{{ url_for('static', filename='uploads/stamps/' + settings.get('company_stamp')) }}"
                                 alt="Company Stamp"
                                 style="max-width: {{ settings.get('stamp_width', '120') }}px; max-height: {{ settings.get('stamp_height', '120') }}px;"
                                 id="current-stamp">
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeStamp()">
                                <i class="bi bi-trash"></i>
                                {{ 'حذف الختم' if language == 'ar' else 'Remove Stamp' }}
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Stamp Preview -->
                    <div id="stamp-preview" class="mb-3" style="display: none;">
                        <label class="form-label">{{ 'معاينة الختم الجديد' if language == 'ar' else 'New Stamp Preview' }}</label>
                        <div class="border rounded p-2 text-center bg-light">
                            <img id="preview-stamp-image" alt="Stamp Preview"
                                 style="max-width: 120px; max-height: 120px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'معاينة' if language == 'ar' else 'Preview' }}</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-light rounded p-3" id="preview-logo-container">
                            {% if settings.get('company_logo') %}
                                <img src="{{ url_for('static', filename='uploads/logos/' + settings.get('company_logo')) }}"
                                     alt="Company Logo"
                                     style="max-width: {{ settings.get('logo_width', '150') }}px; max-height: {{ settings.get('logo_height', '80') }}px;"
                                     id="preview-logo">
                            {% else %}
                                <i class="bi bi-building display-4 text-muted" id="preview-placeholder"></i>
                                <p class="mt-2 mb-0">{{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}</p>
                                <small class="text-muted">{{ 'لم يتم رفع شعار بعد' if language == 'ar' else 'No logo uploaded yet' }}</small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="company-preview">
                        <h6 class="company-name">
                            {{ settings.get('company_name_ar', 'اسم الشركة') if language == 'ar' else settings.get('company_name_en', 'Company Name') }}
                        </h6>
                        <p class="company-address text-muted small mb-2">
                            {{ settings.get('company_address_ar', 'عنوان الشركة') if language == 'ar' else settings.get('company_address_en', 'Company Address') }}
                        </p>
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-telephone"></i> {{ settings.get('company_phone', '+974 XXXX XXXX') }}
                        </p>
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-envelope"></i> {{ settings.get('company_email', '<EMAIL>') }}
                        </p>
                        {% if settings.get('company_cr_number') %}
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-file-text"></i> {{ 'س.ت:' if language == 'ar' else 'CR:' }} {{ settings.get('company_cr_number') }}
                        </p>
                        {% endif %}
                        {% if settings.get('company_tax_number') %}
                        <p class="company-contact text-muted small">
                            <i class="bi bi-receipt"></i> {{ 'ض.ر:' if language == 'ar' else 'Tax:' }} {{ settings.get('company_tax_number') }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">{{ 'إرشادات' if language == 'ar' else 'Guidelines' }}</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'اسم الشركة مطلوب بالعربية والإنجليزية' if language == 'ar' else 'Company name is required in both Arabic and English' }}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'رقم السجل التجاري مطلوب للشركات المسجلة' if language == 'ar' else 'Commercial registration number is required for registered companies' }}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'هذه المعلومات ستظهر في الفواتير' if language == 'ar' else 'This information will appear on invoices' }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i>
                    {{ 'حفظ إعدادات الشركة' if language == 'ar' else 'Save Company Settings' }}
                </button>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// Live preview update
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['company_name_ar', 'company_name_en', 'company_address_ar', 'company_address_en', 'company_phone', 'company_email', 'company_cr_number', 'company_tax_number'];
    
    inputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updatePreview);
        }
    });
    
    function updatePreview() {
        const language = '{{ language }}';
        const companyName = language === 'ar' ? 
            document.getElementById('company_name_ar').value || 'اسم الشركة' :
            document.getElementById('company_name_en').value || 'Company Name';
        
        const companyAddress = language === 'ar' ? 
            document.getElementById('company_address_ar').value || 'عنوان الشركة' :
            document.getElementById('company_address_en').value || 'Company Address';
        
        const phone = document.getElementById('company_phone').value || '+974 XXXX XXXX';
        const email = document.getElementById('company_email').value || '<EMAIL>';
        const crNumber = document.getElementById('company_cr_number').value;
        const taxNumber = document.getElementById('company_tax_number').value;
        
        // Update preview
        document.querySelector('.company-name').textContent = companyName;
        document.querySelector('.company-address').textContent = companyAddress;
        document.querySelectorAll('.company-contact')[0].innerHTML = '<i class="bi bi-telephone"></i> ' + phone;
        document.querySelectorAll('.company-contact')[1].innerHTML = '<i class="bi bi-envelope"></i> ' + email;
        
        // Update CR and Tax if they exist
        const crElement = document.querySelector('.company-contact:nth-child(4)');
        const taxElement = document.querySelector('.company-contact:nth-child(5)');
        
        if (crNumber && crElement) {
            crElement.innerHTML = '<i class="bi bi-file-text"></i> ' + (language === 'ar' ? 'س.ت: ' : 'CR: ') + crNumber;
            crElement.style.display = 'block';
        } else if (crElement) {
            crElement.style.display = 'none';
        }
        
        if (taxNumber && taxElement) {
            taxElement.innerHTML = '<i class="bi bi-receipt"></i> ' + (language === 'ar' ? 'ض.ر: ' : 'Tax: ') + taxNumber;
            taxElement.style.display = 'block';
        } else if (taxElement) {
            taxElement.style.display = 'none';
        }
    }
});

// Logo preview function
function previewLogo(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Check file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            alert('{{ "حجم الملف كبير جداً. الحد الأقصى 2MB" if language == "ar" else "File size too large. Maximum 2MB allowed" }}');
            input.value = '';
            return;
        }

        // Check file type
        if (!file.type.match('image.*')) {
            alert('{{ "يرجى اختيار ملف صورة صحيح" if language == "ar" else "Please select a valid image file" }}');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // Show preview
            const previewDiv = document.getElementById('logo-preview');
            const previewImg = document.getElementById('preview-image');

            previewImg.src = e.target.result;
            previewDiv.style.display = 'block';

            // Update main preview
            const previewContainer = document.getElementById('preview-logo-container');
            const logoWidth = document.getElementById('logo_width').value || '150';
            const logoHeight = document.getElementById('logo_height').value || '80';

            previewContainer.innerHTML = `
                <img src="${e.target.result}"
                     alt="Company Logo Preview"
                     style="max-width: ${logoWidth}px; max-height: ${logoHeight}px;">
            `;
        };
        reader.readAsDataURL(file);
    }
}

// Remove logo function
function removeLogo() {
    if (confirm('{{ "هل أنت متأكد من حذف الشعار؟" if language == "ar" else "Are you sure you want to remove the logo?" }}')) {
        // Create hidden input to mark logo for removal
        const form = document.querySelector('form');
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'remove_logo';
        hiddenInput.value = 'true';
        form.appendChild(hiddenInput);

        // Update preview
        const previewContainer = document.getElementById('preview-logo-container');
        previewContainer.innerHTML = `
            <i class="bi bi-building display-4 text-muted"></i>
            <p class="mt-2 mb-0">{{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}</p>
            <small class="text-muted">{{ 'سيتم حذف الشعار عند الحفظ' if language == 'ar' else 'Logo will be removed when saved' }}</small>
        `;

        // Hide current logo section
        const currentLogoDiv = document.querySelector('.current-logo');
        if (currentLogoDiv) {
            currentLogoDiv.style.display = 'none';
        }
    }
}

// Stamp preview function
function previewStamp(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Check file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            alert('{{ "حجم الملف كبير جداً. الحد الأقصى 2MB" if language == "ar" else "File size too large. Maximum 2MB allowed" }}');
            input.value = '';
            return;
        }

        // Check file type
        if (!file.type.match('image.*')) {
            alert('{{ "يرجى اختيار ملف صورة صحيح" if language == "ar" else "Please select a valid image file" }}');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // Show preview
            const previewDiv = document.getElementById('stamp-preview');
            const previewImg = document.getElementById('preview-stamp-image');

            previewImg.src = e.target.result;
            previewDiv.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

// Remove stamp function
function removeStamp() {
    if (confirm('{{ "هل أنت متأكد من حذف الختم؟" if language == "ar" else "Are you sure you want to remove the stamp?" }}')) {
        // Create hidden input to mark stamp for removal
        const form = document.querySelector('form');
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'remove_stamp';
        hiddenInput.value = 'true';
        form.appendChild(hiddenInput);

        // Hide current stamp section
        const currentStampDiv = document.querySelector('.current-stamp');
        if (currentStampDiv) {
            currentStampDiv.style.display = 'none';
        }
    }
}

// Update logo dimensions in preview
document.getElementById('logo_width').addEventListener('input', updateLogoDimensions);
document.getElementById('logo_height').addEventListener('input', updateLogoDimensions);

function updateLogoDimensions() {
    const width = document.getElementById('logo_width').value || '150';
    const height = document.getElementById('logo_height').value || '80';

    // Update current logo if exists
    const currentLogo = document.getElementById('current-logo');
    if (currentLogo) {
        currentLogo.style.maxWidth = width + 'px';
        currentLogo.style.maxHeight = height + 'px';
    }

    // Update preview logo if exists
    const previewLogo = document.getElementById('preview-logo');
    if (previewLogo) {
        previewLogo.style.maxWidth = width + 'px';
        previewLogo.style.maxHeight = height + 'px';
    }

    // Update preview image if exists
    const previewImage = document.getElementById('preview-image');
    if (previewImage) {
        previewImage.style.maxWidth = width + 'px';
        previewImage.style.maxHeight = height + 'px';
    }
}

// Update stamp dimensions in preview
document.getElementById('stamp_width').addEventListener('input', updateStampDimensions);
document.getElementById('stamp_height').addEventListener('input', updateStampDimensions);

function updateStampDimensions() {
    const width = document.getElementById('stamp_width').value || '120';
    const height = document.getElementById('stamp_height').value || '120';

    // Update current stamp if exists
    const currentStamp = document.getElementById('current-stamp');
    if (currentStamp) {
        currentStamp.style.maxWidth = width + 'px';
        currentStamp.style.maxHeight = height + 'px';
    }

    // Update preview stamp if exists
    const previewStamp = document.getElementById('preview-stamp-image');
    if (previewStamp) {
        previewStamp.style.maxWidth = width + 'px';
        previewStamp.style.maxHeight = height + 'px';
    }
}
</script>
{% endblock %}
