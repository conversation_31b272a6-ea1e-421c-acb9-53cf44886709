{% extends "base.html" %}

{% block title %}{{ 'إعدادات الشركة' if language == 'ar' else 'Company Settings' }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-building"></i>
                {{ 'إعدادات الشركة' if language == 'ar' else 'Company Settings' }}
            </h1>
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>
</div>

<form method="POST">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'معلومات الشركة الأساسية' if language == 'ar' else 'Basic Company Information' }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name_ar" class="form-label">{{ 'اسم الشركة (عربي)' if language == 'ar' else 'Company Name (Arabic)' }} *</label>
                            <input type="text" class="form-control" id="company_name_ar" name="company_name_ar" 
                                   value="{{ settings.get('company_name_ar', '') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_name_en" class="form-label">{{ 'اسم الشركة (إنجليزي)' if language == 'ar' else 'Company Name (English)' }} *</label>
                            <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                   value="{{ settings.get('company_name_en', '') }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_cr_number" class="form-label">{{ 'رقم السجل التجاري' if language == 'ar' else 'Commercial Registration Number' }}</label>
                            <input type="text" class="form-control" id="company_cr_number" name="company_cr_number" 
                                   value="{{ settings.get('company_cr_number', '') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_tax_number" class="form-label">{{ 'الرقم الضريبي' if language == 'ar' else 'Tax Number' }}</label>
                            <input type="text" class="form-control" id="company_tax_number" name="company_tax_number" 
                                   value="{{ settings.get('company_tax_number', '') }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_phone" class="form-label">{{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}</label>
                            <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                   value="{{ settings.get('company_phone', '') }}" placeholder="+974 XXXX XXXX">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_email" class="form-label">{{ 'البريد الإلكتروني' if language == 'ar' else 'Email Address' }}</label>
                            <input type="email" class="form-control" id="company_email" name="company_email" 
                                   value="{{ settings.get('company_email', '') }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_address_ar" class="form-label">{{ 'العنوان (عربي)' if language == 'ar' else 'Address (Arabic)' }}</label>
                            <textarea class="form-control" id="company_address_ar" name="company_address_ar" rows="3">{{ settings.get('company_address_ar', '') }}</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company_address_en" class="form-label">{{ 'العنوان (إنجليزي)' if language == 'ar' else 'Address (English)' }}</label>
                            <textarea class="form-control" id="company_address_en" name="company_address_en" rows="3">{{ settings.get('company_address_en', '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'معاينة' if language == 'ar' else 'Preview' }}</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <i class="bi bi-building display-4 text-muted"></i>
                            <p class="mt-2 mb-0">{{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}</p>
                            <small class="text-muted">{{ 'قريباً' if language == 'ar' else 'Coming Soon' }}</small>
                        </div>
                    </div>
                    
                    <div class="company-preview">
                        <h6 class="company-name">
                            {{ settings.get('company_name_ar', 'اسم الشركة') if language == 'ar' else settings.get('company_name_en', 'Company Name') }}
                        </h6>
                        <p class="company-address text-muted small mb-2">
                            {{ settings.get('company_address_ar', 'عنوان الشركة') if language == 'ar' else settings.get('company_address_en', 'Company Address') }}
                        </p>
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-telephone"></i> {{ settings.get('company_phone', '+974 XXXX XXXX') }}
                        </p>
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-envelope"></i> {{ settings.get('company_email', '<EMAIL>') }}
                        </p>
                        {% if settings.get('company_cr_number') %}
                        <p class="company-contact text-muted small mb-1">
                            <i class="bi bi-file-text"></i> {{ 'س.ت:' if language == 'ar' else 'CR:' }} {{ settings.get('company_cr_number') }}
                        </p>
                        {% endif %}
                        {% if settings.get('company_tax_number') %}
                        <p class="company-contact text-muted small">
                            <i class="bi bi-receipt"></i> {{ 'ض.ر:' if language == 'ar' else 'Tax:' }} {{ settings.get('company_tax_number') }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">{{ 'إرشادات' if language == 'ar' else 'Guidelines' }}</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'اسم الشركة مطلوب بالعربية والإنجليزية' if language == 'ar' else 'Company name is required in both Arabic and English' }}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'رقم السجل التجاري مطلوب للشركات المسجلة' if language == 'ar' else 'Commercial registration number is required for registered companies' }}
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info"></i>
                            {{ 'هذه المعلومات ستظهر في الفواتير' if language == 'ar' else 'This information will appear on invoices' }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i>
                    {{ 'حفظ إعدادات الشركة' if language == 'ar' else 'Save Company Settings' }}
                </button>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// Live preview update
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['company_name_ar', 'company_name_en', 'company_address_ar', 'company_address_en', 'company_phone', 'company_email', 'company_cr_number', 'company_tax_number'];
    
    inputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updatePreview);
        }
    });
    
    function updatePreview() {
        const language = '{{ language }}';
        const companyName = language === 'ar' ? 
            document.getElementById('company_name_ar').value || 'اسم الشركة' :
            document.getElementById('company_name_en').value || 'Company Name';
        
        const companyAddress = language === 'ar' ? 
            document.getElementById('company_address_ar').value || 'عنوان الشركة' :
            document.getElementById('company_address_en').value || 'Company Address';
        
        const phone = document.getElementById('company_phone').value || '+974 XXXX XXXX';
        const email = document.getElementById('company_email').value || '<EMAIL>';
        const crNumber = document.getElementById('company_cr_number').value;
        const taxNumber = document.getElementById('company_tax_number').value;
        
        // Update preview
        document.querySelector('.company-name').textContent = companyName;
        document.querySelector('.company-address').textContent = companyAddress;
        document.querySelectorAll('.company-contact')[0].innerHTML = '<i class="bi bi-telephone"></i> ' + phone;
        document.querySelectorAll('.company-contact')[1].innerHTML = '<i class="bi bi-envelope"></i> ' + email;
        
        // Update CR and Tax if they exist
        const crElement = document.querySelector('.company-contact:nth-child(4)');
        const taxElement = document.querySelector('.company-contact:nth-child(5)');
        
        if (crNumber && crElement) {
            crElement.innerHTML = '<i class="bi bi-file-text"></i> ' + (language === 'ar' ? 'س.ت: ' : 'CR: ') + crNumber;
            crElement.style.display = 'block';
        } else if (crElement) {
            crElement.style.display = 'none';
        }
        
        if (taxNumber && taxElement) {
            taxElement.innerHTML = '<i class="bi bi-receipt"></i> ' + (language === 'ar' ? 'ض.ر: ' : 'Tax: ') + taxNumber;
            taxElement.style.display = 'block';
        } else if (taxElement) {
            taxElement.style.display = 'none';
        }
    }
});
</script>
{% endblock %}
