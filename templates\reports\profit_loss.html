{% extends "base.html" %}

{% block title %}{{ 'تقرير الأرباح والخسائر' if language == 'ar' else 'Profit & Loss Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-graph-up-arrow me-2"></i>
                    {{ 'تقرير الأرباح والخسائر' if language == 'ar' else 'Profit & Loss Report' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Period Filter -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-range me-2"></i>
                        {{ 'فترة التقرير' if language == 'ar' else 'Report Period' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">{{ 'نوع الفترة' if language == 'ar' else 'Period Type' }}</label>
                            <select name="period" class="form-select" onchange="togglePeriodFields()">
                                <option value="month" {% if period == 'month' %}selected{% endif %}>{{ 'شهري' if language == 'ar' else 'Monthly' }}</option>
                                <option value="quarter" {% if period == 'quarter' %}selected{% endif %}>{{ 'ربع سنوي' if language == 'ar' else 'Quarterly' }}</option>
                                <option value="year" {% if period == 'year' %}selected{% endif %}>{{ 'سنوي' if language == 'ar' else 'Yearly' }}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'السنة' if language == 'ar' else 'Year' }}</label>
                            <select name="year" class="form-select">
                                {% for y in range(2020, 2030) %}
                                <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2" id="monthField">
                            <label class="form-label">{{ 'الشهر' if language == 'ar' else 'Month' }}</label>
                            <select name="month" class="form-select">
                                {% for m in range(1, 13) %}
                                <option value="{{ m }}" {% if m == month %}selected{% endif %}>
                                    {{ m }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2" id="quarterField" style="display: none;">
                            <label class="form-label">{{ 'الربع' if language == 'ar' else 'Quarter' }}</label>
                            <select name="quarter" class="form-select">
                                <option value="1" {% if quarter == 1 %}selected{% endif %}>Q1</option>
                                <option value="2" {% if quarter == 2 %}selected{% endif %}>Q2</option>
                                <option value="3" {% if quarter == 3 %}selected{% endif %}>Q3</option>
                                <option value="4" {% if quarter == 4 %}selected{% endif %}>Q4</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    {{ 'عرض التقرير' if language == 'ar' else 'Generate Report' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report Period Info -->
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle me-2"></i>
                <strong>{{ 'فترة التقرير:' if language == 'ar' else 'Report Period:' }}</strong>
                {{ start_date.strftime('%Y-%m-%d') }} {{ 'إلى' if language == 'ar' else 'to' }} {{ end_date.strftime('%Y-%m-%d') }}
            </div>

            <!-- Key Metrics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'صافي الإيرادات' if language == 'ar' else 'Net Revenue' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(net_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                    {% if revenue_growth != 0 %}
                                    <small class="{% if revenue_growth > 0 %}text-success{% else %}text-danger{% endif %}">
                                        <i class="bi bi-arrow-{% if revenue_growth > 0 %}up{% else %}down{% endif %}"></i>
                                        {{ "%.1f"|format(abs(revenue_growth)) }}%
                                    </small>
                                    {% endif %}
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي الربح' if language == 'ar' else 'Gross Profit' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(gross_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                    <small>{{ "%.1f"|format(gross_profit_margin) }}% {{ 'هامش' if language == 'ar' else 'margin' }}</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'الربح التشغيلي' if language == 'ar' else 'Operating Profit' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(operating_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                    <small>{{ "%.1f"|format(operating_profit_margin) }}% {{ 'هامش' if language == 'ar' else 'margin' }}</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-bar-chart fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card {% if net_profit >= 0 %}bg-success{% else %}bg-danger{% endif %} text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'صافي الربح' if language == 'ar' else 'Net Profit' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(net_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                    <small>{{ "%.1f"|format(net_profit_margin) }}% {{ 'هامش' if language == 'ar' else 'margin' }}</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-{% if net_profit >= 0 %}trophy{% else %}exclamation-triangle{% endif %} fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profit & Loss Statement -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>
                        {{ 'بيان الأرباح والخسائر' if language == 'ar' else 'Profit & Loss Statement' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="plTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'البند' if language == 'ar' else 'Item' }}</th>
                                    <th class="text-end">{{ 'المبلغ (ر.ق)' if language == 'ar' else 'Amount (QAR)' }}</th>
                                    <th class="text-end">{{ 'النسبة %' if language == 'ar' else 'Percentage %' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Revenue Section -->
                                <tr class="table-primary">
                                    <td><strong>{{ 'الإيرادات' if language == 'ar' else 'REVENUE' }}</strong></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="ps-4">{{ 'إجمالي المبيعات' if language == 'ar' else 'Gross Sales' }}</td>
                                    <td class="text-end">{{ "%.2f"|format(sales_revenue) }}</td>
                                    <td class="text-end">{{ "%.1f"|format((sales_revenue / net_revenue * 100) if net_revenue > 0 else 0) }}%</td>
                                </tr>
                                <tr>
                                    <td class="ps-4">{{ 'المرتجعات' if language == 'ar' else 'Returns' }}</td>
                                    <td class="text-end text-danger">({{ "%.2f"|format(returns_amount) }})</td>
                                    <td class="text-end">{{ "%.1f"|format((returns_amount / net_revenue * 100) if net_revenue > 0 else 0) }}%</td>
                                </tr>
                                <tr class="table-info">
                                    <td><strong>{{ 'صافي الإيرادات' if language == 'ar' else 'Net Revenue' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(net_revenue) }}</strong></td>
                                    <td class="text-end"><strong>100.0%</strong></td>
                                </tr>
                                
                                <!-- COGS Section -->
                                <tr class="table-warning">
                                    <td><strong>{{ 'تكلفة البضاعة المباعة' if language == 'ar' else 'COST OF GOODS SOLD' }}</strong></td>
                                    <td class="text-end text-danger"><strong>({{ "%.2f"|format(cogs) }})</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format((cogs / net_revenue * 100) if net_revenue > 0 else 0) }}%</strong></td>
                                </tr>
                                
                                <!-- Gross Profit -->
                                <tr class="table-success">
                                    <td><strong>{{ 'إجمالي الربح' if language == 'ar' else 'GROSS PROFIT' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(gross_profit) }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format(gross_profit_margin) }}%</strong></td>
                                </tr>
                                
                                <!-- Operating Expenses -->
                                <tr class="table-secondary">
                                    <td><strong>{{ 'المصروفات التشغيلية' if language == 'ar' else 'OPERATING EXPENSES' }}</strong></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                {% for expense_type, amount in operating_expenses.items() %}
                                <tr>
                                    <td class="ps-4">
                                        {% if expense_type == 'rent' %}{{ 'الإيجار' if language == 'ar' else 'Rent' }}
                                        {% elif expense_type == 'utilities' %}{{ 'المرافق' if language == 'ar' else 'Utilities' }}
                                        {% elif expense_type == 'salaries' %}{{ 'الرواتب' if language == 'ar' else 'Salaries' }}
                                        {% elif expense_type == 'marketing' %}{{ 'التسويق' if language == 'ar' else 'Marketing' }}
                                        {% elif expense_type == 'insurance' %}{{ 'التأمين' if language == 'ar' else 'Insurance' }}
                                        {% else %}{{ 'أخرى' if language == 'ar' else 'Other' }}
                                        {% endif %}
                                    </td>
                                    <td class="text-end text-danger">({{ "%.2f"|format(amount) }})</td>
                                    <td class="text-end">{{ "%.1f"|format((amount / net_revenue * 100) if net_revenue > 0 else 0) }}%</td>
                                </tr>
                                {% endfor %}
                                <tr class="table-warning">
                                    <td><strong>{{ 'إجمالي المصروفات التشغيلية' if language == 'ar' else 'Total Operating Expenses' }}</strong></td>
                                    <td class="text-end text-danger"><strong>({{ "%.2f"|format(total_operating_expenses) }})</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format((total_operating_expenses / net_revenue * 100) if net_revenue > 0 else 0) }}%</strong></td>
                                </tr>
                                
                                <!-- Operating Profit -->
                                <tr class="table-info">
                                    <td><strong>{{ 'الربح التشغيلي' if language == 'ar' else 'OPERATING PROFIT' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(operating_profit) }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format(operating_profit_margin) }}%</strong></td>
                                </tr>
                                
                                <!-- Tax -->
                                <tr>
                                    <td>{{ 'الضرائب' if language == 'ar' else 'Tax' }} ({{ "%.0f"|format(tax_rate * 100) }}%)</td>
                                    <td class="text-end text-danger">({{ "%.2f"|format(tax_amount) }})</td>
                                    <td class="text-end">{{ "%.1f"|format((tax_amount / net_revenue * 100) if net_revenue > 0 else 0) }}%</td>
                                </tr>
                                
                                <!-- Net Profit -->
                                <tr class="table-{% if net_profit >= 0 %}success{% else %}danger{% endif %}">
                                    <td><strong>{{ 'صافي الربح' if language == 'ar' else 'NET PROFIT' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(net_profit) }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format(net_profit_margin) }}%</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePeriodFields() {
    const period = document.querySelector('select[name="period"]').value;
    const monthField = document.getElementById('monthField');
    const quarterField = document.getElementById('quarterField');
    
    if (period === 'month') {
        monthField.style.display = 'block';
        quarterField.style.display = 'none';
    } else if (period === 'quarter') {
        monthField.style.display = 'none';
        quarterField.style.display = 'block';
    } else {
        monthField.style.display = 'none';
        quarterField.style.display = 'none';
    }
}

function exportToExcel() {
    const table = document.getElementById('plTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Profit & Loss"});
    XLSX.writeFile(wb, 'profit_loss_report.xlsx');
}

// Initialize period fields
document.addEventListener('DOMContentLoaded', function() {
    togglePeriodFields();
});

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
