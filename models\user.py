"""
User model for Qatar POS System
Supports role-based access control with Manager, Seller, and Accountant roles
"""

from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from extensions import db

class User(UserMixin, db.Model):
    """User model with role-based access control"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Personal Information
    first_name_ar = db.Column(db.String(100), nullable=False)
    first_name_en = db.Column(db.String(100), nullable=False)
    last_name_ar = db.Column(db.String(100), nullable=False)
    last_name_en = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    
    # Role and Status (using legacy system for compatibility)
    role = db.Column(db.Enum('manager', 'admin', 'seller', 'accountant', 'inventory_manager', name='user_roles'),
                     nullable=False, default='seller')
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    
    # Qatar-specific fields (commented out for compatibility)
    # qatar_id = db.Column(db.String(11))  # Qatar ID number
    # employee_id = db.Column(db.String(20), unique=True)
    
    # Relationships
    sales = db.relationship('Sale', backref='seller', lazy='dynamic')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self, language='ar'):
        """Get full name in specified language"""
        if language == 'ar':
            return f"{self.first_name_ar} {self.last_name_ar}"
        return f"{self.first_name_en} {self.last_name_en}"

    def get_display_name(self, language='ar'):
        """Get display name for user (full name or username)"""
        full_name = self.get_full_name(language)
        if full_name and full_name.strip():
            return full_name
        return self.username
    
    def has_permission(self, permission):
        """Check if user has specific permission"""
        # Use legacy role system for compatibility
        if self.role:
            permissions = {
                'manager': ['all'],
                'admin': ['all'],
                'seller': [
                    'sales', 'products_read', 'customers_read', 'inventory_read',
                    'pos', 'barcode_read'
                ],
                'accountant': [
                    'reports', 'inventory', 'inventory_read', 'inventory_write',
                    'suppliers', 'customers_read', 'products_read', 'sales_read',
                    'users_read'
                ],
                'inventory_manager': [
                    'inventory', 'inventory_read', 'inventory_write', 'inventory_adjust',
                    'products', 'products_read', 'products_write', 'suppliers',
                    'reports', 'barcode', 'users_read'
                ]
            }

            user_permissions = permissions.get(self.role, [])

            # Check for 'all' permission (admin/manager)
            if 'all' in user_permissions:
                return True

            # Check for specific permission
            if permission in user_permissions:
                return True

            # Check for permission patterns (e.g., 'inventory' covers 'inventory_read')
            for perm in user_permissions:
                if permission.startswith(perm + '_'):
                    return True

        return False
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name_ar': self.get_full_name('ar'),
            'full_name_en': self.get_full_name('en'),
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
