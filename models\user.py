"""
User model for Qatar POS System
Supports role-based access control with Manager, Seller, and Accountant roles
"""

from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from extensions import db

class User(UserMixin, db.Model):
    """User model with role-based access control"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Personal Information
    first_name_ar = db.Column(db.String(100), nullable=False)
    first_name_en = db.Column(db.String(100), nullable=False)
    last_name_ar = db.Column(db.String(100), nullable=False)
    last_name_en = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    
    # Role and Status (supporting both old and new systems)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=True)
    role_legacy = db.Column(db.Enum('manager', 'seller', 'accountant', name='user_roles'),
                           nullable=True, default='seller')  # Legacy role system
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    
    # Qatar-specific fields
    qatar_id = db.Column(db.String(11))  # Qatar ID number
    employee_id = db.Column(db.String(20), unique=True)
    
    # Relationships
    sales = db.relationship('Sale', backref='seller', lazy='dynamic')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self, language='ar'):
        """Get full name in specified language"""
        if language == 'ar':
            return f"{self.first_name_ar} {self.last_name_ar}"
        return f"{self.first_name_en} {self.last_name_en}"
    
    def has_permission(self, permission):
        """Check if user has specific permission"""
        # Try new role system first
        if self.role:
            # Admin role has all permissions
            if self.role.name == 'admin':
                return True
            return self.role.has_permission(permission)

        # Fall back to legacy role system
        if self.role_legacy:
            permissions = {
                'manager': ['all'],
                'seller': ['sales', 'products_read', 'customers_read', 'inventory_read'],
                'accountant': ['reports', 'inventory', 'suppliers', 'customers_read']
            }

            user_permissions = permissions.get(self.role_legacy, [])
            return 'all' in user_permissions or permission in user_permissions

        return False
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name_ar': self.get_full_name('ar'),
            'full_name_en': self.get_full_name('en'),
            'role': self.role.name if self.role else self.role_legacy,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
