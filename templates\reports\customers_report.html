{% extends "base.html" %}

{% block title %}{{ 'تقرير العملاء' if language == 'ar' else 'Customers Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-people-fill me-2"></i>
                    {{ 'تقرير العملاء' if language == 'ar' else 'Customers Report' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي العملاء' if language == 'ar' else 'Total Customers' }}</h6>
                                    <h3 class="mb-0">{{ top_customers|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(top_customers|sum(attribute='1')|default(0)) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'متوسط الشراء' if language == 'ar' else 'Average Purchase' }}</h6>
                                    {% set total_sales = top_customers|sum(attribute='1')|default(0) %}
                                    {% set total_count = top_customers|sum(attribute='2')|default(1) %}
                                    <h3 class="mb-0">{{ "%.0f"|format(total_sales / total_count if total_count > 0 else 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي المعاملات' if language == 'ar' else 'Total Transactions' }}</h6>
                                    <h3 class="mb-0">{{ top_customers|sum(attribute='2')|default(0) }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-receipt fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Customers Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-trophy me-2"></i>
                        {{ 'أفضل العملاء' if language == 'ar' else 'Top Customers' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="customersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الترتيب' if language == 'ar' else 'Rank' }}</th>
                                    <th>{{ 'اسم العميل' if language == 'ar' else 'Customer Name' }}</th>
                                    <th>{{ 'رقم الهاتف' if language == 'ar' else 'Phone' }}</th>
                                    <th>{{ 'البريد الإلكتروني' if language == 'ar' else 'Email' }}</th>
                                    <th>{{ 'إجمالي المشتريات' if language == 'ar' else 'Total Purchases' }}</th>
                                    <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transaction Count' }}</th>
                                    <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Average Transaction' }}</th>
                                    <th>{{ 'آخر شراء' if language == 'ar' else 'Last Purchase' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer, total_purchases, purchase_count, last_purchase in top_customers %}
                                <tr>
                                    <td>
                                        {% if loop.index <= 3 %}
                                        <span class="badge bg-warning">
                                            <i class="bi bi-trophy-fill"></i> {{ loop.index }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ loop.index }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.customer_type %}
                                        <br><small class="text-muted">{{ customer.customer_type }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                            <i class="bi bi-telephone me-1"></i>{{ customer.phone }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.email %}
                                        <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                            <i class="bi bi-envelope me-1"></i>{{ customer.email }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ "%.2f"|format(total_purchases) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ purchase_count }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(total_purchases / purchase_count if purchase_count > 0 else 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        {% if last_purchase %}
                                        <small>{{ last_purchase.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                        {{ 'لا توجد بيانات عملاء' if language == 'ar' else 'No customer data available' }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Simple Excel export functionality
    const table = document.getElementById('customersTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Customers Report"});
    XLSX.writeFile(wb, 'customers_report.xlsx');
}

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

// Add print styles
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
