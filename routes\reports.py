"""
Reports routes for Qatar POS System
Handles various business reports and analytics
"""

from flask import Blueprint, render_template, request, jsonify, make_response, flash, redirect, url_for
from flask_login import login_required
from datetime import datetime, timedelta
from sqlalchemy import func, and_
from models.sale import Sale, SaleItem
from models.product import Product
from models.category import Category
from models.customer import Customer
from models.user import User
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language, format_currency

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
@permission_required('reports')
def index():
    """Reports dashboard"""
    language = get_user_language()

    # Get today's statistics
    today = datetime.now().date()

    # Today's sales total
    today_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).scalar() or 0

    # Today's transactions count
    today_transactions = db.session.query(func.count(Sale.id)).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).scalar() or 0

    # Today's customers count
    today_customers = db.session.query(func.count(func.distinct(Sale.customer_id))).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed',
        Sale.customer_id.isnot(None)
    ).scalar() or 0

    # Today's average transaction
    today_avg_transaction = today_sales / today_transactions if today_transactions > 0 else 0

    today_stats = {
        'sales': today_sales,
        'transactions': today_transactions,
        'customers': today_customers,
        'avg_transaction': today_avg_transaction
    }

    # This month's statistics
    this_month_start = today.replace(day=1)
    month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.sale_date >= this_month_start,
        Sale.status == 'completed'
    ).scalar() or 0

    month_stats = {
        'sales': month_sales
    }

    # Total customers count
    total_customers = db.session.query(func.count(Customer.id)).filter(
        Customer.is_active == True
    ).scalar() or 0

    return render_template('reports/index.html',
                         language=language,
                         today_stats=today_stats,
                         month_stats=month_stats,
                         total_customers=total_customers)

@reports_bp.route('/sales')
@login_required
@permission_required('reports')
def sales_report():
    """Sales report"""
    language = get_user_language()
    
    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    seller_id = request.args.get('seller_id', type=int)
    
    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = today.replace(day=1).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')
    
    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.sales_report'))
    
    # Build query
    query = Sale.query.filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    )
    
    if seller_id:
        query = query.filter(Sale.seller_id == seller_id)
    
    sales = query.all()
    
    # Calculate totals
    total_sales = sum(sale.total_amount for sale in sales)
    total_transactions = len(sales)
    average_transaction = total_sales / total_transactions if total_transactions > 0 else 0
    
    # Get sellers for filter
    sellers = User.query.filter(User.role.in_(['seller', 'manager'])).all()
    
    # Create summary object for template
    summary = {
        'total_sales': total_sales,
        'total_transactions': total_transactions,
        'average_transaction': average_transaction,
        'total_discounts': 0  # Calculate if needed
    }

    return render_template('reports/sales_report.html',
                         sales=sales,
                         summary=summary,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         average_transaction=average_transaction,
                         sellers=sellers,
                         date_from=date_from,
                         date_to=date_to,
                         seller_id=seller_id,
                         language=language)

@reports_bp.route('/products')
@login_required
@permission_required('reports')
def products_report():
    """Products inventory and performance report"""
    language = get_user_language()

    # Get filters
    category_id = request.args.get('category_id', '')
    stock_status = request.args.get('stock_status', '')
    sort_by = request.args.get('sort_by', 'name')
    page = request.args.get('page', 1, type=int)

    # Base query
    query = Product.query

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    # Apply stock status filter
    if stock_status == 'in_stock':
        query = query.filter(Product.stock_quantity > Product.min_stock_level)
    elif stock_status == 'low_stock':
        query = query.filter(
            Product.stock_quantity <= Product.min_stock_level,
            Product.stock_quantity > 0
        )
    elif stock_status == 'out_of_stock':
        query = query.filter(Product.stock_quantity <= 0)

    # Apply sorting
    if sort_by == 'name':
        query = query.order_by(Product.name_ar if language == 'ar' else Product.name_en)
    elif sort_by == 'stock':
        query = query.order_by(Product.stock_quantity.desc())
    elif sort_by == 'price':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'sales':
        # Sort by total sales (requires subquery)
        sales_subquery = db.session.query(
            SaleItem.product_id,
            func.sum(SaleItem.quantity).label('total_sold')
        ).join(Sale).filter(
            Sale.status == 'completed'
        ).group_by(SaleItem.product_id).subquery()

        query = query.outerjoin(
            sales_subquery, Product.id == sales_subquery.c.product_id
        ).order_by(sales_subquery.c.total_sold.desc().nullslast())

    # Paginate results
    products = query.paginate(
        page=page, per_page=50, error_out=False
    )

    # Get categories for filter dropdown
    categories = Category.query.order_by(
        Category.name_ar if language == 'ar' else Category.name_en
    ).all()

    # Calculate summary statistics
    all_products = Product.query.all()
    summary = {
        'total_products': len(all_products),
        'total_stock_value': sum(p.price * p.stock_quantity for p in all_products),
        'low_stock_count': len([p for p in all_products if 0 < p.stock_quantity <= (p.min_stock_level or 0)]),
        'out_of_stock_count': len([p for p in all_products if p.stock_quantity <= 0])
    }

    return render_template('reports/products_report.html',
                         products=products,
                         categories=categories,
                         summary=summary,
                         language=language)

@reports_bp.route('/inventory')
@login_required
@permission_required('reports')
def inventory_report():
    """Inventory status report"""
    language = get_user_language()
    
    # Get all products with stock information
    products = Product.query.filter_by(is_active=True, track_inventory=True).all()
    
    # Calculate inventory value
    total_inventory_value = sum(
        product.current_stock * product.cost_price for product in products
    )
    
    # Count stock status
    low_stock_count = sum(1 for product in products if product.is_low_stock())
    out_of_stock_count = sum(1 for product in products if product.is_out_of_stock())
    
    return render_template('reports/inventory_report.html',
                         products=products,
                         total_inventory_value=total_inventory_value,
                         low_stock_count=low_stock_count,
                         out_of_stock_count=out_of_stock_count,
                         language=language)

@reports_bp.route('/customers')
@login_required
@permission_required('reports')
def customers_report():
    """Customer analysis report"""
    language = get_user_language()
    
    # Get top customers by purchase amount
    top_customers = db.session.query(
        Customer,
        func.sum(Sale.total_amount).label('total_purchases'),
        func.count(Sale.id).label('purchase_count'),
        func.max(Sale.sale_date).label('last_purchase')
    ).join(Sale).filter(
        Sale.status == 'completed'
    ).group_by(Customer.id).order_by(
        func.sum(Sale.total_amount).desc()
    ).limit(20).all()
    
    return render_template('reports/customers_report.html',
                         top_customers=top_customers,
                         language=language)

@reports_bp.route('/api/sales-chart')
@login_required
@permission_required('reports')
def api_sales_chart():
    """API endpoint for sales chart data"""
    period = request.args.get('period', 'week')  # week, month, year
    
    if period == 'week':
        days = 7
        date_format = '%Y-%m-%d'
    elif period == 'month':
        days = 30
        date_format = '%Y-%m-%d'
    else:  # year
        days = 365
        date_format = '%Y-%m'
    
    start_date = datetime.now() - timedelta(days=days)
    
    if period == 'year':
        # Group by month for yearly view
        sales_data = db.session.query(
            func.date_format(Sale.sale_date, '%Y-%m').label('period'),
            func.sum(Sale.total_amount).label('total'),
            func.count(Sale.id).label('count')
        ).filter(
            Sale.sale_date >= start_date,
            Sale.status == 'completed'
        ).group_by(func.date_format(Sale.sale_date, '%Y-%m')).all()
    else:
        # Group by day
        sales_data = db.session.query(
            func.date(Sale.sale_date).label('period'),
            func.sum(Sale.total_amount).label('total'),
            func.count(Sale.id).label('count')
        ).filter(
            Sale.sale_date >= start_date,
            Sale.status == 'completed'
        ).group_by(func.date(Sale.sale_date)).all()
    
    # Format data for chart
    chart_data = {
        'labels': [],
        'sales': [],
        'transactions': []
    }
    
    for data in sales_data:
        if period == 'year':
            chart_data['labels'].append(data.period)
        else:
            chart_data['labels'].append(data.period.strftime('%Y-%m-%d'))
        chart_data['sales'].append(float(data.total))
        chart_data['transactions'].append(data.count)
    
    return jsonify(chart_data)

# Placeholder routes for missing report endpoints
@reports_bp.route('/daily-sales')
@login_required
@permission_required('reports')
def daily_sales():
    """Daily sales report"""
    language = get_user_language()

    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.daily_sales'))

    # Get daily sales data
    daily_sales_raw = db.session.query(
        func.date(Sale.sale_date).label('sale_date'),
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(func.date(Sale.sale_date)).order_by(
        func.date(Sale.sale_date).desc()
    ).all()

    # Convert string dates to datetime objects for template
    daily_sales = []
    for day in daily_sales_raw:
        try:
            # Convert string date to datetime object
            if isinstance(day.sale_date, str):
                sale_date = datetime.strptime(day.sale_date, '%Y-%m-%d').date()
            else:
                sale_date = day.sale_date

            # Create a simple object that mimics the original structure
            class DayData:
                def __init__(self, sale_date, total_sales, transaction_count, avg_transaction):
                    self.sale_date = sale_date
                    self.total_sales = total_sales or 0
                    self.transaction_count = transaction_count or 0
                    self.avg_transaction = avg_transaction or 0

            daily_sales.append(DayData(sale_date, day.total_sales, day.transaction_count, day.avg_transaction))
        except (ValueError, AttributeError):
            # Skip invalid dates
            continue

    # Calculate totals
    total_sales = sum(day.total_sales for day in daily_sales)
    total_transactions = sum(day.transaction_count for day in daily_sales)
    avg_daily_sales = total_sales / len(daily_sales) if daily_sales else 0

    return render_template('reports/daily_sales.html',
                         daily_sales=daily_sales,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         avg_daily_sales=avg_daily_sales,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@reports_bp.route('/monthly-sales')
@login_required
@permission_required('reports')
def monthly_sales():
    """Monthly sales report"""
    language = get_user_language()

    # Get year from request, default to current year
    year = request.args.get('year', datetime.now().year, type=int)

    # Get monthly sales data for the year
    monthly_sales = db.session.query(
        func.extract('month', Sale.sale_date).label('month'),
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        func.extract('year', Sale.sale_date) == year,
        Sale.status == 'completed'
    ).group_by(func.extract('month', Sale.sale_date)).order_by(
        func.extract('month', Sale.sale_date)
    ).all()

    # Create month names
    month_names_ar = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_names_en = ['January', 'February', 'March', 'April', 'May', 'June',
                      'July', 'August', 'September', 'October', 'November', 'December']

    # Format data with month names
    formatted_data = []
    for month_data in monthly_sales:
        month_num = int(month_data.month)
        formatted_data.append({
            'month_num': month_num,
            'month_name': month_names_ar[month_num-1] if language == 'ar' else month_names_en[month_num-1],
            'total_sales': month_data.total_sales,
            'transaction_count': month_data.transaction_count,
            'avg_transaction': month_data.avg_transaction
        })

    # Calculate totals
    total_sales = sum(month.total_sales for month in monthly_sales)
    total_transactions = sum(month.transaction_count for month in monthly_sales)
    avg_monthly_sales = total_sales / len(monthly_sales) if monthly_sales else 0

    return render_template('reports/monthly_sales.html',
                         monthly_sales=formatted_data,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         avg_monthly_sales=avg_monthly_sales,
                         year=year,
                         language=language)

@reports_bp.route('/sales-by-payment')
@login_required
@permission_required('reports')
def sales_by_payment():
    """Sales by payment method report"""
    language = get_user_language()

    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = today.replace(day=1).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.sales_by_payment'))

    # Get sales by payment method
    payment_sales = db.session.query(
        Sale.payment_method,
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(Sale.payment_method).order_by(
        func.sum(Sale.total_amount).desc()
    ).all()

    # Payment method translations
    payment_methods = {
        'cash': {'ar': 'نقداً', 'en': 'Cash'},
        'card': {'ar': 'بطاقة', 'en': 'Card'},
        'bank_transfer': {'ar': 'تحويل بنكي', 'en': 'Bank Transfer'},
        'mobile_payment': {'ar': 'دفع محمول', 'en': 'Mobile Payment'},
        'credit': {'ar': 'آجل', 'en': 'Credit'}
    }

    # Format data with translations
    formatted_data = []
    for payment_data in payment_sales:
        method = payment_data.payment_method
        method_name = payment_methods.get(method, {'ar': method, 'en': method})[language]
        formatted_data.append({
            'method': method,
            'method_name': method_name,
            'total_sales': payment_data.total_sales,
            'transaction_count': payment_data.transaction_count,
            'avg_transaction': payment_data.avg_transaction
        })

    # Calculate totals
    total_sales = sum(payment.total_sales for payment in payment_sales)
    total_transactions = sum(payment.transaction_count for payment in payment_sales)

    return render_template('reports/sales_by_payment.html',
                         payment_sales=formatted_data,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@reports_bp.route('/top-selling')
@login_required
@permission_required('reports')
def top_selling():
    """Top selling products report"""
    language = get_user_language()

    # Get date range and limit from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    limit = request.args.get('limit', 20, type=int)

    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.top_selling'))

    # Get top selling products
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_sold'),
        func.sum(SaleItem.total_price).label('total_revenue'),
        func.count(SaleItem.id).label('transaction_count'),
        func.avg(SaleItem.quantity).label('avg_quantity')
    ).join(SaleItem).join(Sale).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(Product.id).order_by(
        func.sum(SaleItem.quantity).desc()
    ).limit(limit).all()

    # Calculate totals
    total_quantity = sum(product.total_sold for product in top_products)
    total_revenue = sum(product.total_revenue for product in top_products)

    return render_template('reports/top_selling.html',
                         top_products=top_products,
                         total_quantity=total_quantity,
                         total_revenue=total_revenue,
                         date_from=date_from,
                         date_to=date_to,
                         limit=limit,
                         language=language)

@reports_bp.route('/slow-moving')
@login_required
@permission_required('reports')
def slow_moving():
    """Slow moving products report"""
    language = get_user_language()

    # Get parameters
    days_threshold = request.args.get('days', 30, type=int)  # Days without sales
    stock_threshold = request.args.get('stock', 0, type=int)  # Minimum stock level

    # Calculate date threshold
    date_threshold = datetime.now() - timedelta(days=days_threshold)

    # Get products with no sales in the specified period
    slow_products = db.session.query(Product).outerjoin(
        SaleItem, and_(
            SaleItem.product_id == Product.id,
            SaleItem.created_at >= date_threshold
        )
    ).join(Sale, Sale.id == SaleItem.sale_id, isouter=True).filter(
        Product.is_active == True,
        Product.current_stock > stock_threshold,
        or_(SaleItem.id.is_(None), Sale.status != 'completed')
    ).group_by(Product.id).all()

    # Get last sale date for each product
    products_with_last_sale = []
    for product in slow_products:
        last_sale = db.session.query(func.max(Sale.sale_date)).join(
            SaleItem
        ).filter(
            SaleItem.product_id == product.id,
            Sale.status == 'completed'
        ).scalar()

        days_since_sale = (datetime.now().date() - last_sale).days if last_sale else 999999

        products_with_last_sale.append({
            'product': product,
            'last_sale_date': last_sale,
            'days_since_sale': days_since_sale,
            'stock_value': product.current_stock * product.cost_price
        })

    # Sort by days since last sale (descending)
    products_with_last_sale.sort(key=lambda x: x['days_since_sale'] or 999999, reverse=True)

    # Calculate totals
    total_products = len(products_with_last_sale)
    total_stock_value = sum(p['stock_value'] for p in products_with_last_sale)
    total_stock_quantity = sum(p['product'].current_stock for p in products_with_last_sale)

    return render_template('reports/slow_moving.html',
                         slow_products=products_with_last_sale,
                         total_products=total_products,
                         total_stock_value=total_stock_value,
                         total_stock_quantity=total_stock_quantity,
                         days_threshold=days_threshold,
                         stock_threshold=stock_threshold,
                         language=language)

@reports_bp.route('/profit-analysis')
@login_required
@permission_required('reports')
def profit_analysis():
    """Profit analysis report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/stock-valuation')
@login_required
@permission_required('reports')
def stock_valuation():
    """Stock valuation report"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category', type=int)
    valuation_method = request.args.get('method', 'cost')  # cost, selling, average

    # Base query
    query = Product.query.filter(Product.is_active == True)

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    products = query.all()

    # Calculate valuations
    valuation_data = []
    total_cost_value = 0
    total_selling_value = 0
    total_quantity = 0

    for product in products:
        if product.current_stock > 0:
            cost_value = product.current_stock * product.cost_price
            selling_value = product.current_stock * product.selling_price
            avg_value = (cost_value + selling_value) / 2

            valuation_data.append({
                'product': product,
                'quantity': product.current_stock,
                'cost_price': product.cost_price,
                'selling_price': product.selling_price,
                'cost_value': cost_value,
                'selling_value': selling_value,
                'avg_value': avg_value,
                'profit_margin': selling_value - cost_value,
                'profit_percentage': ((selling_value - cost_value) / cost_value * 100) if cost_value > 0 else 0
            })

            total_cost_value += cost_value
            total_selling_value += selling_value
            total_quantity += product.current_stock

    # Sort by value (descending)
    if valuation_method == 'selling':
        valuation_data.sort(key=lambda x: x['selling_value'], reverse=True)
    elif valuation_method == 'average':
        valuation_data.sort(key=lambda x: x['avg_value'], reverse=True)
    else:  # cost
        valuation_data.sort(key=lambda x: x['cost_value'], reverse=True)

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).all()

    # Calculate summary
    total_profit = total_selling_value - total_cost_value
    avg_profit_margin = (total_profit / total_cost_value * 100) if total_cost_value > 0 else 0

    return render_template('reports/stock_valuation.html',
                         valuation_data=valuation_data,
                         categories=categories,
                         total_cost_value=total_cost_value,
                         total_selling_value=total_selling_value,
                         total_quantity=total_quantity,
                         total_profit=total_profit,
                         avg_profit_margin=avg_profit_margin,
                         category_id=category_id,
                         valuation_method=valuation_method,
                         language=language)

@reports_bp.route('/stock-movement')
@login_required
@permission_required('reports')
def stock_movement():
    """Stock movement report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/reorder-report')
@login_required
@permission_required('reports')
def reorder_report():
    """Reorder report"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category', type=int)
    urgency = request.args.get('urgency', 'all')  # all, critical, low, out

    # Base query for products that need reordering
    query = Product.query.filter(Product.is_active == True)

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    all_products = query.all()

    # Categorize products by stock status
    reorder_data = {
        'out_of_stock': [],
        'critical': [],
        'low_stock': [],
        'normal': []
    }

    for product in all_products:
        if product.current_stock <= 0:
            category = 'out_of_stock'
        elif product.current_stock <= product.minimum_stock * 0.5:  # Critical (50% of minimum)
            category = 'critical'
        elif product.current_stock <= product.minimum_stock:
            category = 'low_stock'
        else:
            category = 'normal'

        # Calculate suggested order quantity
        suggested_qty = max(product.maximum_stock - product.current_stock, 0)

        # Calculate days of stock remaining (based on average daily sales)
        avg_daily_sales = db.session.query(
            func.avg(func.coalesce(SaleItem.quantity, 0))
        ).join(Sale).filter(
            SaleItem.product_id == product.id,
            Sale.status == 'completed',
            Sale.sale_date >= datetime.now() - timedelta(days=30)
        ).scalar() or 0

        days_remaining = (product.current_stock / avg_daily_sales) if avg_daily_sales > 0 else 999

        product_data = {
            'product': product,
            'suggested_qty': suggested_qty,
            'avg_daily_sales': avg_daily_sales,
            'days_remaining': days_remaining,
            'reorder_cost': suggested_qty * product.cost_price
        }

        reorder_data[category].append(product_data)

    # Filter by urgency
    if urgency == 'critical':
        filtered_products = reorder_data['critical']
    elif urgency == 'low':
        filtered_products = reorder_data['low_stock']
    elif urgency == 'out':
        filtered_products = reorder_data['out_of_stock']
    else:  # all
        filtered_products = (reorder_data['out_of_stock'] +
                           reorder_data['critical'] +
                           reorder_data['low_stock'])

    # Sort by urgency (out of stock first, then by days remaining)
    filtered_products.sort(key=lambda x: (
        0 if x['product'].current_stock <= 0 else 1,
        x['days_remaining']
    ))

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).all()

    # Calculate totals
    total_products = len(filtered_products)
    total_reorder_cost = sum(p['reorder_cost'] for p in filtered_products)
    total_suggested_qty = sum(p['suggested_qty'] for p in filtered_products)

    return render_template('reports/reorder_report.html',
                         reorder_products=filtered_products,
                         reorder_counts=reorder_data,
                         categories=categories,
                         total_products=total_products,
                         total_reorder_cost=total_reorder_cost,
                         total_suggested_qty=total_suggested_qty,
                         category_id=category_id,
                         urgency=urgency,
                         language=language)

@reports_bp.route('/customer-purchases')
@login_required
@permission_required('reports')
def customer_purchases():
    """Customer purchases report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/customer-loyalty')
@login_required
@permission_required('reports')
def customer_loyalty():
    """Customer loyalty report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/profit-loss')
@login_required
@permission_required('reports')
def profit_loss():
    """Profit & Loss report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/cash-flow')
@login_required
@permission_required('reports')
def cash_flow():
    """Cash flow report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/tax-report')
@login_required
@permission_required('reports')
def tax_report():
    """Tax report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/quick-report')
@login_required
@permission_required('reports')
def quick_report():
    """Quick report generator - placeholder"""
    language = get_user_language()
    flash('مولد التقارير السريع قيد التطوير' if language == 'ar' else 'Quick report generator is under development', 'info')
    return redirect(url_for('reports.index'))
