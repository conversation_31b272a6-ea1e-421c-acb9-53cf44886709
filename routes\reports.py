"""
Reports routes for Qatar POS System
Handles various business reports and analytics
"""

from flask import Blueprint, render_template, request, jsonify, make_response, flash, redirect, url_for
from flask_login import login_required
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from models.sale import Sale, SaleItem
from models.product import Product
from models.category import Category
from models.customer import Customer
from models.user import User
from models.inventory_movement import InventoryMovement
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language, format_currency

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
@permission_required('reports')
def index():
    """Reports dashboard"""
    language = get_user_language()

    # Get today's statistics
    today = datetime.now().date()

    # Today's sales total
    today_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).scalar() or 0

    # Today's transactions count
    today_transactions = db.session.query(func.count(Sale.id)).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).scalar() or 0

    # Today's customers count
    today_customers = db.session.query(func.count(func.distinct(Sale.customer_id))).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed',
        Sale.customer_id.isnot(None)
    ).scalar() or 0

    # Today's average transaction
    today_avg_transaction = today_sales / today_transactions if today_transactions > 0 else 0

    today_stats = {
        'sales': today_sales,
        'transactions': today_transactions,
        'customers': today_customers,
        'avg_transaction': today_avg_transaction
    }

    # This month's statistics
    this_month_start = today.replace(day=1)
    month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.sale_date >= this_month_start,
        Sale.status == 'completed'
    ).scalar() or 0

    month_stats = {
        'sales': month_sales
    }

    # Total customers count
    total_customers = db.session.query(func.count(Customer.id)).filter(
        Customer.is_active == True
    ).scalar() or 0

    return render_template('reports/index.html',
                         language=language,
                         today_stats=today_stats,
                         month_stats=month_stats,
                         total_customers=total_customers)

@reports_bp.route('/sales')
@login_required
@permission_required('reports')
def sales_report():
    """Sales report"""
    language = get_user_language()
    
    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    seller_id = request.args.get('seller_id', type=int)
    
    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = today.replace(day=1).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')
    
    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.sales_report'))
    
    # Build query
    query = Sale.query.filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    )
    
    if seller_id:
        query = query.filter(Sale.seller_id == seller_id)
    
    sales = query.all()
    
    # Calculate totals
    total_sales = sum(sale.total_amount for sale in sales)
    total_transactions = len(sales)
    average_transaction = total_sales / total_transactions if total_transactions > 0 else 0
    
    # Get sellers for filter
    sellers = User.query.filter(User.role.in_(['seller', 'manager'])).all()
    
    # Create summary object for template
    summary = {
        'total_sales': total_sales,
        'total_transactions': total_transactions,
        'average_transaction': average_transaction,
        'total_discounts': 0  # Calculate if needed
    }

    return render_template('reports/sales_report.html',
                         sales=sales,
                         summary=summary,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         average_transaction=average_transaction,
                         sellers=sellers,
                         date_from=date_from,
                         date_to=date_to,
                         seller_id=seller_id,
                         language=language)

@reports_bp.route('/products')
@login_required
@permission_required('reports')
def products_report():
    """Products inventory and performance report"""
    language = get_user_language()

    # Get filters
    category_id = request.args.get('category_id', '')
    stock_status = request.args.get('stock_status', '')
    sort_by = request.args.get('sort_by', 'name')
    page = request.args.get('page', 1, type=int)

    # Base query
    query = Product.query

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    # Apply stock status filter
    if stock_status == 'in_stock':
        query = query.filter(Product.current_stock > Product.minimum_stock)
    elif stock_status == 'low_stock':
        query = query.filter(
            Product.current_stock <= Product.minimum_stock,
            Product.current_stock > 0
        )
    elif stock_status == 'out_of_stock':
        query = query.filter(Product.current_stock <= 0)

    # Apply sorting
    if sort_by == 'name':
        query = query.order_by(Product.name_ar if language == 'ar' else Product.name_en)
    elif sort_by == 'stock':
        query = query.order_by(Product.current_stock.desc())
    elif sort_by == 'price':
        query = query.order_by(Product.selling_price.desc())
    elif sort_by == 'profit':
        # Order by unit profit (selling_price - cost_price)
        query = query.order_by((Product.selling_price - Product.cost_price).desc())
    elif sort_by == 'profit_margin':
        # Order by profit margin percentage
        query = query.order_by(((Product.selling_price - Product.cost_price) / Product.cost_price * 100).desc())
    elif sort_by == 'sales':
        # Sort by total sales (requires subquery)
        sales_subquery = db.session.query(
            SaleItem.product_id,
            func.sum(SaleItem.quantity).label('total_sold')
        ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).filter(
            Sale.status == 'completed'
        ).group_by(SaleItem.product_id).subquery()

        query = query.outerjoin(
            sales_subquery, Product.id == sales_subquery.c.product_id
        ).order_by(sales_subquery.c.total_sold.desc().nullslast())

    # Paginate results
    products = query.paginate(
        page=page, per_page=50, error_out=False
    )

    # Get categories for filter dropdown
    categories = Category.query.order_by(
        Category.name_ar if language == 'ar' else Category.name_en
    ).all()

    # Calculate summary statistics with advanced profit analysis
    all_products = Product.query.all()

    # Calculate profit metrics
    total_cost_value = sum(p.cost_price * p.current_stock for p in all_products if p.cost_price and p.current_stock > 0)
    total_selling_value = sum(p.selling_price * p.current_stock for p in all_products if p.current_stock > 0)
    total_potential_profit = total_selling_value - total_cost_value
    profit_margin_percentage = (total_potential_profit / total_cost_value * 100) if total_cost_value > 0 else 0

    # Calculate actual sales profit (from completed sales)
    actual_sales_data = db.session.query(
        func.sum(SaleItem.quantity * (SaleItem.unit_price - Product.cost_price)).label('actual_profit'),
        func.sum(SaleItem.total_price).label('actual_revenue'),
        func.sum(SaleItem.quantity * Product.cost_price).label('actual_cost')
    ).select_from(SaleItem).join(Product, SaleItem.product_id == Product.id).join(Sale, SaleItem.sale_id == Sale.id).filter(
        Sale.status == 'completed',
        Product.cost_price.isnot(None)
    ).first()

    actual_profit = actual_sales_data.actual_profit or 0
    actual_revenue = actual_sales_data.actual_revenue or 0
    actual_cost = actual_sales_data.actual_cost or 0
    actual_profit_margin = (actual_profit / actual_cost * 100) if actual_cost > 0 else 0

    # Calculate profit for each product in the filtered results
    products_with_profit = []
    for product in products.items:
        if product.cost_price and product.current_stock > 0:
            unit_profit = product.selling_price - product.cost_price
            total_profit = unit_profit * product.current_stock
            profit_margin = (unit_profit / product.cost_price * 100) if product.cost_price > 0 else 0
        else:
            unit_profit = 0
            total_profit = 0
            profit_margin = 0

        # Get actual sales data for this product
        product_sales = db.session.query(
            func.sum(SaleItem.quantity).label('total_sold'),
            func.sum(SaleItem.total_price).label('total_revenue'),
            func.sum(SaleItem.quantity * product.cost_price).label('total_cost') if product.cost_price else func.sum(0).label('total_cost')
        ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).filter(
            SaleItem.product_id == product.id,
            Sale.status == 'completed'
        ).first()

        actual_sold = product_sales.total_sold or 0
        actual_revenue = product_sales.total_revenue or 0
        actual_cost = product_sales.total_cost or 0
        actual_profit = actual_revenue - actual_cost

        # Add profit data to product
        product.unit_profit = unit_profit
        product.total_profit = total_profit
        product.profit_margin = profit_margin
        product.actual_sold = actual_sold
        product.actual_revenue = actual_revenue
        product.actual_profit = actual_profit
        product.actual_profit_margin = (actual_profit / actual_cost * 100) if actual_cost > 0 else 0
        products_with_profit.append(product)

    # Profit analysis by categories
    profit_by_category = {}
    for category in Category.query.all():
        category_products = [p for p in all_products if p.category_id == category.id]
        if category_products:
            cat_cost_value = sum(p.cost_price * p.current_stock for p in category_products if p.cost_price and p.current_stock > 0)
            cat_selling_value = sum(p.selling_price * p.current_stock for p in category_products if p.current_stock > 0)
            cat_potential_profit = cat_selling_value - cat_cost_value

            profit_by_category[category.id] = {
                'category': category,
                'cost_value': cat_cost_value,
                'selling_value': cat_selling_value,
                'potential_profit': cat_potential_profit,
                'profit_margin': (cat_potential_profit / cat_cost_value * 100) if cat_cost_value > 0 else 0,
                'products_count': len(category_products)
            }

    # Profit performance metrics
    high_profit_products = len([p for p in all_products if p.cost_price and (p.selling_price - p.cost_price) / float(p.cost_price) > 0.3])
    medium_profit_products = len([p for p in all_products if p.cost_price and 0.15 <= (p.selling_price - p.cost_price) / float(p.cost_price) <= 0.3])
    low_profit_products = len([p for p in all_products if p.cost_price and 0 < (p.selling_price - p.cost_price) / float(p.cost_price) < 0.15])
    loss_products = len([p for p in all_products if p.cost_price and (p.selling_price - p.cost_price) <= 0])

    summary = {
        'total_products': len(all_products),
        'total_stock_value': total_selling_value,
        'total_cost_value': total_cost_value,
        'total_potential_profit': total_potential_profit,
        'profit_margin_percentage': profit_margin_percentage,
        'actual_profit': actual_profit,
        'actual_revenue': actual_revenue,
        'actual_cost': actual_cost,
        'actual_profit_margin': actual_profit_margin,
        'high_profit_products': high_profit_products,
        'medium_profit_products': medium_profit_products,
        'low_profit_products': low_profit_products,
        'loss_products': loss_products,
        'low_stock_count': len([p for p in all_products if 0 < p.current_stock <= (p.minimum_stock or 0)]),
        'out_of_stock_count': len([p for p in all_products if p.current_stock <= 0])
    }

    return render_template('reports/products_report.html',
                         products=products,
                         categories=categories,
                         summary=summary,
                         profit_by_category=profit_by_category,
                         language=language)

@reports_bp.route('/inventory')
@login_required
@permission_required('reports')
def inventory_report():
    """Inventory status report with category breakdown"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category_id', type=int)
    stock_status = request.args.get('stock_status', '')

    # Base query for products
    query = Product.query.filter_by(is_active=True, track_inventory=True)

    # Apply category filter
    if category_id:
        query = query.filter_by(category_id=category_id)

    # Apply stock status filter
    if stock_status == 'low_stock':
        query = query.filter(Product.current_stock <= Product.minimum_stock, Product.current_stock > 0)
    elif stock_status == 'out_of_stock':
        query = query.filter(Product.current_stock <= 0)
    elif stock_status == 'in_stock':
        query = query.filter(Product.current_stock > Product.minimum_stock)

    products = query.all()

    # Get all categories for filter dropdown
    categories = Category.query.filter_by(is_active=True).order_by(
        Category.name_ar if language == 'ar' else Category.name_en
    ).all()

    # Calculate inventory statistics by category
    category_stats = {}
    all_products = Product.query.filter_by(is_active=True, track_inventory=True).all()

    for category in categories:
        category_products = [p for p in all_products if p.category_id == category.id]
        if category_products:
            total_value = sum(p.current_stock * (p.cost_price or 0) for p in category_products)
            total_selling_value = sum(p.current_stock * p.selling_price for p in category_products)
            total_quantity = sum(p.current_stock for p in category_products)
            low_stock = sum(1 for p in category_products if p.is_low_stock())
            out_of_stock = sum(1 for p in category_products if p.is_out_of_stock())

            category_stats[category.id] = {
                'category': category,
                'products_count': len(category_products),
                'total_quantity': total_quantity,
                'total_cost_value': total_value,
                'total_selling_value': total_selling_value,
                'potential_profit': total_selling_value - total_value,
                'low_stock_count': low_stock,
                'out_of_stock_count': out_of_stock,
                'in_stock_count': len(category_products) - low_stock - out_of_stock
            }

    # Calculate overall totals
    total_inventory_value = sum(p.current_stock * (p.cost_price or 0) for p in products)
    total_selling_value = sum(p.current_stock * p.selling_price for p in products)
    total_potential_profit = total_selling_value - total_inventory_value
    low_stock_count = sum(1 for p in products if p.is_low_stock())
    out_of_stock_count = sum(1 for p in products if p.is_out_of_stock())

    return render_template('reports/inventory_report.html',
                         products=products,
                         categories=categories,
                         category_stats=category_stats,
                         total_inventory_value=total_inventory_value,
                         total_selling_value=total_selling_value,
                         total_potential_profit=total_potential_profit,
                         low_stock_count=low_stock_count,
                         out_of_stock_count=out_of_stock_count,
                         selected_category=category_id,
                         selected_stock_status=stock_status,
                         language=language)

@reports_bp.route('/customers')
@login_required
@permission_required('reports')
def customers_report():
    """Customer analysis report"""
    language = get_user_language()
    
    # Get top customers by purchase amount
    top_customers = db.session.query(
        Customer,
        func.sum(Sale.total_amount).label('total_purchases'),
        func.count(Sale.id).label('purchase_count'),
        func.max(Sale.sale_date).label('last_purchase')
    ).select_from(Customer).join(Sale, Customer.id == Sale.customer_id).filter(
        Sale.status == 'completed'
    ).group_by(Customer.id).order_by(
        func.sum(Sale.total_amount).desc()
    ).limit(20).all()
    
    return render_template('reports/customers_report.html',
                         top_customers=top_customers,
                         language=language)

@reports_bp.route('/api/sales-chart')
@login_required
@permission_required('reports')
def api_sales_chart():
    """API endpoint for sales chart data"""
    period = request.args.get('period', 'week')  # week, month, year
    
    if period == 'week':
        days = 7
        date_format = '%Y-%m-%d'
    elif period == 'month':
        days = 30
        date_format = '%Y-%m-%d'
    else:  # year
        days = 365
        date_format = '%Y-%m'
    
    start_date = datetime.now() - timedelta(days=days)
    
    if period == 'year':
        # Group by month for yearly view
        sales_data = db.session.query(
            func.date_format(Sale.sale_date, '%Y-%m').label('period'),
            func.sum(Sale.total_amount).label('total'),
            func.count(Sale.id).label('count')
        ).filter(
            Sale.sale_date >= start_date,
            Sale.status == 'completed'
        ).group_by(func.date_format(Sale.sale_date, '%Y-%m')).all()
    else:
        # Group by day
        sales_data = db.session.query(
            func.date(Sale.sale_date).label('period'),
            func.sum(Sale.total_amount).label('total'),
            func.count(Sale.id).label('count')
        ).filter(
            Sale.sale_date >= start_date,
            Sale.status == 'completed'
        ).group_by(func.date(Sale.sale_date)).all()
    
    # Format data for chart
    chart_data = {
        'labels': [],
        'sales': [],
        'transactions': []
    }
    
    for data in sales_data:
        if period == 'year':
            chart_data['labels'].append(data.period)
        else:
            chart_data['labels'].append(data.period.strftime('%Y-%m-%d'))
        chart_data['sales'].append(float(data.total))
        chart_data['transactions'].append(data.count)
    
    return jsonify(chart_data)

# Placeholder routes for missing report endpoints
@reports_bp.route('/daily-sales')
@login_required
@permission_required('reports')
def daily_sales():
    """Daily sales report"""
    language = get_user_language()

    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.daily_sales'))

    # Get daily sales data
    daily_sales_raw = db.session.query(
        func.date(Sale.sale_date).label('sale_date'),
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(func.date(Sale.sale_date)).order_by(
        func.date(Sale.sale_date).desc()
    ).all()

    # Convert string dates to datetime objects for template
    daily_sales = []
    for day in daily_sales_raw:
        try:
            # Convert string date to datetime object
            if isinstance(day.sale_date, str):
                sale_date = datetime.strptime(day.sale_date, '%Y-%m-%d').date()
            else:
                sale_date = day.sale_date

            # Create a simple object that mimics the original structure
            class DayData:
                def __init__(self, sale_date, total_sales, transaction_count, avg_transaction):
                    self.sale_date = sale_date
                    self.total_sales = total_sales or 0
                    self.transaction_count = transaction_count or 0
                    self.avg_transaction = avg_transaction or 0

            daily_sales.append(DayData(sale_date, day.total_sales, day.transaction_count, day.avg_transaction))
        except (ValueError, AttributeError):
            # Skip invalid dates
            continue

    # Calculate totals
    total_sales = sum(day.total_sales for day in daily_sales)
    total_transactions = sum(day.transaction_count for day in daily_sales)
    avg_daily_sales = total_sales / len(daily_sales) if daily_sales else 0

    return render_template('reports/daily_sales.html',
                         daily_sales=daily_sales,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         avg_daily_sales=avg_daily_sales,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@reports_bp.route('/monthly-sales')
@login_required
@permission_required('reports')
def monthly_sales():
    """Monthly sales report"""
    language = get_user_language()

    # Get year from request, default to current year
    year = request.args.get('year', datetime.now().year, type=int)

    # Get monthly sales data for the year
    monthly_sales = db.session.query(
        func.extract('month', Sale.sale_date).label('month'),
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        func.extract('year', Sale.sale_date) == year,
        Sale.status == 'completed'
    ).group_by(func.extract('month', Sale.sale_date)).order_by(
        func.extract('month', Sale.sale_date)
    ).all()

    # Create month names
    month_names_ar = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_names_en = ['January', 'February', 'March', 'April', 'May', 'June',
                      'July', 'August', 'September', 'October', 'November', 'December']

    # Format data with month names
    formatted_data = []
    for month_data in monthly_sales:
        month_num = int(month_data.month)
        formatted_data.append({
            'month_num': month_num,
            'month_name': month_names_ar[month_num-1] if language == 'ar' else month_names_en[month_num-1],
            'total_sales': month_data.total_sales,
            'transaction_count': month_data.transaction_count,
            'avg_transaction': month_data.avg_transaction
        })

    # Calculate totals
    total_sales = sum(month.total_sales for month in monthly_sales)
    total_transactions = sum(month.transaction_count for month in monthly_sales)
    avg_monthly_sales = total_sales / len(monthly_sales) if monthly_sales else 0

    return render_template('reports/monthly_sales.html',
                         monthly_sales=formatted_data,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         avg_monthly_sales=avg_monthly_sales,
                         year=year,
                         language=language)

@reports_bp.route('/sales-by-payment')
@login_required
@permission_required('reports')
def sales_by_payment():
    """Sales by payment method report"""
    language = get_user_language()

    # Get date range from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = today.replace(day=1).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.sales_by_payment'))

    # Get sales by payment method
    payment_sales = db.session.query(
        Sale.payment_method,
        func.sum(Sale.total_amount).label('total_sales'),
        func.count(Sale.id).label('transaction_count'),
        func.avg(Sale.total_amount).label('avg_transaction')
    ).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(Sale.payment_method).order_by(
        func.sum(Sale.total_amount).desc()
    ).all()

    # Payment method translations
    payment_methods = {
        'cash': {'ar': 'نقداً', 'en': 'Cash'},
        'card': {'ar': 'بطاقة', 'en': 'Card'},
        'bank_transfer': {'ar': 'تحويل بنكي', 'en': 'Bank Transfer'},
        'mobile_payment': {'ar': 'دفع محمول', 'en': 'Mobile Payment'},
        'credit': {'ar': 'آجل', 'en': 'Credit'}
    }

    # Format data with translations
    formatted_data = []
    for payment_data in payment_sales:
        method = payment_data.payment_method
        method_name = payment_methods.get(method, {'ar': method, 'en': method})[language]
        formatted_data.append({
            'method': method,
            'method_name': method_name,
            'total_sales': payment_data.total_sales,
            'transaction_count': payment_data.transaction_count,
            'avg_transaction': payment_data.avg_transaction
        })

    # Calculate totals
    total_sales = sum(payment.total_sales for payment in payment_sales)
    total_transactions = sum(payment.transaction_count for payment in payment_sales)

    return render_template('reports/sales_by_payment.html',
                         payment_sales=formatted_data,
                         total_sales=total_sales,
                         total_transactions=total_transactions,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@reports_bp.route('/top-selling')
@login_required
@permission_required('reports')
def top_selling():
    """Top selling products report"""
    language = get_user_language()

    # Get date range and limit from request
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    limit = request.args.get('limit', 20, type=int)

    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        today = datetime.now()
        date_from = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        date_to = today.strftime('%Y-%m-%d')

    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d')
        to_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
    except ValueError:
        flash('تاريخ غير صحيح' if language == 'ar' else 'Invalid date format', 'error')
        return redirect(url_for('reports.top_selling'))

    # Get top selling products
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_sold'),
        func.sum(SaleItem.total_price).label('total_revenue'),
        func.count(SaleItem.id).label('transaction_count'),
        func.avg(SaleItem.quantity).label('avg_quantity')
    ).select_from(Product).join(SaleItem, Product.id == SaleItem.product_id).join(Sale, SaleItem.sale_id == Sale.id).filter(
        Sale.sale_date >= from_date,
        Sale.sale_date < to_date,
        Sale.status == 'completed'
    ).group_by(Product.id).order_by(
        func.sum(SaleItem.quantity).desc()
    ).limit(limit).all()

    # Calculate totals
    total_quantity = sum(product.total_sold for product in top_products)
    total_revenue = sum(product.total_revenue for product in top_products)

    return render_template('reports/top_selling.html',
                         top_products=top_products,
                         total_quantity=total_quantity,
                         total_revenue=total_revenue,
                         date_from=date_from,
                         date_to=date_to,
                         limit=limit,
                         language=language)

@reports_bp.route('/slow-moving')
@login_required
@permission_required('reports')
def slow_moving():
    """Slow moving products report"""
    language = get_user_language()

    # Get parameters
    days_threshold = request.args.get('days', 30, type=int)  # Days without sales
    stock_threshold = request.args.get('stock', 0, type=int)  # Minimum stock level

    # Calculate date threshold
    date_threshold = datetime.now() - timedelta(days=days_threshold)

    # Get products with no sales in the specified period
    slow_products = db.session.query(Product).outerjoin(
        SaleItem, and_(
            SaleItem.product_id == Product.id,
            SaleItem.created_at >= date_threshold
        )
    ).join(Sale, Sale.id == SaleItem.sale_id, isouter=True).filter(
        Product.is_active == True,
        Product.current_stock > stock_threshold,
        or_(SaleItem.id.is_(None), Sale.status != 'completed')
    ).group_by(Product.id).all()

    # Get last sale date for each product
    products_with_last_sale = []
    for product in slow_products:
        last_sale = db.session.query(func.max(Sale.sale_date)).select_from(Sale).join(
            SaleItem, Sale.id == SaleItem.sale_id
        ).filter(
            SaleItem.product_id == product.id,
            Sale.status == 'completed'
        ).scalar()

        if last_sale:
            if isinstance(last_sale, datetime):
                days_since_sale = (datetime.now().date() - last_sale.date()).days
            else:
                days_since_sale = (datetime.now().date() - last_sale).days
        else:
            days_since_sale = 999999

        products_with_last_sale.append({
            'product': product,
            'last_sale_date': last_sale,
            'days_since_sale': days_since_sale,
            'stock_value': product.current_stock * product.cost_price
        })

    # Sort by days since last sale (descending)
    products_with_last_sale.sort(key=lambda x: x['days_since_sale'] or 999999, reverse=True)

    # Calculate totals
    total_products = len(products_with_last_sale)
    total_stock_value = sum(p['stock_value'] for p in products_with_last_sale)
    total_stock_quantity = sum(p['product'].current_stock for p in products_with_last_sale)

    return render_template('reports/slow_moving.html',
                         slow_products=products_with_last_sale,
                         total_products=total_products,
                         total_stock_value=total_stock_value,
                         total_stock_quantity=total_stock_quantity,
                         days_threshold=days_threshold,
                         stock_threshold=stock_threshold,
                         language=language)

@reports_bp.route('/profit-analysis')
@login_required
@permission_required('reports')
def profit_analysis():
    """Profit analysis report - placeholder"""
    language = get_user_language()
    flash('هذا التقرير قيد التطوير' if language == 'ar' else 'This report is under development', 'info')
    return redirect(url_for('reports.index'))

@reports_bp.route('/stock-valuation')
@login_required
@permission_required('reports')
def stock_valuation():
    """Stock valuation report"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category', type=int)
    valuation_method = request.args.get('method', 'cost')  # cost, selling, average

    # Base query
    query = Product.query.filter(Product.is_active == True)

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    products = query.all()

    # Calculate valuations
    valuation_data = []
    total_cost_value = 0
    total_selling_value = 0
    total_quantity = 0

    for product in products:
        if product.current_stock > 0:
            cost_value = product.current_stock * product.cost_price
            selling_value = product.current_stock * product.selling_price
            avg_value = (cost_value + selling_value) / 2

            valuation_data.append({
                'product': product,
                'quantity': product.current_stock,
                'cost_price': product.cost_price,
                'selling_price': product.selling_price,
                'cost_value': cost_value,
                'selling_value': selling_value,
                'avg_value': avg_value,
                'profit_margin': selling_value - cost_value,
                'profit_percentage': ((selling_value - cost_value) / cost_value * 100) if cost_value > 0 else 0
            })

            total_cost_value += cost_value
            total_selling_value += selling_value
            total_quantity += product.current_stock

    # Sort by value (descending)
    if valuation_method == 'selling':
        valuation_data.sort(key=lambda x: x['selling_value'], reverse=True)
    elif valuation_method == 'average':
        valuation_data.sort(key=lambda x: x['avg_value'], reverse=True)
    else:  # cost
        valuation_data.sort(key=lambda x: x['cost_value'], reverse=True)

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).all()

    # Calculate summary
    total_profit = total_selling_value - total_cost_value
    avg_profit_margin = (total_profit / total_cost_value * 100) if total_cost_value > 0 else 0

    # Calculate total value based on selected method
    if valuation_method == 'selling':
        total_value = total_selling_value
    elif valuation_method == 'average':
        total_value = (total_cost_value + total_selling_value) / 2
    else:  # cost
        total_value = total_cost_value

    return render_template('reports/stock_valuation.html',
                         valuation_data=valuation_data,
                         categories=categories,
                         total_cost_value=total_cost_value,
                         total_selling_value=total_selling_value,
                         total_value=total_value,
                         total_quantity=total_quantity,
                         total_profit=total_profit,
                         avg_profit_margin=avg_profit_margin,
                         category_id=category_id,
                         valuation_method=valuation_method,
                         language=language)

@reports_bp.route('/stock-movement')
@login_required
@permission_required('reports')
def stock_movement():
    """Stock movement report"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category_id', type=int)
    product_id = request.args.get('product_id', type=int)
    movement_type = request.args.get('movement_type', '')
    user_id = request.args.get('user_id', type=int)
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Base query for movements
    query = InventoryMovement.query.join(Product).filter(Product.is_active == True)

    # Apply filters
    if category_id:
        query = query.filter(Product.category_id == category_id)

    if product_id:
        query = query.filter(InventoryMovement.product_id == product_id)

    if movement_type:
        query = query.filter(InventoryMovement.transaction_type == movement_type)

    if user_id:
        query = query.filter(InventoryMovement.user_id == user_id)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InventoryMovement.transaction_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # Add one day to include the entire end date
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(InventoryMovement.transaction_date <= date_to_obj)
        except ValueError:
            pass

    # Get movements ordered by date (most recent first)
    movements = query.order_by(InventoryMovement.transaction_date.desc()).limit(500).all()

    # Calculate summary statistics
    total_movements = query.count()

    # Movement type statistics
    movement_stats = {}
    for movement_type_enum in ['sale', 'purchase', 'adjustment', 'return', 'damage', 'transfer', 'initial']:
        count = query.filter(InventoryMovement.transaction_type == movement_type_enum).count()
        if count > 0:
            movement_stats[movement_type_enum] = count

    # Calculate value changes
    total_value_in = 0
    total_value_out = 0

    for movement in movements:
        if movement.quantity_change > 0:  # Stock increase
            total_value_in += abs(movement.total_cost or 0)
        else:  # Stock decrease
            total_value_out += abs(movement.total_cost or 0)

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_en).all()

    # Get products for filter (limit to 100 most recent)
    products = Product.query.filter_by(is_active=True, track_inventory=True)\
                     .order_by(Product.name_en).limit(100).all()

    # Get users for filter
    users = User.query.filter_by(is_active=True).order_by(User.username).all()

    return render_template('reports/stock_movement.html',
                         movements=movements,
                         total_movements=total_movements,
                         movement_stats=movement_stats,
                         total_value_in=total_value_in,
                         total_value_out=total_value_out,
                         categories=categories,
                         products=products,
                         users=users,
                         selected_category=category_id,
                         selected_product=product_id,
                         selected_movement_type=movement_type,
                         selected_user=user_id,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@reports_bp.route('/reorder-report')
@login_required
@permission_required('reports')
def reorder_report():
    """Reorder report"""
    language = get_user_language()

    # Get filter parameters
    category_id = request.args.get('category', type=int)
    urgency = request.args.get('urgency', 'all')  # all, critical, low, out

    # Base query for products that need reordering
    query = Product.query.filter(Product.is_active == True)

    # Apply category filter
    if category_id:
        query = query.filter(Product.category_id == category_id)

    all_products = query.all()

    # Categorize products by stock status
    reorder_data = {
        'out_of_stock': [],
        'critical': [],
        'low_stock': [],
        'normal': []
    }

    for product in all_products:
        if product.current_stock <= 0:
            category = 'out_of_stock'
        elif product.current_stock <= float(product.minimum_stock) * 0.5:  # Critical (50% of minimum)
            category = 'critical'
        elif product.current_stock <= product.minimum_stock:
            category = 'low_stock'
        else:
            category = 'normal'

        # Calculate suggested order quantity
        suggested_qty = max(product.maximum_stock - product.current_stock, 0)

        # Calculate days of stock remaining (based on average daily sales)
        avg_daily_sales = db.session.query(
            func.avg(func.coalesce(SaleItem.quantity, 0))
        ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).filter(
            SaleItem.product_id == product.id,
            Sale.status == 'completed',
            Sale.sale_date >= datetime.now() - timedelta(days=30)
        ).scalar() or 0

        days_remaining = (product.current_stock / avg_daily_sales) if avg_daily_sales > 0 else 999

        product_data = {
            'product': product,
            'suggested_qty': suggested_qty,
            'avg_daily_sales': avg_daily_sales,
            'days_remaining': days_remaining,
            'reorder_cost': suggested_qty * product.cost_price
        }

        reorder_data[category].append(product_data)

    # Filter by urgency
    if urgency == 'critical':
        filtered_products = reorder_data['critical']
    elif urgency == 'low':
        filtered_products = reorder_data['low_stock']
    elif urgency == 'out':
        filtered_products = reorder_data['out_of_stock']
    else:  # all
        filtered_products = (reorder_data['out_of_stock'] +
                           reorder_data['critical'] +
                           reorder_data['low_stock'])

    # Sort by urgency (out of stock first, then by days remaining)
    filtered_products.sort(key=lambda x: (
        0 if x['product'].current_stock <= 0 else 1,
        x['days_remaining']
    ))

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).all()

    # Calculate totals
    total_products = len(filtered_products)
    total_reorder_cost = sum(p['reorder_cost'] for p in filtered_products)
    total_suggested_qty = sum(p['suggested_qty'] for p in filtered_products)

    return render_template('reports/reorder_report.html',
                         reorder_products=filtered_products,
                         reorder_counts=reorder_data,
                         categories=categories,
                         total_products=total_products,
                         total_reorder_cost=total_reorder_cost,
                         total_suggested_qty=total_suggested_qty,
                         category_id=category_id,
                         urgency=urgency,
                         language=language)

@reports_bp.route('/customer-purchases')
@login_required
@permission_required('reports')
def customer_purchases():
    """Customer purchases report"""
    language = get_user_language()

    # Get filter parameters
    customer_id = request.args.get('customer_id', type=int)
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    min_amount = request.args.get('min_amount', type=float)

    # Base query for customer purchases
    query = db.session.query(
        Customer,
        func.sum(Sale.total_amount).label('total_purchases'),
        func.count(Sale.id).label('purchase_count'),
        func.avg(Sale.total_amount).label('avg_purchase'),
        func.max(Sale.sale_date).label('last_purchase'),
        func.min(Sale.sale_date).label('first_purchase')
    ).select_from(Customer).join(Sale, Customer.id == Sale.customer_id).filter(Sale.status == 'completed')

    # Apply filters
    if customer_id:
        query = query.filter(Customer.id == customer_id)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.sale_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Sale.sale_date <= date_to_obj)
        except ValueError:
            pass

    # Group by customer and order by total purchases
    customer_purchases = query.group_by(Customer.id).order_by(
        func.sum(Sale.total_amount).desc()
    ).all()

    # Filter by minimum amount if specified
    if min_amount:
        customer_purchases = [cp for cp in customer_purchases if cp[1] >= min_amount]

    # Get detailed purchases for selected customer
    detailed_purchases = []
    if customer_id:
        detailed_query = Sale.query.filter(
            Sale.customer_id == customer_id,
            Sale.status == 'completed'
        )

        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                detailed_query = detailed_query.filter(Sale.sale_date >= date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
                detailed_query = detailed_query.filter(Sale.sale_date <= date_to_obj)
            except ValueError:
                pass

        detailed_purchases = detailed_query.order_by(Sale.sale_date.desc()).limit(50).all()

    # Get all customers for filter dropdown
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.customer_code).all()

    # Calculate summary statistics
    total_customers = len(customer_purchases)
    total_revenue = sum(cp[1] for cp in customer_purchases)
    total_transactions = sum(cp[2] for cp in customer_purchases)
    avg_customer_value = total_revenue / total_customers if total_customers > 0 else 0

    return render_template('reports/customer_purchases.html',
                         customer_purchases=customer_purchases,
                         detailed_purchases=detailed_purchases,
                         customers=customers,
                         total_customers=total_customers,
                         total_revenue=total_revenue,
                         total_transactions=total_transactions,
                         avg_customer_value=avg_customer_value,
                         customer_id=customer_id,
                         date_from=date_from,
                         date_to=date_to,
                         min_amount=min_amount,
                         language=language)

@reports_bp.route('/customer-loyalty')
@login_required
@permission_required('reports')
def customer_loyalty():
    """Customer loyalty report"""
    language = get_user_language()

    # Get filter parameters
    period_months = request.args.get('period', type=int, default=12)
    min_purchases = request.args.get('min_purchases', type=int, default=2)

    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=period_months * 30)

    # Get customer loyalty data
    loyalty_query = db.session.query(
        Customer,
        func.sum(Sale.total_amount).label('total_spent'),
        func.count(Sale.id).label('purchase_count'),
        func.avg(Sale.total_amount).label('avg_purchase'),
        func.max(Sale.sale_date).label('last_purchase'),
        func.min(Sale.sale_date).label('first_purchase')
    ).select_from(Customer).join(Sale, Customer.id == Sale.customer_id).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(Customer.id).having(
        func.count(Sale.id) >= min_purchases
    ).order_by(func.count(Sale.id).desc()).all()

    # Categorize customers by loyalty level
    loyalty_categories = {
        'champions': [],      # High frequency + High monetary value
        'loyal': [],          # High frequency + Medium monetary value
        'potential': [],      # Medium frequency + High monetary value
        'new': [],           # Low frequency + Medium monetary value
        'at_risk': []        # Low frequency + Low monetary value
    }

    # Calculate thresholds
    if loyalty_query:
        total_amounts = [lq[1] for lq in loyalty_query]
        purchase_counts = [lq[2] for lq in loyalty_query]

        avg_amount = sum(total_amounts) / len(total_amounts)
        avg_frequency = sum(purchase_counts) / len(purchase_counts)

        for customer, total_spent, purchase_count, avg_purchase, last_purchase, first_purchase in loyalty_query:
            # Calculate days since last purchase
            days_since_last = (datetime.now().date() - last_purchase.date()).days if last_purchase else 999

            # Calculate customer lifetime (in months)
            if first_purchase and last_purchase:
                lifetime_days = (last_purchase.date() - first_purchase.date()).days
                lifetime_months = max(1, lifetime_days / 30)
            else:
                lifetime_months = 1

            # Calculate purchase frequency (purchases per month)
            frequency_per_month = purchase_count / lifetime_months

            loyalty_data = {
                'customer': customer,
                'total_spent': total_spent,
                'purchase_count': purchase_count,
                'avg_purchase': avg_purchase,
                'last_purchase': last_purchase,
                'first_purchase': first_purchase,
                'days_since_last': days_since_last,
                'lifetime_months': lifetime_months,
                'frequency_per_month': frequency_per_month,
                'loyalty_score': (float(purchase_count) * 0.4) + (float(total_spent) / 1000 * 0.4) + (max(0, 30 - days_since_last) * 0.2)
            }

            # Categorize customer
            if purchase_count >= avg_frequency and total_spent >= avg_amount:
                loyalty_categories['champions'].append(loyalty_data)
            elif purchase_count >= avg_frequency and total_spent >= float(avg_amount) * 0.5:
                loyalty_categories['loyal'].append(loyalty_data)
            elif purchase_count >= float(avg_frequency) * 0.5 and total_spent >= avg_amount:
                loyalty_categories['potential'].append(loyalty_data)
            elif days_since_last <= 60 and purchase_count >= 1:
                loyalty_categories['new'].append(loyalty_data)
            else:
                loyalty_categories['at_risk'].append(loyalty_data)

    # Sort each category by loyalty score
    for category in loyalty_categories:
        loyalty_categories[category].sort(key=lambda x: x['loyalty_score'], reverse=True)

    # Calculate summary statistics
    total_loyal_customers = len(loyalty_query)
    total_revenue = sum(lq[1] for lq in loyalty_query)
    avg_customer_value = total_revenue / total_loyal_customers if total_loyal_customers > 0 else 0

    # Category counts
    category_counts = {cat: len(customers) for cat, customers in loyalty_categories.items()}

    return render_template('reports/customer_loyalty.html',
                         loyalty_categories=loyalty_categories,
                         category_counts=category_counts,
                         total_loyal_customers=total_loyal_customers,
                         total_revenue=total_revenue,
                         avg_customer_value=avg_customer_value,
                         period_months=period_months,
                         min_purchases=min_purchases,
                         language=language)

@reports_bp.route('/profit-loss')
@login_required
@permission_required('reports')
def profit_loss():
    """Profit & Loss report"""
    language = get_user_language()

    # Get filter parameters
    period = request.args.get('period', 'month')  # month, quarter, year
    year = request.args.get('year', type=int, default=datetime.now().year)
    month = request.args.get('month', type=int, default=datetime.now().month)
    quarter = request.args.get('quarter', type=int, default=1)

    # Calculate date range based on period
    if period == 'month':
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    elif period == 'quarter':
        quarter_months = {1: (1, 3), 2: (4, 6), 3: (7, 9), 4: (10, 12)}
        start_month, end_month = quarter_months[quarter]
        start_date = datetime(year, start_month, 1)
        if end_month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month + 1, 1) - timedelta(days=1)
    else:  # year
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)

    # REVENUE CALCULATIONS
    # Sales Revenue
    sales_revenue = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    sales_revenue = float(sales_revenue)

    # Returns (negative revenue)
    returns_amount = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'returned',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    returns_amount = float(returns_amount)

    # Net Revenue
    net_revenue = float(sales_revenue - returns_amount)

    # COST OF GOODS SOLD (COGS)
    # Calculate COGS from sale items using product cost price
    cogs_query = db.session.query(
        func.sum(SaleItem.quantity * Product.cost_price)
    ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).join(Product, SaleItem.product_id == Product.id).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    cogs_query = float(cogs_query)

    # Gross Profit
    gross_profit = net_revenue - cogs_query
    gross_profit_margin = (gross_profit / net_revenue * 100) if net_revenue > 0 else 0

    # OPERATING EXPENSES (Mock data - in real system, you'd have expense tracking)
    # For now, we'll estimate based on typical business expenses
    estimated_expenses = {
        'rent': net_revenue * 0.05,  # 5% of revenue
        'utilities': net_revenue * 0.02,  # 2% of revenue
        'salaries': net_revenue * 0.15,  # 15% of revenue
        'marketing': net_revenue * 0.03,  # 3% of revenue
        'insurance': net_revenue * 0.01,  # 1% of revenue
        'other': net_revenue * 0.04  # 4% of revenue
    }

    total_operating_expenses = sum(estimated_expenses.values())

    # Operating Profit (EBIT)
    operating_profit = gross_profit - total_operating_expenses
    operating_profit_margin = (operating_profit / net_revenue * 100) if net_revenue > 0 else 0

    # Tax (estimated at 10% for Qatar - adjust as needed)
    tax_rate = 0.10
    tax_amount = max(0, float(operating_profit) * tax_rate)

    # Net Profit
    net_profit = operating_profit - tax_amount
    net_profit_margin = (net_profit / net_revenue * 100) if net_revenue > 0 else 0

    # Previous period comparison
    if period == 'month':
        if month == 1:
            prev_start = datetime(year - 1, 12, 1)
            prev_end = datetime(year, 1, 1) - timedelta(days=1)
        else:
            prev_start = datetime(year, month - 1, 1)
            prev_end = datetime(year, month, 1) - timedelta(days=1)
    elif period == 'quarter':
        if quarter == 1:
            prev_quarter = 4
            prev_year = year - 1
        else:
            prev_quarter = quarter - 1
            prev_year = year
        prev_start_month, prev_end_month = quarter_months[prev_quarter]
        prev_start = datetime(prev_year, prev_start_month, 1)
        if prev_end_month == 12:
            prev_end = datetime(prev_year + 1, 1, 1) - timedelta(days=1)
        else:
            prev_end = datetime(prev_year, prev_end_month + 1, 1) - timedelta(days=1)
    else:  # year
        prev_start = datetime(year - 1, 1, 1)
        prev_end = datetime(year - 1, 12, 31)

    # Previous period revenue for comparison
    prev_revenue = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= prev_start,
        Sale.sale_date <= prev_end
    ).scalar() or 0
    prev_revenue = float(prev_revenue)

    revenue_growth = ((net_revenue - prev_revenue) / prev_revenue * 100) if prev_revenue > 0 else 0

    return render_template('reports/profit_loss.html',
                         net_revenue=net_revenue,
                         sales_revenue=sales_revenue,
                         returns_amount=returns_amount,
                         cogs=cogs_query,
                         gross_profit=gross_profit,
                         gross_profit_margin=gross_profit_margin,
                         operating_expenses=estimated_expenses,
                         total_operating_expenses=total_operating_expenses,
                         operating_profit=operating_profit,
                         operating_profit_margin=operating_profit_margin,
                         tax_amount=tax_amount,
                         tax_rate=tax_rate,
                         net_profit=net_profit,
                         net_profit_margin=net_profit_margin,
                         prev_revenue=prev_revenue,
                         revenue_growth=revenue_growth,
                         period=period,
                         year=year,
                         month=month,
                         quarter=quarter,
                         start_date=start_date,
                         end_date=end_date,
                         language=language)

@reports_bp.route('/cash-flow')
@login_required
@permission_required('reports')
def cash_flow():
    """Cash flow report"""
    language = get_user_language()

    # Get filter parameters
    period = request.args.get('period', 'month')  # month, quarter, year
    year = request.args.get('year', type=int, default=datetime.now().year)
    month = request.args.get('month', type=int, default=datetime.now().month)

    # Calculate date range
    if period == 'month':
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    elif period == 'quarter':
        quarter = ((month - 1) // 3) + 1
        quarter_months = {1: (1, 3), 2: (4, 6), 3: (7, 9), 4: (10, 12)}
        start_month, end_month = quarter_months[quarter]
        start_date = datetime(year, start_month, 1)
        if end_month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month + 1, 1) - timedelta(days=1)
    else:  # year
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)

    # CASH INFLOWS
    # Sales by payment method
    cash_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.payment_method == 'cash',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    cash_sales = float(cash_sales)

    card_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.payment_method == 'card',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    card_sales = float(card_sales)

    bank_transfer_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.payment_method == 'bank_transfer',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    bank_transfer_sales = float(bank_transfer_sales)

    other_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.payment_method.notin_(['cash', 'card', 'bank_transfer']),
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    other_sales = float(other_sales)

    total_inflows = cash_sales + card_sales + bank_transfer_sales + other_sales

    # CASH OUTFLOWS (Estimated based on business operations)
    # Cost of goods sold (actual cash spent on inventory)
    cogs_outflow = db.session.query(
        func.sum(SaleItem.quantity * Product.cost_price)
    ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).join(Product, SaleItem.product_id == Product.id).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    cogs_outflow = float(cogs_outflow)

    # Operating expenses (estimated)
    estimated_outflows = {
        'inventory_purchases': cogs_outflow,
        'rent': total_inflows * 0.05,
        'utilities': total_inflows * 0.02,
        'salaries': total_inflows * 0.15,
        'marketing': total_inflows * 0.03,
        'insurance': total_inflows * 0.01,
        'maintenance': total_inflows * 0.02,
        'other_expenses': total_inflows * 0.03
    }

    total_outflows = sum(estimated_outflows.values())

    # Net Cash Flow
    net_cash_flow = total_inflows - total_outflows

    # Daily cash flow for the period (for chart)
    daily_cash_flow = []
    current_date = start_date
    while current_date <= end_date:
        daily_inflow = db.session.query(func.sum(Sale.total_amount)).filter(
            Sale.status == 'completed',
            func.date(Sale.sale_date) == current_date.date()
        ).scalar() or 0
        daily_inflow = float(daily_inflow)

        # Estimate daily outflow as a percentage of inflow
        daily_outflow = daily_inflow * 0.7  # Assuming 70% of revenue goes to expenses

        daily_cash_flow.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'inflow': daily_inflow,
            'outflow': daily_outflow,
            'net': daily_inflow - daily_outflow
        })

        current_date += timedelta(days=1)

    # Cash flow by payment method breakdown
    payment_methods = {
        'cash': {'amount': cash_sales, 'percentage': (cash_sales / total_inflows * 100) if total_inflows > 0 else 0},
        'card': {'amount': card_sales, 'percentage': (card_sales / total_inflows * 100) if total_inflows > 0 else 0},
        'bank_transfer': {'amount': bank_transfer_sales, 'percentage': (bank_transfer_sales / total_inflows * 100) if total_inflows > 0 else 0},
        'other': {'amount': other_sales, 'percentage': (other_sales / total_inflows * 100) if total_inflows > 0 else 0}
    }

    # Previous period comparison
    if period == 'month':
        if month == 1:
            prev_start = datetime(year - 1, 12, 1)
            prev_end = datetime(year, 1, 1) - timedelta(days=1)
        else:
            prev_start = datetime(year, month - 1, 1)
            prev_end = datetime(year, month, 1) - timedelta(days=1)
    else:
        prev_start = start_date - timedelta(days=365)
        prev_end = end_date - timedelta(days=365)

    prev_inflows = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= prev_start,
        Sale.sale_date <= prev_end
    ).scalar() or 0
    prev_inflows = float(prev_inflows)

    cash_flow_growth = ((total_inflows - prev_inflows) / prev_inflows * 100) if prev_inflows > 0 else 0

    return render_template('reports/cash_flow.html',
                         total_inflows=total_inflows,
                         cash_sales=cash_sales,
                         card_sales=card_sales,
                         bank_transfer_sales=bank_transfer_sales,
                         other_sales=other_sales,
                         total_outflows=total_outflows,
                         estimated_outflows=estimated_outflows,
                         net_cash_flow=net_cash_flow,
                         daily_cash_flow=daily_cash_flow,
                         payment_methods=payment_methods,
                         prev_inflows=prev_inflows,
                         cash_flow_growth=cash_flow_growth,
                         period=period,
                         year=year,
                         month=month,
                         start_date=start_date,
                         end_date=end_date,
                         language=language)

@reports_bp.route('/tax-report')
@login_required
@permission_required('reports')
def tax_report():
    """Tax report for Qatar tax authority compliance"""
    language = get_user_language()

    # Get filter parameters
    period = request.args.get('period', 'month')  # month, quarter, year
    year = request.args.get('year', type=int, default=datetime.now().year)
    month = request.args.get('month', type=int, default=datetime.now().month)
    quarter = request.args.get('quarter', type=int, default=1)

    # Calculate date range
    if period == 'month':
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    elif period == 'quarter':
        quarter_months = {1: (1, 3), 2: (4, 6), 3: (7, 9), 4: (10, 12)}
        start_month, end_month = quarter_months[quarter]
        start_date = datetime(year, start_month, 1)
        if end_month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month + 1, 1) - timedelta(days=1)
    else:  # year
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)

    # SALES DATA FOR TAX CALCULATION
    # Total sales (including VAT if applicable)
    total_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    total_sales = float(total_sales)

    # Sales by payment method (for tax authority reporting)
    sales_by_payment = db.session.query(
        Sale.payment_method,
        func.sum(Sale.total_amount).label('total'),
        func.count(Sale.id).label('count')
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(Sale.payment_method).all()

    # Daily sales summary
    daily_sales = db.session.query(
        func.date(Sale.sale_date).label('sale_date'),
        func.sum(Sale.total_amount).label('daily_total'),
        func.count(Sale.id).label('transaction_count')
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(func.date(Sale.sale_date)).order_by(func.date(Sale.sale_date)).all()

    # Sales by category (for detailed reporting)
    sales_by_category = db.session.query(
        Category.name_ar.label('category_ar'),
        Category.name_en.label('category_en'),
        func.sum(SaleItem.quantity * SaleItem.unit_price).label('category_total'),
        func.sum(SaleItem.quantity).label('total_quantity')
    ).select_from(Category).join(Product, Category.id == Product.category_id).join(SaleItem, Product.id == SaleItem.product_id).join(Sale, SaleItem.sale_id == Sale.id).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(Category.id, Category.name_ar, Category.name_en).all()

    # TAX CALCULATIONS (Qatar specific)
    # Note: Qatar doesn't have VAT, but has corporate tax for certain businesses
    vat_rate = 0.0  # Qatar doesn't have VAT currently
    vat_amount = float(total_sales) * vat_rate

    # Corporate tax (for businesses with revenue > 750,000 QAR annually)
    corporate_tax_threshold = 750000  # QAR
    corporate_tax_rate = 0.10  # 10% for businesses above threshold

    # Calculate annual revenue for tax determination
    annual_start = datetime(year, 1, 1)
    annual_end = datetime(year, 12, 31)
    annual_revenue = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= annual_start,
        Sale.sale_date <= annual_end
    ).scalar() or 0
    annual_revenue = float(annual_revenue)

    # Determine if corporate tax applies
    corporate_tax_applicable = annual_revenue > corporate_tax_threshold

    # Calculate taxable income (simplified - revenue minus COGS)
    cogs = db.session.query(
        func.sum(SaleItem.quantity * Product.cost_price)
    ).select_from(SaleItem).join(Sale, SaleItem.sale_id == Sale.id).join(Product, SaleItem.product_id == Product.id).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    cogs = float(cogs)

    taxable_income = total_sales - cogs
    corporate_tax_amount = float(taxable_income) * corporate_tax_rate if corporate_tax_applicable else 0

    # Generate invoice numbers for the period (for tax authority)
    invoices_in_period = db.session.query(
        Sale.invoice_number,
        Sale.sale_date,
        Sale.total_amount,
        Sale.payment_method,
        Sale.customer_id
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).order_by(Sale.sale_date).all()

    # Summary statistics
    total_transactions = len(invoices_in_period)
    average_transaction = total_sales / total_transactions if total_transactions > 0 else 0

    # Tax compliance data
    tax_summary = {
        'period': period,
        'start_date': start_date,
        'end_date': end_date,
        'total_sales': total_sales,
        'total_transactions': total_transactions,
        'average_transaction': average_transaction,
        'vat_rate': vat_rate,
        'vat_amount': vat_amount,
        'corporate_tax_applicable': corporate_tax_applicable,
        'corporate_tax_rate': corporate_tax_rate,
        'corporate_tax_amount': corporate_tax_amount,
        'taxable_income': taxable_income,
        'annual_revenue': annual_revenue
    }

    return render_template('reports/tax_report.html',
                         tax_summary=tax_summary,
                         sales_by_payment=sales_by_payment,
                         daily_sales=daily_sales,
                         sales_by_category=sales_by_category,
                         invoices_in_period=invoices_in_period,
                         period=period,
                         year=year,
                         month=month,
                         quarter=quarter,
                         start_date=start_date,
                         end_date=end_date,
                         language=language)

@reports_bp.route('/quick-report')
@login_required
@permission_required('reports')
def quick_report():
    """Quick report generator - placeholder"""
    language = get_user_language()
    flash('مولد التقارير السريع قيد التطوير' if language == 'ar' else 'Quick report generator is under development', 'info')
    return redirect(url_for('reports.index'))
