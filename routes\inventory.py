"""
Inventory management routes for Qatar POS System
Handles stock tracking, adjustments, and inventory reports
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models.product import Product
from models.inventory import InventoryTransaction, StockAdjustment, StockAdjustmentItem
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
@permission_required('inventory_read')
def index():
    """Inventory overview"""
    language = get_user_language()
    
    # Get inventory statistics
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_count = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    out_of_stock_count = Product.query.filter(
        Product.current_stock <= 0,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    
    # Get low stock products
    low_stock_products = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).order_by(Product.current_stock.asc()).limit(10).all()
    
    # Get recent transactions
    recent_transactions = InventoryTransaction.query.order_by(
        InventoryTransaction.transaction_date.desc()
    ).limit(10).all()
    
    stats = {
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count
    }
    
    return render_template('inventory/index.html',
                         stats=stats,
                         low_stock_products=low_stock_products,
                         recent_transactions=recent_transactions,
                         language=language)

@inventory_bp.route('/transactions')
@login_required
@permission_required('inventory_read')
def transactions():
    """List inventory transactions"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    product_id = request.args.get('product_id', type=int)
    transaction_type = request.args.get('transaction_type', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    query = InventoryTransaction.query
    
    # Apply filters
    if product_id:
        query = query.filter(InventoryTransaction.product_id == product_id)
    
    if transaction_type:
        query = query.filter(InventoryTransaction.transaction_type == transaction_type)
    
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InventoryTransaction.transaction_date >= from_date)
        except ValueError:
            pass
    
    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(InventoryTransaction.transaction_date <= to_date)
        except ValueError:
            pass
    
    transactions = query.order_by(InventoryTransaction.transaction_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get products for filter dropdown
    products = Product.query.filter_by(is_active=True).order_by(Product.name_en).all()
    
    return render_template('inventory/transactions.html',
                         transactions=transactions,
                         products=products,
                         product_id=product_id,
                         transaction_type=transaction_type,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@inventory_bp.route('/adjustments')
@login_required
@permission_required('inventory')
def adjustments():
    """List stock adjustments"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    
    query = StockAdjustment.query
    
    if status_filter:
        query = query.filter(StockAdjustment.status == status_filter)
    
    adjustments = query.order_by(StockAdjustment.adjustment_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('inventory/adjustments.html',
                         adjustments=adjustments,
                         status_filter=status_filter,
                         language=language)

@inventory_bp.route('/adjustments/create', methods=['GET', 'POST'])
@login_required
@permission_required('inventory')
def create_adjustment():
    """Create stock adjustment"""
    language = get_user_language()
    
    if request.method == 'POST':
        reason = request.form.get('reason')
        notes = request.form.get('notes', '')
        items_data = request.form.getlist('items')
        
        if not reason:
            flash('سبب التعديل مطلوب' if language == 'ar' 
                  else 'Adjustment reason is required', 'error')
            return render_template('inventory/create_adjustment.html', language=language)
        
        try:
            # Create adjustment
            adjustment = StockAdjustment(
                adjustment_number=StockAdjustment.generate_adjustment_number(),
                reason=reason,
                notes=notes,
                user_id=current_user.id
            )
            
            db.session.add(adjustment)
            db.session.flush()  # Get adjustment ID
            
            # Add items
            for item_data in items_data:
                if item_data:  # Skip empty items
                    product_id, old_qty, new_qty, unit_cost = item_data.split(',')
                    
                    product = Product.query.get(int(product_id))
                    if product:
                        adjustment_item = StockAdjustmentItem(
                            adjustment_id=adjustment.id,
                            product_id=product.id,
                            old_quantity=int(old_qty),
                            new_quantity=int(new_qty),
                            unit_cost=float(unit_cost)
                        )
                        adjustment_item.calculate_values()
                        db.session.add(adjustment_item)
            
            db.session.commit()
            
            flash('تم إنشاء تعديل المخزون بنجاح' if language == 'ar' 
                  else 'Stock adjustment created successfully', 'success')
            return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء تعديل المخزون' if language == 'ar' 
                  else 'Error creating stock adjustment', 'error')
    
    # Get products for selection
    products = Product.query.filter_by(is_active=True, track_inventory=True).order_by(Product.name_en).all()
    
    return render_template('inventory/create_adjustment.html',
                         products=products,
                         language=language)

@inventory_bp.route('/adjustments/<int:adjustment_id>')
@login_required
@permission_required('inventory_read')
def view_adjustment(adjustment_id):
    """View stock adjustment details"""
    language = get_user_language()
    adjustment = StockAdjustment.query.get_or_404(adjustment_id)
    
    return render_template('inventory/view_adjustment.html',
                         adjustment=adjustment,
                         language=language)

@inventory_bp.route('/adjustments/<int:adjustment_id>/approve', methods=['POST'])
@login_required
@permission_required('inventory')
def approve_adjustment(adjustment_id):
    """Approve stock adjustment"""
    language = get_user_language()
    adjustment = StockAdjustment.query.get_or_404(adjustment_id)
    
    if adjustment.status != 'draft':
        flash('يمكن الموافقة على التعديلات في حالة المسودة فقط' if language == 'ar' 
              else 'Only draft adjustments can be approved', 'error')
        return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))
    
    try:
        if adjustment.approve(current_user.id):
            db.session.commit()
            flash('تم الموافقة على تعديل المخزون بنجاح' if language == 'ar' 
                  else 'Stock adjustment approved successfully', 'success')
        else:
            flash('فشل في الموافقة على تعديل المخزون' if language == 'ar' 
                  else 'Failed to approve stock adjustment', 'error')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء الموافقة على تعديل المخزون' if language == 'ar' 
              else 'Error approving stock adjustment', 'error')
    
    return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))

@inventory_bp.route('/quick-adjust/<int:product_id>', methods=['POST'])
@login_required
@permission_required('inventory')
def quick_adjust(product_id):
    """Quick stock adjustment for a single product"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)

    new_stock = request.form.get('new_stock', type=int)
    reason = request.form.get('reason', 'manual_adjustment')
    notes = request.form.get('notes', '')

    if new_stock is None or new_stock < 0:
        flash('المخزون الجديد يجب أن يكون رقم موجب' if language == 'ar'
              else 'New stock must be a positive number', 'error')
        return redirect(request.referrer or url_for('products.view', product_id=product.id))

    try:
        # Calculate the difference
        quantity_change = new_stock - product.current_stock

        if quantity_change != 0:
            # Create inventory transaction
            InventoryTransaction.create_transaction(
                product=product,
                quantity_change=quantity_change,
                transaction_type='adjustment',
                user_id=current_user.id,
                notes=f"Quick adjustment: {reason}. {notes}".strip()
            )

            flash('تم تعديل المخزون بنجاح' if language == 'ar'
                  else 'Stock adjusted successfully', 'success')
        else:
            flash('لا يوجد تغيير في المخزون' if language == 'ar'
                  else 'No stock change detected', 'info')

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تعديل المخزون' if language == 'ar'
              else 'Error adjusting stock', 'error')

    return redirect(request.referrer or url_for('products.view', product_id=product.id))

@inventory_bp.route('/api/quick-adjust/<int:product_id>', methods=['POST'])
@login_required
@permission_required('inventory')
def api_quick_adjust(product_id):
    """API endpoint for quick stock adjustment"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)

    try:
        data = request.get_json()
        new_stock = int(data.get('new_stock', 0))
        reason = data.get('reason', 'manual_adjustment')
        notes = data.get('notes', '')

        if new_stock < 0:
            return jsonify({
                'success': False,
                'error': 'المخزون الجديد يجب أن يكون رقم موجب' if language == 'ar'
                        else 'New stock must be a positive number'
            }), 400

        # Calculate the difference
        quantity_change = new_stock - product.current_stock

        if quantity_change != 0:
            # Create inventory transaction
            InventoryTransaction.create_transaction(
                product=product,
                quantity_change=quantity_change,
                transaction_type='adjustment',
                user_id=current_user.id,
                notes=f"Quick adjustment: {reason}. {notes}".strip()
            )

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تعديل المخزون بنجاح' if language == 'ar'
                          else 'Stock adjusted successfully',
                'new_stock': product.current_stock
            })
        else:
            return jsonify({
                'success': True,
                'message': 'لا يوجد تغيير في المخزون' if language == 'ar'
                          else 'No stock change detected',
                'new_stock': product.current_stock
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'حدث خطأ أثناء تعديل المخزون' if language == 'ar'
                    else 'Error adjusting stock'
        }), 500

@inventory_bp.route('/api/product-stock/<int:product_id>')
@login_required
@permission_required('inventory_read')
def api_product_stock(product_id):
    """API endpoint to get product stock information"""
    product = Product.query.get_or_404(product_id)

    return jsonify({
        'current_stock': product.current_stock,
        'minimum_stock': product.minimum_stock,
        'maximum_stock': product.maximum_stock,
        'cost_price': float(product.cost_price),
        'is_low_stock': product.is_low_stock(),
        'is_out_of_stock': product.is_out_of_stock()
    })

@inventory_bp.route('/reports/stock-levels')
@login_required
@permission_required('inventory_read')
def stock_levels_report():
    """Stock levels report"""
    language = get_user_language()
    
    # Get all products with stock information
    products = Product.query.filter_by(is_active=True, track_inventory=True).order_by(Product.name_en).all()
    
    return render_template('inventory/stock_levels_report.html',
                         products=products,
                         language=language)
