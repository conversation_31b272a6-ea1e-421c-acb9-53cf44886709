"""
Inventory management routes for Qatar POS System
Handles stock tracking, adjustments, and inventory reports
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models.product import Product
from models.inventory import InventoryTransaction, StockAdjustment, StockAdjustmentItem
from models.inventory_movement import InventoryMovement
from models.user import User
from models.category import Category
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
@permission_required('inventory_read')
def index():
    """Inventory overview"""
    language = get_user_language()
    
    # Get inventory statistics
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_count = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    out_of_stock_count = Product.query.filter(
        Product.current_stock <= 0,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    
    # Get low stock products
    low_stock_products = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).order_by(Product.current_stock.asc()).limit(10).all()
    
    # Get recent transactions
    recent_transactions = InventoryTransaction.query.order_by(
        InventoryTransaction.transaction_date.desc()
    ).limit(10).all()
    
    stats = {
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count
    }
    
    return render_template('inventory/index.html',
                         stats=stats,
                         low_stock_products=low_stock_products,
                         recent_transactions=recent_transactions,
                         language=language)

@inventory_bp.route('/transactions')
@login_required
@permission_required('inventory_read')
def transactions():
    """List inventory transactions"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    product_id = request.args.get('product_id', type=int)
    transaction_type = request.args.get('transaction_type', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    query = InventoryTransaction.query
    
    # Apply filters
    if product_id:
        query = query.filter(InventoryTransaction.product_id == product_id)
    
    if transaction_type:
        query = query.filter(InventoryTransaction.transaction_type == transaction_type)
    
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InventoryTransaction.transaction_date >= from_date)
        except ValueError:
            pass
    
    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(InventoryTransaction.transaction_date <= to_date)
        except ValueError:
            pass
    
    transactions = query.order_by(InventoryTransaction.transaction_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get products for filter dropdown
    products = Product.query.filter_by(is_active=True).order_by(Product.name_en).all()
    
    return render_template('inventory/transactions.html',
                         transactions=transactions,
                         products=products,
                         product_id=product_id,
                         transaction_type=transaction_type,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@inventory_bp.route('/adjustments')
@login_required
@permission_required('inventory')
def adjustments():
    """List stock adjustments"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    
    query = StockAdjustment.query
    
    if status_filter:
        query = query.filter(StockAdjustment.status == status_filter)
    
    adjustments = query.order_by(StockAdjustment.adjustment_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('inventory/adjustments.html',
                         adjustments=adjustments,
                         status_filter=status_filter,
                         language=language)

@inventory_bp.route('/adjustments/create', methods=['GET', 'POST'])
@login_required
@permission_required('inventory')
def create_adjustment():
    """Create stock adjustment"""
    language = get_user_language()
    
    if request.method == 'POST':
        reason = request.form.get('reason')
        notes = request.form.get('notes', '')
        items_data = request.form.getlist('items')
        
        if not reason:
            flash('سبب التعديل مطلوب' if language == 'ar' 
                  else 'Adjustment reason is required', 'error')
            return render_template('inventory/create_adjustment.html', language=language)
        
        try:
            # Create adjustment
            adjustment = StockAdjustment(
                adjustment_number=StockAdjustment.generate_adjustment_number(),
                reason=reason,
                notes=notes,
                user_id=current_user.id
            )
            
            db.session.add(adjustment)
            db.session.flush()  # Get adjustment ID
            
            # Add items
            for item_data in items_data:
                if item_data:  # Skip empty items
                    product_id, old_qty, new_qty, unit_cost = item_data.split(',')
                    
                    product = Product.query.get(int(product_id))
                    if product:
                        adjustment_item = StockAdjustmentItem(
                            adjustment_id=adjustment.id,
                            product_id=product.id,
                            old_quantity=int(old_qty),
                            new_quantity=int(new_qty),
                            unit_cost=float(unit_cost)
                        )
                        adjustment_item.calculate_values()
                        db.session.add(adjustment_item)
            
            db.session.commit()
            
            flash('تم إنشاء تعديل المخزون بنجاح' if language == 'ar' 
                  else 'Stock adjustment created successfully', 'success')
            return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء تعديل المخزون' if language == 'ar' 
                  else 'Error creating stock adjustment', 'error')
    
    # Get products for selection
    products = Product.query.filter_by(is_active=True, track_inventory=True).order_by(Product.name_en).all()
    
    return render_template('inventory/create_adjustment.html',
                         products=products,
                         language=language)

@inventory_bp.route('/adjustments/<int:adjustment_id>')
@login_required
@permission_required('inventory_read')
def view_adjustment(adjustment_id):
    """View stock adjustment details"""
    language = get_user_language()
    adjustment = StockAdjustment.query.get_or_404(adjustment_id)
    
    return render_template('inventory/view_adjustment.html',
                         adjustment=adjustment,
                         language=language)

@inventory_bp.route('/adjustments/<int:adjustment_id>/approve', methods=['POST'])
@login_required
@permission_required('inventory')
def approve_adjustment(adjustment_id):
    """Approve stock adjustment"""
    language = get_user_language()
    adjustment = StockAdjustment.query.get_or_404(adjustment_id)
    
    if adjustment.status != 'draft':
        flash('يمكن الموافقة على التعديلات في حالة المسودة فقط' if language == 'ar' 
              else 'Only draft adjustments can be approved', 'error')
        return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))
    
    try:
        if adjustment.approve(current_user.id):
            db.session.commit()
            flash('تم الموافقة على تعديل المخزون بنجاح' if language == 'ar' 
                  else 'Stock adjustment approved successfully', 'success')
        else:
            flash('فشل في الموافقة على تعديل المخزون' if language == 'ar' 
                  else 'Failed to approve stock adjustment', 'error')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء الموافقة على تعديل المخزون' if language == 'ar' 
              else 'Error approving stock adjustment', 'error')
    
    return redirect(url_for('inventory.view_adjustment', adjustment_id=adjustment.id))

@inventory_bp.route('/quick-adjust/<int:product_id>', methods=['POST'])
@login_required
@permission_required('inventory')
def quick_adjust(product_id):
    """Quick stock adjustment for a single product"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)

    new_stock = request.form.get('new_stock', type=int)
    reason = request.form.get('reason', 'manual_adjustment')
    notes = request.form.get('notes', '')

    if new_stock is None or new_stock < 0:
        flash('المخزون الجديد يجب أن يكون رقم موجب' if language == 'ar'
              else 'New stock must be a positive number', 'error')
        return redirect(request.referrer or url_for('products.view', product_id=product.id))

    try:
        # Calculate the difference
        quantity_change = new_stock - product.current_stock

        if quantity_change != 0:
            # Create inventory transaction
            InventoryTransaction.create_transaction(
                product=product,
                quantity_change=quantity_change,
                transaction_type='adjustment',
                user_id=current_user.id,
                notes=f"Quick adjustment: {reason}. {notes}".strip()
            )

            flash('تم تعديل المخزون بنجاح' if language == 'ar'
                  else 'Stock adjusted successfully', 'success')
        else:
            flash('لا يوجد تغيير في المخزون' if language == 'ar'
                  else 'No stock change detected', 'info')

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تعديل المخزون' if language == 'ar'
              else 'Error adjusting stock', 'error')

    return redirect(request.referrer or url_for('products.view', product_id=product.id))

@inventory_bp.route('/api/quick-adjust/<int:product_id>', methods=['POST'])
@login_required
@permission_required('inventory')
def api_quick_adjust(product_id):
    """API endpoint for quick stock adjustment"""
    language = get_user_language()
    product = Product.query.get_or_404(product_id)

    try:
        data = request.get_json()
        new_stock = int(data.get('new_stock', 0))
        reason = data.get('reason', 'manual_adjustment')
        notes = data.get('notes', '')

        if new_stock < 0:
            return jsonify({
                'success': False,
                'error': 'المخزون الجديد يجب أن يكون رقم موجب' if language == 'ar'
                        else 'New stock must be a positive number'
            }), 400

        # Calculate the difference
        quantity_change = new_stock - product.current_stock

        if quantity_change != 0:
            # Create inventory transaction
            InventoryTransaction.create_transaction(
                product=product,
                quantity_change=quantity_change,
                transaction_type='adjustment',
                user_id=current_user.id,
                notes=f"Quick adjustment: {reason}. {notes}".strip()
            )

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تعديل المخزون بنجاح' if language == 'ar'
                          else 'Stock adjusted successfully',
                'new_stock': product.current_stock
            })
        else:
            return jsonify({
                'success': True,
                'message': 'لا يوجد تغيير في المخزون' if language == 'ar'
                          else 'No stock change detected',
                'new_stock': product.current_stock
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': 'حدث خطأ أثناء تعديل المخزون' if language == 'ar'
                    else 'Error adjusting stock'
        }), 500

@inventory_bp.route('/api/product-stock/<int:product_id>')
@login_required
@permission_required('inventory_read')
def api_product_stock(product_id):
    """API endpoint to get product stock information"""
    product = Product.query.get_or_404(product_id)

    return jsonify({
        'current_stock': product.current_stock,
        'minimum_stock': product.minimum_stock,
        'maximum_stock': product.maximum_stock,
        'cost_price': float(product.cost_price),
        'is_low_stock': product.is_low_stock(),
        'is_out_of_stock': product.is_out_of_stock()
    })

@inventory_bp.route('/reports/stock-levels')
@login_required
@permission_required('inventory_read')
def stock_levels_report():
    """Stock levels report"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    category_filter = request.args.get('category_id', '')
    stock_status = request.args.get('stock_status', '')
    search_query = request.args.get('search', '')

    # Import Category model
    from models.category import Category

    # Build query
    query = Product.query.filter_by(is_active=True, track_inventory=True)

    # Apply filters
    if category_filter:
        query = query.filter(Product.category_id == category_filter)

    if stock_status == 'low':
        query = query.filter(Product.current_stock <= Product.minimum_stock)
    elif stock_status == 'out':
        query = query.filter(Product.current_stock <= 0)
    elif stock_status == 'normal':
        query = query.filter(Product.current_stock > Product.minimum_stock)

    if search_query:
        query = query.filter(
            db.or_(
                Product.name_ar.contains(search_query),
                Product.name_en.contains(search_query),
                Product.sku.contains(search_query)
            )
        )

    # Get paginated products
    products = query.order_by(Product.name_en).paginate(
        page=page, per_page=50, error_out=False
    )

    # Calculate statistics
    all_products = Product.query.filter_by(is_active=True, track_inventory=True).all()
    low_stock_count = sum(1 for p in all_products if p.is_low_stock())
    out_of_stock_count = sum(1 for p in all_products if p.is_out_of_stock())
    total_stock_value = sum(p.current_stock * p.cost_price for p in all_products)

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_en).all()

    return render_template('inventory/stock_levels_report.html',
                         products=products,
                         categories=categories,
                         category_filter=category_filter,
                         stock_status=stock_status,
                         search_query=search_query,
                         low_stock_count=low_stock_count,
                         out_of_stock_count=out_of_stock_count,
                         total_stock_value=total_stock_value,
                         language=language)

@inventory_bp.route('/movements')
@login_required
@permission_required('inventory_read')
def movements():
    """Advanced inventory movements page"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)

    # Filters
    movement_type = request.args.get('movement_type', '')
    product_id = request.args.get('product_id', type=int)
    user_id = request.args.get('user_id', type=int)
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Check for export requests
    export_format = request.args.get('export', '')

    # Build query
    query = InventoryMovement.query

    # Apply filters
    if movement_type:
        query = query.filter(InventoryMovement.transaction_type == movement_type)

    if product_id:
        query = query.filter(InventoryMovement.product_id == product_id)

    if user_id:
        query = query.filter(InventoryMovement.user_id == user_id)

    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(InventoryMovement.transaction_date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            # Add one day to include the entire day
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(InventoryMovement.transaction_date <= to_date)
        except ValueError:
            pass

    # Handle export requests
    if export_format in ['excel', 'pdf']:
        movements_data = query.order_by(InventoryMovement.transaction_date.desc()).all()

        if export_format == 'excel':
            return export_movements_excel(movements_data, language)
        elif export_format == 'pdf':
            return export_movements_pdf(movements_data, language)

    # Get paginated movements
    movements = query.order_by(InventoryMovement.transaction_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # Get statistics
    movements_stats = InventoryMovement.get_movements_summary(
        date_from=datetime.strptime(date_from, '%Y-%m-%d') if date_from else None,
        date_to=datetime.strptime(date_to, '%Y-%m-%d') if date_to else None,
        product_id=product_id
    )

    # Get data for filters
    products = Product.query.filter_by(is_active=True).order_by(Product.name_en).all()
    users = User.query.filter_by(is_active=True).order_by(User.first_name_en).all()

    return render_template('inventory/movements.html',
                         movements=movements,
                         movements_stats=movements_stats,
                         products=products,
                         users=users,
                         language=language)

def export_movements_excel(movements, language):
    """Export movements to Excel format"""
    try:
        import io
        import csv
        from flask import make_response

        output = io.StringIO()
        writer = csv.writer(output)

        # Headers
        if language == 'ar':
            headers = ['التاريخ', 'المنتج', 'نوع الحركة', 'التغيير', 'المخزون القديم', 'المخزون الجديد', 'المستخدم', 'الملاحظات']
        else:
            headers = ['Date', 'Product', 'Type', 'Change', 'Old Stock', 'New Stock', 'User', 'Notes']

        writer.writerow(headers)

        # Data rows
        for movement in movements:
            row = [
                movement.transaction_date.strftime('%Y-%m-%d %H:%M'),
                movement.product.get_name(language) if movement.product else '',
                movement.get_transaction_type_display(language),
                movement.quantity_change,
                movement.old_quantity,
                movement.new_quantity,
                movement.user.get_full_name(language) if movement.user else '',
                movement.notes or ''
            ]
            writer.writerow(row)

        output.seek(0)

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=inventory_movements_{datetime.now().strftime("%Y%m%d")}.csv'

        return response

    except Exception as e:
        flash('خطأ في تصدير البيانات' if language == 'ar' else 'Error exporting data', 'error')
        return redirect(url_for('inventory.movements'))

def export_movements_pdf(movements, language):
    """Export movements to PDF format"""
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import io
        from flask import make_response

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)

        # Container for the 'Flowable' objects
        elements = []

        # Define styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        # Title
        title = 'تقرير حركات المخزون' if language == 'ar' else 'Inventory Movements Report'
        elements.append(Paragraph(title, title_style))
        elements.append(Spacer(1, 12))

        # Table data
        if language == 'ar':
            headers = ['التاريخ', 'المنتج', 'النوع', 'التغيير', 'المخزون القديم', 'المخزون الجديد', 'المستخدم']
        else:
            headers = ['Date', 'Product', 'Type', 'Change', 'Old Stock', 'New Stock', 'User']

        data = [headers]

        for movement in movements:
            row = [
                movement.transaction_date.strftime('%Y-%m-%d'),
                movement.product.get_name(language)[:20] if movement.product else '',
                movement.get_transaction_type_display(language),
                str(movement.quantity_change),
                str(movement.old_quantity),
                str(movement.new_quantity),
                movement.user.get_full_name(language)[:15] if movement.user else ''
            ]
            data.append(row)

        # Create table
        table = Table(data, repeatRows=1)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)

        # Build PDF
        doc.build(elements)

        buffer.seek(0)

        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=inventory_movements_{datetime.now().strftime("%Y%m%d")}.pdf'

        return response

    except Exception as e:
        flash('خطأ في تصدير PDF' if language == 'ar' else 'Error exporting PDF', 'error')
        return redirect(url_for('inventory.movements'))
