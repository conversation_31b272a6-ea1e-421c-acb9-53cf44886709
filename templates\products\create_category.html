{% extends "base.html" %}

{% block title %}
{{ 'إضافة فئة جديدة - نظام نقاط البيع القطري' if language == 'ar' else 'Add New Category - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة فئة جديدة' if language == 'ar' else 'Add New Category' }}
            </h1>
            <a href="{{ url_for('products.categories') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة للفئات' if language == 'ar' else 'Back to Categories' }}
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {{ 'معلومات الفئة' if language == 'ar' else 'Category Information' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">
                                {{ 'اسم الفئة بالعربية' if language == 'ar' else 'Category Name (Arabic)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name_ar" name="name_ar" required
                                   placeholder="{{ 'أدخل اسم الفئة بالعربية' if language == 'ar' else 'Enter category name in Arabic' }}">
                            <div class="invalid-feedback">
                                {{ 'اسم الفئة بالعربية مطلوب' if language == 'ar' else 'Arabic category name is required' }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">
                                {{ 'اسم الفئة بالإنجليزية' if language == 'ar' else 'Category Name (English)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name_en" name="name_en" required
                                   placeholder="{{ 'أدخل اسم الفئة بالإنجليزية' if language == 'ar' else 'Enter category name in English' }}">
                            <div class="invalid-feedback">
                                {{ 'اسم الفئة بالإنجليزية مطلوب' if language == 'ar' else 'English category name is required' }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="description_ar" class="form-label">
                                {{ 'الوصف بالعربية' if language == 'ar' else 'Description (Arabic)' }}
                            </label>
                            <textarea class="form-control" id="description_ar" name="description_ar" rows="3"
                                      placeholder="{{ 'وصف الفئة بالعربية (اختياري)' if language == 'ar' else 'Category description in Arabic (optional)' }}"></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="description_en" class="form-label">
                                {{ 'الوصف بالإنجليزية' if language == 'ar' else 'Description (English)' }}
                            </label>
                            <textarea class="form-control" id="description_en" name="description_en" rows="3"
                                      placeholder="{{ 'وصف الفئة بالإنجليزية (اختياري)' if language == 'ar' else 'Category description in English (optional)' }}"></textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">
                            {{ 'الفئة الرئيسية' if language == 'ar' else 'Parent Category' }}
                        </label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">{{ 'فئة رئيسية (بدون فئة أب)' if language == 'ar' else 'Main Category (No Parent)' }}</option>
                            {% for category in parent_categories %}
                            <option value="{{ category.id }}">{{ category.get_name(language) }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            {{ 'اختر فئة رئيسية إذا كانت هذه فئة فرعية' if language == 'ar' else 'Select a parent category if this is a subcategory' }}
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                {{ 'فئة نشطة' if language == 'ar' else 'Active Category' }}
                            </label>
                            <div class="form-text">
                                {{ 'الفئات النشطة فقط تظهر في النظام' if language == 'ar' else 'Only active categories appear in the system' }}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products.categories') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            {{ 'حفظ الفئة' if language == 'ar' else 'Save Category' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Category Preview -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-eye"></i>
                    {{ 'معاينة الفئة' if language == 'ar' else 'Category Preview' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 id="preview_name">{{ 'اسم الفئة سيظهر هنا' if language == 'ar' else 'Category name will appear here' }}</h6>
                    <p id="preview_description" class="mb-0 text-muted">{{ 'وصف الفئة سيظهر هنا' if language == 'ar' else 'Category description will appear here' }}</p>
                    <small id="preview_parent" class="text-muted"></small>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i>
                    {{ 'نصائح' if language == 'ar' else 'Tips' }}
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        {{ 'استخدم أسماء واضحة ومفهومة للفئات' if language == 'ar' else 'Use clear and understandable category names' }}
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        {{ 'يمكنك إنشاء فئات فرعية لتنظيم أفضل' if language == 'ar' else 'You can create subcategories for better organization' }}
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        {{ 'الفئات غير النشطة لن تظهر في نقطة البيع' if language == 'ar' else 'Inactive categories will not appear in POS' }}
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        {{ 'يمكنك تعديل الفئات لاحقاً من قائمة الفئات' if language == 'ar' else 'You can edit categories later from the categories list' }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Live preview
function updatePreview() {
    const nameAr = document.getElementById('name_ar').value;
    const nameEn = document.getElementById('name_en').value;
    const descriptionAr = document.getElementById('description_ar').value;
    const descriptionEn = document.getElementById('description_en').value;
    const parentSelect = document.getElementById('parent_id');
    
    // Update name
    let displayName = '';
    if ('{{ language }}' === 'ar') {
        displayName = nameAr || nameEn || '{{ "اسم الفئة سيظهر هنا" if language == "ar" else "Category name will appear here" }}';
    } else {
        displayName = nameEn || nameAr || '{{ "اسم الفئة سيظهر هنا" if language == "ar" else "Category name will appear here" }}';
    }
    document.getElementById('preview_name').textContent = displayName;
    
    // Update description
    let displayDescription = '';
    if ('{{ language }}' === 'ar') {
        displayDescription = descriptionAr || descriptionEn || '{{ "وصف الفئة سيظهر هنا" if language == "ar" else "Category description will appear here" }}';
    } else {
        displayDescription = descriptionEn || descriptionAr || '{{ "وصف الفئة سيظهر هنا" if language == "ar" else "Category description will appear here" }}';
    }
    document.getElementById('preview_description').textContent = displayDescription;
    
    // Update parent info
    const parentInfo = document.getElementById('preview_parent');
    if (parentSelect.value) {
        const parentName = parentSelect.options[parentSelect.selectedIndex].text;
        parentInfo.textContent = '{{ "الفئة الرئيسية:" if language == "ar" else "Parent Category:" }} ' + parentName;
        parentInfo.style.display = 'block';
    } else {
        parentInfo.style.display = 'none';
    }
}

// Add event listeners for live preview
document.getElementById('name_ar').addEventListener('input', updatePreview);
document.getElementById('name_en').addEventListener('input', updatePreview);
document.getElementById('description_ar').addEventListener('input', updatePreview);
document.getElementById('description_en').addEventListener('input', updatePreview);
document.getElementById('parent_id').addEventListener('change', updatePreview);

// Initialize preview
updatePreview();
</script>
{% endblock %}
