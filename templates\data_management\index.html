{% extends "base.html" %}

{% block title %}
{{ 'إدارة البيانات - نظام نقاط البيع القطري' if language == 'ar' else 'Data Management - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.data-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.data-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.data-stat {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.danger-zone {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-top: 2rem;
}

.danger-zone .btn {
    margin: 0.5rem;
}

.warning-box {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

.info-box {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

.backup-section {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-database"></i>
                    {{ 'إدارة البيانات' if language == 'ar' else 'Data Management' }}
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('dashboard.index') }}">
                                {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            {{ 'إدارة البيانات' if language == 'ar' else 'Data Management' }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Backup Section -->
    <div class="row">
        <div class="col-12">
            <div class="backup-section">
                <h4 class="mb-3">
                    <i class="bi bi-shield-check"></i>
                    {{ 'النسخ الاحتياطي' if language == 'ar' else 'Data Backup' }}
                </h4>
                <p class="mb-3">
                    {{ 'قم بإنشاء نسخة احتياطية من البيانات قبل أي عملية مسح' if language == 'ar' else 'Create a backup before performing any data clearing operations' }}
                </p>
                <button type="button" class="btn btn-light btn-lg" onclick="createBackup()">
                    <i class="bi bi-download"></i>
                    {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                </button>
            </div>
        </div>
    </div>

    <!-- Data Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.sales_count }}</div>
                    <div class="text-muted">{{ 'المبيعات' if language == 'ar' else 'Sales' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.products_count }}</div>
                    <div class="text-muted">{{ 'المنتجات' if language == 'ar' else 'Products' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.customers_count }}</div>
                    <div class="text-muted">{{ 'العملاء' if language == 'ar' else 'Customers' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.suppliers_count }}</div>
                    <div class="text-muted">{{ 'الموردين' if language == 'ar' else 'Suppliers' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.categories_count }}</div>
                    <div class="text-muted">{{ 'الفئات' if language == 'ar' else 'Categories' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.users_count }}</div>
                    <div class="text-muted">{{ 'المستخدمين' if language == 'ar' else 'Users' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ stats.sale_items_count }}</div>
                    <div class="text-muted">{{ 'عناصر المبيعات' if language == 'ar' else 'Sale Items' }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="data-card card h-100">
                <div class="card-body text-center">
                    <div class="data-stat">{{ total_records }}</div>
                    <div class="text-muted">{{ 'إجمالي السجلات' if language == 'ar' else 'Total Records' }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Box -->
    <div class="row">
        <div class="col-12">
            <div class="info-box">
                <h5>
                    <i class="bi bi-info-circle"></i>
                    {{ 'معلومات مهمة' if language == 'ar' else 'Important Information' }}
                </h5>
                <ul class="mb-0">
                    <li>{{ 'قم بإنشاء نسخة احتياطية قبل مسح أي بيانات' if language == 'ar' else 'Create a backup before clearing any data' }}</li>
                    <li>{{ 'عمليات المسح لا يمكن التراجع عنها' if language == 'ar' else 'Data clearing operations cannot be undone' }}</li>
                    <li>{{ 'يتطلب كلمة مرور المدير لتأكيد العمليات' if language == 'ar' else 'Admin password required to confirm operations' }}</li>
                    <li>{{ 'سيتم الاحتفاظ بحساب المدير الحالي' if language == 'ar' else 'Current admin account will be preserved' }}</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="row">
        <div class="col-12">
            <div class="danger-zone">
                <h4 class="mb-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ 'منطقة الخطر - مسح البيانات' if language == 'ar' else 'Danger Zone - Data Clearing' }}
                </h4>
                
                <div class="warning-box">
                    <strong>{{ 'تحذير:' if language == 'ar' else 'Warning:' }}</strong>
                    {{ 'هذه العمليات ستحذف البيانات نهائياً ولا يمكن التراجع عنها!' if language == 'ar' else 'These operations will permanently delete data and cannot be undone!' }}
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('data_management.clear_data_page') }}" class="btn btn-warning btn-lg w-100">
                            <i class="bi bi-trash"></i>
                            {{ 'مسح البيانات' if language == 'ar' else 'Clear Data' }}
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <button type="button" class="btn btn-danger btn-lg w-100" onclick="clearAllData()">
                            <i class="bi bi-exclamation-triangle"></i>
                            {{ 'مسح جميع البيانات' if language == 'ar' else 'Clear All Data' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Password Modal -->
<div class="modal fade" id="adminPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    {{ 'تأكيد كلمة مرور المدير' if language == 'ar' else 'Confirm Admin Password' }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span id="confirmationMessage"></span>
                </div>
                <form id="adminPasswordForm">
                    <div class="mb-3">
                        <label class="form-label">{{ 'كلمة مرور المدير:' if language == 'ar' else 'Admin Password:' }}</label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmAction()">
                    {{ 'تأكيد' if language == 'ar' else 'Confirm' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentAction = null;

function createBackup() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> {{ "جاري الإنشاء..." if language == "ar" else "Creating..." }}';
    
    fetch('/data-management/api/backup-data', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', '{{ "خطأ في الاتصال" if language == "ar" else "Connection error" }}');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

function clearAllData() {
    currentAction = 'clear-all';
    document.getElementById('confirmationMessage').textContent = 
        '{{ "سيتم مسح جميع البيانات نهائياً! هل أنت متأكد؟" if language == "ar" else "All data will be permanently deleted! Are you sure?" }}';
    
    const modal = new bootstrap.Modal(document.getElementById('adminPasswordModal'));
    modal.show();
}

function confirmAction() {
    const password = document.getElementById('adminPassword').value;
    
    if (!password) {
        showAlert('warning', '{{ "يرجى إدخال كلمة مرور المدير" if language == "ar" else "Please enter admin password" }}');
        return;
    }
    
    const formData = new FormData();
    formData.append('admin_password', password);
    
    const btn = document.querySelector('#adminPasswordModal .btn-danger');
    const originalText = btn.textContent;
    
    btn.disabled = true;
    btn.textContent = '{{ "جاري المعالجة..." if language == "ar" else "Processing..." }}';
    
    fetch(`/data-management/api/${currentAction}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('adminPasswordModal')).hide();
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', '{{ "خطأ في الاتصال" if language == "ar" else "Connection error" }}');
    })
    .finally(() => {
        btn.disabled = false;
        btn.textContent = originalText;
        document.getElementById('adminPassword').value = '';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
