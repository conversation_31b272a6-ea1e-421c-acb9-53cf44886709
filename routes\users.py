"""
User management routes for Qatar POS System
Handles user CRUD operations and role management
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.user import User
from extensions import db
from utils.decorators import manager_required, permission_required
from utils.helpers import get_user_language, validate_email, validate_qatar_id

users_bp = Blueprint('users', __name__)

@users_bp.route('/dashboard')
@login_required
@permission_required('users_read')
def dashboard():
    """Users dashboard with statistics"""
    language = get_user_language()

    # Get user statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    managers_count = User.query.filter_by(role='manager').count()
    sellers_count = User.query.filter_by(role='seller').count()
    accountants_count = User.query.filter_by(role='accountant').count()
    inventory_managers_count = User.query.filter_by(role='inventory_manager').count()

    # Get recent users (last 5)
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

    return render_template('dashboard/users_dashboard.html',
                         language=language,
                         total_users=total_users,
                         active_users=active_users,
                         managers_count=managers_count,
                         sellers_count=sellers_count,
                         accountants_count=accountants_count,
                         inventory_managers_count=inventory_managers_count,
                         recent_users=recent_users)

@users_bp.route('/')
@login_required
@manager_required
def index():
    """List all users"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    
    query = User.query
    
    # Apply filters
    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.email.contains(search)) |
            (User.first_name_ar.contains(search)) |
            (User.first_name_en.contains(search)) |
            (User.last_name_ar.contains(search)) |
            (User.last_name_en.contains(search))
        )
    
    if role_filter:
        query = query.filter(User.role == role_filter)
    
    if status_filter == 'active':
        query = query.filter(User.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User.is_active == False)
    
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('users/index.html', 
                         users=users, 
                         search=search,
                         role_filter=role_filter,
                         status_filter=status_filter,
                         language=language)

@users_bp.route('/create', methods=['GET', 'POST'])
@login_required
@manager_required
def create():
    """Create new user"""
    language = get_user_language()
    
    if request.method == 'POST':
        # Get form data
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        first_name_ar = request.form.get('first_name_ar', '').strip()
        first_name_en = request.form.get('first_name_en', '').strip()
        last_name_ar = request.form.get('last_name_ar', '').strip()
        last_name_en = request.form.get('last_name_en', '').strip()
        
        phone = request.form.get('phone', '').strip()
        qatar_id = request.form.get('qatar_id', '').strip()
        employee_id = request.form.get('employee_id', '').strip()
        role = request.form.get('role')
        
        # Validation
        errors = []
        
        if not username:
            errors.append('اسم المستخدم مطلوب' if language == 'ar' else 'Username is required')
        elif User.query.filter_by(username=username).first():
            errors.append('اسم المستخدم موجود بالفعل' if language == 'ar' else 'Username already exists')
        
        if not email:
            errors.append('البريد الإلكتروني مطلوب' if language == 'ar' else 'Email is required')
        elif not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format')
        elif User.query.filter_by(email=email).first():
            errors.append('البريد الإلكتروني موجود بالفعل' if language == 'ar' else 'Email already exists')
        
        if not password:
            errors.append('كلمة المرور مطلوبة' if language == 'ar' else 'Password is required')
        elif len(password) < 6:
            errors.append('كلمة المرور يجب أن تكون 6 أحرف على الأقل' if language == 'ar' 
                         else 'Password must be at least 6 characters')
        elif password != confirm_password:
            errors.append('كلمة المرور وتأكيدها غير متطابقين' if language == 'ar' 
                         else 'Password and confirmation do not match')
        
        if not first_name_ar or not first_name_en:
            errors.append('الاسم الأول مطلوب بالعربية والإنجليزية' if language == 'ar' 
                         else 'First name is required in both Arabic and English')
        
        if not last_name_ar or not last_name_en:
            errors.append('اسم العائلة مطلوب بالعربية والإنجليزية' if language == 'ar' 
                         else 'Last name is required in both Arabic and English')
        
        if qatar_id and not validate_qatar_id(qatar_id):
            errors.append('رقم الهوية القطرية غير صحيح' if language == 'ar' 
                         else 'Invalid Qatar ID format')
        elif qatar_id and User.query.filter_by(qatar_id=qatar_id).first():
            errors.append('رقم الهوية القطرية موجود بالفعل' if language == 'ar' 
                         else 'Qatar ID already exists')
        
        if employee_id and User.query.filter_by(employee_id=employee_id).first():
            errors.append('رقم الموظف موجود بالفعل' if language == 'ar' 
                         else 'Employee ID already exists')
        
        # Valid roles based on User model enum
        valid_roles = ['manager', 'admin', 'seller', 'accountant', 'inventory_manager']
        if role not in valid_roles:
            errors.append('الدور المحدد غير صحيح' if language == 'ar' else 'Invalid role selected')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('users/create.html', language=language)
        
        # Create user
        try:
            user = User(
                username=username,
                email=email,
                first_name_ar=first_name_ar,
                first_name_en=first_name_en,
                last_name_ar=last_name_ar,
                last_name_en=last_name_en,
                phone=phone,
                qatar_id=qatar_id,
                employee_id=employee_id,
                role=role
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            flash('تم إنشاء المستخدم بنجاح' if language == 'ar' 
                  else 'User created successfully', 'success')
            return redirect(url_for('users.index'))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المستخدم' if language == 'ar' 
                  else 'Error creating user', 'error')
    
    return render_template('users/create.html', language=language)

@users_bp.route('/<int:user_id>')
@login_required
@manager_required
def view(user_id):
    """View user details"""
    language = get_user_language()
    user = User.query.get_or_404(user_id)
    
    return render_template('users/view.html', user=user, language=language)

@users_bp.route('/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@manager_required
def edit(user_id):
    """Edit user"""
    language = get_user_language()
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        # Get form data
        email = request.form.get('email', '').strip()
        first_name_ar = request.form.get('first_name_ar', '').strip()
        first_name_en = request.form.get('first_name_en', '').strip()
        last_name_ar = request.form.get('last_name_ar', '').strip()
        last_name_en = request.form.get('last_name_en', '').strip()
        phone = request.form.get('phone', '').strip()
        qatar_id = request.form.get('qatar_id', '').strip()
        employee_id = request.form.get('employee_id', '').strip()
        role = request.form.get('role')
        is_active = bool(request.form.get('is_active'))
        
        # Validation
        errors = []
        
        if not email:
            errors.append('البريد الإلكتروني مطلوب' if language == 'ar' else 'Email is required')
        elif not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format')
        elif email != user.email and User.query.filter_by(email=email).first():
            errors.append('البريد الإلكتروني موجود بالفعل' if language == 'ar' else 'Email already exists')
        
        if qatar_id and qatar_id != user.qatar_id:
            if not validate_qatar_id(qatar_id):
                errors.append('رقم الهوية القطرية غير صحيح' if language == 'ar' 
                             else 'Invalid Qatar ID format')
            elif User.query.filter_by(qatar_id=qatar_id).first():
                errors.append('رقم الهوية القطرية موجود بالفعل' if language == 'ar' 
                             else 'Qatar ID already exists')
        
        if employee_id and employee_id != user.employee_id:
            if User.query.filter_by(employee_id=employee_id).first():
                errors.append('رقم الموظف موجود بالفعل' if language == 'ar'
                             else 'Employee ID already exists')

        # Valid roles based on User model enum
        valid_roles = ['manager', 'admin', 'seller', 'accountant', 'inventory_manager']
        if role not in valid_roles:
            errors.append('الدور المحدد غير صحيح' if language == 'ar' else 'Invalid role selected')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('users/edit.html', user=user, language=language)
        
        # Update user
        try:
            user.email = email
            user.first_name_ar = first_name_ar
            user.first_name_en = first_name_en
            user.last_name_ar = last_name_ar
            user.last_name_en = last_name_en
            user.phone = phone
            user.qatar_id = qatar_id
            user.employee_id = employee_id
            user.role = role
            user.is_active = is_active
            
            db.session.commit()
            
            flash('تم تحديث المستخدم بنجاح' if language == 'ar' 
                  else 'User updated successfully', 'success')
            return redirect(url_for('users.view', user_id=user.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث المستخدم' if language == 'ar' 
                  else 'Error updating user', 'error')
    
    return render_template('users/edit.html', user=user, language=language)

@users_bp.route('/<int:user_id>/reset-password', methods=['POST'])
@login_required
@manager_required
def reset_password(user_id):
    """Reset user password"""
    language = get_user_language()
    user = User.query.get_or_404(user_id)
    
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if not new_password or len(new_password) < 6:
        flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل' if language == 'ar' 
              else 'Password must be at least 6 characters', 'error')
        return redirect(url_for('users.view', user_id=user.id))
    
    if new_password != confirm_password:
        flash('كلمة المرور وتأكيدها غير متطابقين' if language == 'ar' 
              else 'Password and confirmation do not match', 'error')
        return redirect(url_for('users.view', user_id=user.id))
    
    try:
        user.set_password(new_password)
        db.session.commit()
        
        flash('تم إعادة تعيين كلمة المرور بنجاح' if language == 'ar' 
              else 'Password reset successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إعادة تعيين كلمة المرور' if language == 'ar' 
              else 'Error resetting password', 'error')
    
    return redirect(url_for('users.view', user_id=user.id))

@users_bp.route('/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@manager_required
def toggle_status(user_id):
    """Toggle user active status"""
    language = get_user_language()
    user = User.query.get_or_404(user_id)
    
    if user.id == current_user.id:
        flash('لا يمكنك تعطيل حسابك الخاص' if language == 'ar' 
              else 'You cannot deactivate your own account', 'error')
        return redirect(url_for('users.view', user_id=user.id))
    
    try:
        user.is_active = not user.is_active
        db.session.commit()
        
        status_text = 'تم تفعيل' if user.is_active else 'تم تعطيل'
        status_text_en = 'activated' if user.is_active else 'deactivated'
        
        flash(f'{status_text} المستخدم بنجاح' if language == 'ar' 
              else f'User {status_text_en} successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تغيير حالة المستخدم' if language == 'ar' 
              else 'Error changing user status', 'error')
    
    return redirect(url_for('users.view', user_id=user.id))


# Validation functions
def validate_email(email):
    """Validate email format"""
    import re
    if not email:
        return False
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_qatar_id(qatar_id):
    """Validate Qatar ID format (11 digits)"""
    if not qatar_id or qatar_id.strip() == '':
        return True  # Optional field
    return len(qatar_id) == 11 and qatar_id.isdigit()
