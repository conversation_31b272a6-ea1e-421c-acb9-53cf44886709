# دليل رسوم التوصيل - نظام نقاط البيع القطري
# Delivery Fee Guide - Qatar POS System

## 🎯 نظرة عامة | Overview

تم تفعيل نظام رسوم التوصيل المتكامل مع الدفع عند الاستلام (COD) في نظام نقاط البيع القطري.

A comprehensive delivery fee system integrated with Cash on Delivery (COD) has been activated in the Qatar POS system.

## ✅ الميزات المفعلة | Activated Features

### 📦 رسوم التوصيل الذكية
- **إضافة تلقائية** عند اختيار الدفع عند الاستلام
- **رسوم قابلة للتخصيص** حسب كل طلب
- **حساب تلقائي** للإجماليات مع رسوم التوصيل
- **عرض واضح** في الفاتورة والإيصالات

### 🚚 ربط مع الدفع عند الاستلام
- **تفعيل تلقائي** لرسوم التوصيل مع COD
- **معلومات التوصيل** الكاملة (العنوان، الهاتف، الملاحظات)
- **تتبع حالة التوصيل** (في انتظار التوصيل، تم التوصيل)
- **إدارة الدفع** عند الاستلام

### 💰 الحسابات المالية
- **إضافة رسوم التوصيل** إلى إجمالي الفاتورة
- **حساب دقيق** للضرائب والخصومات
- **تقارير مالية** تشمل رسوم التوصيل
- **تتبع الأرباح** من خدمات التوصيل

## 🚀 كيفية الاستخدام | How to Use

### 📱 في نقاط البيع (POS)

#### 1. إضافة المنتجات للسلة
```
1. اختر المنتجات المطلوبة
2. أضفها إلى سلة التسوق
3. تأكد من الكميات والأسعار
```

#### 2. اختيار طريقة الدفع
```
1. انتقل إلى قسم الدفع
2. اختر "دفع عند الاستلام" (COD)
3. ستظهر خانة رسوم التوصيل تلقائياً
4. الرسوم الافتراضية: 10 ر.ق (قابلة للتعديل)
```

#### 3. إدخال معلومات التوصيل
```
العنوان: عنوان التوصيل الكامل
الهاتف: رقم هاتف العميل
الملاحظات: تعليمات خاصة للتوصيل
```

#### 4. تخصيص رسوم التوصيل
```
- يمكن تعديل المبلغ حسب المسافة
- رسوم خاصة للمناطق البعيدة
- خصومات للعملاء المميزين
- رسوم إضافية للطلبات العاجلة
```

### 📄 في الفاتورة

#### عرض رسوم التوصيل:
- **سطر منفصل** لرسوم التوصيل
- **لون مميز** (أزرق) للتمييز
- **إضافة للإجمالي** النهائي
- **معلومات التوصيل** كاملة

#### مثال على الفاتورة:
```
المجموع الفرعي:     100.00 ر.ق
الخصم:              -5.00 ر.ق
رسوم التوصيل:       15.00 ر.ق
─────────────────────────────
الإجمالي:           110.00 ر.ق

معلومات التوصيل:
العنوان: الدوحة، قطر - شارع الكورنيش
الهاتف: +974 5555 1234
الملاحظات: التوصيل بين 2-4 مساءً
```

## 🔧 الإعدادات التقنية | Technical Settings

### 🗄️ قاعدة البيانات
```sql
-- حقل رسوم التوصيل في جدول المبيعات
ALTER TABLE sales 
ADD COLUMN delivery_fee DECIMAL(10,2) DEFAULT 0.00;
```

### 📊 النموذج (Model)
```python
class Sale:
    delivery_fee = db.Column(db.Numeric(10, 2), default=0)
    
    def apply_delivery_fee(self, delivery_fee):
        """تطبيق رسوم التوصيل"""
        self.delivery_fee = delivery_fee
        self.calculate_totals()
        
    def calculate_totals(self):
        """حساب الإجماليات مع رسوم التوصيل"""
        self.total_amount = (
            self.subtotal + 
            self.tax_amount + 
            self.delivery_fee - 
            self.discount_amount
        )
```

### 🎨 الواجهة (Frontend)
```javascript
// حساب الإجماليات مع رسوم التوصيل
calculateTotals() {
    const subtotal = this.getSubtotal();
    const discount = this.getDiscount();
    const deliveryFee = this.getDeliveryFee();
    const total = subtotal - discount + deliveryFee;
    
    this.updateDisplay(total);
}

// إظهار رسوم التوصيل عند اختيار COD
handlePaymentMethodChange(method) {
    if (method === 'cod') {
        this.showDeliveryFeeInput();
        this.setDefaultDeliveryFee(10); // 10 ر.ق افتراضي
    } else {
        this.hideDeliveryFeeInput();
        this.setDeliveryFee(0);
    }
}
```

## 📈 التقارير والإحصائيات | Reports & Analytics

### 💼 تقارير رسوم التوصيل
- **إجمالي رسوم التوصيل** اليومية/الشهرية
- **متوسط رسوم التوصيل** لكل طلب
- **أكثر المناطق طلباً** للتوصيل
- **أرباح خدمة التوصيل** المنفصلة

### 📊 مؤشرات الأداء
```
إجمالي طلبات التوصيل: 150 طلب
متوسط رسوم التوصيل: 12.5 ر.ق
إجمالي إيرادات التوصيل: 1,875 ر.ق
نسبة طلبات COD: 35% من إجمالي المبيعات
```

## 🎯 أفضل الممارسات | Best Practices

### 💡 للمديرين
1. **راقب رسوم التوصيل** بانتظام
2. **حدد رسوم مناسبة** للمناطق المختلفة
3. **قدم خصومات** للعملاء المميزين
4. **تتبع تكاليف التوصيل** الفعلية

### 🚚 لفريق التوصيل
1. **تأكد من معلومات التوصيل** قبل الخروج
2. **اتصل بالعميل** قبل الوصول
3. **احمل فكة مناسبة** للدفع النقدي
4. **سجل حالة التوصيل** فور الانتهاء

### 📞 لخدمة العملاء
1. **اشرح رسوم التوصيل** بوضوح
2. **أكد معلومات التوصيل** مع العميل
3. **قدم خيارات مرنة** للتوصيل
4. **تابع حالة الطلب** مع العميل

## 🔗 الروابط والأدوات | Links & Tools

### 🖥️ الواجهات الرئيسية
- **نقاط البيع:** http://127.0.0.1:2626/sales/pos
- **إدارة المبيعات:** http://127.0.0.1:2626/sales/
- **التقارير:** http://127.0.0.1:2626/reports/
- **لوحة التحكم:** http://127.0.0.1:2626/dashboard/

### 📱 الميزات ذات الصلة
- **إدارة العملاء:** لحفظ عناوين التوصيل
- **تقارير المبيعات:** لتتبع أداء التوصيل
- **إعدادات النظام:** لتخصيص رسوم التوصيل
- **إدارة المستخدمين:** لصلاحيات فريق التوصيل

## 🛠️ استكشاف الأخطاء | Troubleshooting

### ❓ مشاكل شائعة وحلولها

#### 1. رسوم التوصيل لا تظهر
```
الحل:
- تأكد من اختيار "دفع عند الاستلام"
- أعد تحديث الصفحة
- تحقق من إعدادات المتصفح
```

#### 2. الحسابات غير صحيحة
```
الحل:
- تأكد من إدخال رسوم التوصيل بشكل صحيح
- تحقق من الخصومات المطبقة
- راجع إعدادات الضرائب
```

#### 3. معلومات التوصيل مفقودة
```
الحل:
- تأكد من ملء جميع الحقول المطلوبة
- تحقق من صحة رقم الهاتف
- أضف تفاصيل العنوان كاملة
```

## 📋 قائمة التحقق | Checklist

### ✅ قبل تفعيل النظام
- [ ] تدريب الموظفين على النظام الجديد
- [ ] تحديد رسوم التوصيل للمناطق المختلفة
- [ ] إعداد فريق التوصيل
- [ ] اختبار النظام مع طلبات تجريبية

### ✅ عند استلام طلب COD
- [ ] تأكيد معلومات العميل
- [ ] تحديد رسوم التوصيل المناسبة
- [ ] إدخال عنوان التوصيل بدقة
- [ ] تسجيل ملاحظات خاصة

### ✅ عند التوصيل
- [ ] التأكد من هوية العميل
- [ ] عرض الفاتورة والمنتجات
- [ ] تحصيل المبلغ كاملاً
- [ ] تحديث حالة الطلب في النظام

## 🎉 الخلاصة | Summary

نظام رسوم التوصيل المتكامل يوفر:

✅ **إدارة شاملة** لطلبات التوصيل
✅ **حسابات دقيقة** للرسوم والإجماليات
✅ **واجهة سهلة** للموظفين والعملاء
✅ **تقارير مفصلة** لتتبع الأداء
✅ **مرونة كاملة** في تحديد الرسوم
✅ **تكامل مثالي** مع نظام COD
✅ **دعم اللغتين** العربية والإنجليزية

**النظام جاهز لخدمة عملاء أفضل وزيادة الإيرادات!** 🚀

---

*تم تطوير هذا النظام خصيصاً للسوق القطري مع مراعاة احتياجات التجارة الإلكترونية المحلية.*

*This system was developed specifically for the Qatar market considering local e-commerce needs.*
