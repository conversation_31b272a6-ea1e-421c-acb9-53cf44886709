#!/usr/bin/env python3
"""
اختبار إصلاح StockAdjustment AttributeError
Test StockAdjustment AttributeError fix
"""

from app import create_app
from models.user import User
from models.inventory import StockAdjustment, StockAdjustmentItem
from models.product import Product
from extensions import db

def test_stockadjustment_model():
    """اختبار نموذج StockAdjustment"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار نموذج StockAdjustment")
        print("=" * 40)
        
        # اختبار الحقول المتاحة
        adjustment_fields = [
            'id', 'adjustment_number', 'adjustment_date', 'reason', 
            'notes', 'user_id', 'status', 'approved_by', 'approved_at'
        ]
        
        print("  📋 الحقول المتوقعة:")
        for field in adjustment_fields:
            if hasattr(StockAdjustment, field):
                print(f"    ✅ {field}")
            else:
                print(f"    ❌ {field} - مفقود")
        
        # اختبار الحقل المشكوك فيه
        if hasattr(StockAdjustment, 'created_at'):
            print(f"    ⚠️ created_at - موجود (قد يسبب مشاكل)")
        else:
            print(f"    ✅ created_at - غير موجود (صحيح)")
        
        # اختبار إنشاء StockAdjustment
        try:
            adjustment = StockAdjustment(
                adjustment_number=StockAdjustment.generate_adjustment_number(),
                reason='physical_count',
                notes='اختبار',
                user_id=1
            )
            print(f"  ✅ إنشاء StockAdjustment: {adjustment.adjustment_number}")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء StockAdjustment: {e}")

def test_stockadjustment_query():
    """اختبار استعلامات StockAdjustment"""
    app = create_app()
    
    with app.app_context():
        print("\n🔍 اختبار استعلامات StockAdjustment")
        print("=" * 40)
        
        # اختبار الترتيب بـ adjustment_date
        try:
            adjustments = StockAdjustment.query.order_by(StockAdjustment.adjustment_date.desc()).limit(5).all()
            print(f"  ✅ ترتيب بـ adjustment_date: {len(adjustments)} سجل")
        except Exception as e:
            print(f"  ❌ خطأ في ترتيب بـ adjustment_date: {e}")
        
        # اختبار الترتيب بـ created_at (يجب أن يفشل)
        try:
            adjustments = StockAdjustment.query.order_by(StockAdjustment.created_at.desc()).limit(5).all()
            print(f"  ⚠️ ترتيب بـ created_at: {len(adjustments)} سجل (لا يجب أن يعمل)")
        except AttributeError as e:
            print(f"  ✅ ترتيب بـ created_at فشل كما متوقع: {e}")
        except Exception as e:
            print(f"  ❌ خطأ غير متوقع في ترتيب بـ created_at: {e}")
        
        # اختبار pagination
        try:
            adjustments = StockAdjustment.query.order_by(StockAdjustment.adjustment_date.desc()).paginate(
                page=1, per_page=20, error_out=False
            )
            print(f"  ✅ pagination: {adjustments.total} إجمالي، {len(adjustments.items)} في الصفحة")
        except Exception as e:
            print(f"  ❌ خطأ في pagination: {e}")

def test_inventory_routes():
    """اختبار routes المخزون"""
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("\n🌐 اختبار routes المخزون")
            print("=" * 40)
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = client.post('/auth/login', data=login_data, follow_redirects=True)
            
            if response.status_code != 200:
                print(f"  ❌ فشل تسجيل الدخول")
                return
            
            print("  ✅ تم تسجيل الدخول بنجاح")
            
            # اختبار صفحات المخزون
            inventory_routes = [
                ('/inventory/', 'صفحة المخزون الرئيسية'),
                ('/inventory/transactions', 'معاملات المخزون'),
                ('/inventory/adjustments', 'تعديلات المخزون'),
            ]
            
            for route, description in inventory_routes:
                try:
                    response = client.get(route)
                    if response.status_code == 200:
                        print(f"  ✅ {description}: {response.status_code}")
                    else:
                        print(f"  ⚠️ {description}: {response.status_code}")
                except Exception as e:
                    print(f"  ❌ {description}: خطأ - {e}")

def test_stockadjustment_creation():
    """اختبار إنشاء تعديل مخزون"""
    app = create_app()
    
    with app.app_context():
        print("\n📝 اختبار إنشاء تعديل مخزون")
        print("=" * 40)
        
        try:
            # التحقق من وجود منتج للاختبار
            product = Product.query.first()
            if not product:
                print("  ⚠️ لا يوجد منتجات للاختبار")
                return
            
            # إنشاء تعديل مخزون
            adjustment = StockAdjustment(
                adjustment_number=StockAdjustment.generate_adjustment_number(),
                reason='physical_count',
                notes='اختبار إنشاء تعديل مخزون',
                user_id=1
            )
            
            db.session.add(adjustment)
            db.session.flush()  # للحصول على ID
            
            # إضافة عنصر للتعديل
            adjustment_item = StockAdjustmentItem(
                adjustment_id=adjustment.id,
                product_id=product.id,
                old_quantity=product.current_stock,
                new_quantity=product.current_stock + 10,
                unit_cost=float(product.cost_price)
            )
            adjustment_item.calculate_values()
            
            db.session.add(adjustment_item)
            db.session.commit()
            
            print(f"  ✅ تم إنشاء تعديل مخزون: {adjustment.adjustment_number}")
            print(f"      المنتج: {product.get_name('ar')}")
            print(f"      التغيير: {adjustment_item.quantity_change}")
            
            # حذف التعديل التجريبي
            db.session.delete(adjustment)
            db.session.commit()
            print(f"  ✅ تم حذف التعديل التجريبي")
            
        except Exception as e:
            db.session.rollback()
            print(f"  ❌ خطأ في إنشاء تعديل المخزون: {e}")
            import traceback
            print(f"      التفاصيل: {traceback.format_exc()}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار إصلاح StockAdjustment")
    print("=" * 70)
    
    test_stockadjustment_model()
    test_stockadjustment_query()
    test_inventory_routes()
    test_stockadjustment_creation()
    
    print("\n" + "=" * 70)
    print("✅ انتهى الاختبار!")
    print("🎯 إذا نجحت جميع الاختبارات، فقد تم إصلاح AttributeError بنجاح!")
