{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        إضافة مورد جديد
    {% else %}
        Create New Supplier
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            إضافة مورد جديد
                        {% else %}
                            Create New Supplier
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <form method="POST" class="card-body">
                    <!-- Company Information -->
                    <h5 class="mb-3">
                        {% if language == 'ar' %}معلومات الشركة{% else %}Company Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company_name_ar">
                                    {% if language == 'ar' %}اسم الشركة (عربي){% else %}Company Name (Arabic){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_ar" name="company_name_ar" required dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company_name_en">
                                    {% if language == 'ar' %}اسم الشركة (إنجليزي){% else %}Company Name (English){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_en" name="company_name_en" required dir="ltr">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_person_ar">
                                    {% if language == 'ar' %}الشخص المسؤول (عربي){% else %}Contact Person (Arabic){% endif %}
                                </label>
                                <input type="text" class="form-control" id="contact_person_ar" name="contact_person_ar" dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_person_en">
                                    {% if language == 'ar' %}الشخص المسؤول (إنجليزي){% else %}Contact Person (English){% endif %}
                                </label>
                                <input type="text" class="form-control" id="contact_person_en" name="contact_person_en" dir="ltr">
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات الاتصال{% else %}Contact Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="phone">
                                    {% if language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="email">
                                    {% if language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                                </label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="website">
                                    {% if language == 'ar' %}الموقع الإلكتروني{% else %}Website{% endif %}
                                </label>
                                <input type="url" class="form-control" id="website" name="website">
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات العنوان{% else %}Address Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="address_ar">
                                    {% if language == 'ar' %}العنوان (عربي){% else %}Address (Arabic){% endif %}
                                </label>
                                <textarea class="form-control" id="address_ar" name="address_ar" rows="3" dir="rtl"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="address_en">
                                    {% if language == 'ar' %}العنوان (إنجليزي){% else %}Address (English){% endif %}
                                </label>
                                <textarea class="form-control" id="address_en" name="address_en" rows="3" dir="ltr"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="city_ar">
                                    {% if language == 'ar' %}المدينة (عربي){% else %}City (Arabic){% endif %}
                                </label>
                                <input type="text" class="form-control" id="city_ar" name="city_ar" dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="city_en">
                                    {% if language == 'ar' %}المدينة (إنجليزي){% else %}City (English){% endif %}
                                </label>
                                <input type="text" class="form-control" id="city_en" name="city_en" dir="ltr">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="postal_code">
                                    {% if language == 'ar' %}الرمز البريدي{% else %}Postal Code{% endif %}
                                </label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                        </div>
                    </div>

                    <!-- Business Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}المعلومات التجارية{% else %}Business Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="commercial_registration">
                                    {% if language == 'ar' %}السجل التجاري{% else %}Commercial Registration{% endif %}
                                </label>
                                <input type="text" class="form-control" id="commercial_registration" name="commercial_registration">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="tax_number">
                                    {% if language == 'ar' %}الرقم الضريبي{% else %}Tax Number{% endif %}
                                </label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="payment_terms">
                                    {% if language == 'ar' %}شروط الدفع{% else %}Payment Terms{% endif %}
                                </label>
                                <input type="text" class="form-control" id="payment_terms" name="payment_terms" 
                                       placeholder="{% if language == 'ar' %}مثال: 30 يوم{% else %}e.g., 30 days{% endif %}">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if language == 'ar' %}إنشاء المورد{% else %}Create Supplier{% endif %}
                            </button>
                            <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
