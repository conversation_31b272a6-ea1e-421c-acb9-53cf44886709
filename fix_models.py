#!/usr/bin/env python3
"""
Qatar POS System - Fix Models Issues
Fix SQLAlchemy relationship and import issues
"""

import sys
import os

def fix_models():
    """Fix model import and relationship issues"""
    print("🔧 Fixing SQLAlchemy models...")
    
    try:
        # Import Flask app
        from app import create_app
        from extensions import db
        
        # Create app context
        app = create_app()
        
        with app.app_context():
            print("✅ App context created")
            
            # Try to import all models
            try:
                from models.user import User
                print("✅ User model imported")
                
                from models.category import Category
                print("✅ Category model imported")
                
                from models.product import Product
                print("✅ Product model imported")
                
                from models.customer import Customer
                print("✅ Customer model imported")
                
                from models.supplier import Supplier
                print("✅ Supplier model imported")
                
                from models.purchase import PurchaseOrder, PurchaseOrderItem
                print("✅ Purchase models imported")
                
                from models.sale import Sale, SaleItem
                print("✅ Sale models imported")
                
                from models.inventory import InventoryTransaction
                print("✅ Inventory model imported")
                
                from models.settings import SystemSettings
                print("✅ Settings model imported")
                
            except Exception as e:
                print(f"❌ Model import error: {e}")
                return False
            
            # Try to create all tables
            try:
                print("🔧 Creating database tables...")
                db.create_all()
                print("✅ All tables created successfully")
                
                # Test relationships
                print("🔧 Testing model relationships...")
                
                # Test Supplier -> PurchaseOrder relationship
                suppliers = Supplier.query.all()
                print(f"✅ Found {len(suppliers)} suppliers")
                
                purchase_orders = PurchaseOrder.query.all()
                print(f"✅ Found {len(purchase_orders)} purchase orders")
                
                print("✅ All model relationships working")
                
            except Exception as e:
                print(f"❌ Database error: {e}")
                return False
                
        print("🎉 All models fixed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

def create_test_data():
    """Create some test data to verify models work"""
    print("\n🧪 Creating test data...")
    
    try:
        from app import create_app
        from extensions import db
        from models.user import User
        from models.supplier import Supplier
        from werkzeug.security import generate_password_hash
        
        app = create_app()
        
        with app.app_context():
            # Create admin user if not exists
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    first_name_ar='المدير',
                    first_name_en='Admin',
                    last_name_ar='العام',
                    last_name_en='User',
                    role='manager',
                    is_active=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                print("✅ Admin user created")
            
            # Create test supplier if not exists
            test_supplier = Supplier.query.filter_by(supplier_code='S000001').first()
            if not test_supplier:
                test_supplier = Supplier(
                    supplier_code='S000001',
                    company_name_ar='شركة الاختبار',
                    company_name_en='Test Company',
                    phone='+974-4444-5555',
                    email='<EMAIL>',
                    address_ar='الدوحة، قطر',
                    address_en='Doha, Qatar',
                    city_ar='الدوحة',
                    city_en='Doha'
                )
                db.session.add(test_supplier)
                print("✅ Test supplier created")
            
            db.session.commit()
            print("✅ Test data created successfully")
            
    except Exception as e:
        print(f"❌ Test data creation failed: {e}")

def main():
    """Main function"""
    print("🇶🇦 Qatar POS System - Model Fixer")
    print("=" * 50)
    
    if fix_models():
        create_test_data()
        print("\n🎉 All fixes completed successfully!")
        print("✅ Models are working correctly")
        print("✅ Database tables created")
        print("✅ Test data added")
        print("\n🚀 You can now run the server:")
        print("python server_2626.py")
    else:
        print("\n❌ Fix failed. Check the errors above.")

if __name__ == '__main__':
    main()
