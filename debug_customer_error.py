#!/usr/bin/env python3
"""
تشخيص خطأ إنشاء العميل
Debug customer creation error
"""

from app import create_app
from models.user import User
import re

def extract_error_messages(html_content):
    """استخراج رسائل الخطأ من HTML"""
    # البحث عن رسائل الخطأ
    error_patterns = [
        r'<div[^>]*alert-danger[^>]*>(.*?)</div>',
        r'<div[^>]*class="[^"]*alert[^"]*danger[^"]*"[^>]*>(.*?)</div>',
        r'flash\([\'"]([^\'"]*)[\'"],\s*[\'"]error[\'"]',
    ]
    
    errors = []
    for pattern in error_patterns:
        matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
        for match in matches:
            # تنظيف HTML tags
            clean_text = re.sub(r'<[^>]+>', '', match).strip()
            if clean_text:
                errors.append(clean_text)
    
    return errors

def debug_customer_creation():
    """تشخيص مشكلة إنشاء العميل"""
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("🔍 تشخيص مشكلة إنشاء العميل")
            print("=" * 50)
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = client.post('/auth/login', data=login_data, follow_redirects=True)
            
            if response.status_code != 200:
                print(f"  ❌ فشل تسجيل الدخول: {response.status_code}")
                return
            
            print("  ✅ تم تسجيل الدخول بنجاح")
            
            # بيانات عميل بسيطة
            customer_data = {
                'customer_type': 'individual',
                'first_name_ar': 'أحمد',
                'first_name_en': 'Ahmed',
                'last_name_ar': 'محمد',
                'last_name_en': 'Mohammed',
                'phone': '97412345678',  # رقم بسيط
                'email': '<EMAIL>'
            }
            
            print("\n📝 بيانات العميل المُرسلة:")
            for key, value in customer_data.items():
                print(f"    {key}: {value}")
            
            # إرسال البيانات
            response = client.post('/customers/create', data=customer_data, follow_redirects=False)
            
            print(f"\n📊 نتيجة الطلب:")
            print(f"    كود الحالة: {response.status_code}")
            print(f"    نوع المحتوى: {response.content_type}")
            
            if response.status_code == 302:
                print(f"    إعادة توجيه إلى: {response.location}")
                
                # متابعة إعادة التوجيه
                response = client.get(response.location)
                print(f"    كود الحالة بعد إعادة التوجيه: {response.status_code}")
            
            # فحص المحتوى
            content = response.get_data(as_text=True)
            
            # البحث عن رسائل النجاح
            success_messages = [
                'تم إنشاء العميل بنجاح',
                'Customer created successfully'
            ]
            
            found_success = False
            for msg in success_messages:
                if msg in content:
                    print(f"  ✅ رسالة نجاح: {msg}")
                    found_success = True
                    break
            
            if not found_success:
                # البحث عن رسائل الخطأ
                error_messages = extract_error_messages(content)
                
                if error_messages:
                    print(f"\n❌ رسائل الخطأ المكتشفة:")
                    for i, error in enumerate(error_messages, 1):
                        print(f"    {i}. {error}")
                else:
                    print(f"\n⚠️ لم يتم العثور على رسائل خطأ واضحة")
                
                # البحث عن كلمات مفتاحية للأخطاء
                error_keywords = [
                    'خطأ', 'error', 'مطلوب', 'required', 
                    'غير صحيح', 'invalid', 'موجود', 'exists'
                ]
                
                found_keywords = []
                for keyword in error_keywords:
                    if keyword in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"\n🔍 كلمات مفتاحية للأخطاء: {', '.join(found_keywords)}")
                
                # حفظ المحتوى للفحص اليدوي
                with open('debug_customer_response.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"\n💾 تم حفظ المحتوى في: debug_customer_response.html")

def test_direct_customer_creation():
    """اختبار إنشاء العميل مباشرة"""
    app = create_app()
    
    with app.app_context():
        print("\n🧪 اختبار إنشاء العميل مباشرة")
        print("=" * 50)
        
        from models.customer import Customer
        from extensions import db
        
        try:
            # إنشاء عميل مباشرة
            customer = Customer(
                customer_code=Customer.generate_customer_code(),
                customer_type='individual',
                first_name_ar='أحمد',
                first_name_en='Ahmed',
                last_name_ar='محمد',
                last_name_en='Mohammed',
                phone='97412345678',
                email='<EMAIL>'
            )
            
            db.session.add(customer)
            db.session.commit()
            
            print(f"  ✅ تم إنشاء العميل مباشرة: {customer.customer_code}")
            
            # حذف العميل
            db.session.delete(customer)
            db.session.commit()
            print(f"  ✅ تم حذف العميل التجريبي")
            
        except Exception as e:
            db.session.rollback()
            print(f"  ❌ خطأ في الإنشاء المباشر: {e}")
            import traceback
            print(f"      التفاصيل: {traceback.format_exc()}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - تشخيص خطأ العميل")
    print("=" * 60)
    
    debug_customer_creation()
    test_direct_customer_creation()
    
    print("\n" + "=" * 60)
    print("✅ انتهى التشخيص!")
    print("🎯 تحقق من الملف debug_customer_response.html للتفاصيل")
