"""
Flask extensions initialization
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_babel import Babel

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
babel = Babel()

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    from models.user import User
    return User.query.get(int(user_id))
