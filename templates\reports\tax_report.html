{% extends "base.html" %}

{% block title %}{{ 'التقرير الضريبي' if language == 'ar' else 'Tax Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-receipt-cutoff me-2"></i>
                    {{ 'التقرير الضريبي - دولة قطر' if language == 'ar' else 'Tax Report - State of Qatar' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Period Filter -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-range me-2"></i>
                        {{ 'فترة التقرير الضريبي' if language == 'ar' else 'Tax Report Period' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">{{ 'نوع الفترة' if language == 'ar' else 'Period Type' }}</label>
                            <select name="period" class="form-select" onchange="togglePeriodFields()">
                                <option value="month" {% if period == 'month' %}selected{% endif %}>{{ 'شهري' if language == 'ar' else 'Monthly' }}</option>
                                <option value="quarter" {% if period == 'quarter' %}selected{% endif %}>{{ 'ربع سنوي' if language == 'ar' else 'Quarterly' }}</option>
                                <option value="year" {% if period == 'year' %}selected{% endif %}>{{ 'سنوي' if language == 'ar' else 'Yearly' }}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'السنة' if language == 'ar' else 'Year' }}</label>
                            <select name="year" class="form-select">
                                {% for y in range(2020, 2030) %}
                                <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2" id="monthField">
                            <label class="form-label">{{ 'الشهر' if language == 'ar' else 'Month' }}</label>
                            <select name="month" class="form-select">
                                {% for m in range(1, 13) %}
                                <option value="{{ m }}" {% if m == month %}selected{% endif %}>
                                    {{ m }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2" id="quarterField" style="display: none;">
                            <label class="form-label">{{ 'الربع' if language == 'ar' else 'Quarter' }}</label>
                            <select name="quarter" class="form-select">
                                <option value="1" {% if quarter == 1 %}selected{% endif %}>Q1</option>
                                <option value="2" {% if quarter == 2 %}selected{% endif %}>Q2</option>
                                <option value="3" {% if quarter == 3 %}selected{% endif %}>Q3</option>
                                <option value="4" {% if quarter == 4 %}selected{% endif %}>Q4</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    {{ 'إنشاء التقرير' if language == 'ar' else 'Generate Report' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report Period Info -->
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle me-2"></i>
                <strong>{{ 'فترة التقرير:' if language == 'ar' else 'Report Period:' }}</strong>
                {{ start_date.strftime('%Y-%m-%d') }} {{ 'إلى' if language == 'ar' else 'to' }} {{ end_date.strftime('%Y-%m-%d') }}
            </div>

            <!-- Tax Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(tax_summary.total_sales) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'عدد المعاملات' if language == 'ar' else 'Total Transactions' }}</h6>
                                    <h3 class="mb-0">{{ tax_summary.total_transactions }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-receipt fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'الدخل الخاضع للضريبة' if language == 'ar' else 'Taxable Income' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(tax_summary.taxable_income) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-calculator fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'الضريبة المستحقة' if language == 'ar' else 'Tax Due' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(tax_summary.corporate_tax_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-percent fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tax Compliance Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-shield-check me-2"></i>
                        {{ 'حالة الامتثال الضريبي' if language == 'ar' else 'Tax Compliance Status' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert {% if tax_summary.corporate_tax_applicable %}alert-warning{% else %}alert-success{% endif %}">
                                <h6>{{ 'ضريبة الشركات' if language == 'ar' else 'Corporate Tax' }}</h6>
                                {% if tax_summary.corporate_tax_applicable %}
                                <p class="mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    {{ 'تنطبق ضريبة الشركات (10%) - الإيرادات السنوية تتجاوز 750,000 ر.ق' if language == 'ar' else 'Corporate tax (10%) applies - Annual revenue exceeds QAR 750,000' }}
                                </p>
                                <small>{{ 'الإيرادات السنوية:' if language == 'ar' else 'Annual Revenue:' }} {{ "%.0f"|format(tax_summary.annual_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</small>
                                {% else %}
                                <p class="mb-0">
                                    <i class="bi bi-check-circle me-2"></i>
                                    {{ 'لا تنطبق ضريبة الشركات - الإيرادات السنوية أقل من 750,000 ر.ق' if language == 'ar' else 'Corporate tax does not apply - Annual revenue below QAR 750,000' }}
                                </p>
                                <small>{{ 'الإيرادات السنوية:' if language == 'ar' else 'Annual Revenue:' }} {{ "%.0f"|format(tax_summary.annual_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6>{{ 'ضريبة القيمة المضافة' if language == 'ar' else 'Value Added Tax (VAT)' }}</h6>
                                <p class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    {{ 'لا تطبق دولة قطر ضريبة القيمة المضافة حالياً' if language == 'ar' else 'Qatar does not currently apply VAT' }}
                                </p>
                                <small>{{ 'معدل الضريبة:' if language == 'ar' else 'Tax Rate:' }} {{ "%.1f"|format(tax_summary.vat_rate * 100) }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Sales by Payment Method -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-credit-card me-2"></i>
                                {{ 'المبيعات حسب طريقة الدفع' if language == 'ar' else 'Sales by Payment Method' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</th>
                                            <th class="text-end">{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th class="text-end">{{ 'العدد' if language == 'ar' else 'Count' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment_method, total, count in sales_by_payment %}
                                        <tr>
                                            <td>
                                                {% if payment_method == 'cash' %}
                                                <i class="bi bi-cash me-2"></i>{{ 'نقدي' if language == 'ar' else 'Cash' }}
                                                {% elif payment_method == 'card' %}
                                                <i class="bi bi-credit-card me-2"></i>{{ 'بطاقة' if language == 'ar' else 'Card' }}
                                                {% elif payment_method == 'bank_transfer' %}
                                                <i class="bi bi-bank me-2"></i>{{ 'تحويل بنكي' if language == 'ar' else 'Bank Transfer' }}
                                                {% else %}
                                                <i class="bi bi-three-dots me-2"></i>{{ payment_method or 'أخرى' if language == 'ar' else payment_method or 'Other' }}
                                                {% endif %}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(total) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ count }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales by Category -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-tags me-2"></i>
                                {{ 'المبيعات حسب الفئة' if language == 'ar' else 'Sales by Category' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                                            <th class="text-end">{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th class="text-end">{{ 'الكمية' if language == 'ar' else 'Quantity' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for category in sales_by_category %}
                                        <tr>
                                            <td>{{ category.category_ar if language == 'ar' else category.category_en }}</td>
                                            <td class="text-end">{{ "%.2f"|format(category.category_total) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ category.total_quantity }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tax Calculation Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calculator me-2"></i>
                        {{ 'ملخص حساب الضريبة' if language == 'ar' else 'Tax Calculation Summary' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="taxTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'البند' if language == 'ar' else 'Item' }}</th>
                                    <th class="text-end">{{ 'المبلغ (ر.ق)' if language == 'ar' else 'Amount (QAR)' }}</th>
                                    <th class="text-end">{{ 'المعدل %' if language == 'ar' else 'Rate %' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</td>
                                    <td class="text-end">{{ "%.2f"|format(tax_summary.total_sales) }}</td>
                                    <td class="text-end">-</td>
                                </tr>
                                <tr>
                                    <td>{{ 'الدخل الخاضع للضريبة' if language == 'ar' else 'Taxable Income' }}</td>
                                    <td class="text-end">{{ "%.2f"|format(tax_summary.taxable_income) }}</td>
                                    <td class="text-end">-</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>{{ 'ضريبة الشركات' if language == 'ar' else 'Corporate Tax' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(tax_summary.corporate_tax_amount) }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.1f"|format(tax_summary.corporate_tax_rate * 100) }}%</strong></td>
                                </tr>
                                <tr>
                                    <td>{{ 'ضريبة القيمة المضافة' if language == 'ar' else 'VAT' }}</td>
                                    <td class="text-end">{{ "%.2f"|format(tax_summary.vat_amount) }}</td>
                                    <td class="text-end">{{ "%.1f"|format(tax_summary.vat_rate * 100) }}%</td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>{{ 'إجمالي الضرائب المستحقة' if language == 'ar' else 'Total Tax Due' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(tax_summary.corporate_tax_amount + tax_summary.vat_amount) }}</strong></td>
                                    <td class="text-end">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Invoice Details (Limited to first 20 for display) -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-receipt me-2"></i>
                        {{ 'تفاصيل الفواتير' if language == 'ar' else 'Invoice Details' }}
                        <span class="badge bg-secondary ms-2">{{ invoices_in_period|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-secondary">
                                <tr>
                                    <th>{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice #' }}</th>
                                    <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                                    <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                                    <th class="text-end">{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                    <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices_in_period[:20] %}
                                <tr>
                                    <td><code>{{ invoice.tax_invoice_number }}</code></td>
                                    <td>{{ invoice.sale_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if invoice.customer_id %}
                                        {{ 'عميل رقم' if language == 'ar' else 'Customer ID' }} {{ invoice.customer_id }}
                                        {% else %}
                                        {{ 'عميل نقدي' if language == 'ar' else 'Cash Customer' }}
                                        {% endif %}
                                    </td>
                                    <td class="text-end">{{ "%.2f"|format(invoice.total_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {% if invoice.payment_method == 'cash' %}{{ 'نقدي' if language == 'ar' else 'Cash' }}
                                            {% elif invoice.payment_method == 'card' %}{{ 'بطاقة' if language == 'ar' else 'Card' }}
                                            {% elif invoice.payment_method == 'bank_transfer' %}{{ 'تحويل بنكي' if language == 'ar' else 'Bank Transfer' }}
                                            {% else %}{{ invoice.payment_method or ('أخرى' if language == 'ar' else 'Other') }}
                                            {% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if invoices_in_period|length > 20 %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        <i class="bi bi-three-dots"></i>
                                        {{ 'و' if language == 'ar' else 'and' }} {{ invoices_in_period|length - 20 }} {{ 'فاتورة أخرى' if language == 'ar' else 'more invoices' }}
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePeriodFields() {
    const period = document.querySelector('select[name="period"]').value;
    const monthField = document.getElementById('monthField');
    const quarterField = document.getElementById('quarterField');
    
    if (period === 'month') {
        monthField.style.display = 'block';
        quarterField.style.display = 'none';
    } else if (period === 'quarter') {
        monthField.style.display = 'none';
        quarterField.style.display = 'block';
    } else {
        monthField.style.display = 'none';
        quarterField.style.display = 'none';
    }
}

function exportToExcel() {
    const table = document.getElementById('taxTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Tax Report"});
    XLSX.writeFile(wb, 'tax_report_qatar.xlsx');
}

// Initialize period fields
document.addEventListener('DOMContentLoaded', function() {
    togglePeriodFields();
});

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
