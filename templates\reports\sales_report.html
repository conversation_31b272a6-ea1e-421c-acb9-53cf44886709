{% extends "base.html" %}

{% block title %}
{{ 'تقرير المبيعات - نظام نقاط البيع القطري' if language == 'ar' else 'Sales Report - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-graph-up"></i>
                {{ 'تقرير المبيعات' if language == 'ar' else 'Sales Report' }}
            </h1>
            <div>
                <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                    <i class="bi bi-file-pdf"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="bi bi-file-excel"></i>
                    Excel
                </button>
                <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للتقارير' if language == 'ar' else 'Back to Reports' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'البائع' if language == 'ar' else 'Seller' }}</label>
                <select class="form-select" name="seller_id">
                    <option value="">{{ 'جميع البائعين' if language == 'ar' else 'All Sellers' }}</option>
                    {% for seller in sellers %}
                    <option value="{{ seller.id }}" {{ 'selected' if seller.id == seller_id }}>
                        {{ seller.get_full_name(language) }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</label>
                <select class="form-select" name="payment_method">
                    <option value="">{{ 'جميع الطرق' if language == 'ar' else 'All Methods' }}</option>
                    <option value="cash" {{ 'selected' if payment_method == 'cash' }}>{{ 'نقدي' if language == 'ar' else 'Cash' }}</option>
                    <option value="card" {{ 'selected' if payment_method == 'card' }}>{{ 'بطاقة' if language == 'ar' else 'Card' }}</option>
                    <option value="bank_transfer" {{ 'selected' if payment_method == 'bank_transfer' }}>{{ 'تحويل' if language == 'ar' else 'Transfer' }}</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(summary.total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(summary.total_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'عدد المعاملات' if language == 'ar' else 'Total Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ summary.total_transactions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'متوسط المعاملة' if language == 'ar' else 'Average Transaction' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(summary.average_transaction) if language == 'ar' else 'QAR {:,.2f}'.format(summary.average_transaction) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'إجمالي الخصومات' if language == 'ar' else 'Total Discounts' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(summary.total_discounts) if language == 'ar' else 'QAR {:,.2f}'.format(summary.total_discounts) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-percent fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <!-- Sales Trend Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'اتجاه المبيعات' if language == 'ar' else 'Sales Trend' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'طرق الدفع' if language == 'ar' else 'Payment Methods' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="paymentMethodsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales by Seller -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'المبيعات حسب البائع' if language == 'ar' else 'Sales by Seller' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{{ 'البائع' if language == 'ar' else 'Seller' }}</th>
                                <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</th>
                                <th>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</th>
                                <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Avg Transaction' }}</th>
                                <th>{{ 'النسبة' if language == 'ar' else 'Percentage' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for seller_stat in seller_stats %}
                            <tr>
                                <td>{{ seller_stat.seller_name }}</td>
                                <td>{{ seller_stat.transaction_count }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(seller_stat.total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(seller_stat.total_sales) }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(seller_stat.average_transaction) if language == 'ar' else 'QAR {:,.2f}'.format(seller_stat.average_transaction) }}</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ seller_stat.percentage }}%" 
                                             aria-valuenow="{{ seller_stat.percentage }}" 
                                             aria-valuemin="0" aria-valuemax="100">
                                            {{ '{:.1f}%'.format(seller_stat.percentage) }}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hourly Sales Distribution -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'توزيع المبيعات حسب الساعة' if language == 'ar' else 'Hourly Sales Distribution' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="hourlySalesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Chart.js configurations
Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
Chart.defaults.global.defaultFontColor = '#858796';

// Sales Trend Chart
var ctx = document.getElementById("salesTrendChart");
var salesTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ daily_sales_labels | safe }},
        datasets: [{
            label: "{{ 'المبيعات' if language == 'ar' else 'Sales' }}",
            lineTension: 0.3,
            backgroundColor: "rgba(78, 115, 223, 0.05)",
            borderColor: "rgba(78, 115, 223, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(78, 115, 223, 1)",
            pointBorderColor: "rgba(78, 115, 223, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
            pointHoverBorderColor: "rgba(78, 115, 223, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: {{ daily_sales_data | safe }},
        }],
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                time: {
                    unit: 'date'
                },
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 7
                }
            }],
            yAxes: [{
                ticks: {
                    maxTicksLimit: 5,
                    padding: 10,
                    callback: function(value, index, values) {
                        return '{{ "ر.ق " if language == "ar" else "QAR " }}' + value.toLocaleString();
                    }
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: false
        },
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            intersect: false,
            mode: 'index',
            caretPadding: 10,
            callbacks: {
                label: function(tooltipItem, chart) {
                    var datasetLabel = chart.datasets[tooltipItem.datasetIndex].label || '';
                    return datasetLabel + ': {{ "ر.ق " if language == "ar" else "QAR " }}' + tooltipItem.yLabel.toLocaleString();
                }
            }
        }
    }
});

// Payment Methods Pie Chart
var ctx = document.getElementById("paymentMethodsChart");
var paymentMethodsChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {{ payment_methods_labels | safe }},
        datasets: [{
            data: {{ payment_methods_data | safe }},
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});

// Hourly Sales Chart
var ctx = document.getElementById("hourlySalesChart");
var hourlySalesChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: {{ hourly_labels | safe }},
        datasets: [{
            label: "{{ 'المبيعات' if language == 'ar' else 'Sales' }}",
            backgroundColor: "#4e73df",
            hoverBackgroundColor: "#2e59d9",
            borderColor: "#4e73df",
            data: {{ hourly_data | safe }},
        }],
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 24
                },
                maxBarThickness: 25,
            }],
            yAxes: [{
                ticks: {
                    maxTicksLimit: 5,
                    padding: 10,
                    callback: function(value, index, values) {
                        return '{{ "ر.ق " if language == "ar" else "QAR " }}' + value.toLocaleString();
                    }
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: false
        },
        tooltips: {
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
            callbacks: {
                label: function(tooltipItem, chart) {
                    var datasetLabel = chart.datasets[tooltipItem.datasetIndex].label || '';
                    return datasetLabel + ': {{ "ر.ق " if language == "ar" else "QAR " }}' + tooltipItem.yLabel.toLocaleString();
                }
            }
        },
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    const url = `${window.location.pathname}?${params.toString()}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
