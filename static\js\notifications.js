/**
 * Enhanced Notification System for Qatar POS
 * Handles all types of notifications with advanced features
 */

class NotificationManager {
    constructor() {
        this.settings = {};
        this.queue = [];
        this.activeNotifications = [];
        this.maxNotifications = 5;
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.createContainer();
        this.requestDesktopPermission();
        this.bindEvents();
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/settings/notifications');
            if (response.ok) {
                this.settings = await response.json();
            }
        } catch (error) {
            console.warn('Failed to load notification settings:', error);
            // Use default settings
            this.settings = {
                notifications_enabled: { value: 'true' },
                notification_duration: { value: '5' },
                notification_position: { value: 'top-right' },
                desktop_notifications_enabled: { value: 'false' }
            };
        }
    }

    createContainer() {
        // Remove existing container
        const existing = document.getElementById('notification-container');
        if (existing) {
            existing.remove();
        }

        // Create new container
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = `notification-container ${this.getPosition()}`;
        
        // Add styles
        container.style.cssText = `
            position: fixed;
            z-index: 9999;
            pointer-events: none;
            max-width: 400px;
            width: 100%;
        `;

        this.setContainerPosition(container);
        document.body.appendChild(container);
        this.container = container;
    }

    getPosition() {
        const position = this.settings.notification_position?.value || 'top-right';
        return `position-${position}`;
    }

    setContainerPosition(container) {
        const position = this.settings.notification_position?.value || 'top-right';
        
        // Reset all positions
        container.style.top = '';
        container.style.bottom = '';
        container.style.left = '';
        container.style.right = '';

        switch (position) {
            case 'top-right':
                container.style.top = '20px';
                container.style.right = '20px';
                break;
            case 'top-left':
                container.style.top = '20px';
                container.style.left = '20px';
                break;
            case 'bottom-right':
                container.style.bottom = '20px';
                container.style.right = '20px';
                break;
            case 'bottom-left':
                container.style.bottom = '20px';
                container.style.left = '20px';
                break;
            case 'top-center':
                container.style.top = '20px';
                container.style.left = '50%';
                container.style.transform = 'translateX(-50%)';
                break;
        }
    }

    show(message, type = 'info', options = {}) {
        if (!this.isEnabled()) {
            return;
        }

        const notification = this.createNotification(message, type, options);
        
        // Add to queue if too many active notifications
        if (this.activeNotifications.length >= this.maxNotifications) {
            this.queue.push({ message, type, options });
            return;
        }

        this.displayNotification(notification);
        
        // Show desktop notification if enabled
        if (this.isDesktopEnabled() && options.desktop !== false) {
            this.showDesktopNotification(message, type);
        }

        // Play sound if enabled
        if (window.playSound && options.sound !== false) {
            window.playSound(type);
        }
    }

    createNotification(message, type, options) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getBootstrapType(type)} alert-dismissible notification-item`;
        notification.style.cssText = `
            pointer-events: auto;
            margin-bottom: 10px;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
            border: none;
        `;

        const icon = this.getIcon(type);
        const closeBtn = options.persistent ? '' : `
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi ${icon} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                ${closeBtn}
            </div>
        `;

        // Add timestamp if enabled
        if (options.timestamp) {
            const time = new Date().toLocaleTimeString();
            const timeElement = document.createElement('small');
            timeElement.className = 'text-muted d-block mt-1';
            timeElement.textContent = time;
            notification.querySelector('.flex-grow-1').appendChild(timeElement);
        }

        return notification;
    }

    displayNotification(notification) {
        this.container.appendChild(notification);
        this.activeNotifications.push(notification);

        // Auto-dismiss after duration
        const duration = parseInt(this.settings.notification_duration?.value || '5') * 1000;
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        // Handle manual dismiss
        const closeBtn = notification.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.removeNotification(notification);
            });
        }
    }

    removeNotification(notification) {
        if (!notification.parentNode) return;

        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
            
            // Remove from active list
            const index = this.activeNotifications.indexOf(notification);
            if (index > -1) {
                this.activeNotifications.splice(index, 1);
            }

            // Show next queued notification
            if (this.queue.length > 0) {
                const next = this.queue.shift();
                this.show(next.message, next.type, next.options);
            }
        }, 300);
    }

    showDesktopNotification(message, type) {
        if (!('Notification' in window) || Notification.permission !== 'granted') {
            return;
        }

        const options = {
            body: message,
            icon: '/static/images/logo.png',
            badge: '/static/images/logo.png',
            tag: `qatar-pos-${type}`,
            requireInteraction: type === 'error',
            silent: false
        };

        const notification = new Notification('Qatar POS System', options);
        
        // Auto-close desktop notification
        setTimeout(() => {
            notification.close();
        }, 5000);
    }

    async requestDesktopPermission() {
        if (!('Notification' in window)) {
            return false;
        }

        if (Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }

        return Notification.permission === 'granted';
    }

    getBootstrapType(type) {
        const typeMap = {
            'success': 'success',
            'error': 'danger',
            'danger': 'danger',
            'warning': 'warning',
            'info': 'info',
            'primary': 'primary',
            'secondary': 'secondary'
        };
        return typeMap[type] || 'info';
    }

    getIcon(type) {
        const iconMap = {
            'success': 'bi-check-circle-fill',
            'error': 'bi-x-circle-fill',
            'danger': 'bi-x-circle-fill',
            'warning': 'bi-exclamation-triangle-fill',
            'info': 'bi-info-circle-fill',
            'primary': 'bi-bell-fill',
            'secondary': 'bi-gear-fill'
        };
        return iconMap[type] || 'bi-info-circle-fill';
    }

    isEnabled() {
        return this.settings.notifications_enabled?.value === 'true';
    }

    isDesktopEnabled() {
        return this.settings.desktop_notifications_enabled?.value === 'true';
    }

    bindEvents() {
        // Listen for setting changes
        document.addEventListener('settingsUpdated', () => {
            this.loadSettings();
            this.createContainer();
        });

        // Listen for notification events
        document.addEventListener('showNotification', (event) => {
            const { message, type, options } = event.detail;
            this.show(message, type, options);
        });
    }

    // Public methods for different notification types
    success(message, options = {}) {
        this.show(message, 'success', options);
    }

    error(message, options = {}) {
        this.show(message, 'error', { ...options, persistent: true });
    }

    warning(message, options = {}) {
        this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        this.show(message, 'info', options);
    }

    // Clear all notifications
    clearAll() {
        this.activeNotifications.forEach(notification => {
            this.removeNotification(notification);
        });
        this.queue = [];
    }

    // Update settings
    async updateSettings(newSettings) {
        try {
            const response = await fetch('/api/settings/notifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(newSettings)
            });

            if (response.ok) {
                await this.loadSettings();
                this.createContainer();
                return true;
            }
        } catch (error) {
            console.error('Failed to update notification settings:', error);
        }
        return false;
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification-container.position-top-left @keyframes slideIn {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
    }

    .notification-container.position-top-left @keyframes slideOut {
        from { transform: translateX(0); }
        to { transform: translateX(-100%); }
    }

    .notification-container.position-bottom-left @keyframes slideIn {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
    }

    .notification-container.position-bottom-left @keyframes slideOut {
        from { transform: translateX(0); }
        to { transform: translateX(-100%); }
    }

    .notification-item {
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        transform: scale(1.02);
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    }
`;
document.head.appendChild(style);

// Initialize notification manager
window.notificationManager = new NotificationManager();

// Global notification functions for backward compatibility
window.showAlert = function(message, type = 'info', options = {}) {
    window.notificationManager.show(message, type, options);
};

window.showNotification = window.showAlert;
