{% extends "base.html" %}

{% block title %}{{ 'طباعة ملصقات الباركود' if language == 'ar' else 'Print Barcode Labels' }}{% endblock %}

{% block extra_css %}
<style>
.label-preview {
    border: 1px dashed #ccc;
    padding: 10px;
    margin: 5px;
    display: inline-block;
    text-align: center;
    background: white;
}

.label-size-30x20 {
    width: 30mm;
    height: 20mm;
}

.label-size-40x30 {
    width: 40mm;
    height: 30mm;
}

.label-size-50x30 {
    width: 50mm;
    height: 30mm;
}

.barcode-image {
    max-width: 100%;
    height: auto;
}

.product-name {
    font-size: 8px;
    margin: 2px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-price {
    font-size: 10px;
    font-weight: bold;
    margin: 2px 0;
}

.print-area {
    background: white;
    padding: 20px;
    margin-top: 20px;
}

@media print {
    .no-print {
        display: none !important;
    }
    
    .print-area {
        padding: 0;
        margin: 0;
    }
    
    .label-preview {
        border: none;
        page-break-inside: avoid;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid no-print">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة ملصقات الباركود' if language == 'ar' else 'Print Barcode Labels' }}
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('barcode.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        {{ 'العودة' if language == 'ar' else 'Back' }}
                    </a>
                    <button type="button" class="btn btn-primary" onclick="printLabels()">
                        <i class="bi bi-printer"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Settings -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'إعدادات الطباعة' if language == 'ar' else 'Print Settings' }}</h5>
                </div>
                <div class="card-body">
                    <form id="printForm">
                        <div class="mb-3">
                            <label for="labelSize" class="form-label">{{ 'حجم الملصق' if language == 'ar' else 'Label Size' }}</label>
                            <select class="form-select" id="labelSize" onchange="updatePreview()">
                                <option value="30x20">30mm x 20mm</option>
                                <option value="40x30" selected>40mm x 30mm</option>
                                <option value="50x30">50mm x 30mm</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="categoryFilter" class="form-label">{{ 'تصفية حسب الفئة' if language == 'ar' else 'Filter by Category' }}</label>
                            <select class="form-select" id="categoryFilter" onchange="filterProducts()">
                                <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.get_name(language) }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrice" checked onchange="updatePreview()">
                                <label class="form-check-label" for="showPrice">
                                    {{ 'إظهار السعر' if language == 'ar' else 'Show Price' }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showName" checked onchange="updatePreview()">
                                <label class="form-check-label" for="showName">
                                    {{ 'إظهار اسم المنتج' if language == 'ar' else 'Show Product Name' }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="copies" class="form-label">{{ 'عدد النسخ لكل منتج' if language == 'ar' else 'Copies per Product' }}</label>
                            <input type="number" class="form-control" id="copies" value="1" min="1" max="10" onchange="updatePreview()">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'اختيار المنتجات' if language == 'ar' else 'Select Products' }}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                            {{ 'تحديد الكل' if language == 'ar' else 'Select All' }}
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                            {{ 'إلغاء التحديد' if language == 'ar' else 'Select None' }}
                        </button>
                    </div>
                    
                    <div style="max-height: 300px; overflow-y: auto;">
                        {% for product in products %}
                        <div class="form-check product-item" data-category="{{ product.category_id or '' }}">
                            <input class="form-check-input product-checkbox" type="checkbox" 
                                   value="{{ product.id }}" id="product{{ product.id }}" 
                                   data-barcode="{{ product.barcode }}"
                                   data-name="{{ product.get_name(language) }}"
                                   data-price="{{ '%.2f'|format(product.get_final_price()) }}"
                                   onchange="updatePreview()">
                            <label class="form-check-label" for="product{{ product.id }}">
                                <strong>{{ product.get_name(language) }}</strong><br>
                                <small class="text-muted">{{ product.barcode }} - {{ '%.2f'|format(product.get_final_price()) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</small>
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Preview -->
<div class="print-area">
    <div id="labelContainer" class="text-center">
        <!-- Labels will be generated here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updatePreview() {
    const labelSize = document.getElementById('labelSize').value;
    const showPrice = document.getElementById('showPrice').checked;
    const showName = document.getElementById('showName').checked;
    const copies = parseInt(document.getElementById('copies').value) || 1;
    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
    
    const container = document.getElementById('labelContainer');
    container.innerHTML = '';
    
    selectedProducts.forEach(checkbox => {
        const barcode = checkbox.dataset.barcode;
        const name = checkbox.dataset.name;
        const price = checkbox.dataset.price;
        
        for (let i = 0; i < copies; i++) {
            const label = createLabel(barcode, name, price, labelSize, showPrice, showName);
            container.appendChild(label);
        }
    });
}

function createLabel(barcode, name, price, size, showPrice, showName) {
    const label = document.createElement('div');
    label.className = `label-preview label-size-${size}`;
    
    let content = `<img src="/barcode/image/${barcode}" class="barcode-image" alt="${barcode}">`;
    
    if (showName) {
        content += `<div class="product-name">${name}</div>`;
    }
    
    if (showPrice) {
        content += `<div class="product-price">${price} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</div>`;
    }
    
    label.innerHTML = content;
    return label;
}

function filterProducts() {
    const categoryId = document.getElementById('categoryFilter').value;
    const productItems = document.querySelectorAll('.product-item');
    
    productItems.forEach(item => {
        const itemCategory = item.dataset.category;
        if (!categoryId || itemCategory === categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
            // Uncheck hidden items
            const checkbox = item.querySelector('.product-checkbox');
            checkbox.checked = false;
        }
    });
    
    updatePreview();
}

function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.product-item:not([style*="display: none"]) .product-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updatePreview();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updatePreview();
}

function printLabels() {
    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
    
    if (selectedProducts.length === 0) {
        alert('{{ "يرجى اختيار منتج واحد على الأقل" if language == "ar" else "Please select at least one product" }}');
        return;
    }
    
    // Update preview before printing
    updatePreview();
    
    // Print
    window.print();
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Select first 5 products by default
    const checkboxes = document.querySelectorAll('.product-checkbox');
    for (let i = 0; i < Math.min(5, checkboxes.length); i++) {
        checkboxes[i].checked = true;
    }
    updatePreview();
});
</script>
{% endblock %}
