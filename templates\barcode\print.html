{% extends "base.html" %}

{% block title %}{{ 'طباعة ملصقات الباركود' if language == 'ar' else 'Print Barcode Labels' }}{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/barcode-labels.css') }}" rel="stylesheet">
<style>
.label-preview {
    border: 2px solid #333;
    padding: 8px;
    margin: 8px;
    display: inline-block;
    text-align: center;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
}

.label-size-30x20 {
    width: 30mm;
    height: 20mm;
    font-size: 6px;
}

.label-size-40x30 {
    width: 40mm;
    height: 30mm;
    font-size: 8px;
}

.label-size-50x30 {
    width: 50mm;
    height: 30mm;
    font-size: 9px;
}

.label-size-60x40 {
    width: 60mm;
    height: 40mm;
    font-size: 10px;
}

.barcode-image {
    max-width: 90%;
    height: auto;
    border: 1px solid #ddd;
    padding: 2px;
    background: white;
}

.product-name {
    font-size: 0.8em;
    margin: 3px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
    color: #333;
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 2px;
    border: 1px solid #dee2e6;
}

.product-price {
    font-size: 1em;
    font-weight: bold;
    margin: 4px 0;
    color: #1976d2;
    background: #e3f2fd;
    padding: 3px 6px;
    border-radius: 3px;
    border: 2px solid #1976d2;
}

.barcode-number {
    font-size: 0.7em;
    margin: 2px 0;
    font-family: 'Courier New', monospace;
    color: #666;
    background: white;
    padding: 2px 4px;
    border: 1px solid #999;
    border-radius: 2px;
    letter-spacing: 1px;
}

.print-area {
    background: white;
    padding: 20px;
    margin-top: 20px;
}

@media print {
    .no-print {
        display: none !important;
    }
    
    .print-area {
        padding: 0;
        margin: 0;
    }
    
    .label-preview {
        border: none;
        page-break-inside: avoid;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid no-print">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة ملصقات الباركود' if language == 'ar' else 'Print Barcode Labels' }}
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('barcode.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        {{ 'العودة' if language == 'ar' else 'Back' }}
                    </a>
                    <button type="button" class="btn btn-primary" onclick="printLabels()">
                        <i class="bi bi-printer"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Settings -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'إعدادات الطباعة' if language == 'ar' else 'Print Settings' }}</h5>
                </div>
                <div class="card-body">
                    <form id="printForm">
                        <div class="mb-3">
                            <label for="labelSize" class="form-label">{{ 'حجم الملصق' if language == 'ar' else 'Label Size' }}</label>
                            <select class="form-select" id="labelSize" onchange="updatePreview()">
                                <option value="30x20">30mm x 20mm ({{ 'صغير' if language == 'ar' else 'Small' }})</option>
                                <option value="40x30" selected>40mm x 30mm ({{ 'متوسط' if language == 'ar' else 'Medium' }})</option>
                                <option value="50x30">50mm x 30mm ({{ 'كبير' if language == 'ar' else 'Large' }})</option>
                                <option value="60x40">60mm x 40mm ({{ 'كبير جداً' if language == 'ar' else 'Extra Large' }})</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="categoryFilter" class="form-label">{{ 'تصفية حسب الفئة' if language == 'ar' else 'Filter by Category' }}</label>
                            <select class="form-select" id="categoryFilter" onchange="filterProducts()">
                                <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.get_name(language) }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrice" checked onchange="updatePreview()">
                                <label class="form-check-label" for="showPrice">
                                    {{ 'إظهار السعر' if language == 'ar' else 'Show Price' }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showName" checked onchange="updatePreview()">
                                <label class="form-check-label" for="showName">
                                    {{ 'إظهار اسم المنتج' if language == 'ar' else 'Show Product Name' }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showBarcodeNumber" checked onchange="updatePreview()">
                                <label class="form-check-label" for="showBarcodeNumber">
                                    {{ 'إظهار رقم الباركود' if language == 'ar' else 'Show Barcode Number' }}
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="copies" class="form-label">{{ 'عدد النسخ لكل منتج' if language == 'ar' else 'Copies per Product' }}</label>
                            <input type="number" class="form-control" id="copies" value="1" min="1" max="10" onchange="updatePreview()">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ 'اختيار المنتجات' if language == 'ar' else 'Select Products' }}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                            {{ 'تحديد الكل' if language == 'ar' else 'Select All' }}
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                            {{ 'إلغاء التحديد' if language == 'ar' else 'Select None' }}
                        </button>
                    </div>
                    
                    <div style="max-height: 300px; overflow-y: auto;">
                        {% for product in products %}
                        <div class="form-check product-item" data-category="{{ product.category_id or '' }}">
                            <input class="form-check-input product-checkbox" type="checkbox" 
                                   value="{{ product.id }}" id="product{{ product.id }}" 
                                   data-barcode="{{ product.barcode }}"
                                   data-name="{{ product.get_name(language) }}"
                                   data-price="{{ '%.2f'|format(product.get_final_price()) }}"
                                   onchange="updatePreview()">
                            <label class="form-check-label" for="product{{ product.id }}">
                                <strong>{{ product.get_name(language) }}</strong><br>
                                <small class="text-muted">{{ product.barcode }} - {{ '%.2f'|format(product.get_final_price()) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</small>
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Preview -->
<div class="print-area">
    <div class="container-fluid no-print mb-3">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    {{ 'معاينة الملصقات - استخدم زر الطباعة لطباعة الملصقات المحددة' if language == 'ar' else 'Label Preview - Use print button to print selected labels' }}
                </div>
            </div>
        </div>
    </div>

    <div id="labelContainer" class="label-container">
        <div class="text-muted text-center py-5">
            <i class="bi bi-upc-scan display-1 opacity-25"></i>
            <p class="mt-3">
                {{ 'اختر المنتجات لمعاينة الملصقات' if language == 'ar' else 'Select products to preview labels' }}
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updatePreview() {
    const labelSize = document.getElementById('labelSize').value;
    const showPrice = document.getElementById('showPrice').checked;
    const showName = document.getElementById('showName').checked;
    const showBarcodeNumber = document.getElementById('showBarcodeNumber').checked;
    const copies = parseInt(document.getElementById('copies').value) || 1;
    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');

    const container = document.getElementById('labelContainer');

    if (selectedProducts.length === 0) {
        container.innerHTML = `
            <div class="text-muted text-center py-5">
                <i class="bi bi-upc-scan display-1 opacity-25"></i>
                <p class="mt-3">
                    {{ 'اختر المنتجات لمعاينة الملصقات' if language == 'ar' else 'Select products to preview labels' }}
                </p>
            </div>
        `;
        return;
    }

    // Show loading
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="label-loading"></div>
            <p class="mt-3">{{ 'جاري تحميل الملصقات...' if language == 'ar' else 'Loading labels...' }}</p>
        </div>
    `;

    // Simulate loading delay for better UX
    setTimeout(() => {
        container.innerHTML = '';

        selectedProducts.forEach(checkbox => {
            const barcode = checkbox.dataset.barcode;
            const name = checkbox.dataset.name;
            const price = checkbox.dataset.price;

            for (let i = 0; i < copies; i++) {
                const label = createLabel(barcode, name, price, labelSize, showPrice, showName, showBarcodeNumber);
                container.appendChild(label);
            }
        });

        // Add summary info
        const summary = document.createElement('div');
        summary.className = 'col-12 mt-3 no-print';
        summary.innerHTML = `
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                {{ 'تم إنشاء' if language == 'ar' else 'Generated' }} ${selectedProducts.length * copies} {{ 'ملصق' if language == 'ar' else 'labels' }}
            </div>
        `;
        container.appendChild(summary);
    }, 300);
}

function createLabel(barcode, name, price, size, showPrice, showName, showBarcodeNumber) {
    const label = document.createElement('div');
    label.className = `label-preview label-size-${size} premium`;

    let content = '';

    // Add barcode type indicator
    content += `<div class="barcode-type">EAN</div>`;

    // Add product name at top
    if (showName) {
        content += `<div class="product-name" title="${name}">${name}</div>`;
    }

    // Add barcode image with error handling
    content += `
        <img src="/barcode/image/${barcode}"
             class="barcode-image"
             alt="${barcode}"
             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div style="display:none; padding:10px; background:#f8d7da; color:#721c24; border:1px solid #f5c6cb; border-radius:3px; font-size:0.7em;">
            {{ 'خطأ في تحميل الباركود' if language == 'ar' else 'Barcode loading error' }}
        </div>
    `;

    // Add price with currency
    if (showPrice) {
        content += `<div class="product-price">${price} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</div>`;
    }

    // Add separator line
    if (showBarcodeNumber && (showPrice || showName)) {
        content += `<div style="border-top: 1px dashed #ccc; margin: 2px 0;"></div>`;
    }

    // Add barcode number at bottom
    if (showBarcodeNumber) {
        content += `<div class="barcode-number" title="Barcode: ${barcode}">${barcode}</div>`;
    }

    label.innerHTML = content;

    // Add hover effect
    label.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.zIndex = '10';
    });

    label.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.zIndex = '1';
    });

    return label;
}

function filterProducts() {
    const categoryId = document.getElementById('categoryFilter').value;
    const productItems = document.querySelectorAll('.product-item');
    
    productItems.forEach(item => {
        const itemCategory = item.dataset.category;
        if (!categoryId || itemCategory === categoryId) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
            // Uncheck hidden items
            const checkbox = item.querySelector('.product-checkbox');
            checkbox.checked = false;
        }
    });
    
    updatePreview();
}

function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.product-item:not([style*="display: none"]) .product-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updatePreview();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updatePreview();
}

function printLabels() {
    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
    
    if (selectedProducts.length === 0) {
        alert('{{ "يرجى اختيار منتج واحد على الأقل" if language == "ar" else "Please select at least one product" }}');
        return;
    }
    
    // Update preview before printing
    updatePreview();
    
    // Print
    window.print();
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Select first 5 products by default
    const checkboxes = document.querySelectorAll('.product-checkbox');
    for (let i = 0; i < Math.min(5, checkboxes.length); i++) {
        checkboxes[i].checked = true;
    }
    updatePreview();
});
</script>
{% endblock %}
