# 🚀 البدء السريع | Quick Start

## نظام نقاط البيع القطري | Qatar POS System

### ⚡ التشغيل الفوري | Instant Run

#### الطريقة الأسرع - اختبار النظام:
```bash
# تشغيل خادم الاختبار (لا يحتاج تثبيت)
python test_server.py
```

#### لنظام Windows:
```bash
# 1. تثبيت المتطلبات
install.bat

# 2. تشغيل النظام
start.bat
```

#### لنظام Linux/Mac:
```bash
# 1. تثبيت المتطلبات
pip install flask

# 2. تشغيل النظام
python test_server.py
```

#### التشغيل المتقدم:
```bash
# تثبيت جميع المتطلبات
pip install Flask Flask-SQLAlchemy Flask-Login Flask-Migrate Flask-WTF python-dotenv

# تشغيل النظام الكامل
python run.py

# أو النظام البسيط
python simple_run.py
```

### 🌐 الوصول للنظام | System Access

بعد تشغيل النظام، افتح المتصفح وانتقل إلى:

- **الصفحة الرئيسية**: http://localhost:5000
- **صفحة الاختبار**: http://localhost:5000/test
- **تسجيل الدخول**: http://localhost:5000/auth/login

### 🔑 بيانات الدخول الافتراضية | Default Login

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### 🛠️ استكشاف الأخطاء | Troubleshooting

#### ❌ المشكلة: الرابط لا يعمل
**الحل**:
1. **تأكد من تشغيل الخادم**:
   ```bash
   # ابحث عن هذه الرسالة
   * Running on http://127.0.0.1:5000
   ```

2. **جرب روابط مختلفة**:
   - http://localhost:5000
   - http://127.0.0.1:5000
   - http://0.0.0.0:5000

3. **تحقق من المنفذ**:
   ```bash
   # تأكد أن المنفذ 5000 غير مستخدم
   netstat -an | findstr :5000
   ```

#### ❌ المشكلة: خطأ "ModuleNotFoundError"
**الحل**:
```bash
# تثبيت Flask فقط للاختبار
pip install flask

# تشغيل الخادم البسيط
python test_server.py
```

#### ❌ المشكلة: خطأ في قاعدة البيانات
**الحل**:
```bash
# احذف قاعدة البيانات وأعد إنشاءها (Windows)
del qatarpos.db
python run.py

# Linux/Mac
rm qatarpos.db
python run.py
```

#### ❌ المشكلة: Python غير مثبت
**الحل**:
1. **تحميل Python**: https://python.org/downloads/
2. **تأكد من إضافة Python للـ PATH**
3. **إعادة تشغيل Command Prompt**
4. **اختبار**: `python --version`

### 📱 اختبار النظام | System Testing

1. **افتح صفحة الاختبار**: http://localhost:5000/test
2. **تحقق من حالة النظام**: http://localhost:5000/health
3. **جرب تسجيل الدخول**: http://localhost:5000/auth/login

### 🔧 الإعداد المتقدم | Advanced Setup

#### إعداد قاعدة بيانات PostgreSQL:
```bash
# تحديث ملف .env
DATABASE_URL=postgresql://username:password@localhost/qatarpos
```

#### إعداد Redis للتخزين المؤقت:
```bash
# تحديث ملف .env
REDIS_URL=redis://localhost:6379/0
```

### 📞 الدعم | Support

إذا واجهت أي مشاكل:

1. **تحقق من الأخطاء في Terminal**
2. **راجع ملف logs/qatarpos.log**
3. **تأكد من تثبيت Python 3.8+**
4. **تحقق من توفر المنافذ (Port 5000)**

### 🎉 مبروك!

إذا رأيت الصفحة تعمل، فقد نجحت في تشغيل نظام نقاط البيع القطري! 🇶🇦

---

**صُنع بـ ❤️ في قطر | Made with ❤️ in Qatar**
