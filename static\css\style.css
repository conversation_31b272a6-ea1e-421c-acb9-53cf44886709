/* Qatar POS System - Custom Styles */

:root {
    --primary-color: #8B1538;  /* Qatar Maroon */
    --secondary-color: #FFFFFF; /* White */
    --accent-color: #FFD700;    /* Gold */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Custom Bootstrap Theme */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #6d1028;
    border-color: #6d1028;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #e3e6f0;
}

/* Border Left Cards for Dashboard */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

/* Icon Circle */
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin: 0 0.1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Tables */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-responsive {
    border-radius: 0.5rem;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.25);
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Badges */
.badge {
    font-weight: 500;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* POS Interface Styles */
.pos-container {
    height: calc(100vh - 120px);
}

.pos-products {
    height: 100%;
    overflow-y: auto;
}

.pos-cart {
    height: 100%;
    border-left: 1px solid #dee2e6;
}

.product-card {
    cursor: pointer;
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.cart-item {
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

/* Search and Filter Styles */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Status Badges */
.status-active {
    background-color: var(--success-color);
}

.status-inactive {
    background-color: var(--danger-color);
}

.status-pending {
    background-color: var(--warning-color);
}

.status-completed {
    background-color: var(--success-color);
}

.status-cancelled {
    background-color: var(--danger-color);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12px;
    }
    
    .container-fluid {
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .pos-container {
        height: auto;
    }
    
    .pos-cart {
        border-left: none;
        border-top: 1px solid #dee2e6;
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0;
    }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
