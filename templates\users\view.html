{% extends "base.html" %}

{% block title %}
{{ 'تفاصيل المستخدم - نظام نقاط البيع القطري' if language == 'ar' else 'User Details - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.user-info-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.user-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.role-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.role-admin {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.role-manager {
    background: linear-gradient(135deg, #4834d4, #686de0);
    color: white;
}

.role-seller {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
    color: white;
}

.role-accountant {
    background: linear-gradient(135deg, #5f27cd, #a55eea);
    color: white;
}

.role-inventory_manager {
    background: linear-gradient(135deg, #00d2d3, #01a3a4);
    color: white;
}

.info-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.info-item:hover {
    background-color: #f8f9fa;
}

.info-item:last-child {
    border-bottom: none;
}

.action-buttons .btn {
    margin: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-person-circle"></i>
                {{ 'تفاصيل المستخدم' if language == 'ar' else 'User Details' }}
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('dashboard.index') }}">
                            {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('users.index') }}">
                            {{ 'المستخدمون' if language == 'ar' else 'Users' }}
                        </a>
                    </li>
                    <li class="breadcrumb-item active">
                        {{ user.get_display_name(language) }}
                    </li>
                </ol>
            </nav>
        </div>
        <div class="action-buttons">
            <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i>
                {{ 'تعديل' if language == 'ar' else 'Edit' }}
            </a>
            <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Info Card -->
        <div class="col-md-8">
            <div class="card user-info-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i>
                        {{ 'معلومات المستخدم' if language == 'ar' else 'User Information' }}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <!-- Basic Info -->
                    <div class="info-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="user-avatar">
                                    {{ user.first_name_ar[0] if user.first_name_ar else user.username[0] }}
                                </div>
                            </div>
                            <div class="col">
                                <h4 class="mb-1">{{ user.get_display_name(language) }}</h4>
                                <p class="text-muted mb-1">@{{ user.username }}</p>
                                <span class="user-status {{ 'status-active' if user.is_active else 'status-inactive' }}">
                                    {{ 'نشط' if user.is_active else 'غير نشط' if language == 'ar' else 'Active' if user.is_active else 'Inactive' }}
                                </span>
                            </div>
                            <div class="col-auto">
                                <span class="role-badge role-{{ user.role }}">
                                    {% if user.role == 'admin' %}
                                        {{ 'مدير النظام' if language == 'ar' else 'Admin' }}
                                    {% elif user.role == 'manager' %}
                                        {{ 'مدير' if language == 'ar' else 'Manager' }}
                                    {% elif user.role == 'seller' %}
                                        {{ 'بائع' if language == 'ar' else 'Seller' }}
                                    {% elif user.role == 'accountant' %}
                                        {{ 'محاسب' if language == 'ar' else 'Accountant' }}
                                    {% elif user.role == 'inventory_manager' %}
                                        {{ 'مدير مخزون' if language == 'ar' else 'Inventory Manager' }}
                                    {% else %}
                                        {{ user.role }}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-envelope"></i>
                                    {{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                            </div>
                        </div>
                    </div>

                    {% if user.phone %}
                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-telephone"></i>
                                    {{ 'الهاتف:' if language == 'ar' else 'Phone:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                <a href="tel:{{ user.phone }}">{{ user.phone }}</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Names -->
                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-person"></i>
                                    {{ 'الاسم بالعربية:' if language == 'ar' else 'Arabic Name:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.get_full_name('ar') }}
                            </div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-person"></i>
                                    {{ 'الاسم بالإنجليزية:' if language == 'ar' else 'English Name:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.get_full_name('en') }}
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-calendar-plus"></i>
                                    {{ 'تاريخ الإنشاء:' if language == 'ar' else 'Created:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                        </div>
                    </div>

                    {% if user.last_login %}
                    <div class="info-item">
                        <div class="row">
                            <div class="col-sm-3">
                                <strong>
                                    <i class="bi bi-clock-history"></i>
                                    {{ 'آخر دخول:' if language == 'ar' else 'Last Login:' }}
                                </strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-gear"></i>
                        {{ 'الإجراءات' if language == 'ar' else 'Actions' }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Edit User -->
                    <div class="d-grid gap-2 mb-3">
                        <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-primary">
                            <i class="bi bi-pencil"></i>
                            {{ 'تعديل المستخدم' if language == 'ar' else 'Edit User' }}
                        </a>
                    </div>

                    <!-- Reset Password -->
                    <div class="d-grid gap-2 mb-3">
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                            <i class="bi bi-key"></i>
                            {{ 'إعادة تعيين كلمة المرور' if language == 'ar' else 'Reset Password' }}
                        </button>
                    </div>

                    <!-- Toggle Status -->
                    {% if user.id != current_user.id %}
                    <div class="d-grid gap-2 mb-3">
                        <form method="POST" action="{{ url_for('users.toggle_status', user_id=user.id) }}"
                              onsubmit="return confirm('{{ 'هل أنت متأكد؟' if language == 'ar' else 'Are you sure?' }}')">
                            <button type="submit" class="btn {{ 'btn-danger' if user.is_active else 'btn-success' }} w-100">
                                <i class="bi {{ 'bi-person-x' if user.is_active else 'bi-person-check' }}"></i>
                                {{ 'تعطيل المستخدم' if user.is_active else 'تفعيل المستخدم' if language == 'ar' else 'Deactivate User' if user.is_active else 'Activate User' }}
                            </button>
                        </form>
                    </div>
                    {% endif %}

                    <!-- Back to List -->
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة للقائمة' if language == 'ar' else 'Back to List' }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Statistics -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i>
                        {{ 'الإحصائيات' if language == 'ar' else 'Statistics' }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if user.role in ['seller', 'manager', 'admin'] %}
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary">{{ user.sales.count() }}</h4>
                            <small class="text-muted">{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</small>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row text-center">
                        <div class="col-12">
                            <h6 class="text-muted">{{ 'عضو منذ' if language == 'ar' else 'Member since' }}</h6>
                            <p>{{ user.created_at.strftime('%B %Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{ 'إعادة تعيين كلمة المرور' if language == 'ar' else 'Reset Password' }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('users.reset_password', user_id=user.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">
                            {{ 'كلمة المرور الجديدة:' if language == 'ar' else 'New Password:' }}
                        </label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">
                            {{ 'تأكيد كلمة المرور:' if language == 'ar' else 'Confirm Password:' }}
                        </label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}