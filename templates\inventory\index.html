{% extends "base.html" %}

{% block title %}
{{ 'إدارة المخزون - نظام نقاط البيع القطري' if language == 'ar' else 'Inventory Management - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-boxes"></i>
                {{ 'إدارة المخزون' if language == 'ar' else 'Inventory Management' }}
            </h1>
            <div>
                {% if current_user.has_permission('inventory') %}
                <a href="{{ url_for('inventory.create_adjustment') }}" class="btn btn-warning">
                    <i class="bi bi-gear"></i>
                    {{ 'تعديل المخزون' if language == 'ar' else 'Stock Adjustment' }}
                </a>
                {% endif %}
                <a href="{{ url_for('inventory.stock_levels_report') }}" class="btn btn-outline-primary">
                    <i class="bi bi-file-text"></i>
                    {{ 'تقرير المخزون' if language == 'ar' else 'Stock Report' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock Items' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.low_stock_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.out_of_stock_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'قيمة المخزون' if language == 'ar' else 'Inventory Value' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set total_value = low_stock_products | sum(attribute='current_stock') * low_stock_products | sum(attribute='cost_price') %}
                            {{ '{:,.0f} ر.ق'.format(total_value) if language == 'ar' else 'QAR {:,.0f}'.format(total_value) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Low Stock Products -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">
                    {{ 'منتجات بمخزون منخفض' if language == 'ar' else 'Low Stock Products' }}
                </h6>
                <a href="{{ url_for('products.index', stock='low') }}" class="btn btn-sm btn-outline-warning">
                    {{ 'عرض الكل' if language == 'ar' else 'View All' }}
                </a>
            </div>
            <div class="card-body">
                {% if low_stock_products %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                <th>{{ 'الحد الأدنى' if language == 'ar' else 'Min Stock' }}</th>
                                <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in low_stock_products %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ product.get_name(language) }}</strong>
                                        <br>
                                        <small class="text-muted">{{ product.sku }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if product.is_out_of_stock() else 'warning' }}">
                                        {{ product.current_stock }}
                                    </span>
                                </td>
                                <td>{{ product.minimum_stock }}</td>
                                <td>
                                    {% if product.is_out_of_stock() %}
                                    <span class="badge bg-danger">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</span>
                                    {% else %}
                                    <span class="badge bg-warning">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('products.view', product_id=product.id) }}" 
                                           class="btn btn-outline-primary" title="{{ 'عرض' if language == 'ar' else 'View' }}">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        {% if current_user.has_permission('inventory') %}
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="quickAdjustStock({{ product.id }}, '{{ product.get_name(language) }}', {{ product.current_stock }})"
                                                title="{{ 'تعديل سريع' if language == 'ar' else 'Quick Adjust' }}">
                                            <i class="bi bi-gear"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-check-circle display-4 text-success"></i>
                    <h5 class="mt-3 text-success">{{ 'ممتاز!' if language == 'ar' else 'Excellent!' }}</h5>
                    <p class="text-muted">{{ 'جميع المنتجات لديها مخزون كافي' if language == 'ar' else 'All products have sufficient stock' }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'حركات المخزون الأخيرة' if language == 'ar' else 'Recent Inventory Transactions' }}
                </h6>
                <a href="{{ url_for('inventory.transactions') }}" class="btn btn-sm btn-outline-primary">
                    {{ 'عرض الكل' if language == 'ar' else 'View All' }}
                </a>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                    {% for transaction in recent_transactions %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <div class="icon-circle bg-{{ 'success' if transaction.quantity_change > 0 else 'danger' }}">
                                <i class="bi bi-{{ 'plus' if transaction.quantity_change > 0 else 'dash' }} text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ transaction.transaction_date.strftime('%H:%M') }}</div>
                            <div class="font-weight-bold">{{ transaction.product.get_name(language) }}</div>
                            <div class="text-xs text-gray-500">
                                {{ transaction.transaction_type.title() }}
                                {% if transaction.quantity_change > 0 %}+{% endif %}{{ transaction.quantity_change }}
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-weight-bold">{{ transaction.new_quantity }}</div>
                            <div class="text-xs text-gray-500">{{ 'المخزون' if language == 'ar' else 'Stock' }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">
                        {{ 'لا توجد حركات مخزون حديثة' if language == 'ar' else 'No recent inventory transactions' }}
                    </p>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    {{ 'إجراءات سريعة' if language == 'ar' else 'Quick Actions' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if current_user.has_permission('inventory') %}
                    <a href="{{ url_for('inventory.create_adjustment') }}" class="btn btn-warning">
                        <i class="bi bi-gear"></i>
                        {{ 'تعديل المخزون' if language == 'ar' else 'Stock Adjustment' }}
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('inventory.transactions') }}" class="btn btn-outline-primary">
                        <i class="bi bi-clock-history"></i>
                        {{ 'حركات المخزون' if language == 'ar' else 'Inventory Transactions' }}
                    </a>
                    
                    <a href="{{ url_for('inventory.stock_levels_report') }}" class="btn btn-outline-info">
                        <i class="bi bi-file-text"></i>
                        {{ 'تقرير مستويات المخزون' if language == 'ar' else 'Stock Levels Report' }}
                    </a>
                    
                    <a href="{{ url_for('products.index', stock='low') }}" class="btn btn-outline-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        {{ 'منتجات بمخزون منخفض' if language == 'ar' else 'Low Stock Products' }}
                    </a>
                    
                    <a href="{{ url_for('products.index', stock='out') }}" class="btn btn-outline-danger">
                        <i class="bi bi-x-circle"></i>
                        {{ 'منتجات نفد مخزونها' if language == 'ar' else 'Out of Stock Products' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stock Adjustment Modal -->
<div class="modal fade" id="quickAdjustModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'تعديل سريع للمخزون' if language == 'ar' else 'Quick Stock Adjustment' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickAdjustForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ 'المنتج' if language == 'ar' else 'Product' }}</label>
                        <input type="text" class="form-control" id="product_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</label>
                        <input type="number" class="form-control" id="current_stock" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'المخزون الجديد' if language == 'ar' else 'New Stock' }}</label>
                        <input type="number" class="form-control" name="new_stock" id="new_stock" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'السبب' if language == 'ar' else 'Reason' }}</label>
                        <select class="form-select" name="reason" required>
                            <option value="physical_count">{{ 'جرد فعلي' if language == 'ar' else 'Physical Count' }}</option>
                            <option value="damage">{{ 'تلف' if language == 'ar' else 'Damage' }}</option>
                            <option value="theft">{{ 'سرقة' if language == 'ar' else 'Theft' }}</option>
                            <option value="expired">{{ 'منتهي الصلاحية' if language == 'ar' else 'Expired' }}</option>
                            <option value="other">{{ 'أخرى' if language == 'ar' else 'Other' }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'ملاحظات' if language == 'ar' else 'Notes' }}</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        {{ 'تعديل المخزون' if language == 'ar' else 'Adjust Stock' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickAdjustStock(productId, productName, currentStock) {
    document.getElementById('product_name').value = productName;
    document.getElementById('current_stock').value = currentStock;
    document.getElementById('new_stock').value = currentStock;
    
    const form = document.getElementById('quickAdjustForm');
    form.action = `/inventory/api/quick-adjust/${productId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('quickAdjustModal'));
    modal.show();
    
    setTimeout(() => {
        document.getElementById('new_stock').focus();
        document.getElementById('new_stock').select();
    }, 500);
}

// Handle quick adjust form submission
document.getElementById('quickAdjustForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    try {
        const response = await fetch(this.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('quickAdjustModal')).hide();
            location.reload(); // Refresh the page to show updated data
        } else {
            alert(result.error || 'خطأ في تعديل المخزون');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('خطأ في تعديل المخزون');
    }
});
</script>
{% endblock %}
