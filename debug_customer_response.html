<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
إضافة عميل جديد - نظام نقاط البيع القطري
</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    
    <!-- RTL Support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="/static/css/rtl.css" rel="stylesheet">
    
    
    
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-shop"></i>
                نظام نقاط البيع
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-cart"></i>
                            المبيعات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/sales/pos">
                                <i class="bi bi-calculator"></i>
                                نقطة البيع
                            </a></li>
                            <li><a class="dropdown-item" href="/sales/">
                                <i class="bi bi-list-ul"></i>
                                قائمة المبيعات
                            </a></li>
                        </ul>
                    </li>
                    
                    
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-box"></i>
                            المنتجات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/products/">
                                <i class="bi bi-list-ul"></i>
                                قائمة المنتجات
                            </a></li>
                            <li><a class="dropdown-item" href="/products/categories">
                                <i class="bi bi-tags"></i>
                                الفئات
                            </a></li>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/products/create">
                                <i class="bi bi-plus-circle"></i>
                                إضافة منتج
                            </a></li>
                            
                        </ul>
                    </li>
                    
                    
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-boxes"></i>
                            المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/inventory/">
                                <i class="bi bi-speedometer2"></i>
                                نظرة عامة
                            </a></li>
                            <li><a class="dropdown-item" href="/inventory/transactions">
                                <i class="bi bi-arrow-left-right"></i>
                                حركات المخزون
                            </a></li>
                            
                            <li><a class="dropdown-item" href="/inventory/adjustments">
                                <i class="bi bi-gear"></i>
                                تعديلات المخزون
                            </a></li>
                            
                        </ul>
                    </li>
                    
                    
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/customers/">
                            <i class="bi bi-people"></i>
                            العملاء
                        </a>
                    </li>
                    
                    
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/suppliers/">
                            <i class="bi bi-truck"></i>
                            الموردين
                        </a>
                    </li>
                    
                    
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/reports/">
                            <i class="bi bi-graph-up"></i>
                            التقارير
                        </a>
                    </li>
                    
                    
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-translate"></i>
                            العربية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/auth/switch-language/ar">العربية</a></li>
                            <li><a class="dropdown-item" href="/auth/switch-language/en">English</a></li>
                        </ul>
                    </li>
                    
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            المدير العام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/auth/profile">
                                <i class="bi bi-person"></i>
                                الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="/auth/change-password">
                                <i class="bi bi-key"></i>
                                تغيير كلمة المرور
                            </a></li>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/users/">
                                <i class="bi bi-people"></i>
                                إدارة المستخدمين
                            </a></li>
                            
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/auth/logout">
                                <i class="bi bi-box-arrow-right"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Flash Messages -->
        
            
                
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        حدث خطأ أثناء إنشاء العميل
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                
            
        
        
        
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i>
                إضافة عميل جديد
            </h1>
            <a href="/customers/" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات العميل
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- Customer Type Selection -->
                    <div class="mb-4">
                        <label class="form-label">نوع العميل</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="customer_type" id="individual" value="individual" checked>
                            <label class="btn btn-outline-primary" for="individual">
                                <i class="bi bi-person"></i>
                                فرد
                            </label>
                            
                            <input type="radio" class="btn-check" name="customer_type" id="company" value="company">
                            <label class="btn btn-outline-primary" for="company">
                                <i class="bi bi-building"></i>
                                شركة
                            </label>
                        </div>
                    </div>
                    
                    <!-- Individual Fields -->
                    <div id="individual_fields">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name_ar" class="form-label">
                                    الاسم الأول بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_ar" name="first_name_ar" required>
                                <div class="invalid-feedback">
                                    الاسم الأول بالعربية مطلوب
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="first_name_en" class="form-label">
                                    الاسم الأول بالإنجليزية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_en" name="first_name_en" required>
                                <div class="invalid-feedback">
                                    الاسم الأول بالإنجليزية مطلوب
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="last_name_ar" class="form-label">
                                    اسم العائلة بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_ar" name="last_name_ar" required>
                                <div class="invalid-feedback">
                                    اسم العائلة بالعربية مطلوب
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name_en" class="form-label">
                                    اسم العائلة بالإنجليزية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_en" name="last_name_en" required>
                                <div class="invalid-feedback">
                                    اسم العائلة بالإنجليزية مطلوب
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Company Fields -->
                    <div id="company_fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name_ar" class="form-label">
                                    اسم الشركة بالعربية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_ar" name="company_name_ar">
                                <div class="invalid-feedback">
                                    اسم الشركة بالعربية مطلوب
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_name_en" class="form-label">
                                    اسم الشركة بالإنجليزية
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_en" name="company_name_en">
                                <div class="invalid-feedback">
                                    اسم الشركة بالإنجليزية مطلوب
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_person_ar" class="form-label">
                                    جهة الاتصال بالعربية
                                </label>
                                <input type="text" class="form-control" id="contact_person_ar" name="contact_person_ar">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_person_en" class="form-label">
                                    جهة الاتصال بالإنجليزية
                                </label>
                                <input type="text" class="form-control" id="contact_person_en" name="contact_person_en">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="commercial_registration" class="form-label">
                                    السجل التجاري
                                </label>
                                <input type="text" class="form-control" id="commercial_registration" name="commercial_registration">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">
                                    الرقم الضريبي
                                </label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <hr>
                    <h6 class="mb-3">معلومات الاتصال</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                رقم الهاتف
                                <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" required
                                   placeholder="+974-XXXX-XXXX">
                            <div class="invalid-feedback">
                                رقم الهاتف مطلوب
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="<EMAIL>">
                            <div class="invalid-feedback">
                                البريد الإلكتروني غير صحيح
                            </div>
                        </div>
                    </div>
                    
                    <!-- Address Information -->
                    <hr>
                    <h6 class="mb-3">معلومات العنوان</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="address_ar" class="form-label">
                                العنوان بالعربية
                            </label>
                            <textarea class="form-control" id="address_ar" name="address_ar" rows="3"></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address_en" class="form-label">
                                العنوان بالإنجليزية
                            </label>
                            <textarea class="form-control" id="address_en" name="address_en" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="city_ar" class="form-label">
                                المدينة بالعربية
                            </label>
                            <input type="text" class="form-control" id="city_ar" name="city_ar">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="city_en" class="form-label">
                                المدينة بالإنجليزية
                            </label>
                            <input type="text" class="form-control" id="city_en" name="city_en">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="postal_code" class="form-label">
                                الرمز البريدي
                            </label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code">
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <hr>
                    <h6 class="mb-3">معلومات إضافية</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="qatar_id" class="form-label">
                                رقم الهوية القطرية
                            </label>
                            <input type="text" class="form-control" id="qatar_id" name="qatar_id"
                                   placeholder="12345678901">
                            <div class="invalid-feedback">
                                رقم الهوية القطرية يجب أن يكون 11 رقم
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/customers/" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; 2024 نظام نقاط البيع القطري. 
                جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    
    
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Customer type toggle
document.querySelectorAll('input[name="customer_type"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const individualFields = document.getElementById('individual_fields');
        const companyFields = document.getElementById('company_fields');
        
        if (this.value === 'individual') {
            individualFields.style.display = 'block';
            companyFields.style.display = 'none';
            
            // Set required attributes
            document.querySelectorAll('#individual_fields input[required]').forEach(input => input.required = true);
            document.querySelectorAll('#company_fields input').forEach(input => input.required = false);
        } else {
            individualFields.style.display = 'none';
            companyFields.style.display = 'block';
            
            // Set required attributes
            document.querySelectorAll('#individual_fields input').forEach(input => input.required = false);
            document.querySelectorAll('#company_fields input[data-required]').forEach(input => input.required = true);
        }
    });
});

// Qatar ID validation
document.getElementById('qatar_id').addEventListener('input', function() {
    const value = this.value.replace(/\D/g, '');
    this.value = value;
    
    if (value.length > 0 && value.length !== 11) {
        this.setCustomValidity('رقم الهوية القطرية يجب أن يكون 11 رقم');
    } else {
        this.setCustomValidity('');
    }
});

// Phone number formatting
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    
    // Add Qatar country code if not present
    if (value.length > 0 && !value.startsWith('974')) {
        if (value.startsWith('0')) {
            value = '974' + value.substring(1);
        } else if (value.length === 8) {
            value = '974' + value;
        }
    }
    
    // Format the number
    if (value.length >= 3) {
        value = '+' + value.substring(0, 3) + '-' + value.substring(3);
    }
    
    this.value = value;
});
</script>

</body>
</html>