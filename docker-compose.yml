# Qatar POS System - Docker Compose Configuration
version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: qatarpos_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: qatarpos
      POSTGRES_USER: qatarpos
      POSTGRES_PASSWORD: ${DB_PASSWORD:-qatarpos_secure_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - qatarpos_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U qatarpos -d qatarpos"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: qatarpos_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - qatarpos_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qatar POS Application
  app:
    build: .
    container_name: qatarpos_app
    restart: unless-stopped
    environment:
      # Database Configuration
      DATABASE_URL: postgresql://qatarpos:${DB_PASSWORD:-qatarpos_secure_password}@db:5432/qatarpos
      
      # Redis Configuration
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_secure_password}@redis:6379/0
      
      # Application Configuration
      SECRET_KEY: ${SECRET_KEY:-your_very_secure_secret_key_here}
      FLASK_ENV: production
      FLASK_DEBUG: 0
      
      # Security Configuration
      WTF_CSRF_ENABLED: 1
      SESSION_COOKIE_SECURE: 1
      SESSION_COOKIE_HTTPONLY: 1
      
      # Email Configuration (for notifications)
      MAIL_SERVER: ${MAIL_SERVER:-smtp.gmail.com}
      MAIL_PORT: ${MAIL_PORT:-587}
      MAIL_USE_TLS: ${MAIL_USE_TLS:-1}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      
      # Qatar Specific Configuration
      DEFAULT_TIMEZONE: Asia/Qatar
      DEFAULT_CURRENCY: QAR
      DEFAULT_LANGUAGE: ar
      
      # File Upload Configuration
      MAX_CONTENT_LENGTH: 16777216  # 16MB
      UPLOAD_FOLDER: /app/uploads
      
      # Backup Configuration
      BACKUP_FOLDER: /app/backups
      
    volumes:
      - app_uploads:/app/uploads
      - app_backups:/app/backups
      - app_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - qatarpos_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: qatarpos_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - qatarpos_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backup Service
  backup:
    build: 
      context: .
      dockerfile: docker/Dockerfile.backup
    container_name: qatarpos_backup
    restart: unless-stopped
    environment:
      DATABASE_URL: postgresql://qatarpos:${DB_PASSWORD:-qatarpos_secure_password}@db:5432/qatarpos
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM
      BACKUP_RETENTION_DAYS: 30
      S3_BUCKET: ${S3_BACKUP_BUCKET}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION:-me-south-1}  # Middle East (Bahrain) - closest to Qatar
    volumes:
      - app_backups:/backups
      - postgres_data:/var/lib/postgresql/data:ro
    depends_on:
      - db
    networks:
      - qatarpos_network

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: qatarpos_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - qatarpos_network
    profiles:
      - monitoring

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: qatarpos_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - qatarpos_network
    profiles:
      - monitoring

# Named Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_backups:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Networks
networks:
  qatarpos_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
