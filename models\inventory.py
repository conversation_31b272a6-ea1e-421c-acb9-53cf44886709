"""
Inventory management models for Qatar POS System
Tracks stock movements, transactions, and inventory adjustments
"""

from datetime import datetime
from extensions import db

class InventoryTransaction(db.Model):
    """Track all inventory movements"""
    
    __tablename__ = 'inventory_transactions'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.In<PERSON>ger, db.ForeignKey('products.id'), nullable=False)
    
    # Transaction details
    transaction_type = db.Column(db.Enum('sale', 'purchase', 'adjustment', 'return', 
                                        'damage', 'transfer', name='transaction_types'),
                                nullable=False)
    quantity_change = db.Column(db.Integer, nullable=False)  # Positive for in, negative for out
    
    # Stock levels
    old_quantity = db.Column(db.Integer, nullable=False)
    new_quantity = db.Column(db.Integer, nullable=False)
    
    # Cost information
    unit_cost = db.Column(db.Numeric(10, 3), default=0)
    total_cost = db.Column(db.Numeric(10, 2), default=0)
    
    # References
    reference_type = db.Column(db.String(50))  # 'sale', 'purchase_order', 'adjustment'
    reference_id = db.Column(db.Integer)
    reference_number = db.Column(db.String(50))
    
    # User and timestamp
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    transaction_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Notes
    notes = db.Column(db.Text)
    
    # Relationships
    user = db.relationship('User', backref='inventory_transactions')
    
    def to_dict(self, language='ar'):
        """Convert transaction to dictionary"""
        return {
            'id': self.id,
            'product_name': self.product.get_name(language),
            'product_sku': self.product.sku,
            'transaction_type': self.transaction_type,
            'quantity_change': self.quantity_change,
            'old_quantity': self.old_quantity,
            'new_quantity': self.new_quantity,
            'unit_cost': float(self.unit_cost),
            'total_cost': float(self.total_cost),
            'reference_type': self.reference_type,
            'reference_number': self.reference_number,
            'user_name': self.user.get_full_name(language) if self.user else None,
            'transaction_date': self.transaction_date.isoformat(),
            'notes': self.notes
        }
    
    @staticmethod
    def create_transaction(product, quantity_change, transaction_type, 
                          user_id=None, reference_type=None, reference_id=None,
                          reference_number=None, unit_cost=0, notes=None):
        """Create a new inventory transaction"""
        old_quantity = product.current_stock
        new_quantity = old_quantity + quantity_change
        
        transaction = InventoryTransaction(
            product_id=product.id,
            transaction_type=transaction_type,
            quantity_change=quantity_change,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            unit_cost=unit_cost,
            total_cost=abs(quantity_change) * unit_cost,
            reference_type=reference_type,
            reference_id=reference_id,
            reference_number=reference_number,
            user_id=user_id,
            notes=notes
        )
        
        # Update product stock
        product.current_stock = new_quantity
        
        db.session.add(transaction)
        return transaction
    
    def __repr__(self):
        return f'<InventoryTransaction {self.product.sku}: {self.quantity_change}>'


class StockAdjustment(db.Model):
    """Stock adjustment records for inventory corrections"""
    
    __tablename__ = 'stock_adjustments'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Adjustment details
    adjustment_number = db.Column(db.String(20), unique=True, nullable=False)
    adjustment_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Reason and notes
    reason = db.Column(db.Enum('physical_count', 'damage', 'theft', 'expired', 
                              'system_error', 'other', name='adjustment_reasons'),
                      nullable=False)
    notes = db.Column(db.Text)
    
    # User who made the adjustment
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Status
    status = db.Column(db.Enum('draft', 'approved', 'cancelled', name='adjustment_statuses'),
                      default='draft', nullable=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='adjustments_created')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='adjustments_approved')
    items = db.relationship('StockAdjustmentItem', backref='adjustment', 
                           cascade='all, delete-orphan')
    
    def calculate_total_value(self):
        """Calculate total value of adjustment"""
        return sum(item.value_change for item in self.items)
    
    def approve(self, approver_id):
        """Approve the stock adjustment"""
        if self.status != 'draft':
            return False
        
        # Create inventory transactions for each item
        for item in self.items:
            InventoryTransaction.create_transaction(
                product=item.product,
                quantity_change=item.quantity_change,
                transaction_type='adjustment',
                user_id=approver_id,
                reference_type='stock_adjustment',
                reference_id=self.id,
                reference_number=self.adjustment_number,
                unit_cost=item.unit_cost,
                notes=f"Stock adjustment: {self.reason}"
            )
        
        self.status = 'approved'
        self.approved_by = approver_id
        self.approved_at = datetime.utcnow()
        
        return True
    
    def to_dict(self, language='ar'):
        """Convert adjustment to dictionary"""
        return {
            'id': self.id,
            'adjustment_number': self.adjustment_number,
            'adjustment_date': self.adjustment_date.isoformat(),
            'reason': self.reason,
            'notes': self.notes,
            'status': self.status,
            'user_name': self.user.get_full_name(language),
            'approver_name': self.approver.get_full_name(language) if self.approver else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'items_count': len(self.items),
            'total_value': float(self.calculate_total_value())
        }
    
    @staticmethod
    def generate_adjustment_number():
        """Generate unique adjustment number"""
        from datetime import datetime
        today = datetime.now()
        prefix = f"ADJ{today.strftime('%Y%m%d')}"
        
        last_adjustment = StockAdjustment.query.filter(
            StockAdjustment.adjustment_number.like(f"{prefix}%")
        ).order_by(StockAdjustment.adjustment_number.desc()).first()
        
        if last_adjustment:
            last_number = int(last_adjustment.adjustment_number[-3:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:03d}"
    
    def __repr__(self):
        return f'<StockAdjustment {self.adjustment_number}>'


class StockAdjustmentItem(db.Model):
    """Individual items in a stock adjustment"""
    
    __tablename__ = 'stock_adjustment_items'
    
    id = db.Column(db.Integer, primary_key=True)
    adjustment_id = db.Column(db.Integer, db.ForeignKey('stock_adjustments.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Quantities
    old_quantity = db.Column(db.Integer, nullable=False)
    new_quantity = db.Column(db.Integer, nullable=False)
    quantity_change = db.Column(db.Integer, nullable=False)
    
    # Cost information
    unit_cost = db.Column(db.Numeric(10, 3), nullable=False)
    value_change = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Notes for this specific item
    notes = db.Column(db.Text)
    
    # Relationships
    product = db.relationship('Product')
    
    def calculate_values(self):
        """Calculate quantity change and value change"""
        self.quantity_change = self.new_quantity - self.old_quantity
        self.value_change = self.quantity_change * self.unit_cost
    
    def to_dict(self, language='ar'):
        """Convert adjustment item to dictionary"""
        return {
            'id': self.id,
            'product_name': self.product.get_name(language),
            'product_sku': self.product.sku,
            'old_quantity': self.old_quantity,
            'new_quantity': self.new_quantity,
            'quantity_change': self.quantity_change,
            'unit_cost': float(self.unit_cost),
            'value_change': float(self.value_change),
            'notes': self.notes
        }
    
    def __repr__(self):
        return f'<StockAdjustmentItem {self.product.sku}: {self.quantity_change}>'
