#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإعدادات
Test Settings System
"""

import requests
from app import create_app
from extensions import db
from models.user import User
from models.setting import Setting, SystemInfo

def test_settings_system():
    """اختبار نظام الإعدادات"""
    print("🇶🇦 نظام نقاط البيع القطري - اختبار نظام الإعدادات")
    print("=" * 70)
    
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("\n🔐 تسجيل الدخول...")
            
            # Login as admin
            login_response = client.post('/auth/login', data={
                'username': 'admin',
                'password': 'admin123'
            }, follow_redirects=True)
            
            if login_response.status_code != 200:
                print("❌ فشل تسجيل الدخول")
                return False
            
            print("✅ تم تسجيل الدخول بنجاح")
            
            # Test 1: Check settings index page
            print("\n📋 اختبار صفحة الإعدادات الرئيسية...")
            settings_response = client.get('/settings/')
            print(f"  📊 حالة الاستجابة: {settings_response.status_code}")
            
            if settings_response.status_code == 200:
                print("  ✅ صفحة الإعدادات تعمل")
                
                # Check if page contains expected elements
                content = settings_response.get_data(as_text=True)
                if 'إعدادات النظام' in content or 'System Settings' in content:
                    print("  ✅ العنوان الرئيسي موجود")
                else:
                    print("  ⚠️ العنوان الرئيسي غير موجود")
                    
                if 'معلومات النظام' in content or 'System Information' in content:
                    print("  ✅ قسم معلومات النظام موجود")
                else:
                    print("  ⚠️ قسم معلومات النظام غير موجود")
            else:
                print("  ❌ فشل في تحميل صفحة الإعدادات")
                return False
            
            # Test 2: Check company settings page
            print("\n🏢 اختبار صفحة إعدادات الشركة...")
            company_response = client.get('/settings/company')
            print(f"  📊 حالة الاستجابة: {company_response.status_code}")
            
            if company_response.status_code == 200:
                print("  ✅ صفحة إعدادات الشركة تعمل")
            else:
                print("  ❌ فشل في تحميل صفحة إعدادات الشركة")
            
            # Test 3: Check system settings page
            print("\n⚙️ اختبار صفحة إعدادات النظام...")
            system_response = client.get('/settings/system')
            print(f"  📊 حالة الاستجابة: {system_response.status_code}")
            
            if system_response.status_code == 200:
                print("  ✅ صفحة إعدادات النظام تعمل")
            else:
                print("  ❌ فشل في تحميل صفحة إعدادات النظام")
            
            # Test 4: Check backup page
            print("\n💾 اختبار صفحة النسخ الاحتياطي...")
            backup_response = client.get('/settings/backup')
            print(f"  📊 حالة الاستجابة: {backup_response.status_code}")
            
            if backup_response.status_code == 200:
                print("  ✅ صفحة النسخ الاحتياطي تعمل")
            else:
                print("  ❌ فشل في تحميل صفحة النسخ الاحتياطي")
            
            # Test 5: Test settings initialization
            print("\n🔧 اختبار تهيئة الإعدادات...")
            init_response = client.get('/settings/initialize')
            print(f"  📊 حالة الاستجابة: {init_response.status_code}")
            
            if init_response.status_code == 302:  # Redirect after success
                print("  ✅ تهيئة الإعدادات تعمل")
                
                # Check if default settings were created
                settings_count = Setting.query.count()
                print(f"  📊 عدد الإعدادات المُنشأة: {settings_count}")
                
                if settings_count > 0:
                    print("  ✅ تم إنشاء الإعدادات الافتراضية")
                else:
                    print("  ⚠️ لم يتم إنشاء إعدادات افتراضية")
            else:
                print("  ❌ فشل في تهيئة الإعدادات")
            
            # Test 6: Test setting model functionality
            print("\n🧪 اختبار وظائف نموذج الإعدادات...")
            
            # Test setting creation
            test_setting = Setting.set_setting(
                'test_setting', 
                'test_value', 
                'string', 
                'test',
                'إعداد تجريبي',
                'Test setting'
            )
            
            if test_setting:
                print("  ✅ إنشاء إعداد جديد يعمل")
                
                # Test setting retrieval
                retrieved_value = Setting.get_setting('test_setting')
                if retrieved_value == 'test_value':
                    print("  ✅ استرجاع قيمة الإعداد يعمل")
                else:
                    print("  ❌ فشل في استرجاع قيمة الإعداد")
                
                # Test setting update
                Setting.set_setting('test_setting', 'updated_value')
                updated_value = Setting.get_setting('test_setting')
                if updated_value == 'updated_value':
                    print("  ✅ تحديث قيمة الإعداد يعمل")
                else:
                    print("  ❌ فشل في تحديث قيمة الإعداد")
                
                # Clean up test setting
                db.session.delete(test_setting)
                db.session.commit()
            else:
                print("  ❌ فشل في إنشاء إعداد جديد")
            
            # Test 7: Test system info
            print("\n📊 اختبار معلومات النظام...")
            system_info = SystemInfo.get_info()
            
            if system_info:
                print("  ✅ استرجاع معلومات النظام يعمل")
                print(f"  📋 إصدار النظام: {system_info.version}")
                print(f"  📅 تاريخ التثبيت: {system_info.installation_date}")
                
                # Test stats update
                system_info.update_stats()
                print(f"  📊 إجمالي المبيعات: {system_info.total_sales}")
                print(f"  📦 إجمالي المنتجات: {system_info.total_products}")
                print(f"  👥 إجمالي العملاء: {system_info.total_customers}")
                print("  ✅ تحديث الإحصائيات يعمل")
            else:
                print("  ❌ فشل في استرجاع معلومات النظام")
            
            # Test 8: Test company settings update
            print("\n🏢 اختبار تحديث إعدادات الشركة...")
            company_update_response = client.post('/settings/company', data={
                'company_name_ar': 'شركة نقاط البيع القطرية المحدودة',
                'company_name_en': 'Qatar POS Company Ltd',
                'company_phone': '+974 4444 5555',
                'company_email': '<EMAIL>'
            }, follow_redirects=True)
            
            if company_update_response.status_code == 200:
                print("  ✅ تحديث إعدادات الشركة يعمل")
                
                # Verify settings were saved
                company_name_ar = Setting.get_setting('company_name_ar')
                if company_name_ar == 'شركة نقاط البيع القطرية المحدودة':
                    print("  ✅ تم حفظ اسم الشركة بالعربية")
                else:
                    print("  ⚠️ لم يتم حفظ اسم الشركة بالعربية")
            else:
                print("  ❌ فشل في تحديث إعدادات الشركة")
            
            print("\n" + "=" * 70)
            print("✅ انتهى اختبار نظام الإعدادات!")
            
            return True

def test_settings_api():
    """اختبار API الإعدادات"""
    print("\n🔌 اختبار API الإعدادات...")
    
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            # Login first
            client.post('/auth/login', data={
                'username': 'admin',
                'password': 'admin123'
            })
            
            # Test API get settings
            api_response = client.get('/settings/api/settings/company')
            print(f"  📊 API الحصول على الإعدادات: {api_response.status_code}")
            
            if api_response.status_code == 200:
                data = api_response.get_json()
                print(f"  📋 عدد إعدادات الشركة: {len(data)}")
                print("  ✅ API الحصول على الإعدادات يعمل")
            else:
                print("  ❌ فشل API الحصول على الإعدادات")
            
            # Test API update settings
            update_data = {
                'company_name_ar': 'شركة اختبار',
                'company_name_en': 'Test Company'
            }
            
            api_update_response = client.post('/settings/api/settings/company',
                                            json=update_data,
                                            content_type='application/json')
            print(f"  📊 API تحديث الإعدادات: {api_update_response.status_code}")
            
            if api_update_response.status_code == 200:
                result = api_update_response.get_json()
                if result.get('success'):
                    print("  ✅ API تحديث الإعدادات يعمل")
                else:
                    print("  ❌ فشل API تحديث الإعدادات")
            else:
                print("  ❌ فشل API تحديث الإعدادات")

if __name__ == '__main__':
    print("🧪 بدء اختبار نظام الإعدادات...")
    success = test_settings_system()
    
    if success:
        test_settings_api()
        print("\n🎉 تم اختبار نظام الإعدادات بنجاح!")
        print("\n📋 الميزات المُختبرة:")
        print("  ✅ صفحة الإعدادات الرئيسية")
        print("  ✅ إعدادات الشركة")
        print("  ✅ إعدادات النظام")
        print("  ✅ النسخ الاحتياطي")
        print("  ✅ تهيئة الإعدادات")
        print("  ✅ نموذج الإعدادات")
        print("  ✅ معلومات النظام")
        print("  ✅ API الإعدادات")
    else:
        print("\n❌ فشل في اختبار نظام الإعدادات")
