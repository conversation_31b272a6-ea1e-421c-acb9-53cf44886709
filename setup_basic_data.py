#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد البيانات الأساسية لنظام نقاط البيع القطري
Setup Basic Data for Qatar POS System
"""

from app import create_app
from extensions import db
from models.user import User
from models.role import Role, Permission
from models.category import Category
from models.product import Product
from models.customer import Customer
from models.supplier import Supplier
from models.setting import Setting, SystemInfo, DEFAULT_SETTINGS
from decimal import Decimal
from datetime import datetime
import os

def create_permissions():
    """إنشاء الصلاحيات الأساسية"""
    print("🔐 إنشاء الصلاحيات...")
    
    permissions = [
        # Admin permissions
        ('admin', 'إدارة النظام', 'System Administration'),
        ('users_read', 'عرض المستخدمين', 'View Users'),
        ('users_write', 'إدارة المستخدمين', 'Manage Users'),
        
        # Products permissions
        ('products_read', 'عرض المنتجات', 'View Products'),
        ('products_write', 'إدارة المنتجات', 'Manage Products'),
        
        # Sales permissions
        ('sales', 'المبيعات', 'Sales'),
        ('sales_read', 'عرض المبيعات', 'View Sales'),
        ('sales_write', 'إدارة المبيعات', 'Manage Sales'),
        
        # Inventory permissions
        ('inventory_read', 'عرض المخزون', 'View Inventory'),
        ('inventory', 'إدارة المخزون', 'Manage Inventory'),
        
        # Customers permissions
        ('customers_read', 'عرض العملاء', 'View Customers'),
        ('customers_write', 'إدارة العملاء', 'Manage Customers'),
        
        # Suppliers permissions
        ('suppliers_read', 'عرض الموردين', 'View Suppliers'),
        ('suppliers_write', 'إدارة الموردين', 'Manage Suppliers'),
        
        # Reports permissions
        ('reports_read', 'عرض التقارير', 'View Reports'),
        ('reports_write', 'إدارة التقارير', 'Manage Reports'),
    ]
    
    created_count = 0
    for name, desc_ar, desc_en in permissions:
        if not Permission.query.filter_by(name=name).first():
            permission = Permission(
                name=name,
                description_ar=desc_ar,
                description_en=desc_en
            )
            db.session.add(permission)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} صلاحية")

def create_roles():
    """إنشاء الأدوار الأساسية"""
    print("👥 إنشاء الأدوار...")
    
    roles_data = [
        {
            'name': 'admin',
            'display_name_ar': 'مدير النظام',
            'display_name_en': 'System Administrator',
            'permissions': ['admin', 'users_read', 'users_write', 'products_read', 'products_write', 
                          'sales', 'sales_read', 'sales_write', 'inventory_read', 'inventory',
                          'customers_read', 'customers_write', 'suppliers_read', 'suppliers_write',
                          'reports_read', 'reports_write']
        },
        {
            'name': 'manager',
            'display_name_ar': 'مدير',
            'display_name_en': 'Manager',
            'permissions': ['products_read', 'products_write', 'sales', 'sales_read', 'sales_write',
                          'inventory_read', 'inventory', 'customers_read', 'customers_write',
                          'suppliers_read', 'suppliers_write', 'reports_read']
        },
        {
            'name': 'cashier',
            'display_name_ar': 'كاشير',
            'display_name_en': 'Cashier',
            'permissions': ['products_read', 'sales', 'sales_read', 'customers_read', 'customers_write']
        },
        {
            'name': 'inventory_clerk',
            'display_name_ar': 'موظف مخزون',
            'display_name_en': 'Inventory Clerk',
            'permissions': ['products_read', 'products_write', 'inventory_read', 'inventory',
                          'suppliers_read', 'suppliers_write']
        }
    ]
    
    created_count = 0
    for role_data in roles_data:
        if not Role.query.filter_by(name=role_data['name']).first():
            role = Role(
                name=role_data['name'],
                display_name_ar=role_data['display_name_ar'],
                display_name_en=role_data['display_name_en']
            )
            
            # Add permissions to role
            for perm_name in role_data['permissions']:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    role.permissions.append(permission)
            
            db.session.add(role)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} دور")

def create_admin_user():
    """إنشاء المستخدم الإداري الأساسي"""
    print("👤 إنشاء المستخدم الإداري...")
    
    admin_role = Role.query.filter_by(name='admin').first()
    if not admin_role:
        print("❌ لم يتم العثور على دور المدير")
        return
    
    # Check if admin user already exists
    if User.query.filter_by(username='admin').first():
        print("⚠️ المستخدم الإداري موجود بالفعل")
        return
    
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        first_name_ar='مدير',
        first_name_en='Admin',
        last_name_ar='النظام',
        last_name_en='System',
        phone='+974 5555 5555',
        qatar_id='12345678901',
        employee_id='EMP001',
        is_active=True,
        role_id=admin_role.id if admin_role else None,
        role_legacy='manager'  # Fallback to legacy system
    )
    admin_user.set_password('admin123')
    
    db.session.add(admin_user)
    db.session.commit()
    print("✅ تم إنشاء المستخدم الإداري (admin/admin123)")

def create_categories():
    """إنشاء الفئات الأساسية"""
    print("📂 إنشاء الفئات...")
    
    categories = [
        {
            'name_ar': 'مشروبات',
            'name_en': 'Beverages',
            'description_ar': 'مشروبات متنوعة',
            'description_en': 'Various beverages'
        },
        {
            'name_ar': 'وجبات خفيفة',
            'name_en': 'Snacks',
            'description_ar': 'وجبات خفيفة ومقرمشات',
            'description_en': 'Light snacks and chips'
        },
        {
            'name_ar': 'منتجات ألبان',
            'name_en': 'Dairy Products',
            'description_ar': 'منتجات الألبان والأجبان',
            'description_en': 'Dairy products and cheese'
        },
        {
            'name_ar': 'مواد غذائية',
            'name_en': 'Groceries',
            'description_ar': 'مواد غذائية أساسية',
            'description_en': 'Basic grocery items'
        },
        {
            'name_ar': 'منظفات',
            'name_en': 'Cleaning Products',
            'description_ar': 'منتجات التنظيف والعناية',
            'description_en': 'Cleaning and care products'
        }
    ]
    
    created_count = 0
    for cat_data in categories:
        if not Category.query.filter_by(name_en=cat_data['name_en']).first():
            category = Category(
                name_ar=cat_data['name_ar'],
                name_en=cat_data['name_en'],
                description_ar=cat_data['description_ar'],
                description_en=cat_data['description_en']
            )
            db.session.add(category)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} فئة")

def create_sample_products():
    """إنشاء منتجات تجريبية"""
    print("📦 إنشاء منتجات تجريبية...")
    
    # Get categories
    beverages = Category.query.filter_by(name_en='Beverages').first()
    snacks = Category.query.filter_by(name_en='Snacks').first()
    dairy = Category.query.filter_by(name_en='Dairy Products').first()
    groceries = Category.query.filter_by(name_en='Groceries').first()
    
    products = [
        # Beverages
        {
            'name_ar': 'كوكا كولا',
            'name_en': 'Coca Cola',
            'sku': 'COKE001',
            'barcode': '1234567890123',
            'category_id': beverages.id if beverages else None,
            'cost_price': Decimal('1.50'),
            'selling_price': Decimal('2.00'),
            'current_stock': 100,
            'minimum_stock': 20
        },
        {
            'name_ar': 'بيبسي',
            'name_en': 'Pepsi',
            'sku': 'PEPSI001',
            'barcode': '1234567890124',
            'category_id': beverages.id if beverages else None,
            'cost_price': Decimal('1.50'),
            'selling_price': Decimal('2.00'),
            'current_stock': 80,
            'minimum_stock': 20
        },
        {
            'name_ar': 'ماء',
            'name_en': 'Water',
            'sku': 'WATER001',
            'barcode': '1234567890125',
            'category_id': beverages.id if beverages else None,
            'cost_price': Decimal('0.50'),
            'selling_price': Decimal('1.00'),
            'current_stock': 200,
            'minimum_stock': 50
        },
        
        # Snacks
        {
            'name_ar': 'شيبس',
            'name_en': 'Chips',
            'sku': 'CHIPS001',
            'barcode': '1234567890126',
            'category_id': snacks.id if snacks else None,
            'cost_price': Decimal('2.00'),
            'selling_price': Decimal('3.00'),
            'current_stock': 50,
            'minimum_stock': 10
        },
        {
            'name_ar': 'بسكويت',
            'name_en': 'Biscuits',
            'sku': 'BISCUIT001',
            'barcode': '1234567890127',
            'category_id': snacks.id if snacks else None,
            'cost_price': Decimal('1.50'),
            'selling_price': Decimal('2.50'),
            'current_stock': 30,
            'minimum_stock': 10
        },
        
        # Dairy
        {
            'name_ar': 'حليب',
            'name_en': 'Milk',
            'sku': 'MILK001',
            'barcode': '1234567890128',
            'category_id': dairy.id if dairy else None,
            'cost_price': Decimal('3.00'),
            'selling_price': Decimal('4.00'),
            'current_stock': 25,
            'minimum_stock': 5
        },
        {
            'name_ar': 'جبن',
            'name_en': 'Cheese',
            'sku': 'CHEESE001',
            'barcode': '1234567890129',
            'category_id': dairy.id if dairy else None,
            'cost_price': Decimal('8.00'),
            'selling_price': Decimal('12.00'),
            'current_stock': 15,
            'minimum_stock': 5
        },
        
        # Groceries
        {
            'name_ar': 'أرز',
            'name_en': 'Rice',
            'sku': 'RICE001',
            'barcode': '1234567890130',
            'category_id': groceries.id if groceries else None,
            'cost_price': Decimal('15.00'),
            'selling_price': Decimal('20.00'),
            'current_stock': 40,
            'minimum_stock': 10
        },
        {
            'name_ar': 'سكر',
            'name_en': 'Sugar',
            'sku': 'SUGAR001',
            'barcode': '1234567890131',
            'category_id': groceries.id if groceries else None,
            'cost_price': Decimal('3.00'),
            'selling_price': Decimal('4.50'),
            'current_stock': 60,
            'minimum_stock': 15
        }
    ]
    
    created_count = 0
    for prod_data in products:
        if not Product.query.filter_by(sku=prod_data['sku']).first():
            product = Product(**prod_data)
            db.session.add(product)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} منتج")

def create_sample_customers():
    """إنشاء عملاء تجريبيين"""
    print("👥 إنشاء عملاء تجريبيين...")
    
    customers = [
        {
            'customer_code': 'CUST001',
            'first_name_ar': 'أحمد',
            'first_name_en': 'Ahmed',
            'last_name_ar': 'محمد',
            'last_name_en': 'Mohammed',
            'phone': '+974 5555 1111',
            'email': '<EMAIL>'
        },
        {
            'customer_code': 'CUST002',
            'first_name_ar': 'فاطمة',
            'first_name_en': 'Fatima',
            'last_name_ar': 'علي',
            'last_name_en': 'Ali',
            'phone': '+974 5555 2222',
            'email': '<EMAIL>'
        },
        {
            'customer_code': 'CUST003',
            'company_name_ar': 'شركة قطر للتجارة',
            'company_name_en': 'Qatar Trading Company',
            'phone': '+974 4444 3333',
            'email': '<EMAIL>',
            'customer_type': 'business'
        }
    ]
    
    created_count = 0
    for cust_data in customers:
        if not Customer.query.filter_by(customer_code=cust_data['customer_code']).first():
            customer = Customer(**cust_data)
            db.session.add(customer)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} عميل")

def create_sample_suppliers():
    """إنشاء موردين تجريبيين"""
    print("🏭 إنشاء موردين تجريبيين...")
    
    suppliers = [
        {
            'supplier_code': 'SUP001',
            'company_name_ar': 'شركة المشروبات القطرية',
            'company_name_en': 'Qatar Beverages Company',
            'contact_person_ar': 'خالد أحمد',
            'contact_person_en': 'Khalid Ahmed',
            'phone': '+974 4444 1111',
            'email': '<EMAIL>'
        },
        {
            'supplier_code': 'SUP002',
            'company_name_ar': 'مؤسسة الأغذية الحديثة',
            'company_name_en': 'Modern Food Establishment',
            'contact_person_ar': 'سارة محمد',
            'contact_person_en': 'Sara Mohammed',
            'phone': '+974 4444 2222',
            'email': '<EMAIL>'
        }
    ]
    
    created_count = 0
    for supp_data in suppliers:
        if not Supplier.query.filter_by(supplier_code=supp_data['supplier_code']).first():
            supplier = Supplier(**supp_data)
            db.session.add(supplier)
            created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} مورد")

def create_default_settings():
    """إنشاء الإعدادات الافتراضية"""
    print("⚙️ إنشاء الإعدادات الافتراضية...")
    
    created_count = 0
    for setting_data in DEFAULT_SETTINGS:
        if not Setting.query.filter_by(key=setting_data['key']).first():
            setting = Setting(
                key=setting_data['key'],
                value=setting_data['value'],
                value_type=setting_data.get('value_type', 'string'),
                category=setting_data['category'],
                description_ar=setting_data.get('description_ar'),
                description_en=setting_data.get('description_en'),
                is_public=setting_data.get('is_public', False)
            )
            db.session.add(setting)
            created_count += 1
    
    # Create system info
    if not SystemInfo.query.first():
        system_info = SystemInfo()
        db.session.add(system_info)
        created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} إعداد")

def main():
    """تشغيل إعداد البيانات الأساسية"""
    print("🇶🇦 نظام نقاط البيع القطري - إعداد البيانات الأساسية")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        # Create database tables
        print("🗄️ إنشاء جداول قاعدة البيانات...")
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # Setup basic data
        create_permissions()
        create_roles()
        create_admin_user()
        create_categories()
        create_sample_products()
        create_sample_customers()
        create_sample_suppliers()
        create_default_settings()
        
        print("\n" + "=" * 60)
        print("🎉 تم إعداد البيانات الأساسية بنجاح!")
        print("\n📋 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("\n🌐 يمكنك الآن الوصول للنظام على: http://localhost:2626")

if __name__ == '__main__':
    main()
