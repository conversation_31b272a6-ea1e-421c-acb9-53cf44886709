{% extends "base.html" %}

{% block title %}
{{ product.get_name(language) + ' - نظام نقاط البيع القطري' if language == 'ar' else product.get_name(language) + ' - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-box"></i>
                {{ 'تفاصيل المنتج' if language == 'ar' else 'Product Details' }}
            </h1>
            <div>
                {% if current_user.has_permission('products_write') %}
                <a href="{{ url_for('products.edit', product_id=product.id) }}" class="btn btn-warning">
                    <i class="bi bi-pencil"></i>
                    {{ 'تعديل' if language == 'ar' else 'Edit' }}
                </a>
                {% endif %}
                <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للقائمة' if language == 'ar' else 'Back to List' }}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Product Information -->
    <div class="col-lg-8">
        <!-- Basic Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {{ 'المعلومات الأساسية' if language == 'ar' else 'Basic Information' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{{ 'رمز المنتج:' if language == 'ar' else 'SKU:' }}</td>
                                <td>{{ product.sku }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الباركود:' if language == 'ar' else 'Barcode:' }}</td>
                                <td>{{ product.barcode or '-' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الاسم بالعربية:' if language == 'ar' else 'Arabic Name:' }}</td>
                                <td>{{ product.name_ar }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الاسم بالإنجليزية:' if language == 'ar' else 'English Name:' }}</td>
                                <td>{{ product.name_en }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الفئة:' if language == 'ar' else 'Category:' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ product.category.get_name(language) if product.category else '-' }}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{{ 'وحدة القياس:' if language == 'ar' else 'Unit of Measure:' }}</td>
                                <td>{{ product.unit_of_measure }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الوزن:' if language == 'ar' else 'Weight:' }}</td>
                                <td>{{ product.weight or '-' }} {{ 'كجم' if product.weight and language == 'ar' else 'kg' if product.weight else '' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الأبعاد:' if language == 'ar' else 'Dimensions:' }}</td>
                                <td>{{ product.dimensions or '-' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'الحالة:' if language == 'ar' else 'Status:' }}</td>
                                <td>
                                    {% if product.is_active %}
                                    <span class="badge bg-success">{{ 'نشط' if language == 'ar' else 'Active' }}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{{ 'غير نشط' if language == 'ar' else 'Inactive' }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{{ 'تاريخ الإنشاء:' if language == 'ar' else 'Created:' }}</td>
                                <td>{{ product.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if product.description_ar or product.description_en %}
                <hr>
                <div class="row">
                    {% if product.description_ar %}
                    <div class="col-md-6">
                        <h6>{{ 'الوصف بالعربية:' if language == 'ar' else 'Arabic Description:' }}</h6>
                        <p class="text-muted">{{ product.description_ar }}</p>
                    </div>
                    {% endif %}
                    {% if product.description_en %}
                    <div class="col-md-6">
                        <h6>{{ 'الوصف بالإنجليزية:' if language == 'ar' else 'English Description:' }}</h6>
                        <p class="text-muted">{{ product.description_en }}</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Pricing -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-currency-dollar"></i>
                    {{ 'التسعير' if language == 'ar' else 'Pricing' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</h6>
                            <h4 class="text-info">{{ '{:,.3f} ر.ق'.format(product.cost_price) if language == 'ar' else 'QAR {:,.3f}'.format(product.cost_price) }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'سعر البيع' if language == 'ar' else 'Selling Price' }}</h6>
                            <h4 class="text-warning">{{ '{:,.2f} ر.ق'.format(product.selling_price) if language == 'ar' else 'QAR {:,.2f}'.format(product.selling_price) }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'السعر النهائي' if language == 'ar' else 'Final Price' }}</h6>
                            <h4 class="text-success">{{ '{:,.2f} ر.ق'.format(product.get_final_price()) if language == 'ar' else 'QAR {:,.2f}'.format(product.get_final_price()) }}</h4>
                            {% if product.discount_percentage > 0 %}
                            <span class="badge bg-warning">{{ product.discount_percentage }}% {{ 'خصم' if language == 'ar' else 'OFF' }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'هامش الربح' if language == 'ar' else 'Profit Margin' }}</h6>
                            <h4 class="text-primary">{{ '{:.1f}%'.format(product.get_profit_margin()) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Inventory -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-boxes"></i>
                    {{ 'إدارة المخزون' if language == 'ar' else 'Inventory Management' }}
                </h6>
            </div>
            <div class="card-body">
                {% if product.track_inventory %}
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</h6>
                            <h4 class="{% if product.is_out_of_stock() %}text-danger{% elif product.is_low_stock() %}text-warning{% else %}text-success{% endif %}">
                                {{ product.current_stock }}
                            </h4>
                            {% if product.is_out_of_stock() %}
                            <span class="badge bg-danger">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</span>
                            {% elif product.is_low_stock() %}
                            <span class="badge bg-warning">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'الحد الأدنى' if language == 'ar' else 'Minimum Stock' }}</h6>
                            <h4 class="text-info">{{ product.minimum_stock }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'الحد الأقصى' if language == 'ar' else 'Maximum Stock' }}</h6>
                            <h4 class="text-info">{{ product.maximum_stock }}</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</h6>
                            <h4 class="text-primary">{{ '{:,.2f} ر.ق'.format(product.current_stock * product.cost_price) if language == 'ar' else 'QAR {:,.2f}'.format(product.current_stock * product.cost_price) }}</h4>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center">
                    <i class="bi bi-infinity display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'المخزون غير محدود' if language == 'ar' else 'Unlimited Stock' }}</h5>
                    <p class="text-muted">{{ 'هذا المنتج لا يتطلب تتبع المخزون' if language == 'ar' else 'This product does not require inventory tracking' }}</p>
                </div>
                {% endif %}
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" disabled {{ 'checked' if product.track_inventory }}>
                            <label class="form-check-label">
                                {{ 'تتبع المخزون' if language == 'ar' else 'Track Inventory' }}
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" disabled {{ 'checked' if product.is_taxable }}>
                            <label class="form-check-label">
                                {{ 'خاضع للضريبة' if language == 'ar' else 'Taxable' }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Image and Actions -->
    <div class="col-lg-4">
        <!-- Product Image -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-image"></i>
                    {{ 'صورة المنتج' if language == 'ar' else 'Product Image' }}
                </h6>
            </div>
            <div class="card-body text-center">
                {% if product.image_filename %}
                <img src="{{ url_for('static', filename='uploads/products/' + product.image_filename) }}" 
                     alt="{{ product.get_name(language) }}" class="img-fluid rounded mb-3" style="max-height: 300px;">
                {% else %}
                <div class="border rounded p-4 mb-3" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                    <div class="text-muted">
                        <i class="bi bi-image display-4"></i>
                        <p class="mt-2">{{ 'لا توجد صورة' if language == 'ar' else 'No image available' }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning"></i>
                    {{ 'إجراءات سريعة' if language == 'ar' else 'Quick Actions' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if current_user.has_permission('sales') %}
                    <a href="{{ url_for('sales.pos') }}?product={{ product.id }}" class="btn btn-success">
                        <i class="bi bi-cart-plus"></i>
                        {{ 'إضافة للبيع' if language == 'ar' else 'Add to Sale' }}
                    </a>
                    {% endif %}
                    
                    {% if current_user.has_permission('inventory') %}
                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#adjustStockModal">
                        <i class="bi bi-gear"></i>
                        {{ 'تعديل المخزون' if language == 'ar' else 'Adjust Stock' }}
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('inventory.transactions') }}?product_id={{ product.id }}" class="btn btn-info">
                        <i class="bi bi-clock-history"></i>
                        {{ 'تاريخ المخزون' if language == 'ar' else 'Stock History' }}
                    </a>
                    
                    {% if product.barcode %}
                    <button type="button" class="btn btn-outline-primary" onclick="printBarcode()">
                        <i class="bi bi-upc-scan"></i>
                        {{ 'طباعة الباركود' if language == 'ar' else 'Print Barcode' }}
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Product Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {{ 'إحصائيات المنتج' if language == 'ar' else 'Product Statistics' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-muted">{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</h6>
                        <h5 class="text-success">{{ product.get_sales_count() }}</h5>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">{{ 'آخر بيع' if language == 'ar' else 'Last Sale' }}</h6>
                        {% set last_sale = product.get_last_sale() %}
                        <h6 class="text-info">
                            {% if last_sale %}
                            {{ last_sale.sale.sale_date.strftime('%Y-%m-%d') }}
                            {% else %}
                            {{ 'لا يوجد' if language == 'ar' else 'None' }}
                            {% endif %}
                        </h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
{% if current_user.has_permission('inventory') %}
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'تعديل المخزون' if language == 'ar' else 'Adjust Stock' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('inventory.quick_adjust', product_id=product.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</label>
                        <input type="number" class="form-control" value="{{ product.current_stock }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'المخزون الجديد' if language == 'ar' else 'New Stock' }}</label>
                        <input type="number" class="form-control" name="new_stock" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'السبب' if language == 'ar' else 'Reason' }}</label>
                        <select class="form-select" name="reason" required>
                            <option value="physical_count">{{ 'جرد فعلي' if language == 'ar' else 'Physical Count' }}</option>
                            <option value="damage">{{ 'تلف' if language == 'ar' else 'Damage' }}</option>
                            <option value="theft">{{ 'سرقة' if language == 'ar' else 'Theft' }}</option>
                            <option value="expired">{{ 'منتهي الصلاحية' if language == 'ar' else 'Expired' }}</option>
                            <option value="other">{{ 'أخرى' if language == 'ar' else 'Other' }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ 'ملاحظات' if language == 'ar' else 'Notes' }}</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        {{ 'تعديل المخزون' if language == 'ar' else 'Adjust Stock' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function printBarcode() {
    const barcode = '{{ product.barcode }}';
    const productName = '{{ product.get_name(language) }}';
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Barcode - ${productName}</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                .barcode { font-family: 'Courier New', monospace; font-size: 24px; letter-spacing: 2px; margin: 20px 0; }
                .product-name { font-size: 14px; margin: 10px 0; }
                @media print { body { margin: 0; padding: 10px; } }
            </style>
        </head>
        <body>
            <div class="product-name">${productName}</div>
            <div class="barcode">${barcode}</div>
            <div class="product-name">{{ product.sku }}</div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}
</script>
{% endblock %}
