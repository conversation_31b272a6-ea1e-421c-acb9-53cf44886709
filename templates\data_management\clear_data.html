{% extends "base.html" %}

{% block title %}
{{ 'مسح البيانات - نظام نقاط البيع القطري' if language == 'ar' else 'Clear Data - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.clear-option {
    border: 2px solid #dee2e6;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.clear-option:hover {
    border-color: #007bff;
    box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
}

.clear-option.danger:hover {
    border-color: #dc3545;
    box-shadow: 0 0.5rem 1rem rgba(220, 53, 69, 0.15);
}

.data-count {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.warning-section {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.danger-section {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.clear-btn {
    min-width: 150px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-trash"></i>
                    {{ 'مسح البيانات' if language == 'ar' else 'Clear Data' }}
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('dashboard.index') }}">
                                {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('data_management.index') }}">
                                {{ 'إدارة البيانات' if language == 'ar' else 'Data Management' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            {{ 'مسح البيانات' if language == 'ar' else 'Clear Data' }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Warning Section -->
    <div class="row">
        <div class="col-12">
            <div class="warning-section">
                <h4 class="mb-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ 'تحذير مهم' if language == 'ar' else 'Important Warning' }}
                </h4>
                <ul class="mb-0">
                    <li>{{ 'عمليات مسح البيانات لا يمكن التراجع عنها' if language == 'ar' else 'Data clearing operations cannot be undone' }}</li>
                    <li>{{ 'قم بإنشاء نسخة احتياطية قبل المتابعة' if language == 'ar' else 'Create a backup before proceeding' }}</li>
                    <li>{{ 'ستحتاج إلى كلمة مرور المدير لتأكيد العملية' if language == 'ar' else 'Admin password required to confirm operation' }}</li>
                    <li>{{ 'سيتم الاحتفاظ بحساب المدير الحالي' if language == 'ar' else 'Current admin account will be preserved' }}</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Clear Options -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="clear-option" onclick="clearData('sales')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-2">
                            <i class="bi bi-receipt text-primary"></i>
                            {{ 'مسح بيانات المبيعات' if language == 'ar' else 'Clear Sales Data' }}
                        </h5>
                        <p class="text-muted mb-0">
                            {{ 'مسح جميع المبيعات وعناصر المبيعات' if language == 'ar' else 'Clear all sales and sale items' }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="data-count">{{ stats.sales_count }}</div>
                        <small class="text-muted">{{ 'مبيعة' if language == 'ar' else 'sales' }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="clear-option" onclick="clearData('products')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-2">
                            <i class="bi bi-box text-success"></i>
                            {{ 'مسح بيانات المنتجات' if language == 'ar' else 'Clear Products Data' }}
                        </h5>
                        <p class="text-muted mb-0">
                            {{ 'مسح جميع المنتجات وحركات المخزون' if language == 'ar' else 'Clear all products and inventory transactions' }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="data-count">{{ stats.products_count }}</div>
                        <small class="text-muted">{{ 'منتج' if language == 'ar' else 'products' }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="clear-option" onclick="clearData('customers')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-2">
                            <i class="bi bi-people text-info"></i>
                            {{ 'مسح بيانات العملاء' if language == 'ar' else 'Clear Customers Data' }}
                        </h5>
                        <p class="text-muted mb-0">
                            {{ 'مسح جميع بيانات العملاء' if language == 'ar' else 'Clear all customer data' }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="data-count">{{ stats.customers_count }}</div>
                        <small class="text-muted">{{ 'عميل' if language == 'ar' else 'customers' }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="clear-option" onclick="clearData('suppliers')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-2">
                            <i class="bi bi-truck text-warning"></i>
                            {{ 'مسح بيانات الموردين' if language == 'ar' else 'Clear Suppliers Data' }}
                        </h5>
                        <p class="text-muted mb-0">
                            {{ 'مسح جميع بيانات الموردين' if language == 'ar' else 'Clear all supplier data' }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="data-count">{{ stats.suppliers_count }}</div>
                        <small class="text-muted">{{ 'مورد' if language == 'ar' else 'suppliers' }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="row">
        <div class="col-12">
            <div class="danger-section">
                <h4 class="mb-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ 'منطقة الخطر الأقصى' if language == 'ar' else 'Maximum Danger Zone' }}
                </h4>
                
                <div class="clear-option danger" onclick="clearData('all')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-2 text-white">
                                <i class="bi bi-exclamation-triangle text-warning"></i>
                                {{ 'مسح جميع البيانات' if language == 'ar' else 'Clear All Data' }}
                            </h5>
                            <p class="text-light mb-0">
                                {{ 'مسح جميع البيانات باستثناء حساب المدير الحالي' if language == 'ar' else 'Clear all data except current admin account' }}
                            </p>
                        </div>
                        <div class="text-end">
                            <div class="data-count text-warning">{{ stats.sales_count + stats.products_count + stats.customers_count + stats.suppliers_count }}</div>
                            <small class="text-light">{{ 'سجل' if language == 'ar' else 'records' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="row">
        <div class="col-12 text-center">
            <a href="{{ url_for('data_management.index') }}" class="btn btn-secondary btn-lg">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة لإدارة البيانات' if language == 'ar' else 'Back to Data Management' }}
            </a>
        </div>
    </div>
</div>

<!-- Admin Password Modal -->
<div class="modal fade" id="adminPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    {{ 'تأكيد مسح البيانات' if language == 'ar' else 'Confirm Data Clearing' }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span id="confirmationMessage"></span>
                </div>
                <form id="adminPasswordForm">
                    <div class="mb-3">
                        <label class="form-label">{{ 'كلمة مرور المدير:' if language == 'ar' else 'Admin Password:' }}</label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-danger clear-btn" onclick="confirmClearData()">
                    {{ 'تأكيد المسح' if language == 'ar' else 'Confirm Clear' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentClearType = null;

const clearMessages = {
    'sales': '{{ "سيتم مسح جميع بيانات المبيعات نهائياً!" if language == "ar" else "All sales data will be permanently deleted!" }}',
    'products': '{{ "سيتم مسح جميع بيانات المنتجات نهائياً!" if language == "ar" else "All products data will be permanently deleted!" }}',
    'customers': '{{ "سيتم مسح جميع بيانات العملاء نهائياً!" if language == "ar" else "All customers data will be permanently deleted!" }}',
    'suppliers': '{{ "سيتم مسح جميع بيانات الموردين نهائياً!" if language == "ar" else "All suppliers data will be permanently deleted!" }}',
    'all': '{{ "سيتم مسح جميع البيانات نهائياً باستثناء حساب المدير!" if language == "ar" else "All data will be permanently deleted except admin account!" }}'
};

function clearData(type) {
    currentClearType = type;
    document.getElementById('confirmationMessage').textContent = clearMessages[type];
    
    const modal = new bootstrap.Modal(document.getElementById('adminPasswordModal'));
    modal.show();
}

function confirmClearData() {
    const password = document.getElementById('adminPassword').value;
    
    if (!password) {
        showAlert('warning', '{{ "يرجى إدخال كلمة مرور المدير" if language == "ar" else "Please enter admin password" }}');
        return;
    }
    
    const formData = new FormData();
    formData.append('admin_password', password);
    
    const btn = document.querySelector('.clear-btn');
    const originalText = btn.textContent;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> {{ "جاري المسح..." if language == "ar" else "Clearing..." }}';
    
    fetch(`/data-management/api/clear-${currentClearType}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('adminPasswordModal')).hide();
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        showAlert('danger', '{{ "خطأ في الاتصال" if language == "ar" else "Connection error" }}');
    })
    .finally(() => {
        btn.disabled = false;
        btn.textContent = originalText;
        document.getElementById('adminPassword').value = '';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
