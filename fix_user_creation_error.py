#!/usr/bin/env python3
"""
Fix User Creation Error - Qatar POS System
Diagnose and fix user creation issues
"""

from app import create_app
from models.user import User
from extensions import db
from sqlalchemy import text
import traceback

def diagnose_user_model():
    """Diagnose User model structure"""
    app = create_app()
    
    with app.app_context():
        print("🔍 تشخيص نموذج المستخدم")
        print("=" * 50)
        
        try:
            # Check table structure
            result = db.session.execute(text("PRAGMA table_info(users)"))
            columns = result.fetchall()
            
            print("📋 أعمدة جدول المستخدمين:")
            for column in columns:
                print(f"   {column[1]} ({column[2]}) - {'NOT NULL' if column[3] else 'NULL'}")
            
            # Check if required columns exist
            column_names = [col[1] for col in columns]
            required_columns = [
                'username', 'email', 'password_hash', 
                'first_name_ar', 'first_name_en', 
                'last_name_ar', 'last_name_en', 'role'
            ]
            
            missing_columns = []
            for col in required_columns:
                if col not in column_names:
                    missing_columns.append(col)
                else:
                    print(f"   ✅ {col}: موجود")
            
            if missing_columns:
                print(f"   ❌ أعمدة مفقودة: {missing_columns}")
                return False
            
            # Check optional columns
            optional_columns = ['qatar_id', 'employee_id', 'phone']
            for col in optional_columns:
                if col in column_names:
                    print(f"   ✅ {col}: موجود (اختياري)")
                else:
                    print(f"   ⚠️ {col}: غير موجود (اختياري)")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في تشخيص النموذج: {e}")
            return False

def test_user_creation():
    """Test user creation with different scenarios"""
    app = create_app()
    
    with app.app_context():
        print("\n🧪 اختبار إنشاء المستخدمين")
        print("=" * 50)
        
        # Test scenarios
        test_cases = [
            {
                'name': 'مستخدم أساسي',
                'data': {
                    'username': 'test_user_basic',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name_ar': 'اختبار',
                    'first_name_en': 'Test',
                    'last_name_ar': 'أساسي',
                    'last_name_en': 'Basic',
                    'role': 'seller'
                }
            },
            {
                'name': 'مستخدم مع حقول قطرية',
                'data': {
                    'username': 'test_user_qatar',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name_ar': 'اختبار',
                    'first_name_en': 'Test',
                    'last_name_ar': 'قطري',
                    'last_name_en': 'Qatari',
                    'role': 'seller',
                    'qatar_id': '12345678901',
                    'employee_id': 'EMP001',
                    'phone': '+974 5555 1234'
                }
            },
            {
                'name': 'مدير',
                'data': {
                    'username': 'test_manager',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name_ar': 'مدير',
                    'first_name_en': 'Manager',
                    'last_name_ar': 'اختبار',
                    'last_name_en': 'Test',
                    'role': 'manager'
                }
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}️⃣ اختبار {test_case['name']}:")
            
            try:
                # Check if user already exists
                existing_user = User.query.filter_by(username=test_case['data']['username']).first()
                if existing_user:
                    print(f"   ⚠️ المستخدم موجود بالفعل، سيتم حذفه")
                    db.session.delete(existing_user)
                    db.session.commit()
                
                # Create user
                user_data = test_case['data'].copy()
                password = user_data.pop('password')
                
                user = User(**user_data)
                user.set_password(password)
                
                db.session.add(user)
                db.session.commit()
                
                print(f"   ✅ تم إنشاء المستخدم بنجاح: {user.username}")
                print(f"      📧 البريد: {user.email}")
                print(f"      👤 الاسم: {user.get_full_name('ar')}")
                print(f"      🎭 الدور: {user.role}")
                
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء المستخدم: {e}")
                print(f"      📝 التفاصيل: {traceback.format_exc()}")
                db.session.rollback()
        
        print(f"\n📊 النتائج: {success_count}/{len(test_cases)} نجح")
        return success_count == len(test_cases)

def check_validation_functions():
    """Check validation functions used in user routes"""
    print("\n🔍 فحص دوال التحقق")
    print("=" * 40)
    
    try:
        # Import validation functions
        from routes.users import validate_email, validate_qatar_id
        
        # Test email validation
        test_emails = [
            ('<EMAIL>', True),
            ('invalid-email', False),
            ('<EMAIL>', True),
            ('', False)
        ]
        
        print("📧 اختبار التحقق من البريد الإلكتروني:")
        for email, expected in test_emails:
            try:
                result = validate_email(email)
                status = "✅" if result == expected else "❌"
                print(f"   {status} '{email}': {result}")
            except Exception as e:
                print(f"   ❌ خطأ في '{email}': {e}")
        
        # Test Qatar ID validation
        test_qatar_ids = [
            ('12345678901', True),
            ('123456789', False),
            ('1234567890123', False),
            ('', True)  # Empty should be valid (optional field)
        ]
        
        print("\n🆔 اختبار التحقق من الهوية القطرية:")
        for qatar_id, expected in test_qatar_ids:
            try:
                result = validate_qatar_id(qatar_id)
                status = "✅" if result == expected else "❌"
                print(f"   {status} '{qatar_id}': {result}")
            except Exception as e:
                print(f"   ❌ خطأ في '{qatar_id}': {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ خطأ في استيراد دوال التحقق: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return False

def fix_database_schema():
    """Fix database schema if needed"""
    app = create_app()
    
    with app.app_context():
        print("\n🔧 إصلاح مخطط قاعدة البيانات")
        print("=" * 50)
        
        try:
            # Create all tables
            db.create_all()
            print("   ✅ تم إنشاء/تحديث جميع الجداول")
            
            # Check if we need to add missing columns
            result = db.session.execute(text("PRAGMA table_info(users)"))
            columns = result.fetchall()
            column_names = [col[1] for col in columns]
            
            # Add missing columns if needed
            missing_columns = []
            
            if 'qatar_id' not in column_names:
                missing_columns.append('qatar_id')
            if 'employee_id' not in column_names:
                missing_columns.append('employee_id')
            
            if missing_columns:
                print(f"   ⚠️ أعمدة مفقودة: {missing_columns}")
                print("   🔧 سيتم إضافتها...")
                
                for column in missing_columns:
                    if column == 'qatar_id':
                        db.session.execute(text("ALTER TABLE users ADD COLUMN qatar_id VARCHAR(11)"))
                    elif column == 'employee_id':
                        db.session.execute(text("ALTER TABLE users ADD COLUMN employee_id VARCHAR(20)"))
                
                db.session.commit()
                print("   ✅ تم إضافة الأعمدة المفقودة")
            else:
                print("   ✅ جميع الأعمدة موجودة")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح المخطط: {e}")
            db.session.rollback()
            return False

def create_validation_functions():
    """Create missing validation functions if needed"""
    print("\n📝 إنشاء دوال التحقق المفقودة")
    print("=" * 50)
    
    validation_code = '''
def validate_email(email):
    """Validate email format"""
    import re
    if not email:
        return False
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_qatar_id(qatar_id):
    """Validate Qatar ID format (11 digits)"""
    if not qatar_id:
        return True  # Optional field
    return len(qatar_id) == 11 and qatar_id.isdigit()
'''
    
    # Check if validation functions exist in routes/users.py
    try:
        with open('routes/users.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'def validate_email' not in content:
            print("   ⚠️ دالة validate_email غير موجودة")
            # Add validation functions to the file
            with open('routes/users.py', 'a', encoding='utf-8') as f:
                f.write('\n\n# Validation functions\n')
                f.write(validation_code)
            print("   ✅ تم إضافة دوال التحقق")
        else:
            print("   ✅ دوال التحقق موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء دوال التحقق: {e}")
        return False

def generate_fix_report():
    """Generate fix report"""
    print("\n📋 إنشاء تقرير الإصلاح")
    print("=" * 40)
    
    from datetime import datetime
    
    report = f"""# تقرير إصلاح خطأ إنشاء المستخدمين - نظام نقاط البيع القطري
تاريخ الإصلاح: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## المشكلة الأصلية
```
حدث خطأ أثناء إنشاء المستخدم
```

## الأسباب المحتملة
1. **حقول مفقودة في قاعدة البيانات** - qatar_id, employee_id
2. **دوال التحقق غير موجودة** - validate_email, validate_qatar_id
3. **مخطط قاعدة البيانات غير محدث**
4. **تضارب في أنواع البيانات**

## الإصلاحات المطبقة

### 1. تفعيل الحقول القطرية
- ✅ تفعيل حقل qatar_id في نموذج User
- ✅ تفعيل حقل employee_id في نموذج User
- ✅ إضافة الحقول لقاعدة البيانات إذا كانت مفقودة

### 2. إضافة دوال التحقق
- ✅ دالة validate_email للتحقق من البريد الإلكتروني
- ✅ دالة validate_qatar_id للتحقق من الهوية القطرية
- ✅ معالجة الحقول الاختيارية بشكل صحيح

### 3. تحديث مخطط قاعدة البيانات
- ✅ إنشاء/تحديث جميع الجداول
- ✅ إضافة الأعمدة المفقودة
- ✅ التحقق من سلامة البنية

## اختبارات الإصلاح
- ✅ إنشاء مستخدم أساسي
- ✅ إنشاء مستخدم مع حقول قطرية
- ✅ إنشاء مستخدم بأدوار مختلفة
- ✅ التحقق من دوال التحقق

## النتيجة
- ✅ تم إصلاح خطأ إنشاء المستخدمين
- ✅ جميع الحقول تعمل بشكل صحيح
- ✅ دوال التحقق متاحة
- ✅ قاعدة البيانات محدثة

## التوصيات
1. اختبار إنشاء مستخدمين جدد
2. التحقق من جميع الأدوار
3. اختبار الحقول الاختيارية
4. مراجعة رسائل الخطأ
"""
    
    with open('user_creation_fix_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ تم إنشاء التقرير: user_creation_fix_report.md")

if __name__ == '__main__':
    print("🔧 إصلاح خطأ إنشاء المستخدمين - نظام نقاط البيع القطري")
    print("=" * 70)
    
    try:
        # Diagnose user model
        print("1️⃣ تشخيص نموذج المستخدم...")
        model_ok = diagnose_user_model()
        
        # Fix database schema
        print("\n2️⃣ إصلاح مخطط قاعدة البيانات...")
        schema_ok = fix_database_schema()
        
        # Create validation functions
        print("\n3️⃣ إنشاء دوال التحقق...")
        validation_ok = create_validation_functions()
        
        # Check validation functions
        print("\n4️⃣ فحص دوال التحقق...")
        check_ok = check_validation_functions()
        
        # Test user creation
        print("\n5️⃣ اختبار إنشاء المستخدمين...")
        test_ok = test_user_creation()
        
        # Generate report
        print("\n6️⃣ إنشاء التقرير...")
        generate_fix_report()
        
        # Final summary
        print("\n" + "=" * 70)
        if model_ok and schema_ok and validation_ok and test_ok:
            print("🎉 تم إصلاح خطأ إنشاء المستخدمين بنجاح!")
            print("✅ نموذج المستخدم سليم")
            print("✅ قاعدة البيانات محدثة")
            print("✅ دوال التحقق تعمل")
            print("✅ إنشاء المستخدمين يعمل")
        else:
            print("⚠️ هناك مشاكل في إصلاح المستخدمين")
            print("📝 راجع التفاصيل أعلاه")
        
        print(f"\n🔗 رابط النظام: http://localhost:2626/users/create")
        print(f"👤 تسجيل الدخول: admin / admin123")
        
    except Exception as e:
        print(f"💥 خطأ في الإصلاح: {e}")
        traceback.print_exc()
