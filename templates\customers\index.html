{% extends "base.html" %}

{% block title %}
{{ 'إدارة العملاء - نظام نقاط البيع القطري' if language == 'ar' else 'Customer Management - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-people"></i>
                {{ 'إدارة العملاء' if language == 'ar' else 'Customer Management' }}
            </h1>
            {% if current_user.has_permission('customers_write') %}
            <div>
                <a href="{{ url_for('customers.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'إضافة عميل جديد' if language == 'ar' else 'Add New Customer' }}
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Customer Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي العملاء' if language == 'ar' else 'Total Customers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ customers.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'عملاء أفراد' if language == 'ar' else 'Individual Customers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ customers.items | selectattr('customer_type', 'equalto', 'individual') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'عملاء شركات' if language == 'ar' else 'Company Customers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ customers.items | selectattr('customer_type', 'equalto', 'company') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'عملاء نشطون' if language == 'ar' else 'Active Customers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ customers.items | selectattr('is_active', 'equalto', true) | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">{{ 'البحث' if language == 'ar' else 'Search' }}</label>
                <div class="search-box">
                    <input type="text" class="form-control" name="search" value="{{ search }}" 
                           placeholder="{{ 'اسم العميل، الهاتف، أو رقم العميل' if language == 'ar' else 'Customer name, phone, or customer code' }}">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">{{ 'نوع العميل' if language == 'ar' else 'Customer Type' }}</label>
                <select class="form-select" name="type">
                    <option value="">{{ 'جميع الأنواع' if language == 'ar' else 'All Types' }}</option>
                    <option value="individual" {{ 'selected' if customer_type == 'individual' }}>
                        {{ 'فرد' if language == 'ar' else 'Individual' }}
                    </option>
                    <option value="company" {{ 'selected' if customer_type == 'company' }}>
                        {{ 'شركة' if language == 'ar' else 'Company' }}
                    </option>
                </select>
            </div>
            <div class="col-md-5">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        {{ 'بحث' if language == 'ar' else 'Search' }}
                    </button>
                    <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            {{ 'قائمة العملاء' if language == 'ar' else 'Customers List' }}
            <span class="badge bg-primary">{{ customers.total }}</span>
        </h6>
    </div>
    <div class="card-body p-0">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{{ 'رقم العميل' if language == 'ar' else 'Customer #' }}</th>
                        <th>{{ 'الاسم' if language == 'ar' else 'Name' }}</th>
                        <th>{{ 'النوع' if language == 'ar' else 'Type' }}</th>
                        <th>{{ 'معلومات الاتصال' if language == 'ar' else 'Contact Info' }}</th>
                        <th>{{ 'المدينة' if language == 'ar' else 'City' }}</th>
                        <th>{{ 'آخر شراء' if language == 'ar' else 'Last Purchase' }}</th>
                        <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                        <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <strong>{{ customer.customer_code }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ customer.get_display_name(language) }}</strong>
                                {% if customer.customer_type == 'company' and customer.contact_person_ar %}
                                <br>
                                <small class="text-muted">
                                    {{ 'جهة الاتصال:' if language == 'ar' else 'Contact:' }} 
                                    {{ customer.contact_person_ar if language == 'ar' else customer.contact_person_en }}
                                </small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if customer.customer_type == 'individual' %}
                            <span class="badge bg-info">{{ 'فرد' if language == 'ar' else 'Individual' }}</span>
                            {% else %}
                            <span class="badge bg-success">{{ 'شركة' if language == 'ar' else 'Company' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <i class="bi bi-telephone"></i> {{ customer.phone }}
                                {% if customer.email %}
                                <br>
                                <i class="bi bi-envelope"></i> {{ customer.email }}
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {{ customer.city_ar if language == 'ar' else customer.city_en }}
                        </td>
                        <td>
                            {% if customer.last_purchase_date %}
                            <div>{{ customer.last_purchase_date.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ customer.last_purchase_date.strftime('%H:%M') }}</small>
                            {% else %}
                            <span class="text-muted">{{ 'لا يوجد' if language == 'ar' else 'None' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.is_active %}
                            <span class="badge bg-success">{{ 'نشط' if language == 'ar' else 'Active' }}</span>
                            {% else %}
                            <span class="badge bg-danger">{{ 'غير نشط' if language == 'ar' else 'Inactive' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('customers.view', customer_id=customer.id) }}" 
                                   class="btn btn-outline-primary" title="{{ 'عرض' if language == 'ar' else 'View' }}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.has_permission('customers_write') %}
                                <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" 
                                   class="btn btn-outline-warning" title="{{ 'تعديل' if language == 'ar' else 'Edit' }}">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_permission('sales') %}
                                <a href="{{ url_for('sales.pos') }}?customer={{ customer.id }}" 
                                   class="btn btn-outline-success" title="{{ 'بيع' if language == 'ar' else 'Sell' }}">
                                    <i class="bi bi-cart-plus"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Customers pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if customers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.prev_num, search=search, type=customer_type) }}">
                            {{ 'السابق' if language == 'ar' else 'Previous' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in customers.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != customers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers.index', page=page_num, search=search, type=customer_type) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if customers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.next_num, search=search, type=customer_type) }}">
                            {{ 'التالي' if language == 'ar' else 'Next' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد عملاء' if language == 'ar' else 'No customers found' }}</h5>
            <p class="text-muted">
                {% if search or customer_type %}
                {{ 'جرب تغيير معايير البحث' if language == 'ar' else 'Try changing your search criteria' }}
                {% else %}
                {{ 'ابدأ بإضافة عميل جديد' if language == 'ar' else 'Start by adding a new customer' }}
                {% endif %}
            </p>
            {% if current_user.has_permission('customers_write') and not (search or customer_type) %}
            <a href="{{ url_for('customers.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة عميل جديد' if language == 'ar' else 'Add New Customer' }}
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on filter change
document.querySelector('select[name="type"]').addEventListener('change', function() {
    this.form.submit();
});

// Search input with debounce
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>
{% endblock %}
