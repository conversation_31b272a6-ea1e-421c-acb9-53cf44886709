"""
Desktop Application Configuration
إعدادات تطبيق سطح المكتب
"""

import os
import sys
from pathlib import Path

class DesktopConfig:
    """إعدادات التطبيق المكتبي"""
    
    # معلومات التطبيق
    APP_NAME = "Qatar POS System"
    APP_NAME_AR = "نظام نقاط البيع القطري"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "Point of Sale System for Qatar Market"
    APP_DESCRIPTION_AR = "نظام نقاط البيع للسوق القطري"
    
    # إعدادات الخادم
    DEFAULT_HOST = "127.0.0.1"
    DEFAULT_PORT = 2626
    PORT_RANGE_START = 2626
    PORT_RANGE_END = 2636
    
    # إعدادات قاعدة البيانات
    if getattr(sys, 'frozen', False):
        # Running as compiled executable
        BASE_DIR = Path(sys.executable).parent
    else:
        # Running as script
        BASE_DIR = Path(__file__).parent
    
    DATABASE_PATH = BASE_DIR / "qatar_pos.db"
    DATABASE_URI = f"sqlite:///{DATABASE_PATH}"
    
    # إعدادات الملفات
    UPLOAD_FOLDER = BASE_DIR / "uploads"
    STATIC_FOLDER = BASE_DIR / "static"
    TEMPLATES_FOLDER = BASE_DIR / "templates"
    
    # إعدادات الأمان
    SECRET_KEY = "qatar-pos-desktop-2024-secure-key"
    
    # إعدادات اللغة
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    
    # إعدادات الواجهة
    WINDOW_TITLE = f"{APP_NAME_AR} - {APP_NAME}"
    WINDOW_SIZE = "800x600"
    WINDOW_MIN_SIZE = "600x400"
    
    # إعدادات السجل
    LOG_MAX_LINES = 1000
    LOG_FILE = BASE_DIR / "logs" / "qatar_pos.log"
    
    # إعدادات النسخ الاحتياطي
    BACKUP_FOLDER = BASE_DIR / "backups"
    AUTO_BACKUP = True
    BACKUP_INTERVAL_HOURS = 24
    
    # إعدادات التحديث
    CHECK_UPDATES = True
    UPDATE_URL = "https://api.qatarpos.com/updates"
    
    # إعدادات الشبكة
    NETWORK_TIMEOUT = 30
    MAX_RETRIES = 3
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            cls.UPLOAD_FOLDER,
            cls.BACKUP_FOLDER,
            cls.LOG_FILE.parent,
            cls.UPLOAD_FOLDER / "products",
            cls.UPLOAD_FOLDER / "invoices",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_database_uri(cls):
        """الحصول على رابط قاعدة البيانات"""
        return cls.DATABASE_URI
    
    @classmethod
    def get_app_info(cls):
        """الحصول على معلومات التطبيق"""
        return {
            'name': cls.APP_NAME,
            'name_ar': cls.APP_NAME_AR,
            'version': cls.APP_VERSION,
            'description': cls.APP_DESCRIPTION,
            'description_ar': cls.APP_DESCRIPTION_AR,
        }
    
    @classmethod
    def get_server_config(cls):
        """الحصول على إعدادات الخادم"""
        return {
            'host': cls.DEFAULT_HOST,
            'port': cls.DEFAULT_PORT,
            'port_range': (cls.PORT_RANGE_START, cls.PORT_RANGE_END),
        }
    
    @classmethod
    def is_portable(cls):
        """التحقق من كون التطبيق محمول"""
        return getattr(sys, 'frozen', False)

# إعدادات Flask للتطبيق المكتبي
class FlaskDesktopConfig:
    """إعدادات Flask للتطبيق المكتبي"""
    
    SECRET_KEY = DesktopConfig.SECRET_KEY
    SQLALCHEMY_DATABASE_URI = DesktopConfig.get_database_uri()
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الرفع
    UPLOAD_FOLDER = str(DesktopConfig.UPLOAD_FOLDER)
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # إعدادات اللغة
    LANGUAGES = DesktopConfig.LANGUAGES
    BABEL_DEFAULT_LOCALE = DesktopConfig.DEFAULT_LANGUAGE
    BABEL_DEFAULT_TIMEZONE = 'Asia/Qatar'
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = 3600 * 8  # 8 hours
    
    # إعدادات التطوير
    DEBUG = False
    TESTING = False
    
    # إعدادات قطر
    COUNTRY = 'Qatar'
    CURRENCY = 'QAR'
    CURRENCY_SYMBOL = 'ر.ق'
    TAX_RATE = 0.0  # Qatar doesn't have VAT yet
    
    @classmethod
    def init_app(cls, app):
        """تهيئة التطبيق"""
        # إنشاء المجلدات المطلوبة
        DesktopConfig.create_directories()
        
        # تعيين مسارات الملفات
        app.static_folder = str(DesktopConfig.STATIC_FOLDER)
        app.template_folder = str(DesktopConfig.TEMPLATES_FOLDER)

# إعدادات إضافية للتطبيق المكتبي
DESKTOP_SETTINGS = {
    'auto_start_server': True,
    'auto_open_browser': False,
    'minimize_to_tray': True,
    'show_splash_screen': True,
    'check_updates_on_start': True,
    'enable_logging': True,
    'log_level': 'INFO',
    'theme': 'default',
    'font_size': 10,
    'window_position': 'center',
    'remember_window_size': True,
    'enable_notifications': True,
    'backup_on_exit': True,
    'confirm_exit': True,
}

# رسائل التطبيق
MESSAGES = {
    'ar': {
        'starting_server': 'جاري بدء الخادم...',
        'server_started': 'تم بدء الخادم بنجاح',
        'server_stopped': 'تم إيقاف الخادم',
        'server_error': 'خطأ في الخادم',
        'opening_browser': 'جاري فتح المتصفح...',
        'browser_opened': 'تم فتح المتصفح',
        'browser_error': 'فشل في فتح المتصفح',
        'database_created': 'تم إنشاء قاعدة البيانات',
        'database_error': 'خطأ في قاعدة البيانات',
        'backup_created': 'تم إنشاء نسخة احتياطية',
        'backup_error': 'فشل في إنشاء النسخة الاحتياطية',
        'update_available': 'يتوفر تحديث جديد',
        'update_error': 'فشل في التحقق من التحديثات',
        'exit_confirm': 'هل تريد إغلاق التطبيق؟',
        'server_running_exit': 'الخادم يعمل. هل تريد إيقافه وإغلاق التطبيق؟',
    },
    'en': {
        'starting_server': 'Starting server...',
        'server_started': 'Server started successfully',
        'server_stopped': 'Server stopped',
        'server_error': 'Server error',
        'opening_browser': 'Opening browser...',
        'browser_opened': 'Browser opened',
        'browser_error': 'Failed to open browser',
        'database_created': 'Database created',
        'database_error': 'Database error',
        'backup_created': 'Backup created',
        'backup_error': 'Failed to create backup',
        'update_available': 'Update available',
        'update_error': 'Failed to check for updates',
        'exit_confirm': 'Do you want to exit the application?',
        'server_running_exit': 'Server is running. Do you want to stop it and exit?',
    }
}

def get_message(key, language='ar'):
    """الحصول على رسالة بلغة محددة"""
    return MESSAGES.get(language, MESSAGES['ar']).get(key, key)
