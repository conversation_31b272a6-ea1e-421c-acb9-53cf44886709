"""
Product Category model for Qatar POS System
Supports bilingual category names and hierarchical structure
"""

from datetime import datetime
from extensions import db

class Category(db.Model):
    """Product category model with bilingual support"""
    
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Bilingual names
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100), nullable=False)
    
    # Description
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    
    # Hierarchy support
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    parent = db.relationship('Category', remote_side=[id], backref='subcategories')
    
    # Status
    is_active = db.Column(db.Bo<PERSON>an, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    products = db.relationship('Product', backref='category', lazy='dynamic')
    
    def get_name(self, language='ar'):
        """Get category name in specified language"""
        if language == 'ar':
            return self.name_ar or self.name_en or ''
        else:
            return self.name_en or self.name_ar or ''

    def get_display_name(self, language='ar'):
        """Get display name for category (alias for get_name for consistency)"""
        return self.get_name(language)

    def get_description(self, language='ar'):
        """Get category description in specified language"""
        if language == 'ar':
            return self.description_ar or self.description_en or ''
        else:
            return self.description_en or self.description_ar or ''
    
    def get_full_path(self, language='ar'):
        """Get full category path (parent > child)"""
        if self.parent:
            return f"{self.parent.get_name(language)} > {self.get_name(language)}"
        return self.get_name(language)
    
    def get_all_products_count(self):
        """Get count of all products in this category and subcategories"""
        from models.product import Product
        count = Product.query.filter_by(category_id=self.id, is_active=True).count()
        for subcategory in self.subcategories:
            if subcategory.is_active:
                count += subcategory.get_all_products_count()
        return count

    def get_active_products_count(self):
        """Get count of active products in this category only"""
        from models.product import Product
        return Product.query.filter_by(category_id=self.id, is_active=True).count()

    def get_active_subcategories(self):
        """Get list of active subcategories"""
        return [sub for sub in self.subcategories if sub.is_active]

    def get_active_subcategories_count(self):
        """Get count of active subcategories"""
        return len([sub for sub in self.subcategories if sub.is_active])
    
    def to_dict(self, language='ar'):
        """Convert category to dictionary"""
        return {
            'id': self.id,
            'name': self.get_name(language),
            'description': self.get_description(language),
            'parent_id': self.parent_id,
            'parent_name': self.parent.get_name(language) if self.parent else None,
            'full_path': self.get_full_path(language),
            'products_count': self.get_all_products_count(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def get_root_categories():
        """Get all root categories (no parent)"""
        return Category.query.filter_by(parent_id=None, is_active=True).all()
    
    def __repr__(self):
        return f'<Category {self.name_en}>'
