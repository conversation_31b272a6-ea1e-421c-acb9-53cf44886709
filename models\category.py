"""
Product Category model for Qatar POS System
Supports bilingual category names and hierarchical structure
"""

from datetime import datetime
from extensions import db

class Category(db.Model):
    """Product category model with bilingual support"""
    
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Bilingual names
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100), nullable=False)
    
    # Description
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    
    # Hierarchy support
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    parent = db.relationship('Category', remote_side=[id], backref='subcategories')
    
    # Status
    is_active = db.Column(db.Bo<PERSON>an, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    products = db.relationship('Product', backref='category', lazy='dynamic')
    
    def get_name(self, language='ar'):
        """Get category name in specified language"""
        return self.name_ar if language == 'ar' else self.name_en
    
    def get_description(self, language='ar'):
        """Get category description in specified language"""
        return self.description_ar if language == 'ar' else self.description_en
    
    def get_full_path(self, language='ar'):
        """Get full category path (parent > child)"""
        if self.parent:
            return f"{self.parent.get_name(language)} > {self.get_name(language)}"
        return self.get_name(language)
    
    def get_all_products_count(self):
        """Get count of all products in this category and subcategories"""
        count = self.products.filter_by(is_active=True).count()
        for subcategory in self.subcategories:
            if subcategory.is_active:
                count += subcategory.get_all_products_count()
        return count
    
    def to_dict(self, language='ar'):
        """Convert category to dictionary"""
        return {
            'id': self.id,
            'name': self.get_name(language),
            'description': self.get_description(language),
            'parent_id': self.parent_id,
            'parent_name': self.parent.get_name(language) if self.parent else None,
            'full_path': self.get_full_path(language),
            'products_count': self.products.filter_by(is_active=True).count(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def get_root_categories():
        """Get all root categories (no parent)"""
        return Category.query.filter_by(parent_id=None, is_active=True).all()
    
    def __repr__(self):
        return f'<Category {self.name_en}>'
