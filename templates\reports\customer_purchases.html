{% extends "base.html" %}

{% block title %}{{ 'تقرير مشتريات العملاء' if language == 'ar' else 'Customer Purchases Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-cart-check-fill me-2"></i>
                    {{ 'تقرير مشتريات العملاء' if language == 'ar' else 'Customer Purchases Report' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        {{ 'فلاتر التقرير' if language == 'ar' else 'Report Filters' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">{{ 'العميل' if language == 'ar' else 'Customer' }}</label>
                            <select name="customer_id" class="form-select">
                                <option value="">{{ 'جميع العملاء' if language == 'ar' else 'All Customers' }}</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" {% if customer.id == customer_id %}selected{% endif %}>
                                    {{ customer.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'الحد الأدنى للمبلغ' if language == 'ar' else 'Min Amount' }}</label>
                            <input type="number" name="min_amount" class="form-control" step="0.01" value="{{ min_amount or '' }}" placeholder="0.00">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    {{ 'بحث' if language == 'ar' else 'Search' }}
                                </button>
                                <a href="{{ url_for('reports.customer_purchases') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي العملاء' if language == 'ar' else 'Total Customers' }}</h6>
                                    <h3 class="mb-0">{{ total_customers }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي الإيرادات' if language == 'ar' else 'Total Revenue' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(total_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي المعاملات' if language == 'ar' else 'Total Transactions' }}</h6>
                                    <h3 class="mb-0">{{ total_transactions }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-receipt fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'متوسط قيمة العميل' if language == 'ar' else 'Avg Customer Value' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(avg_customer_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Purchases Table -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>
                        {{ 'مشتريات العملاء' if language == 'ar' else 'Customer Purchases' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="purchasesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'اسم العميل' if language == 'ar' else 'Customer Name' }}</th>
                                    <th>{{ 'رقم الهاتف' if language == 'ar' else 'Phone' }}</th>
                                    <th>{{ 'إجمالي المشتريات' if language == 'ar' else 'Total Purchases' }}</th>
                                    <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transaction Count' }}</th>
                                    <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Average Transaction' }}</th>
                                    <th>{{ 'أول شراء' if language == 'ar' else 'First Purchase' }}</th>
                                    <th>{{ 'آخر شراء' if language == 'ar' else 'Last Purchase' }}</th>
                                    <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer, total_purchases, purchase_count, avg_purchase, last_purchase, first_purchase in customer_purchases %}
                                <tr>
                                    <td>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.customer_type %}
                                        <br><small class="text-muted">{{ customer.customer_type }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                            <i class="bi bi-telephone me-1"></i>{{ customer.phone }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">{{ "%.2f"|format(total_purchases) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ purchase_count }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(avg_purchase) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        {% if first_purchase %}
                                        <small>{{ first_purchase.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if last_purchase %}
                                        <small>{{ last_purchase.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('reports.customer_purchases', customer_id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>
                                            {{ 'التفاصيل' if language == 'ar' else 'Details' }}
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                        {{ 'لا توجد بيانات مشتريات' if language == 'ar' else 'No purchase data available' }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Detailed Purchases (if customer selected) -->
            {% if customer_id and detailed_purchases %}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        {{ 'تفاصيل المشتريات' if language == 'ar' else 'Purchase Details' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-secondary">
                                <tr>
                                    <th>{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice #' }}</th>
                                    <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                                    <th>{{ 'المبلغ الإجمالي' if language == 'ar' else 'Total Amount' }}</th>
                                    <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</th>
                                    <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in detailed_purchases %}
                                <tr>
                                    <td><code>{{ sale.invoice_number }}</code></td>
                                    <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td><strong>{{ "%.2f"|format(sale.total_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ sale.payment_method.replace('_', ' ').title() if sale.payment_method else 'Cash' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {% if sale.status == 'completed' %}bg-success{% elif sale.status == 'pending' %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ sale.status.title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    const table = document.getElementById('purchasesTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Customer Purchases"});
    XLSX.writeFile(wb, 'customer_purchases_report.xlsx');
}

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
