#!/usr/bin/env python3
"""
Qatar POS System - Run All Tests
Test all server files to find which one works
"""

import sys
import os
import subprocess
import time
import threading
import socket

def print_header():
    print("🇶🇦 نظام نقاط البيع القطري - اختبار جميع الملفات")
    print("Qatar POS System - Test All Files")
    print("=" * 60)

def check_port(port=5000):
    """Check if port is available"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result != 0  # True if port is available
    except:
        return False

def test_file(filename, timeout=10):
    """Test a Python file"""
    print(f"\n🧪 Testing {filename}...")
    
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found")
        return False
    
    try:
        # Start the process
        process = subprocess.Popen(
            [sys.executable, filename],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print(f"✅ {filename} started successfully")
            
            # Try to connect to the server
            try:
                import urllib.request
                response = urllib.request.urlopen('http://localhost:5000', timeout=5)
                if response.getcode() == 200:
                    print(f"✅ {filename} server responding")
                    result = True
                else:
                    print(f"⚠️ {filename} server not responding properly")
                    result = False
            except Exception as e:
                print(f"⚠️ {filename} connection failed: {e}")
                result = False
            
            # Terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return result
        else:
            # Process ended, check output
            stdout, stderr = process.communicate()
            print(f"❌ {filename} failed to start")
            if stderr:
                print(f"Error: {stderr[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ {filename} test failed: {e}")
        return False

def test_import(filename):
    """Test if file can be imported without errors"""
    print(f"\n📦 Import test for {filename}...")
    
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found")
        return False
    
    try:
        # Try to compile the file
        with open(filename, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, filename, 'exec')
        print(f"✅ {filename} syntax OK")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ {filename} import error: {e}")
        return False

def main():
    print_header()
    
    # List of files to test
    test_files = [
        'test_server.py',
        'simple_run.py',
        'run.py',
        'app.py'
    ]
    
    print("🔍 Checking Python and Flask...")
    
    # Check Python
    print(f"Python version: {sys.version}")
    
    # Check Flask
    try:
        import flask
        print(f"✅ Flask version: {flask.__version__}")
    except ImportError:
        print("❌ Flask not installed")
        print("💡 Install with: pip install flask")
        return
    
    # Check port availability
    if not check_port():
        print("⚠️ Port 5000 is already in use")
        print("💡 Close other applications using port 5000")
    
    print("\n" + "=" * 60)
    print("🧪 IMPORT TESTS")
    print("=" * 60)
    
    import_results = {}
    for filename in test_files:
        import_results[filename] = test_import(filename)
    
    print("\n" + "=" * 60)
    print("🌐 SERVER TESTS")
    print("=" * 60)
    
    server_results = {}
    for filename in test_files:
        if import_results.get(filename, False):
            server_results[filename] = test_file(filename)
        else:
            print(f"\n⏭️ Skipping {filename} (import failed)")
            server_results[filename] = False
    
    print("\n" + "=" * 60)
    print("📊 RESULTS SUMMARY")
    print("=" * 60)
    
    working_files = []
    for filename in test_files:
        import_ok = import_results.get(filename, False)
        server_ok = server_results.get(filename, False)
        
        if import_ok and server_ok:
            status = "✅ WORKING"
            working_files.append(filename)
        elif import_ok:
            status = "⚠️ IMPORT OK, SERVER FAILED"
        else:
            status = "❌ FAILED"
        
        print(f"{status:<25} {filename}")
    
    print("\n" + "=" * 60)
    
    if working_files:
        print("🎉 SUCCESS! Working files found:")
        for filename in working_files:
            print(f"   ✅ {filename}")
        
        print(f"\n🚀 Recommended: python {working_files[0]}")
        print("🌐 Then open: http://localhost:5000")
        
    else:
        print("❌ No working files found!")
        print("\n💡 Solutions:")
        print("1. Install Flask: pip install flask")
        print("2. Run fix script: python fix_system.py")
        print("3. Check system: python check_system.py")
        print("4. Manual install: install.bat (Windows)")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
