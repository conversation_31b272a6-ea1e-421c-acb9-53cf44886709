{% extends "base.html" %}

{% block title %}
{{ 'نقطة البيع - نظام نقاط البيع القطري' if language == 'ar' else 'Point of Sale - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.pos-container {
    height: calc(100vh - 120px);
    overflow: hidden;
}

.pos-products {
    height: 100%;
    overflow-y: auto;
    padding-right: 15px;
}

.pos-cart {
    height: 100%;
    border-left: 2px solid #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
}

.product-card {
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #dee2e6;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.cart-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.cart-item.suspended-item {
    background: #f8f9fa;
    border-color: #ffc107;
    opacity: 0.7;
}

.item-note {
    margin-top: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #e3f2fd;
    border-radius: 0.25rem;
    border-left: 3px solid #2196f3;
}

.suspended-badge {
    margin-top: 0.25rem;
}

.cart-summary {
    background: white;
    border-top: 2px solid #dee2e6;
    padding: 1rem;
}

.search-box, .barcode-box {
    position: relative;
}

.search-box .form-control, .barcode-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon, .barcode-box .barcode-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.barcode-box .form-control {
    border: 2px solid #28a745;
    border-radius: 8px;
}

.barcode-box .form-control:focus {
    border-color: #20c997;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.category-filter {
    max-height: 200px;
    overflow-y: auto;
}

.barcode-scanner {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
}

.payment-methods .btn {
    margin: 0.25rem;
}

@media (max-width: 768px) {
    .pos-container {
        height: auto;
    }
    
    .pos-cart {
        border-left: none;
        border-top: 2px solid #dee2e6;
        margin-top: 1rem;
        height: 400px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-lg-8 pos-products">
            <!-- Search and Filters -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="search-box">
                        <input type="text" class="form-control" id="product_search"
                               placeholder="{{ 'البحث عن منتج أو رمز' if language == 'ar' else 'Search product or SKU' }}">
                        <i class="bi bi-search search-icon"></i>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="barcode-box">
                        <input type="text" class="form-control" id="barcode_input"
                               placeholder="{{ 'مسح الباركود أو إدخاله' if language == 'ar' else 'Scan or enter barcode' }}"
                               autocomplete="off">
                        <i class="bi bi-upc-scan barcode-icon"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="category_filter" onchange="pos.filterByCategory(this.value)">
                        <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                        <!-- Categories will be loaded dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn barcode-scanner w-100" onclick="openBarcodeScanner()">
                        <i class="bi bi-upc-scan"></i>
                        {{ 'مسح الباركود' if language == 'ar' else 'Scan Barcode' }}
                    </button>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="row" id="products_grid">
                <!-- Products will be loaded here -->
            </div>
            
            <!-- Loading Spinner -->
            <div class="text-center py-5" id="products_loading" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{{ 'جاري التحميل...' if language == 'ar' else 'Loading...' }}</span>
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="col-lg-4 pos-cart">
            <!-- Cart Header -->
            <div class="p-3 bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-cart"></i>
                    {{ 'سلة المشتريات' if language == 'ar' else 'Shopping Cart' }}
                    <span class="badge bg-light text-dark ms-2" id="cart_count">0</span>
                </h5>
            </div>
            
            <!-- Customer Selection -->
            <div class="p-3 border-bottom">
                <label class="form-label small">{{ 'العميل' if language == 'ar' else 'Customer' }}</label>
                <select class="form-select form-select-sm" id="customer_select">
                    <option value="">{{ 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</option>
                    {% for customer in recent_customers %}
                    <option value="{{ customer.id }}">{{ customer.get_display_name(language) }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Cart Items -->
            <div class="cart-items" id="cart_items">
                <div class="text-center text-muted py-5">
                    <i class="bi bi-cart display-4"></i>
                    <p class="mt-2">{{ 'السلة فارغة' if language == 'ar' else 'Cart is empty' }}</p>
                    <small>{{ 'اختر منتجات لإضافتها للسلة' if language == 'ar' else 'Select products to add to cart' }}</small>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="row mb-2">
                    <div class="col">{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</div>
                    <div class="col-auto"><strong id="subtotal">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</strong></div>
                </div>
                <div class="row mb-2">
                    <div class="col">{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</div>
                    <div class="col-auto">
                        <input type="number" class="form-control form-control-sm" id="discount_amount"
                               style="width: 80px;" min="0" step="0.01" value="0" onchange="pos.calculateTotals()">
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">{{ 'الضريبة:' if language == 'ar' else 'Tax:' }}</div>
                    <div class="col-auto"><strong id="tax_amount">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</strong></div>
                </div>
                <hr>
                <div class="row mb-3">
                    <div class="col"><h5>{{ 'الإجمالي:' if language == 'ar' else 'Total:' }}</h5></div>
                    <div class="col-auto"><h5 class="text-primary" id="total_amount">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</h5></div>
                </div>

                <!-- Suspended Items Info -->
                <div id="suspended_info" style="display: none;" class="mb-2">
                    <!-- Content will be populated by JavaScript -->
                </div>
                
                <!-- Payment Methods -->
                <div class="payment-methods mb-3">
                    <label class="form-label small">{{ 'طريقة الدفع:' if language == 'ar' else 'Payment Method:' }}</label>

                    <!-- Primary Payment Methods -->
                    <div class="btn-group w-100 mb-2" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="cash" value="cash" checked>
                        <label class="btn btn-outline-success btn-sm" for="cash">
                            <i class="bi bi-cash"></i> {{ 'نقدي' if language == 'ar' else 'Cash' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="debit_card" value="debit_card">
                        <label class="btn btn-outline-primary btn-sm" for="debit_card">
                            <i class="bi bi-credit-card-2-front"></i> {{ 'بطاقة خصم' if language == 'ar' else 'Debit' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="credit_card" value="credit_card">
                        <label class="btn btn-outline-info btn-sm" for="credit_card">
                            <i class="bi bi-credit-card"></i> {{ 'بطاقة ائتمان' if language == 'ar' else 'Credit' }}
                        </label>
                    </div>

                    <!-- Digital Payment Methods -->
                    <div class="btn-group w-100 mb-2" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="bank_transfer" value="bank_transfer">
                        <label class="btn btn-outline-warning btn-sm" for="bank_transfer">
                            <i class="bi bi-bank"></i> {{ 'تحويل بنكي' if language == 'ar' else 'Transfer' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="mobile_payment" value="mobile_payment">
                        <label class="btn btn-outline-secondary btn-sm" for="mobile_payment">
                            <i class="bi bi-phone"></i> {{ 'دفع جوال' if language == 'ar' else 'Mobile Pay' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="digital_wallet" value="digital_wallet">
                        <label class="btn btn-outline-dark btn-sm" for="digital_wallet">
                            <i class="bi bi-wallet2"></i> {{ 'محفظة رقمية' if language == 'ar' else 'E-Wallet' }}
                        </label>
                    </div>

                    <!-- Additional Payment Methods -->
                    <div class="btn-group w-100 mb-2" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="check" value="check">
                        <label class="btn btn-outline-secondary btn-sm" for="check">
                            <i class="bi bi-file-earmark-text"></i> {{ 'شيك' if language == 'ar' else 'Check' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="credit" value="credit">
                        <label class="btn btn-outline-danger btn-sm" for="credit">
                            <i class="bi bi-clock-history"></i> {{ 'آجل' if language == 'ar' else 'Credit' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="installment" value="installment">
                        <label class="btn btn-outline-warning btn-sm" for="installment">
                            <i class="bi bi-calendar3"></i> {{ 'تقسيط' if language == 'ar' else 'Installment' }}
                        </label>
                    </div>

                    <!-- Store Payment Methods -->
                    <div class="btn-group w-100 mb-2" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="gift_card" value="gift_card">
                        <label class="btn btn-outline-success btn-sm" for="gift_card">
                            <i class="bi bi-gift"></i> {{ 'بطاقة هدية' if language == 'ar' else 'Gift Card' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="store_credit" value="store_credit">
                        <label class="btn btn-outline-info btn-sm" for="store_credit">
                            <i class="bi bi-piggy-bank"></i> {{ 'رصيد المتجر' if language == 'ar' else 'Store Credit' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="mixed" value="mixed">
                        <label class="btn btn-outline-primary btn-sm" for="mixed">
                            <i class="bi bi-layers"></i> {{ 'مختلط' if language == 'ar' else 'Mixed' }}
                        </label>
                    </div>

                    <!-- Delivery Payment Methods -->
                    <div class="btn-group w-100 mb-2" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="cod" value="cod">
                        <label class="btn btn-outline-warning btn-sm" for="cod">
                            <i class="bi bi-truck"></i> {{ 'دفع عند الاستلام' if language == 'ar' else 'Cash on Delivery' }}
                        </label>
                    </div>

                    <!-- Payment Details Section (Hidden by default) -->
                    <div id="payment_details" class="mt-2" style="display: none;">
                        <div class="card card-body bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-2">
                                        <label class="form-label small">{{ 'رقم المرجع:' if language == 'ar' else 'Reference:' }}</label>
                                        <input type="text" class="form-control form-control-sm" id="payment_reference"
                                               placeholder="{{ 'رقم البطاقة/المرجع' if language == 'ar' else 'Card/Reference Number' }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-2">
                                        <label class="form-label small">{{ 'البنك/الجهة:' if language == 'ar' else 'Bank/Provider:' }}</label>
                                        <input type="text" class="form-control form-control-sm" id="payment_provider"
                                               placeholder="{{ 'اسم البنك أو مقدم الخدمة' if language == 'ar' else 'Bank or Service Provider' }}">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-0">
                                <label class="form-label small">{{ 'ملاحظات الدفع:' if language == 'ar' else 'Payment Notes:' }}</label>
                                <textarea class="form-control form-control-sm" id="payment_notes" rows="2"
                                          placeholder="{{ 'ملاحظات إضافية حول الدفع' if language == 'ar' else 'Additional payment notes' }}"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success btn-lg" id="complete_sale" disabled onclick="pos.completeSale()">
                        <i class="bi bi-check-circle"></i>
                        {{ 'إتمام البيع' if language == 'ar' else 'Complete Sale' }}
                    </button>
                    <div class="row mb-2">
                        <div class="col">
                            <button type="button" class="btn btn-outline-warning w-100" id="hold_sale">
                                <i class="bi bi-pause-circle"></i>
                                {{ 'تعليق' if language == 'ar' else 'Hold' }}
                            </button>
                        </div>
                        <div class="col">
                            <button type="button" class="btn btn-outline-info w-100" onclick="pos.unsuspendAllItems()">
                                <i class="bi bi-play-circle"></i>
                                {{ 'إلغاء تعليق الكل' if language == 'ar' else 'Unsuspend All' }}
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <button type="button" class="btn btn-outline-danger w-100" id="clear_cart" onclick="pos.clearCart()">
                                <i class="bi bi-trash"></i>
                                {{ 'مسح' if language == 'ar' else 'Clear' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="barcodeScannerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'مسح الباركود' if language == 'ar' else 'Barcode Scanner' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{{ 'أدخل الباركود:' if language == 'ar' else 'Enter Barcode:' }}</label>
                    <input type="text" class="form-control" id="barcode_input" 
                           placeholder="{{ 'امسح أو اكتب الباركود' if language == 'ar' else 'Scan or type barcode' }}">
                </div>
                <div class="text-center">
                    <i class="bi bi-upc-scan display-1 text-muted"></i>
                    <p class="text-muted">{{ 'امسح الباركود أو اكتبه يدوياً' if language == 'ar' else 'Scan barcode or enter manually' }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="searchByBarcode()">
                    {{ 'بحث' if language == 'ar' else 'Search' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Sale Completion Modal -->
<div class="modal fade" id="saleCompletionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle"></i>
                    {{ 'تم إتمام البيع بنجاح' if language == 'ar' else 'Sale Completed Successfully' }}
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <h4 id="sale_number_display"></h4>
                    <p class="text-muted">{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice Number' }}</p>
                </div>
                <div class="row">
                    <div class="col-6">
                        <h5 id="sale_total_display"></h5>
                        <small class="text-muted">{{ 'إجمالي المبلغ' if language == 'ar' else 'Total Amount' }}</small>
                    </div>
                    <div class="col-6">
                        <h5 id="sale_items_display"></h5>
                        <small class="text-muted">{{ 'عدد الأصناف' if language == 'ar' else 'Items Count' }}</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" onclick="printInvoice()">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة الفاتورة' if language == 'ar' else 'Print Invoice' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="newSale()">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'بيع جديد' if language == 'ar' else 'New Sale' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/pos.js') }}"></script>
<script>
// Add sound effects for payment method selection and barcode handling
document.addEventListener('DOMContentLoaded', function() {
    // Barcode input handling
    const barcodeInput = document.getElementById('barcode_input');
    if (barcodeInput) {
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = this.value.trim();
                if (barcode) {
                    searchProductByBarcode(barcode);
                    this.value = '';
                }
            }
        });

        // Auto-focus on barcode input for scanner
        barcodeInput.addEventListener('focus', function() {
            this.select();
        });

        // Handle barcode scanner input (usually very fast)
        let barcodeBuffer = '';
        let barcodeTimeout;

        document.addEventListener('keypress', function(e) {
            // Only process if barcode input is focused or no input is focused
            if (document.activeElement === barcodeInput ||
                !document.activeElement ||
                document.activeElement.tagName === 'BODY') {

                // Clear timeout
                clearTimeout(barcodeTimeout);

                // Add character to buffer
                barcodeBuffer += e.key;

                // Set timeout to process barcode
                barcodeTimeout = setTimeout(function() {
                    if (barcodeBuffer.length >= 8) { // Minimum barcode length
                        searchProductByBarcode(barcodeBuffer);
                        if (window.playSound) {
                            window.playSound('scan');
                        }
                    }
                    barcodeBuffer = '';
                }, 100); // 100ms timeout for scanner input
            }
        });
    }

    // Payment method selection sounds and details handling
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (window.playSound) {
                window.playSound('payment');
            }

            // Show/hide payment details based on method
            handlePaymentMethodChange(this.value);

            // Trigger payment method selected event
            document.dispatchEvent(new CustomEvent('paymentReceived', {
                detail: { method: this.value }
            }));
        });
    });

    // Add sound to print invoice button
    document.querySelector('[onclick="printInvoice()"]')?.addEventListener('click', function() {
        if (window.playSound) {
            window.playSound('success');
        }
    });

    // Add sound to new sale button
    document.querySelector('[onclick="newSale()"]')?.addEventListener('click', function() {
        if (window.playSound) {
            window.playSound('notification');
        }
    });
});

// Barcode search function
function searchProductByBarcode(barcode) {
    // Show loading indicator
    const barcodeInput = document.getElementById('barcode_input');
    if (barcodeInput) {
        barcodeInput.style.backgroundColor = '#fff3cd';
        barcodeInput.placeholder = '{{ "جاري البحث..." if language == "ar" else "Searching..." }}';
    }

    fetch(`/api/products/search-barcode/${encodeURIComponent(barcode)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.product) {
                // Add product to cart
                pos.addToCart(data.product);

                // Play success sound
                if (window.playSound) {
                    window.playSound('success');
                }

                // Show success feedback
                showBarcodeMessage('{{ "تم إضافة المنتج" if language == "ar" else "Product added" }}', 'success');
            } else {
                // Play error sound
                if (window.playSound) {
                    window.playSound('error');
                }

                // Show error message
                showBarcodeMessage(data.message || '{{ "المنتج غير موجود" if language == "ar" else "Product not found" }}', 'error');
            }
        })
        .catch(error => {
            console.error('Barcode search error:', error);
            if (window.playSound) {
                window.playSound('error');
            }
            showBarcodeMessage('{{ "خطأ في البحث" if language == "ar" else "Search error" }}', 'error');
        })
        .finally(() => {
            // Reset input
            if (barcodeInput) {
                barcodeInput.style.backgroundColor = '';
                barcodeInput.placeholder = '{{ "مسح الباركود أو إدخاله" if language == "ar" else "Scan or enter barcode" }}';
            }
        });
}

// Show barcode message
function showBarcodeMessage(message, type) {
    const barcodeInput = document.getElementById('barcode_input');
    if (!barcodeInput) return;

    // Create message element
    const messageEl = document.createElement('div');
    messageEl.className = `alert alert-${type === 'success' ? 'success' : 'danger'} barcode-message`;
    messageEl.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        margin-top: 5px;
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 4px;
    `;
    messageEl.textContent = message;

    // Add to parent
    const parent = barcodeInput.parentElement;
    parent.style.position = 'relative';
    parent.appendChild(messageEl);

    // Remove after 3 seconds
    setTimeout(() => {
        if (messageEl.parentElement) {
            messageEl.parentElement.removeChild(messageEl);
        }
    }, 3000);
}

// Handle payment method change
function handlePaymentMethodChange(method) {
    const paymentDetails = document.getElementById('payment_details');
    const referenceInput = document.getElementById('payment_reference');
    const providerInput = document.getElementById('payment_provider');

    // Methods that require additional details
    const methodsRequiringDetails = [
        'debit_card', 'credit_card', 'bank_transfer', 'mobile_payment',
        'digital_wallet', 'check', 'gift_card', 'mixed', 'cod'
    ];

    if (methodsRequiringDetails.includes(method)) {
        paymentDetails.style.display = 'block';

        // Update placeholders based on method
        switch(method) {
            case 'debit_card':
            case 'credit_card':
                referenceInput.placeholder = '{{ "آخر 4 أرقام من البطاقة" if language == "ar" else "Last 4 digits of card" }}';
                providerInput.placeholder = '{{ "البنك المصدر" if language == "ar" else "Issuing Bank" }}';
                break;
            case 'bank_transfer':
                referenceInput.placeholder = '{{ "رقم التحويل" if language == "ar" else "Transfer Reference" }}';
                providerInput.placeholder = '{{ "البنك" if language == "ar" else "Bank Name" }}';
                break;
            case 'mobile_payment':
                referenceInput.placeholder = '{{ "رقم المعاملة" if language == "ar" else "Transaction ID" }}';
                providerInput.placeholder = '{{ "مقدم الخدمة (مثل: فودافون كاش)" if language == "ar" else "Provider (e.g., Vodafone Cash)" }}';
                break;
            case 'digital_wallet':
                referenceInput.placeholder = '{{ "رقم المعاملة" if language == "ar" else "Transaction ID" }}';
                providerInput.placeholder = '{{ "المحفظة الرقمية (مثل: PayPal)" if language == "ar" else "Digital Wallet (e.g., PayPal)" }}';
                break;
            case 'check':
                referenceInput.placeholder = '{{ "رقم الشيك" if language == "ar" else "Check Number" }}';
                providerInput.placeholder = '{{ "البنك المسحوب عليه" if language == "ar" else "Drawn on Bank" }}';
                break;
            case 'gift_card':
                referenceInput.placeholder = '{{ "رقم بطاقة الهدية" if language == "ar" else "Gift Card Number" }}';
                providerInput.placeholder = '{{ "مصدر البطاقة" if language == "ar" else "Card Issuer" }}';
                break;
            case 'mixed':
                referenceInput.placeholder = '{{ "تفاصيل الدفع المختلط" if language == "ar" else "Mixed Payment Details" }}';
                providerInput.placeholder = '{{ "طرق الدفع المستخدمة" if language == "ar" else "Payment Methods Used" }}';
                break;
            case 'cod':
                referenceInput.placeholder = '{{ "عنوان التسليم" if language == "ar" else "Delivery Address" }}';
                providerInput.placeholder = '{{ "شركة التوصيل" if language == "ar" else "Delivery Company" }}';
                break;
            default:
                referenceInput.placeholder = '{{ "رقم المرجع" if language == "ar" else "Reference Number" }}';
                providerInput.placeholder = '{{ "مقدم الخدمة" if language == "ar" else "Service Provider" }}';
        }
    } else {
        paymentDetails.style.display = 'none';
        // Clear the fields when hiding
        referenceInput.value = '';
        providerInput.value = '';
        document.getElementById('payment_notes').value = '';
    }
}

// Initialize payment method handling
document.addEventListener('DOMContentLoaded', function() {
    // Set initial state
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (selectedMethod) {
        handlePaymentMethodChange(selectedMethod.value);
    }
});
</script>
{% endblock %}
