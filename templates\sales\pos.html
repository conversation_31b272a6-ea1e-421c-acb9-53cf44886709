{% extends "base.html" %}

{% block title %}
{{ 'نقطة البيع - نظام نقاط البيع القطري' if language == 'ar' else 'Point of Sale - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.pos-container {
    height: calc(100vh - 120px);
    overflow: hidden;
}

.pos-products {
    height: 100%;
    overflow-y: auto;
    padding-right: 15px;
}

.pos-cart {
    height: 100%;
    border-left: 2px solid #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    max-width: 380px;
}

.product-card {
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid #dee2e6;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem;
    max-height: 250px;
}

.cart-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.cart-item.suspended-item {
    background: #f8f9fa;
    border-color: #ffc107;
    opacity: 0.7;
}

.item-note {
    margin-top: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #e3f2fd;
    border-radius: 0.25rem;
    border-left: 3px solid #2196f3;
}

.suspended-badge {
    margin-top: 0.25rem;
}

.cart-summary {
    background: white;
    border-top: 2px solid #dee2e6;
    padding: 0.75rem;
}

.search-box, .barcode-box {
    position: relative;
}

.search-box .form-control, .barcode-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon, .barcode-box .barcode-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.barcode-box .form-control {
    border: 2px solid #28a745;
    border-radius: 8px;
}

.barcode-box .form-control:focus {
    border-color: #20c997;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.category-filter {
    max-height: 200px;
    overflow-y: auto;
}

.barcode-scanner {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
}

.payment-methods .btn {
    margin: 0.1rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.payment-methods .btn i {
    font-size: 0.8rem;
    margin-right: 0.25rem;
}

/* Compact cart styling */
.pos-cart .p-3 {
    padding: 0.75rem !important;
}

.pos-cart .mb-3 {
    margin-bottom: 0.75rem !important;
}

.pos-cart .mb-2 {
    margin-bottom: 0.5rem !important;
}

.pos-cart h5 {
    font-size: 1rem;
}

/* Compact payment methods */
.payment-methods .btn-group {
    margin-bottom: 0.25rem !important;
}

/* Compact form controls */
.cart-summary .form-control-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Compact action buttons */
.cart-summary .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.cart-summary .btn:not(.btn-lg) {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* Compact payment details */
#payment_details .card-body {
    padding: 0.5rem;
}

#payment_details .form-control-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
}

#payment_details .form-label {
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
}

/* تحسينات إضافية لسهولة الاستخدام */
.btn-group .btn {
    transition: all 0.2s ease;
    border-width: 2px;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
}

.btn-check:checked + .btn {
    transform: scale(0.95);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسين حقول الإدخال */
.form-control {
    transition: all 0.2s ease;
    border-width: 2px;
}

.form-control:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسين الأزرار الرئيسية */
.btn-lg {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

/* تحسين عرض المبالغ */
#total_amount {
    font-size: 24px !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    font-weight: 700 !important;
}

/* تحسين بطاقات المنتجات */
.product-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.product-card:hover {
    border-color: #007bff;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* تحسين عناصر السلة */
.cart-item {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.cart-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

/* تحسين ألوان النظام */
.text-primary {
    color: #0066cc !important;
}

.btn-outline-success:checked,
.btn-outline-success.active {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-danger:checked,
.btn-outline-danger.active {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* تحسين معلومات التوصيل */
#cod_fields .bg-light {
    border: 2px solid #17a2b8;
    border-radius: 10px;
}

#cod_fields .form-control {
    border-color: #17a2b8;
}

#cod_fields .form-control:focus {
    border-color: #138496;
    box-shadow: 0 0 0 0.2rem rgba(23,162,184,0.25);
}

/* تحسينات إضافية للأزرار المدمجة */
.payment-methods .btn-group {
    margin-bottom: 0.25rem;
}

.payment-methods .btn-sm {
    padding: 4px 6px;
    font-size: 0.7rem;
    line-height: 1.2;
    min-height: 45px;
}

.payment-methods .btn i {
    font-size: 0.9rem;
    margin-bottom: 2px;
}

/* زر COD السريع */
.btn-warning.w-100 {
    background: linear-gradient(135deg, #ffc107, #ffb300);
    border: none;
    color: #000;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-warning.w-100:hover {
    background: linear-gradient(135deg, #ffb300, #ff8f00);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
    color: #000;
}

@media (max-width: 768px) {
    .pos-container {
        height: auto;
    }

    .pos-cart {
        border-left: none;
        border-top: 2px solid #dee2e6;
        margin-top: 1rem;
        height: 400px;
    }

    .btn-group .btn {
        min-height: 40px;
        font-size: 0.65rem;
        padding: 3px 4px;
    }

    #total_amount {
        font-size: 20px !important;
    }

    .cart-summary,
    .payment-methods {
        padding: 10px;
        margin: 5px 0;
    }

    .payment-methods .btn-sm {
        min-height: 35px;
        font-size: 0.6rem;
        padding: 2px 4px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-lg-8 pos-products">
            <!-- Search and Filters -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="search-box">
                        <input type="text" class="form-control" id="product_search"
                               placeholder="{{ 'البحث عن منتج أو رمز' if language == 'ar' else 'Search product or SKU' }}">
                        <i class="bi bi-search search-icon"></i>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="barcode-box">
                        <input type="text" class="form-control" id="barcode_input"
                               placeholder="{{ 'مسح الباركود أو إدخاله' if language == 'ar' else 'Scan or enter barcode' }}"
                               autocomplete="off">
                        <i class="bi bi-upc-scan barcode-icon"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="category_filter" onchange="pos.filterByCategory(this.value)">
                        <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                        <!-- Categories will be loaded dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn barcode-scanner w-100" onclick="openBarcodeScanner()">
                        <i class="bi bi-upc-scan"></i>
                        {{ 'مسح الباركود' if language == 'ar' else 'Scan Barcode' }}
                    </button>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="row" id="products_grid">
                <!-- Products will be loaded here -->
            </div>
            
            <!-- Loading Spinner -->
            <div class="text-center py-5" id="products_loading" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{{ 'جاري التحميل...' if language == 'ar' else 'Loading...' }}</span>
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="col-lg-4 pos-cart">
            <!-- Cart Header -->
            <div class="p-2 bg-primary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-cart"></i>
                    {{ 'سلة المشتريات' if language == 'ar' else 'Shopping Cart' }}
                    <span class="badge bg-light text-dark ms-2" id="cart_count">0</span>
                </h6>
            </div>

            <!-- Customer Selection -->
            <div class="p-2 border-bottom">
                <label class="form-label small mb-1">{{ 'العميل' if language == 'ar' else 'Customer' }}</label>
                <select class="form-select form-select-sm" id="customer_select">
                    <option value="">{{ 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</option>
                    {% for customer in recent_customers %}
                    <option value="{{ customer.id }}">{{ customer.get_display_name(language) }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Cart Items -->
            <div class="cart-items" id="cart_items">
                <div class="text-center text-muted py-5">
                    <i class="bi bi-cart display-4"></i>
                    <p class="mt-2">{{ 'السلة فارغة' if language == 'ar' else 'Cart is empty' }}</p>
                    <small>{{ 'اختر منتجات لإضافتها للسلة' if language == 'ar' else 'Select products to add to cart' }}</small>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="cart-summary bg-light p-3 rounded">
                <div class="row mb-2 align-items-center">
                    <div class="col-7"><span class="fw-normal">{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</span></div>
                    <div class="col-5 text-end"><strong id="subtotal" class="fs-6">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</strong></div>
                </div>
                <div class="row mb-2 align-items-center">
                    <div class="col-7"><span class="fw-normal">{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</span></div>
                    <div class="col-5 text-end">
                        <input type="number" class="form-control form-control-sm text-center" id="discount_amount"
                               style="width: 90px; height: 35px; font-size: 14px;" min="0" step="0.01" value="0" onchange="pos.calculateTotals()">
                    </div>
                </div>
                <div class="row mb-2 align-items-center">
                    <div class="col-7"><span class="fw-normal">{{ 'الضريبة:' if language == 'ar' else 'Tax:' }}</span></div>
                    <div class="col-5 text-end"><strong id="tax_amount" class="fs-6">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</strong></div>
                </div>
                <div class="row mb-2 align-items-center" id="delivery_fee_row" style="display: none;">
                    <div class="col-7"><span class="fw-normal text-info">{{ 'رسوم التوصيل:' if language == 'ar' else 'Delivery Fee:' }}</span></div>
                    <div class="col-5 text-end">
                        <input type="number" class="form-control form-control-sm text-center border-info" id="delivery_fee"
                               style="width: 90px; height: 35px; font-size: 14px;" min="0" step="0.01" value="0" onchange="pos.calculateTotals()">
                    </div>
                </div>
                <hr class="my-3">
                <div class="row mb-2 align-items-center">
                    <div class="col-7"><h5 class="mb-0 text-primary">{{ 'الإجمالي:' if language == 'ar' else 'Total:' }}</h5></div>
                    <div class="col-5 text-end"><h4 class="text-primary mb-0 fw-bold" id="total_amount">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</h4></div>
                </div>

                <!-- Suspended Items Info -->
                <div id="suspended_info" style="display: none;" class="mb-2">
                    <!-- Content will be populated by JavaScript -->
                </div>
                
                <!-- Payment Methods -->
                <div class="payment-methods mb-2">
                    <label class="form-label small mb-1">{{ 'طريقة الدفع:' if language == 'ar' else 'Payment Method:' }}</label>

                    <!-- Primary Payment Methods -->
                    <div class="btn-group w-100 mb-1" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="cash" value="cash" checked>
                        <label class="btn btn-outline-success btn-sm" for="cash" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-cash" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'نقدي' if language == 'ar' else 'Cash' }}</span>
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="debit_card" value="debit_card">
                        <label class="btn btn-outline-primary btn-sm" for="debit_card" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-credit-card-2-front" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'خصم' if language == 'ar' else 'Debit' }}</span>
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="credit_card" value="credit_card">
                        <label class="btn btn-outline-info btn-sm" for="credit_card" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-credit-card" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'ائتمان' if language == 'ar' else 'Credit' }}</span>
                        </label>
                    </div>

                    <!-- Digital Payment Methods -->
                    <div class="btn-group w-100 mb-1" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="bank_transfer" value="bank_transfer">
                        <label class="btn btn-outline-warning btn-sm" for="bank_transfer" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-bank" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'تحويل' if language == 'ar' else 'Transfer' }}</span>
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="mobile_payment" value="mobile_payment">
                        <label class="btn btn-outline-secondary btn-sm" for="mobile_payment" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-phone" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'جوال' if language == 'ar' else 'Mobile' }}</span>
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="cod" value="cod">
                        <label class="btn btn-outline-danger btn-sm" for="cod" style="padding: 6px 4px; font-size: 0.75rem;">
                            <i class="bi bi-truck" style="font-size: 1rem;"></i><br>
                            <span style="font-size: 0.7rem;">{{ 'عند الاستلام' if language == 'ar' else 'COD' }}</span>
                        </label>
                    </div>

                    <!-- Additional Payment Methods -->
                    <div class="btn-group w-100 mb-1" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="check" value="check">
                        <label class="btn btn-outline-secondary btn-sm" for="check" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-file-earmark-text" style="font-size: 0.8rem;"></i> {{ 'شيك' if language == 'ar' else 'Check' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="credit" value="credit">
                        <label class="btn btn-outline-danger btn-sm" for="credit" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-clock-history" style="font-size: 0.8rem;"></i> {{ 'آجل' if language == 'ar' else 'Credit' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="installment" value="installment">
                        <label class="btn btn-outline-warning btn-sm" for="installment" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-calendar3" style="font-size: 0.8rem;"></i> {{ 'تقسيط' if language == 'ar' else 'Install' }}
                        </label>
                    </div>

                    <!-- Store Payment Methods -->
                    <div class="btn-group w-100 mb-1" role="group">
                        <input type="radio" class="btn-check" name="payment_method" id="gift_card" value="gift_card">
                        <label class="btn btn-outline-success btn-sm" for="gift_card" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-gift" style="font-size: 0.8rem;"></i> {{ 'هدية' if language == 'ar' else 'Gift' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="store_credit" value="store_credit">
                        <label class="btn btn-outline-info btn-sm" for="store_credit" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-piggy-bank" style="font-size: 0.8rem;"></i> {{ 'رصيد' if language == 'ar' else 'Credit' }}
                        </label>

                        <input type="radio" class="btn-check" name="payment_method" id="mixed" value="mixed">
                        <label class="btn btn-outline-primary btn-sm" for="mixed" style="padding: 4px 6px; font-size: 0.7rem;">
                            <i class="bi bi-layers" style="font-size: 0.8rem;"></i> {{ 'مختلط' if language == 'ar' else 'Mixed' }}
                        </label>
                    </div>

                    <!-- Quick COD Button -->
                    <div class="btn-group w-100 mb-1" role="group">
                        <button type="button" class="btn btn-warning btn-sm w-100" onclick="quickCOD()" style="padding: 6px; font-size: 0.8rem;">
                            <i class="bi bi-truck-flatbed" style="font-size: 1rem;"></i> {{ 'دفع عند الاستلام' if language == 'ar' else 'Quick COD' }}
                        </button>
                    </div>

                    <!-- Payment Details Section (Hidden by default) -->
                    <div id="payment_details" class="mt-1" style="display: none;">
                        <div class="card card-body bg-light p-2">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group mb-1">
                                        <label class="form-label small mb-1">{{ 'مرجع:' if language == 'ar' else 'Ref:' }}</label>
                                        <input type="text" class="form-control form-control-sm" id="payment_reference"
                                               placeholder="{{ 'رقم المرجع' if language == 'ar' else 'Reference' }}">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group mb-1">
                                        <label class="form-label small mb-1">{{ 'جهة:' if language == 'ar' else 'Provider:' }}</label>
                                        <input type="text" class="form-control form-control-sm" id="payment_provider"
                                               placeholder="{{ 'البنك/الجهة' if language == 'ar' else 'Bank/Provider' }}">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-0">
                                <label class="form-label small mb-1">{{ 'ملاحظات:' if language == 'ar' else 'Notes:' }}</label>
                                <textarea class="form-control form-control-sm" id="payment_notes" rows="1"
                                          placeholder="{{ 'ملاحظات الدفع' if language == 'ar' else 'Payment notes' }}"></textarea>
                            </div>

                            <!-- COD specific fields -->
                            <div id="cod_fields" style="display: none;">
                                <hr class="my-3">
                                <div class="bg-light p-3 rounded">
                                    <h6 class="text-info mb-3">
                                        <i class="bi bi-truck"></i>
                                        {{ 'معلومات التوصيل' if language == 'ar' else 'Delivery Information' }}
                                    </h6>
                                    <div class="form-group mb-3">
                                        <label class="form-label fw-bold mb-2">{{ 'عنوان التوصيل:' if language == 'ar' else 'Delivery Address:' }}</label>
                                        <textarea class="form-control" id="delivery_address" rows="3" style="font-size: 14px;"
                                                  placeholder="{{ 'أدخل عنوان التوصيل الكامل مع تفاصيل الوصول' if language == 'ar' else 'Enter full delivery address with access details' }}"></textarea>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="form-group mb-2">
                                                <label class="form-label fw-bold mb-2">{{ 'هاتف التوصيل:' if language == 'ar' else 'Delivery Phone:' }}</label>
                                                <input type="tel" class="form-control" id="delivery_phone" style="font-size: 14px; height: 45px;"
                                                       placeholder="{{ '+974 5555 1234' if language == 'ar' else '+974 5555 1234' }}">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group mb-2">
                                                <label class="form-label fw-bold mb-2">{{ 'ملاحظات التوصيل:' if language == 'ar' else 'Delivery Notes:' }}</label>
                                                <input type="text" class="form-control" id="delivery_notes" style="font-size: 14px; height: 45px;"
                                                       placeholder="{{ 'وقت التوصيل المفضل' if language == 'ar' else 'Preferred delivery time' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success btn-lg py-3" id="complete_sale" disabled onclick="pos.completeSale()">
                        <i class="bi bi-check-circle fs-4"></i>
                        <span class="fw-bold d-block">{{ 'إتمام البيع' if language == 'ar' else 'Complete Sale' }}</span>
                    </button>
                    <div class="row g-2 mb-2">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-warning w-100 py-2" id="hold_sale">
                                <i class="bi bi-pause-circle fs-5"></i><br>
                                <span class="fw-bold">{{ 'تعليق' if language == 'ar' else 'Hold' }}</span>
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-info w-100 py-2" onclick="pos.unsuspendAllItems()">
                                <i class="bi bi-play-circle fs-5"></i><br>
                                <span class="fw-bold">{{ 'إلغاء تعليق' if language == 'ar' else 'Resume' }}</span>
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-danger w-100 py-2" id="clear_cart" onclick="pos.clearCart()">
                        <i class="bi bi-trash fs-5"></i>
                        <span class="fw-bold">{{ 'مسح السلة' if language == 'ar' else 'Clear Cart' }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="barcodeScannerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'مسح الباركود' if language == 'ar' else 'Barcode Scanner' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{{ 'أدخل الباركود:' if language == 'ar' else 'Enter Barcode:' }}</label>
                    <input type="text" class="form-control" id="barcode_input" 
                           placeholder="{{ 'امسح أو اكتب الباركود' if language == 'ar' else 'Scan or type barcode' }}">
                </div>
                <div class="text-center">
                    <i class="bi bi-upc-scan display-1 text-muted"></i>
                    <p class="text-muted">{{ 'امسح الباركود أو اكتبه يدوياً' if language == 'ar' else 'Scan barcode or enter manually' }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="searchByBarcode()">
                    {{ 'بحث' if language == 'ar' else 'Search' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Sale Completion Modal -->
<div class="modal fade" id="saleCompletionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle"></i>
                    {{ 'تم إتمام البيع بنجاح' if language == 'ar' else 'Sale Completed Successfully' }}
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <h4 id="sale_number_display"></h4>
                    <p class="text-muted">{{ 'رقم الفاتورة' if language == 'ar' else 'Invoice Number' }}</p>
                </div>
                <div class="row">
                    <div class="col-6">
                        <h5 id="sale_total_display"></h5>
                        <small class="text-muted">{{ 'إجمالي المبلغ' if language == 'ar' else 'Total Amount' }}</small>
                    </div>
                    <div class="col-6">
                        <h5 id="sale_items_display"></h5>
                        <small class="text-muted">{{ 'عدد الأصناف' if language == 'ar' else 'Items Count' }}</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" onclick="printInvoice()">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة الفاتورة' if language == 'ar' else 'Print Invoice' }}
                </button>
                <button type="button" class="btn btn-primary" onclick="newSale()">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'بيع جديد' if language == 'ar' else 'New Sale' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/pos.js') }}"></script>
<script>
// Add sound effects for payment method selection and barcode handling
document.addEventListener('DOMContentLoaded', function() {
    // Barcode input handling
    const barcodeInput = document.getElementById('barcode_input');
    if (barcodeInput) {
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = this.value.trim();
                if (barcode) {
                    searchProductByBarcode(barcode);
                    this.value = '';
                }
            }
        });

        // Auto-focus on barcode input for scanner
        barcodeInput.addEventListener('focus', function() {
            this.select();
        });

        // Handle barcode scanner input (usually very fast)
        let barcodeBuffer = '';
        let barcodeTimeout;

        document.addEventListener('keypress', function(e) {
            // Only process if barcode input is focused or no input is focused
            if (document.activeElement === barcodeInput ||
                !document.activeElement ||
                document.activeElement.tagName === 'BODY') {

                // Clear timeout
                clearTimeout(barcodeTimeout);

                // Add character to buffer
                barcodeBuffer += e.key;

                // Set timeout to process barcode
                barcodeTimeout = setTimeout(function() {
                    if (barcodeBuffer.length >= 8) { // Minimum barcode length
                        searchProductByBarcode(barcodeBuffer);
                        if (window.playSound) {
                            window.playSound('scan');
                        }
                    }
                    barcodeBuffer = '';
                }, 100); // 100ms timeout for scanner input
            }
        });
    }

    // Payment method selection sounds and details handling
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (window.playSound) {
                window.playSound('payment');
            }

            // Show/hide payment details based on method
            handlePaymentMethodChange(this.value);

            // Trigger payment method selected event
            document.dispatchEvent(new CustomEvent('paymentReceived', {
                detail: { method: this.value }
            }));
        });
    });

    // Add sound to print invoice button
    document.querySelector('[onclick="printInvoice()"]')?.addEventListener('click', function() {
        if (window.playSound) {
            window.playSound('success');
        }
    });

    // Add sound to new sale button
    document.querySelector('[onclick="newSale()"]')?.addEventListener('click', function() {
        if (window.playSound) {
            window.playSound('notification');
        }
    });
});

// Barcode search function
function searchProductByBarcode(barcode) {
    // Show loading indicator
    const barcodeInput = document.getElementById('barcode_input');
    if (barcodeInput) {
        barcodeInput.style.backgroundColor = '#fff3cd';
        barcodeInput.placeholder = '{{ "جاري البحث..." if language == "ar" else "Searching..." }}';
    }

    fetch(`/api/products/search-barcode/${encodeURIComponent(barcode)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.product) {
                // Add product to cart
                pos.addToCart(data.product);

                // Play success sound
                if (window.playSound) {
                    window.playSound('success');
                }

                // Show success feedback
                showBarcodeMessage('{{ "تم إضافة المنتج" if language == "ar" else "Product added" }}', 'success');
            } else {
                // Play error sound
                if (window.playSound) {
                    window.playSound('error');
                }

                // Show error message
                showBarcodeMessage(data.message || '{{ "المنتج غير موجود" if language == "ar" else "Product not found" }}', 'error');
            }
        })
        .catch(error => {
            console.error('Barcode search error:', error);
            if (window.playSound) {
                window.playSound('error');
            }
            showBarcodeMessage('{{ "خطأ في البحث" if language == "ar" else "Search error" }}', 'error');
        })
        .finally(() => {
            // Reset input
            if (barcodeInput) {
                barcodeInput.style.backgroundColor = '';
                barcodeInput.placeholder = '{{ "مسح الباركود أو إدخاله" if language == "ar" else "Scan or enter barcode" }}';
            }
        });
}

// Show barcode message
function showBarcodeMessage(message, type) {
    const barcodeInput = document.getElementById('barcode_input');
    if (!barcodeInput) return;

    // Create message element
    const messageEl = document.createElement('div');
    messageEl.className = `alert alert-${type === 'success' ? 'success' : 'danger'} barcode-message`;
    messageEl.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        margin-top: 5px;
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 4px;
    `;
    messageEl.textContent = message;

    // Add to parent
    const parent = barcodeInput.parentElement;
    parent.style.position = 'relative';
    parent.appendChild(messageEl);

    // Remove after 3 seconds
    setTimeout(() => {
        if (messageEl.parentElement) {
            messageEl.parentElement.removeChild(messageEl);
        }
    }, 3000);
}

// Handle payment method change
function handlePaymentMethodChange(method) {
    const paymentDetails = document.getElementById('payment_details');
    const codFields = document.getElementById('cod_fields');
    const deliveryFeeRow = document.getElementById('delivery_fee_row');
    const deliveryFeeInput = document.getElementById('delivery_fee');
    const referenceInput = document.getElementById('payment_reference');
    const providerInput = document.getElementById('payment_provider');

    // Methods that require additional details
    const methodsRequiringDetails = [
        'debit_card', 'credit_card', 'bank_transfer', 'mobile_payment',
        'digital_wallet', 'check', 'gift_card', 'mixed', 'cod'
    ];

    if (methodsRequiringDetails.includes(method)) {
        paymentDetails.style.display = 'block';

        // Show/hide COD specific fields and delivery fee
        if (method === 'cod') {
            codFields.style.display = 'block';
            deliveryFeeRow.style.display = 'flex';
            // Set default delivery fee for COD orders
            if (deliveryFeeInput.value == '0') {
                deliveryFeeInput.value = '10'; // Default delivery fee
                pos.calculateTotals();
            }
        } else {
            codFields.style.display = 'none';
            deliveryFeeRow.style.display = 'none';
            deliveryFeeInput.value = '0';
            pos.calculateTotals();
        }

        // Update placeholders based on method
        switch(method) {
            case 'debit_card':
            case 'credit_card':
                referenceInput.placeholder = '{{ "آخر 4 أرقام من البطاقة" if language == "ar" else "Last 4 digits of card" }}';
                providerInput.placeholder = '{{ "البنك المصدر" if language == "ar" else "Issuing Bank" }}';
                break;
            case 'bank_transfer':
                referenceInput.placeholder = '{{ "رقم التحويل" if language == "ar" else "Transfer Reference" }}';
                providerInput.placeholder = '{{ "البنك" if language == "ar" else "Bank Name" }}';
                break;
            case 'mobile_payment':
                referenceInput.placeholder = '{{ "رقم المعاملة" if language == "ar" else "Transaction ID" }}';
                providerInput.placeholder = '{{ "مقدم الخدمة (مثل: فودافون كاش)" if language == "ar" else "Provider (e.g., Vodafone Cash)" }}';
                break;
            case 'digital_wallet':
                referenceInput.placeholder = '{{ "رقم المعاملة" if language == "ar" else "Transaction ID" }}';
                providerInput.placeholder = '{{ "المحفظة الرقمية (مثل: PayPal)" if language == "ar" else "Digital Wallet (e.g., PayPal)" }}';
                break;
            case 'check':
                referenceInput.placeholder = '{{ "رقم الشيك" if language == "ar" else "Check Number" }}';
                providerInput.placeholder = '{{ "البنك المسحوب عليه" if language == "ar" else "Drawn on Bank" }}';
                break;
            case 'gift_card':
                referenceInput.placeholder = '{{ "رقم بطاقة الهدية" if language == "ar" else "Gift Card Number" }}';
                providerInput.placeholder = '{{ "مصدر البطاقة" if language == "ar" else "Card Issuer" }}';
                break;
            case 'mixed':
                referenceInput.placeholder = '{{ "تفاصيل الدفع المختلط" if language == "ar" else "Mixed Payment Details" }}';
                providerInput.placeholder = '{{ "طرق الدفع المستخدمة" if language == "ar" else "Payment Methods Used" }}';
                break;
            case 'cod':
                referenceInput.placeholder = '{{ "عنوان التسليم" if language == "ar" else "Delivery Address" }}';
                providerInput.placeholder = '{{ "شركة التوصيل" if language == "ar" else "Delivery Company" }}';
                break;
            default:
                referenceInput.placeholder = '{{ "رقم المرجع" if language == "ar" else "Reference Number" }}';
                providerInput.placeholder = '{{ "مقدم الخدمة" if language == "ar" else "Service Provider" }}';
        }
    } else {
        paymentDetails.style.display = 'none';
        codFields.style.display = 'none';
        // Clear the fields when hiding
        referenceInput.value = '';
        providerInput.value = '';
        document.getElementById('payment_notes').value = '';
        document.getElementById('delivery_address').value = '';
        document.getElementById('delivery_phone').value = '';
        document.getElementById('delivery_notes').value = '';
    }
}

// Quick COD function
function quickCOD() {
    // Select COD payment method
    const codRadio = document.getElementById('cod');
    if (codRadio) {
        codRadio.checked = true;
        handlePaymentMethodChange('cod');

        // Focus on delivery address field
        setTimeout(() => {
            const addressField = document.getElementById('delivery_address');
            if (addressField) {
                addressField.focus();
            }
        }, 100);

        // Play sound
        if (window.playSound) {
            window.playSound('notification');
        }

        // Show notification
        pos.showAlert(pos.language === 'ar' ? 'تم اختيار الدفع عند الاستلام' : 'COD payment selected', 'info');
    }
}

// Initialize payment method handling
document.addEventListener('DOMContentLoaded', function() {
    // Set initial state
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (selectedMethod) {
        handlePaymentMethodChange(selectedMethod.value);
    }
});
</script>
{% endblock %}
