"""
Test cases for Qatar POS System views/routes
"""

import unittest
import json
from decimal import Decimal

from app import create_app, db
from app.models import User, Customer, Product, Category, Sale


class ViewTestCase(unittest.TestCase):
    """Base test case for view tests"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        db.create_all()
        
        # Create test user
        self.user = User(
            username='testuser',
            email='<EMAIL>',
            first_name_en='Test',
            last_name_en='User',
            role='manager'
        )
        self.user.set_password('password123')
        db.session.add(self.user)
        db.session.commit()
        
    def tearDown(self):
        """Clean up test environment"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        
    def login(self, username='testuser', password='password123'):
        """Helper method to login user"""
        return self.client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)
        
    def logout(self):
        """Helper method to logout user"""
        return self.client.get('/auth/logout', follow_redirects=True)


class AuthViewTest(ViewTestCase):
    """Test cases for authentication views"""
    
    def test_login_page(self):
        """Test login page loads correctly"""
        response = self.client.get('/auth/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'login', response.data.lower())
        
    def test_valid_login(self):
        """Test login with valid credentials"""
        response = self.login()
        self.assertEqual(response.status_code, 200)
        # Should redirect to dashboard after login
        
    def test_invalid_login(self):
        """Test login with invalid credentials"""
        response = self.login('testuser', 'wrongpassword')
        self.assertEqual(response.status_code, 200)
        # Should stay on login page with error
        
    def test_logout(self):
        """Test user logout"""
        self.login()
        response = self.logout()
        self.assertEqual(response.status_code, 200)
        
    def test_protected_route_without_login(self):
        """Test accessing protected route without login"""
        response = self.client.get('/dashboard/')
        self.assertEqual(response.status_code, 302)  # Redirect to login


class DashboardViewTest(ViewTestCase):
    """Test cases for dashboard views"""
    
    def test_dashboard_access_with_login(self):
        """Test dashboard access with valid login"""
        self.login()
        response = self.client.get('/dashboard/')
        self.assertEqual(response.status_code, 200)
        
    def test_dashboard_content(self):
        """Test dashboard contains expected content"""
        self.login()
        response = self.client.get('/dashboard/')
        self.assertEqual(response.status_code, 200)
        # Check for dashboard elements
        self.assertIn(b'dashboard', response.data.lower())


class ProductViewTest(ViewTestCase):
    """Test cases for product views"""
    
    def setUp(self):
        super().setUp()
        # Create test category
        self.category = Category(
            name_ar='إلكترونيات',
            name_en='Electronics'
        )
        db.session.add(self.category)
        db.session.commit()
        
    def test_products_list(self):
        """Test products list page"""
        self.login()
        response = self.client.get('/products/')
        self.assertEqual(response.status_code, 200)
        
    def test_product_create_get(self):
        """Test product create form display"""
        self.login()
        response = self.client.get('/products/create')
        self.assertEqual(response.status_code, 200)
        
    def test_product_create_post(self):
        """Test product creation via POST"""
        self.login()
        response = self.client.post('/products/create', data={
            'name_ar': 'هاتف ذكي',
            'name_en': 'Smartphone',
            'sku': 'PHONE001',
            'category_id': self.category.id,
            'cost_price': '500.00',
            'selling_price': '750.00',
            'current_stock': '10',
            'minimum_stock': '2'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Check product was created
        product = Product.query.filter_by(sku='PHONE001').first()
        self.assertIsNotNone(product)
        self.assertEqual(product.name_en, 'Smartphone')
        
    def test_product_edit(self):
        """Test product editing"""
        # Create test product
        product = Product(
            name_ar='منتج تجريبي',
            name_en='Test Product',
            sku='TEST001',
            category_id=self.category.id,
            cost_price=Decimal('100.00'),
            selling_price=Decimal('150.00'),
            current_stock=5
        )
        db.session.add(product)
        db.session.commit()
        
        self.login()
        
        # Test GET edit form
        response = self.client.get(f'/products/{product.id}/edit')
        self.assertEqual(response.status_code, 200)
        
        # Test POST edit
        response = self.client.post(f'/products/{product.id}/edit', data={
            'name_ar': 'منتج محدث',
            'name_en': 'Updated Product',
            'sku': 'TEST001',
            'category_id': self.category.id,
            'cost_price': '120.00',
            'selling_price': '180.00',
            'current_stock': '8',
            'minimum_stock': '2'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Check product was updated
        updated_product = Product.query.get(product.id)
        self.assertEqual(updated_product.name_en, 'Updated Product')
        self.assertEqual(updated_product.cost_price, Decimal('120.00'))


class CustomerViewTest(ViewTestCase):
    """Test cases for customer views"""
    
    def test_customers_list(self):
        """Test customers list page"""
        self.login()
        response = self.client.get('/customers/')
        self.assertEqual(response.status_code, 200)
        
    def test_customer_create_individual(self):
        """Test individual customer creation"""
        self.login()
        response = self.client.post('/customers/create', data={
            'customer_type': 'individual',
            'first_name_ar': 'أحمد',
            'first_name_en': 'Ahmed',
            'last_name_ar': 'محمد',
            'last_name_en': 'Mohammed',
            'phone': '+974-5555-1234',
            'email': '<EMAIL>'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Check customer was created
        customer = Customer.query.filter_by(phone='+974-5555-1234').first()
        self.assertIsNotNone(customer)
        self.assertEqual(customer.customer_type, 'individual')
        self.assertEqual(customer.first_name_en, 'Ahmed')
        
    def test_customer_create_company(self):
        """Test company customer creation"""
        self.login()
        response = self.client.post('/customers/create', data={
            'customer_type': 'company',
            'company_name_ar': 'شركة قطر',
            'company_name_en': 'Qatar Company',
            'contact_person_ar': 'محمد أحمد',
            'contact_person_en': 'Mohammed Ahmed',
            'phone': '+974-4444-5678',
            'email': '<EMAIL>',
            'commercial_registration': '12345678'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Check customer was created
        customer = Customer.query.filter_by(commercial_registration='12345678').first()
        self.assertIsNotNone(customer)
        self.assertEqual(customer.customer_type, 'company')
        self.assertEqual(customer.company_name_en, 'Qatar Company')


class SalesViewTest(ViewTestCase):
    """Test cases for sales views"""
    
    def setUp(self):
        super().setUp()
        # Create test data
        self.category = Category(name_en='Test Category')
        db.session.add(self.category)
        db.session.flush()
        
        self.product = Product(
            name_en='Test Product',
            sku='TEST001',
            category_id=self.category.id,
            cost_price=Decimal('50.00'),
            selling_price=Decimal('100.00'),
            current_stock=10
        )
        db.session.add(self.product)
        
        self.customer = Customer(
            customer_type='individual',
            first_name_en='John',
            last_name_en='Doe',
            phone='+974-5555-0000'
        )
        db.session.add(self.customer)
        db.session.commit()
        
    def test_sales_list(self):
        """Test sales list page"""
        self.login()
        response = self.client.get('/sales/')
        self.assertEqual(response.status_code, 200)
        
    def test_pos_interface(self):
        """Test POS interface loads"""
        self.login()
        response = self.client.get('/sales/pos')
        self.assertEqual(response.status_code, 200)
        
    def test_sale_creation_api(self):
        """Test sale creation via API"""
        self.login()
        
        sale_data = {
            'items': [
                {
                    'product_id': self.product.id,
                    'quantity': 2,
                    'unit_price': float(self.product.selling_price),
                    'total_price': float(self.product.selling_price * 2)
                }
            ],
            'customer_id': self.customer.id,
            'payment_method': 'cash',
            'discount_amount': 0,
            'payment_amount': float(self.product.selling_price * 2)
        }
        
        response = self.client.post('/sales/api/create',
                                  data=json.dumps(sale_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        # Check sale was created
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        sale = Sale.query.get(data['sale']['id'])
        self.assertIsNotNone(sale)
        self.assertEqual(sale.customer_id, self.customer.id)
        self.assertEqual(sale.items.count(), 1)


class ReportsViewTest(ViewTestCase):
    """Test cases for reports views"""
    
    def test_reports_index(self):
        """Test reports index page"""
        self.login()
        response = self.client.get('/reports/')
        self.assertEqual(response.status_code, 200)
        
    def test_sales_report(self):
        """Test sales report page"""
        self.login()
        response = self.client.get('/reports/sales')
        self.assertEqual(response.status_code, 200)


class APIViewTest(ViewTestCase):
    """Test cases for API endpoints"""
    
    def setUp(self):
        super().setUp()
        self.category = Category(name_en='Test Category')
        db.session.add(self.category)
        db.session.flush()
        
        self.product = Product(
            name_en='Test Product',
            sku='TEST001',
            category_id=self.category.id,
            selling_price=Decimal('100.00'),
            current_stock=10
        )
        db.session.add(self.product)
        db.session.commit()
        
    def test_products_search_api(self):
        """Test products search API"""
        self.login()
        response = self.client.get('/products/api/search?q=Test')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, list)
        
    def test_product_barcode_api(self):
        """Test product barcode lookup API"""
        self.login()
        response = self.client.get(f'/products/api/barcode/{self.product.sku}')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['id'], self.product.id)
        self.assertEqual(data['name'], self.product.name_en)


class PermissionTest(ViewTestCase):
    """Test cases for permission system"""
    
    def test_manager_access(self):
        """Test manager can access all areas"""
        self.login()
        
        # Test access to various areas
        areas = ['/dashboard/', '/products/', '/customers/', '/sales/', '/reports/', '/users/', '/settings/']
        
        for area in areas:
            response = self.client.get(area)
            self.assertIn(response.status_code, [200, 302])  # 200 OK or 302 redirect (but not 403 forbidden)
            
    def test_seller_limited_access(self):
        """Test seller has limited access"""
        # Create seller user
        seller = User(
            username='seller',
            email='<EMAIL>',
            first_name_en='Seller',
            last_name_en='User',
            role='seller'
        )
        seller.set_password('password123')
        db.session.add(seller)
        db.session.commit()
        
        self.login('seller', 'password123')
        
        # Should have access to POS and basic areas
        response = self.client.get('/sales/pos')
        self.assertEqual(response.status_code, 200)
        
        # Should not have access to user management
        response = self.client.get('/users/')
        self.assertIn(response.status_code, [302, 403])  # Redirect or forbidden


if __name__ == '__main__':
    unittest.main()
