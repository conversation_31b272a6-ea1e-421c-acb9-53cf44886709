#!/usr/bin/env python3
"""
Setup script for notification settings
Qatar POS System
"""

import sys
import os
sys.path.append('.')

from app import create_app
from extensions import db
from models.setting import Setting

def setup_notification_settings():
    """Setup default notification settings"""
    
    print("🔔 Setting up notification settings...")
    
    app = create_app()
    
    with app.app_context():
        # Notification settings to create
        notification_settings = [
            {
                'key': 'notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'تفعيل الإشعارات',
                'description_en': 'Enable notifications'
            },
            {
                'key': 'notification_duration',
                'value': '5',
                'value_type': 'integer',
                'category': 'notifications',
                'description_ar': 'مدة عرض الإشعار بالثواني',
                'description_en': 'Notification display duration in seconds'
            },
            {
                'key': 'notification_position',
                'value': 'top-right',
                'value_type': 'string',
                'category': 'notifications',
                'description_ar': 'موضع الإشعارات',
                'description_en': 'Notification position'
            },
            {
                'key': 'sale_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات البيع',
                'description_en': 'Sale notifications'
            },
            {
                'key': 'payment_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات الدفع',
                'description_en': 'Payment notifications'
            },
            {
                'key': 'low_stock_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات نفاد المخزون',
                'description_en': 'Low stock notifications'
            },
            {
                'key': 'error_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات الأخطاء',
                'description_en': 'Error notifications'
            },
            {
                'key': 'system_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات النظام',
                'description_en': 'System notifications'
            },
            {
                'key': 'backup_notifications_enabled',
                'value': 'true',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات النسخ الاحتياطي',
                'description_en': 'Backup notifications'
            },
            {
                'key': 'low_stock_threshold',
                'value': '10',
                'value_type': 'integer',
                'category': 'notifications',
                'description_ar': 'حد المخزون المنخفض',
                'description_en': 'Low stock threshold'
            },
            {
                'key': 'desktop_notifications_enabled',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات سطح المكتب',
                'description_en': 'Desktop notifications'
            },
            {
                'key': 'email_notifications_enabled',
                'value': 'false',
                'value_type': 'boolean',
                'category': 'notifications',
                'description_ar': 'إشعارات البريد الإلكتروني',
                'description_en': 'Email notifications'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for setting_data in notification_settings:
            # Check if setting already exists
            existing = Setting.query.filter_by(key=setting_data['key']).first()
            
            if existing:
                # Update existing setting
                existing.value = setting_data['value']
                existing.value_type = setting_data['value_type']
                existing.category = setting_data['category']
                existing.description_ar = setting_data['description_ar']
                existing.description_en = setting_data['description_en']
                updated_count += 1
                print(f"✅ Updated: {setting_data['key']}")
            else:
                # Create new setting
                new_setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    value_type=setting_data['value_type'],
                    category=setting_data['category'],
                    description_ar=setting_data['description_ar'],
                    description_en=setting_data['description_en']
                )
                db.session.add(new_setting)
                created_count += 1
                print(f"🆕 Created: {setting_data['key']}")
        
        # Commit all changes
        try:
            db.session.commit()
            print(f"\n✅ Successfully setup notification settings!")
            print(f"📊 Created: {created_count} settings")
            print(f"🔄 Updated: {updated_count} settings")
            
            # Display current notification settings
            print(f"\n📋 Current notification settings:")
            notification_settings = Setting.query.filter_by(category='notifications').all()
            for setting in notification_settings:
                print(f"   • {setting.key}: {setting.value}")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error setting up notification settings: {str(e)}")
            return False
            
        return True

def test_notification_system():
    """Test the notification system"""
    
    print("\n🧪 Testing notification system...")
    
    app = create_app()
    
    with app.app_context():
        # Test getting notification settings
        settings = Setting.get_category_settings('notifications')
        
        print(f"📊 Found {len(settings)} notification settings:")
        for setting in settings:
            print(f"   • {setting.key}: {setting.get_value()} ({setting.value_type})")
        
        # Test specific settings
        test_keys = [
            'notifications_enabled',
            'notification_duration',
            'notification_position',
            'low_stock_threshold'
        ]
        
        print(f"\n🔍 Testing specific settings:")
        for key in test_keys:
            value = Setting.get_setting(key)
            print(f"   • {key}: {value}")
        
        print(f"\n✅ Notification system test completed!")

if __name__ == "__main__":
    print("🚀 Qatar POS Notification Setup")
    print("=" * 40)
    
    # Setup notification settings
    if setup_notification_settings():
        # Test the system
        test_notification_system()
        
        print(f"\n🎉 Setup completed successfully!")
        print(f"💡 You can now access notification settings at:")
        print(f"   http://localhost:2626/settings#notifications")
    else:
        print(f"\n❌ Setup failed!")
        sys.exit(1)
