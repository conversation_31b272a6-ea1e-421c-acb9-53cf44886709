{% extends "base.html" %}

{% block title %}
{{ 'تقرير المنتجات - نظام نقاط البيع القطري' if language == 'ar' else 'Products Report - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-box-seam"></i>
                {{ 'تقرير المنتجات' if language == 'ar' else 'Products Report' }}
            </h1>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة' if language == 'ar' else 'Print' }}
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="bi bi-file-earmark-excel"></i>
                    {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'فلاتر التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('reports.products_report') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'الفئة' if language == 'ar' else 'Category' }}</label>
                            <select class="form-select" name="category_id">
                                <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {{ 'selected' if request.args.get('category_id') == category.id|string }}>
                                    {{ category.name_ar if language == 'ar' else category.name_en }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'حالة المخزون' if language == 'ar' else 'Stock Status' }}</label>
                            <select class="form-select" name="stock_status">
                                <option value="">{{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}</option>
                                <option value="in_stock" {{ 'selected' if request.args.get('stock_status') == 'in_stock' }}>
                                    {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                                </option>
                                <option value="low_stock" {{ 'selected' if request.args.get('stock_status') == 'low_stock' }}>
                                    {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                                </option>
                                <option value="out_of_stock" {{ 'selected' if request.args.get('stock_status') == 'out_of_stock' }}>
                                    {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'ترتيب حسب' if language == 'ar' else 'Sort By' }}</label>
                            <select class="form-select" name="sort_by">
                                <option value="name" {{ 'selected' if request.args.get('sort_by') == 'name' }}>
                                    {{ 'الاسم' if language == 'ar' else 'Name' }}
                                </option>
                                <option value="stock" {{ 'selected' if request.args.get('sort_by') == 'stock' }}>
                                    {{ 'المخزون' if language == 'ar' else 'Stock' }}
                                </option>
                                <option value="price" {{ 'selected' if request.args.get('sort_by') == 'price' }}>
                                    {{ 'السعر' if language == 'ar' else 'Price' }}
                                </option>
                                <option value="sales" {{ 'selected' if request.args.get('sort_by') == 'sales' }}>
                                    {{ 'المبيعات' if language == 'ar' else 'Sales' }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                    {{ 'تطبيق الفلاتر' if language == 'ar' else 'Apply Filters' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                        <h3 class="mb-0">{{ summary.total_products }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box-seam fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</h6>
                        <h3 class="mb-0">{{ "%.2f"|format(summary.total_stock_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-dollar fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</h6>
                        <h3 class="mb-0">{{ summary.low_stock_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</h6>
                        <h3 class="mb-0">{{ summary.out_of_stock_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-x-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'قائمة المنتجات' if language == 'ar' else 'Products List' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="productsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                                <th>{{ 'السعر' if language == 'ar' else 'Price' }}</th>
                                <th>{{ 'المخزون' if language == 'ar' else 'Stock' }}</th>
                                <th>{{ 'الحد الأدنى' if language == 'ar' else 'Min Stock' }}</th>
                                <th>{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</th>
                                <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <code>{{ product.barcode }}</code>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if product.image %}
                                        <img src="{{ url_for('static', filename='uploads/products/' + product.image) }}" 
                                             alt="{{ product.name_ar if language == 'ar' else product.name_en }}" 
                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% endif %}
                                        <div>
                                            <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                            {% if product.description_ar or product.description_en %}
                                            <br><small class="text-muted">{{ product.description_ar if language == 'ar' else product.description_en }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if product.category %}
                                    <span class="badge bg-secondary">
                                        {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ "%.2f"|format(product.price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                                <td>
                                    <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                        {{ product.current_stock }}
                                    </span>
                                </td>
                                <td>{{ product.minimum_stock or 0 }}</td>
                                <td>
                                    <strong>{{ "%.2f"|format(product.selling_price * product.current_stock) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                                <td>
                                    {% if product.current_stock <= 0 %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle"></i>
                                        {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                                    </span>
                                    {% elif product.current_stock <= product.minimum_stock %}
                                    <span class="badge bg-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle"></i>
                                        {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                                    </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if products.pages > 1 %}
                <nav aria-label="Products pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=products.prev_num, **request.args) }}">
                                {{ 'السابق' if language == 'ar' else 'Previous' }}
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=products.next_num, **request.args) }}">
                                {{ 'التالي' if language == 'ar' else 'Next' }}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Export table to Excel
    const table = document.getElementById('productsTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Products Report"});
    XLSX.writeFile(wb, 'products_report.xlsx');
}

// Initialize DataTable for better functionality
$(document).ready(function() {
    $('#productsTable').DataTable({
        "paging": false,
        "searching": false,
        "info": false,
        "ordering": true,
        "language": {
            "url": "{{ 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json' if language == 'ar' else '' }}"
        }
    });
});
</script>
{% endblock %}
