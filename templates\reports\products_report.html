{% extends "base.html" %}

{% block title %}
{{ 'تقرير المنتجات - نظام نقاط البيع القطري' if language == 'ar' else 'Products Report - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-box-seam"></i>
                {{ 'تقرير المنتجات' if language == 'ar' else 'Products Report' }}
            </h1>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة' if language == 'ar' else 'Print' }}
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="bi bi-file-earmark-excel"></i>
                    {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'فلاتر التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('reports.products_report') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'الفئة' if language == 'ar' else 'Category' }}</label>
                            <select class="form-select" name="category_id">
                                <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {{ 'selected' if request.args.get('category_id') == category.id|string }}>
                                    {{ category.name_ar if language == 'ar' else category.name_en }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'حالة المخزون' if language == 'ar' else 'Stock Status' }}</label>
                            <select class="form-select" name="stock_status">
                                <option value="">{{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}</option>
                                <option value="in_stock" {{ 'selected' if request.args.get('stock_status') == 'in_stock' }}>
                                    {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                                </option>
                                <option value="low_stock" {{ 'selected' if request.args.get('stock_status') == 'low_stock' }}>
                                    {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                                </option>
                                <option value="out_of_stock" {{ 'selected' if request.args.get('stock_status') == 'out_of_stock' }}>
                                    {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">{{ 'ترتيب حسب' if language == 'ar' else 'Sort By' }}</label>
                            <select class="form-select" name="sort_by">
                                <option value="name" {{ 'selected' if request.args.get('sort_by') == 'name' }}>
                                    {{ 'الاسم' if language == 'ar' else 'Name' }}
                                </option>
                                <option value="stock" {{ 'selected' if request.args.get('sort_by') == 'stock' }}>
                                    {{ 'المخزون' if language == 'ar' else 'Stock' }}
                                </option>
                                <option value="price" {{ 'selected' if request.args.get('sort_by') == 'price' }}>
                                    {{ 'السعر' if language == 'ar' else 'Price' }}
                                </option>
                                <option value="sales" {{ 'selected' if request.args.get('sort_by') == 'sales' }}>
                                    {{ 'المبيعات' if language == 'ar' else 'Sales' }}
                                </option>
                                <option value="profit" {{ 'selected' if request.args.get('sort_by') == 'profit' }}>
                                    {{ 'الربح' if language == 'ar' else 'Profit' }}
                                </option>
                                <option value="profit_margin" {{ 'selected' if request.args.get('sort_by') == 'profit_margin' }}>
                                    {{ 'هامش الربح' if language == 'ar' else 'Profit Margin' }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                    {{ 'تطبيق الفلاتر' if language == 'ar' else 'Apply Filters' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                        <h3 class="mb-0">{{ summary.total_products }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box-seam fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'قيمة البيع' if language == 'ar' else 'Selling Value' }}</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(summary.total_stock_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-currency-dollar fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'قيمة التكلفة' if language == 'ar' else 'Cost Value' }}</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(summary.total_cost_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cash-stack fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'الربح المحتمل' if language == 'ar' else 'Potential Profit' }}</h6>
                        <h3 class="mb-0">{{ "%.0f"|format(summary.total_potential_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up-arrow fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'هامش الربح' if language == 'ar' else 'Profit Margin' }}</h6>
                        <h3 class="mb-0">{{ "%.1f"|format(summary.profit_margin_percentage) }}%</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-percent fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</h6>
                        <h3 class="mb-0">{{ summary.out_of_stock_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-x-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Profit Analysis -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {{ 'تحليل الأرباح الفعلية' if language == 'ar' else 'Actual Profit Analysis' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'الإيرادات الفعلية' if language == 'ar' else 'Actual Revenue' }}</h6>
                                <h4 class="text-success">{{ "%.0f"|format(summary.actual_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'الأرباح الفعلية' if language == 'ar' else 'Actual Profit' }}</h6>
                                <h4 class="text-primary">{{ "%.0f"|format(summary.actual_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'التكلفة الفعلية' if language == 'ar' else 'Actual Cost' }}</h6>
                                <h4 class="text-info">{{ "%.0f"|format(summary.actual_cost) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'هامش الربح الفعلي' if language == 'ar' else 'Actual Profit Margin' }}</h6>
                                <h4 class="text-warning">{{ "%.1f"|format(summary.actual_profit_margin) }}%</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    {{ 'توزيع المنتجات حسب الربحية' if language == 'ar' else 'Products Distribution by Profitability' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'ربح عالي (>30%)' if language == 'ar' else 'High Profit (>30%)' }}</h6>
                                <h4>{{ summary.high_profit_products }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'ربح متوسط (15-30%)' if language == 'ar' else 'Medium Profit (15-30%)' }}</h6>
                                <h4>{{ summary.medium_profit_products }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'ربح منخفض (<15%)' if language == 'ar' else 'Low Profit (<15%)' }}</h6>
                                <h4>{{ summary.low_profit_products }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ 'خسارة (≤0%)' if language == 'ar' else 'Loss (≤0%)' }}</h6>
                                <h4>{{ summary.loss_products }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profit by Category -->
{% if profit_by_category %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {{ 'تحليل الأرباح حسب الأقسام' if language == 'ar' else 'Profit Analysis by Categories' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                <th>{{ 'عدد المنتجات' if language == 'ar' else 'Products Count' }}</th>
                                <th>{{ 'قيمة التكلفة' if language == 'ar' else 'Cost Value' }}</th>
                                <th>{{ 'قيمة البيع' if language == 'ar' else 'Selling Value' }}</th>
                                <th>{{ 'الربح المحتمل' if language == 'ar' else 'Potential Profit' }}</th>
                                <th>{{ 'هامش الربح' if language == 'ar' else 'Profit Margin' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category_id, data in profit_by_category.items() %}
                            <tr>
                                <td>
                                    <strong>{{ data.category.name_ar if language == 'ar' else data.category.name_en }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ data.products_count }}</span>
                                </td>
                                <td>{{ "%.0f"|format(data.cost_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                <td>{{ "%.0f"|format(data.selling_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                <td>
                                    <span class="{% if data.potential_profit > 0 %}text-success{% elif data.potential_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        <strong>{{ "%.0f"|format(data.potential_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {% if data.profit_margin >= 30 %}bg-success{% elif data.profit_margin >= 15 %}bg-warning{% elif data.profit_margin > 0 %}bg-info{% else %}bg-danger{% endif %}">
                                        {{ "%.1f"|format(data.profit_margin) }}%
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Products Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'قائمة المنتجات' if language == 'ar' else 'Products List' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="productsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                                <th>{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</th>
                                <th>{{ 'سعر البيع' if language == 'ar' else 'Selling Price' }}</th>
                                <th>{{ 'ربح الوحدة' if language == 'ar' else 'Unit Profit' }}</th>
                                <th>{{ 'هامش الربح' if language == 'ar' else 'Profit Margin' }}</th>
                                <th>{{ 'المخزون' if language == 'ar' else 'Stock' }}</th>
                                <th>{{ 'إجمالي الربح' if language == 'ar' else 'Total Profit' }}</th>
                                <th>{{ 'المبيعات الفعلية' if language == 'ar' else 'Actual Sales' }}</th>
                                <th>{{ 'الربح الفعلي' if language == 'ar' else 'Actual Profit' }}</th>
                                <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <code>{{ product.barcode }}</code>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if product.image %}
                                        <img src="{{ url_for('static', filename='uploads/products/' + product.image) }}" 
                                             alt="{{ product.name_ar if language == 'ar' else product.name_en }}" 
                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% endif %}
                                        <div>
                                            <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                            {% if product.description_ar or product.description_en %}
                                            <br><small class="text-muted">{{ product.description_ar if language == 'ar' else product.description_en }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if product.category %}
                                    <span class="badge bg-secondary">
                                        {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</span>
                                </td>
                                <td>
                                    <strong>{{ "%.2f"|format(product.selling_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                                <td>
                                    <span class="{% if product.unit_profit > 0 %}text-success{% elif product.unit_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        <strong>{{ "%.2f"|format(product.unit_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {% if product.profit_margin >= 30 %}bg-success{% elif product.profit_margin >= 15 %}bg-warning{% elif product.profit_margin > 0 %}bg-info{% else %}bg-danger{% endif %}">
                                        {{ "%.1f"|format(product.profit_margin) }}%
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                        {{ product.current_stock }}
                                    </span>
                                </td>
                                <td>
                                    <span class="{% if product.total_profit > 0 %}text-success{% elif product.total_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                        <strong>{{ "%.2f"|format(product.total_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </span>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold">{{ product.actual_sold or 0 }}</div>
                                        <small class="text-muted">{{ "%.0f"|format(product.actual_revenue or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold {% if product.actual_profit > 0 %}text-success{% elif product.actual_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                            {{ "%.0f"|format(product.actual_profit or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                                        </div>
                                        {% if product.actual_profit_margin %}
                                        <small class="badge {% if product.actual_profit_margin >= 30 %}bg-success{% elif product.actual_profit_margin >= 15 %}bg-warning{% elif product.actual_profit_margin > 0 %}bg-info{% else %}bg-danger{% endif %}">
                                            {{ "%.1f"|format(product.actual_profit_margin) }}%
                                        </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if product.current_stock <= 0 %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle"></i>
                                        {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                                    </span>
                                    {% elif product.current_stock <= product.minimum_stock %}
                                    <span class="badge bg-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle"></i>
                                        {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                                    </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if products.pages > 1 %}
                <nav aria-label="Products pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=products.prev_num, **request.args) }}">
                                {{ 'السابق' if language == 'ar' else 'Previous' }}
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports.products_report', page=products.next_num, **request.args) }}">
                                {{ 'التالي' if language == 'ar' else 'Next' }}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Export table to Excel
    const table = document.getElementById('productsTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Products Report"});
    XLSX.writeFile(wb, 'products_report.xlsx');
}

// Initialize DataTable for better functionality
$(document).ready(function() {
    $('#productsTable').DataTable({
        "paging": false,
        "searching": false,
        "info": false,
        "ordering": true,
        "language": {
            "url": "{{ 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json' if language == 'ar' else '' }}"
        }
    });
});
</script>
{% endblock %}
