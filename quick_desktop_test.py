"""
اختبار سريع لتطبيق سطح المكتب
Quick Desktop Application Test
"""

import sys
import os

def main():
    print("=" * 60)
    print("    Qatar POS System - Desktop Test")
    print("    نظام نقاط البيع القطري - اختبار سطح المكتب")
    print("=" * 60)
    print()
    
    # اختبار tkinter
    print("🔍 اختبار tkinter...")
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        print("يرجى تثبيت Python مع tkinter")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار Flask
    print("\n🔍 اختبار Flask...")
    try:
        from flask import Flask
        print("✅ Flask متوفر")
    except ImportError:
        print("❌ Flask غير متوفر")
        print("تشغيل: pip install flask")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار ملفات التطبيق
    print("\n📁 اختبار ملفات التطبيق...")
    required_files = ['app.py', 'config.py', 'extensions.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
    
    # اختبار ملفات سطح المكتب
    print("\n🖥️ اختبار ملفات سطح المكتب...")
    desktop_files = [
        'desktop_app.py',
        'simple_desktop.py', 
        'build_exe.py',
        'requirements_desktop.txt'
    ]
    
    for file in desktop_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
    
    print("\n🎉 اختبار الملفات مكتمل!")
    print("\n📋 الملفات المتوفرة لتحويل التطبيق:")
    print("   1. desktop_app.py - التطبيق الرئيسي")
    print("   2. simple_desktop.py - الإصدار المبسط")
    print("   3. build_exe.py - سكريپت البناء")
    print("   4. requirements_desktop.txt - المتطلبات")
    
    print("\n🚀 لتشغيل التطبيق:")
    print("   python simple_desktop.py")
    print("   أو")
    print("   python desktop_app.py")
    
    print("\n🔨 لبناء ملف EXE:")
    print("   python build_exe.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
