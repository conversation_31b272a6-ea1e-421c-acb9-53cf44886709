"""
Image processing utilities for Qatar POS System
Handles image upload, resize, optimization, and validation
"""

import os
import uuid
from PIL import Image, ImageOps
from werkzeug.utils import secure_filename
import io

class ImageProcessor:
    """Handle image processing operations"""
    
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    MAX_FILE_SIZE = 2 * 1024 * 1024  # 2MB
    
    def __init__(self, upload_folder='static/uploads'):
        self.upload_folder = upload_folder
        
    def is_allowed_file(self, filename):
        """Check if file extension is allowed"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS
    
    def validate_image(self, file):
        """Validate uploaded image file"""
        if not file or not file.filename:
            return False, "لم يتم اختيار ملف"
        
        if not self.is_allowed_file(file.filename):
            return False, "نوع الملف غير مدعوم. الأنواع المدعومة: PNG, JPG, JPEG, GIF, WEBP"
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > self.MAX_FILE_SIZE:
            return False, f"حجم الملف كبير جداً. الحد الأقصى {self.MAX_FILE_SIZE // (1024*1024)}MB"
        
        # Try to open as image
        try:
            img = Image.open(file)
            img.verify()
            file.seek(0)  # Reset file pointer
            return True, "صالح"
        except Exception:
            return False, "الملف ليس صورة صالحة"
    
    def process_logo(self, file, max_width=300, max_height=200, quality=85):
        """Process logo image with optimization"""
        try:
            # Validate file first
            is_valid, message = self.validate_image(file)
            if not is_valid:
                return None, message
            
            # Open and process image
            img = Image.open(file)
            
            # Convert to RGB if necessary (for JPEG output)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Auto-orient based on EXIF data
            img = ImageOps.exif_transpose(img)
            
            # Resize while maintaining aspect ratio
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Generate unique filename
            file_ext = '.jpg'  # Always save as JPEG for consistency
            filename = f"logo_{uuid.uuid4().hex[:8]}{file_ext}"
            
            # Create directory if it doesn't exist
            logo_dir = os.path.join(self.upload_folder, 'logos')
            os.makedirs(logo_dir, exist_ok=True)
            
            # Save optimized image
            file_path = os.path.join(logo_dir, filename)
            img.save(file_path, 'JPEG', quality=quality, optimize=True)
            
            return filename, "تم رفع الشعار بنجاح"
            
        except Exception as e:
            return None, f"خطأ في معالجة الصورة: {str(e)}"
    
    def process_product_image(self, file, max_width=800, max_height=600, quality=80):
        """Process product image with optimization"""
        try:
            # Validate file first
            is_valid, message = self.validate_image(file)
            if not is_valid:
                return None, message
            
            # Open and process image
            img = Image.open(file)
            
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Auto-orient based on EXIF data
            img = ImageOps.exif_transpose(img)
            
            # Resize while maintaining aspect ratio
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Generate unique filename
            file_ext = '.jpg'
            filename = f"product_{uuid.uuid4().hex[:8]}{file_ext}"
            
            # Create directory if it doesn't exist
            product_dir = os.path.join(self.upload_folder, 'products')
            os.makedirs(product_dir, exist_ok=True)
            
            # Save optimized image
            file_path = os.path.join(product_dir, filename)
            img.save(file_path, 'JPEG', quality=quality, optimize=True)
            
            return filename, "تم رفع صورة المنتج بنجاح"
            
        except Exception as e:
            return None, f"خطأ في معالجة الصورة: {str(e)}"

    def process_stamp(self, file, max_width=200, max_height=200, quality=90):
        """Process company stamp image with optimization"""
        try:
            # Validate file first
            is_valid, message = self.validate_image(file)
            if not is_valid:
                return None, message

            # Open and process image
            img = Image.open(file)

            # For stamps, preserve transparency if it exists
            if img.mode in ('RGBA', 'LA'):
                # Keep transparency for PNG output
                pass
            elif img.mode == 'P':
                img = img.convert('RGBA')
            elif img.mode != 'RGBA':
                # Convert to RGBA to support transparency
                img = img.convert('RGBA')

            # Auto-orient based on EXIF data
            img = ImageOps.exif_transpose(img)

            # Resize while maintaining aspect ratio
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

            # Generate unique filename
            file_ext = '.png'  # Save as PNG to preserve transparency
            filename = f"stamp_{uuid.uuid4().hex[:8]}{file_ext}"

            # Create directory if it doesn't exist
            stamp_dir = os.path.join(self.upload_folder, 'stamps')
            os.makedirs(stamp_dir, exist_ok=True)

            # Save optimized image
            file_path = os.path.join(stamp_dir, filename)
            img.save(file_path, 'PNG', optimize=True)

            return filename, "تم رفع الختم بنجاح"

        except Exception as e:
            return None, f"خطأ في معالجة الصورة: {str(e)}"

    def process_signature(self, file, max_width=300, max_height=150, quality=90):
        """Process manager signature image with optimization"""
        try:
            # Validate file first
            is_valid, message = self.validate_image(file)
            if not is_valid:
                return None, message

            # Open and process image
            img = Image.open(file)

            # For signatures, preserve transparency if it exists
            if img.mode in ('RGBA', 'LA'):
                # Keep transparency for PNG output
                pass
            elif img.mode == 'P':
                img = img.convert('RGBA')
            elif img.mode != 'RGBA':
                # Convert to RGBA to support transparency
                img = img.convert('RGBA')

            # Auto-orient based on EXIF data
            img = ImageOps.exif_transpose(img)

            # Resize while maintaining aspect ratio
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

            # Generate unique filename
            file_ext = '.png'  # Save as PNG to preserve transparency
            filename = f"signature_{uuid.uuid4().hex[:8]}{file_ext}"

            # Create directory if it doesn't exist
            signature_dir = os.path.join(self.upload_folder, 'signatures')
            os.makedirs(signature_dir, exist_ok=True)

            # Save optimized image
            file_path = os.path.join(signature_dir, filename)
            img.save(file_path, 'PNG', optimize=True)

            return filename, "تم رفع التوقيع بنجاح"

        except Exception as e:
            return None, f"خطأ في معالجة الصورة: {str(e)}"

    def delete_image(self, filename, subfolder='logos'):
        """Delete image file"""
        try:
            file_path = os.path.join(self.upload_folder, subfolder, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True, "تم حذف الصورة بنجاح"
            return False, "الملف غير موجود"
        except Exception as e:
            return False, f"خطأ في حذف الصورة: {str(e)}"
    
    def get_image_info(self, filename, subfolder='logos'):
        """Get image information"""
        try:
            file_path = os.path.join(self.upload_folder, subfolder, filename)
            if not os.path.exists(file_path):
                return None
            
            with Image.open(file_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'size': os.path.getsize(file_path)
                }
        except Exception:
            return None
    
    def create_thumbnail(self, filename, subfolder='logos', thumb_size=(100, 100)):
        """Create thumbnail for image"""
        try:
            file_path = os.path.join(self.upload_folder, subfolder, filename)
            if not os.path.exists(file_path):
                return None, "الملف غير موجود"
            
            # Create thumbnail directory
            thumb_dir = os.path.join(self.upload_folder, subfolder, 'thumbnails')
            os.makedirs(thumb_dir, exist_ok=True)
            
            # Generate thumbnail filename
            name, ext = os.path.splitext(filename)
            thumb_filename = f"{name}_thumb{ext}"
            thumb_path = os.path.join(thumb_dir, thumb_filename)
            
            # Create thumbnail
            with Image.open(file_path) as img:
                img.thumbnail(thumb_size, Image.Resampling.LANCZOS)
                img.save(thumb_path, quality=70, optimize=True)
            
            return thumb_filename, "تم إنشاء الصورة المصغرة بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء الصورة المصغرة: {str(e)}"

# Global instance
image_processor = ImageProcessor()

def process_logo_upload(file, max_width=300, max_height=200):
    """Convenience function for logo processing"""
    return image_processor.process_logo(file, max_width, max_height)

def process_product_image_upload(file, max_width=800, max_height=600):
    """Convenience function for product image processing"""
    return image_processor.process_product_image(file, max_width, max_height)

def process_stamp_upload(file, max_width=200, max_height=200):
    """Convenience function for stamp processing"""
    return image_processor.process_stamp(file, max_width, max_height)

def process_signature_upload(file, max_width=300, max_height=150):
    """Convenience function for signature processing"""
    return image_processor.process_signature(file, max_width, max_height)

def delete_uploaded_image(filename, subfolder='logos'):
    """Convenience function for image deletion"""
    return image_processor.delete_image(filename, subfolder)
