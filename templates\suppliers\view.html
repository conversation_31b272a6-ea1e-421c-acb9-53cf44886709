{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        عرض المورد: {{ supplier.get_company_name(language) }}
    {% else %}
        View Supplier: {{ supplier.get_company_name(language) }}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            معلومات المورد
                        {% else %}
                            Supplier Information
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i>
                            {% if language == 'ar' %}تعديل{% else %}Edit{% endif %}
                        </a>
                        <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Company Information -->
                    <h5 class="mb-3">
                        {% if language == 'ar' %}معلومات الشركة{% else %}Company Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}كود المورد:{% else %}Supplier Code:{% endif %}</strong>
                            <p>{{ supplier.supplier_code }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}الحالة:{% else %}Status:{% endif %}</strong>
                            <p>
                                {% if supplier.is_active %}
                                    <span class="badge badge-success">
                                        {% if language == 'ar' %}نشط{% else %}Active{% endif %}
                                    </span>
                                {% else %}
                                    <span class="badge badge-danger">
                                        {% if language == 'ar' %}غير نشط{% else %}Inactive{% endif %}
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}اسم الشركة (عربي):{% else %}Company Name (Arabic):{% endif %}</strong>
                            <p>{{ supplier.company_name_ar or '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}اسم الشركة (إنجليزي):{% else %}Company Name (English):{% endif %}</strong>
                            <p>{{ supplier.company_name_en or '-' }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}الشخص المسؤول (عربي):{% else %}Contact Person (Arabic):{% endif %}</strong>
                            <p>{{ supplier.contact_person_ar or '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}الشخص المسؤول (إنجليزي):{% else %}Contact Person (English):{% endif %}</strong>
                            <p>{{ supplier.contact_person_en or '-' }}</p>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات الاتصال{% else %}Contact Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}الهاتف:{% else %}Phone:{% endif %}</strong>
                            <p>{{ supplier.phone or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}البريد الإلكتروني:{% else %}Email:{% endif %}</strong>
                            <p>{{ supplier.email or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}الموقع الإلكتروني:{% else %}Website:{% endif %}</strong>
                            <p>
                                {% if supplier.website %}
                                    <a href="{{ supplier.website }}" target="_blank">{{ supplier.website }}</a>
                                {% else %}
                                    -
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات العنوان{% else %}Address Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}العنوان (عربي):{% else %}Address (Arabic):{% endif %}</strong>
                            <p>{{ supplier.address_ar or '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}العنوان (إنجليزي):{% else %}Address (English):{% endif %}</strong>
                            <p>{{ supplier.address_en or '-' }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}المدينة (عربي):{% else %}City (Arabic):{% endif %}</strong>
                            <p>{{ supplier.city_ar or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}المدينة (إنجليزي):{% else %}City (English):{% endif %}</strong>
                            <p>{{ supplier.city_en or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}الرمز البريدي:{% else %}Postal Code:{% endif %}</strong>
                            <p>{{ supplier.postal_code or '-' }}</p>
                        </div>
                    </div>

                    <!-- Business Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}المعلومات التجارية{% else %}Business Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}السجل التجاري:{% else %}Commercial Registration:{% endif %}</strong>
                            <p>{{ supplier.commercial_registration or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}الرقم الضريبي:{% else %}Tax Number:{% endif %}</strong>
                            <p>{{ supplier.tax_number or '-' }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>{% if language == 'ar' %}شروط الدفع:{% else %}Payment Terms:{% endif %}</strong>
                            <p>{{ supplier.payment_terms or '-' }}</p>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات النظام{% else %}System Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}تاريخ الإنشاء:{% else %}Created Date:{% endif %}</strong>
                            <p>{{ supplier.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{% if language == 'ar' %}آخر تحديث:{% else %}Last Updated:{% endif %}</strong>
                            <p>{{ supplier.updated_at.strftime('%Y-%m-%d %H:%M') if supplier.updated_at else '-' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Recent Purchase Orders -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            أوامر الشراء الأخيرة
                        {% else %}
                            Recent Purchase Orders
                        {% endif %}
                    </h3>
                </div>
                
                <div class="card-body">
                    {% if recent_orders %}
                        <div class="list-group">
                            {% for order in recent_orders %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ order.order_number }}</h6>
                                    <small>{{ order.created_at.strftime('%Y-%m-%d') }}</small>
                                </div>
                                <p class="mb-1">
                                    {% if language == 'ar' %}
                                        المبلغ: {{ "%.2f"|format(order.total_amount) }} ريال
                                    {% else %}
                                        Amount: QAR {{ "%.2f"|format(order.total_amount) }}
                                    {% endif %}
                                </p>
                                <small>
                                    <span class="badge badge-{{ 'success' if order.status == 'completed' else 'warning' }}">
                                        {{ order.status }}
                                    </span>
                                </small>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted text-center">
                            {% if language == 'ar' %}
                                لا توجد أوامر شراء بعد
                            {% else %}
                                No purchase orders yet
                            {% endif %}
                        </p>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            إجراءات سريعة
                        {% else %}
                            Quick Actions
                        {% endif %}
                    </h3>
                </div>
                
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            {% if language == 'ar' %}إنشاء أمر شراء{% else %}Create Purchase Order{% endif %}
                        </a>
                        <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i>
                            {% if language == 'ar' %}تعديل المورد{% else %}Edit Supplier{% endif %}
                        </a>
                        <a href="#" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i>
                            {% if language == 'ar' %}تقرير المورد{% else %}Supplier Report{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
