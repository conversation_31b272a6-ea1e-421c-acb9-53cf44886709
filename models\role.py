"""
Role and Permission models for Qatar POS System
Handles user roles and permissions management
"""

from extensions import db
from datetime import datetime

# Association table for many-to-many relationship between roles and permissions
role_permissions = db.Table('role_permissions',
    db.<PERSON>umn('role_id', db.<PERSON><PERSON><PERSON>, db.<PERSON>('roles.id'), primary_key=True),
    db.<PERSON>umn('permission_id', db.Integer, db.<PERSON>('permissions.id'), primary_key=True)
)

class Permission(db.Model):
    """Permission model for system access control"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False, index=True)
    description_ar = db.Column(db.String(200))
    description_en = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Permission {self.name}>'
    
    def get_description(self, language='ar'):
        """Get description in specified language"""
        if language == 'en':
            return self.description_en or self.description_ar
        return self.description_ar or self.description_en

class Role(db.Model):
    """Role model for user role management"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False, index=True)
    display_name_ar = db.Column(db.String(100))
    display_name_en = db.Column(db.String(100))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    permissions = db.relationship('Permission', secondary=role_permissions, 
                                backref=db.backref('roles', lazy='dynamic'))
    users = db.relationship('User', backref='role', lazy='dynamic')
    
    def __repr__(self):
        return f'<Role {self.name}>'
    
    def get_display_name(self, language='ar'):
        """Get display name in specified language"""
        if language == 'en':
            return self.display_name_en or self.display_name_ar or self.name
        return self.display_name_ar or self.display_name_en or self.name
    
    def get_description(self, language='ar'):
        """Get description in specified language"""
        if language == 'en':
            return self.description_en or self.description_ar
        return self.description_ar or self.description_en
    
    def has_permission(self, permission_name):
        """Check if role has a specific permission"""
        return any(perm.name == permission_name for perm in self.permissions)
    
    def add_permission(self, permission):
        """Add permission to role"""
        if permission not in self.permissions:
            self.permissions.append(permission)
    
    def remove_permission(self, permission):
        """Remove permission from role"""
        if permission in self.permissions:
            self.permissions.remove(permission)
    
    @staticmethod
    def get_by_name(name):
        """Get role by name"""
        return Role.query.filter_by(name=name).first()
    
    def to_dict(self, language='ar'):
        """Convert role to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.get_display_name(language),
            'description': self.get_description(language),
            'is_active': self.is_active,
            'permissions': [perm.name for perm in self.permissions],
            'users_count': self.users.count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
