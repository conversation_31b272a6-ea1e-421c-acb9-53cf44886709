"""
Data Management Routes - Qatar POS System
مسارات إدارة البيانات - نظام نقاط البيع القطري
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from extensions import db
from models.sale import Sale
from models.product import Product
from models.customer import Customer
from models.supplier import Supplier
from models.category import Category
from models.user import User
# Note: SaleItem and InventoryTransaction models may not exist
# We'll handle this gracefully in the code
from utils.helpers import get_user_language
from utils.decorators import permission_required
from datetime import datetime, timedelta
import os

data_management_bp = Blueprint('data_management', __name__, url_prefix='/data-management')

@data_management_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """Data management dashboard"""
    language = get_user_language()
    
    # Get data statistics
    stats = {
        'sales_count': Sale.query.count(),
        'products_count': Product.query.count(),
        'customers_count': Customer.query.count(),
        'suppliers_count': Supplier.query.count(),
        'categories_count': Category.query.count(),
        'users_count': User.query.count(),
        'sale_items_count': 0,  # Calculate from sales
        'inventory_transactions_count': 0  # Not available
    }

    # Calculate sale items count
    try:
        total_items = 0
        for sale in Sale.query.all():
            total_items += sale.items.count()
        stats['sale_items_count'] = total_items
    except:
        stats['sale_items_count'] = 0
    
    # Calculate total database size (approximate)
    total_records = sum(stats.values())
    
    return render_template('data_management/index.html',
                         stats=stats,
                         total_records=total_records,
                         language=language)

@data_management_bp.route('/clear-data')
@login_required
@permission_required('admin')
def clear_data_page():
    """Clear data page"""
    language = get_user_language()
    
    # Get data statistics
    stats = {
        'sales_count': Sale.query.count(),
        'products_count': Product.query.count(),
        'customers_count': Customer.query.count(),
        'suppliers_count': Supplier.query.count(),
        'categories_count': Category.query.count(),
        'users_count': User.query.count(),
        'sale_items_count': 0,  # Calculate from sales
        'inventory_transactions_count': 0  # Not available
    }
    
    return render_template('data_management/clear_data.html',
                         stats=stats,
                         language=language)

@data_management_bp.route('/api/clear-sales', methods=['POST'])
@login_required
@permission_required('admin')
def api_clear_sales():
    """Clear all sales data"""
    try:
        language = get_user_language()
        
        # Verify admin password
        admin_password = request.form.get('admin_password')
        if not current_user.check_password(admin_password):
            return jsonify({
                'success': False,
                'error': 'كلمة مرور المدير غير صحيحة' if language == 'ar' else 'Invalid admin password'
            }), 400
        
        # Clear sales data
        # First clear sale items (if they exist as separate table)
        try:
            # Clear sale items through relationship
            for sale in Sale.query.all():
                sale.items.delete()
        except:
            pass

        # Clear sales
        Sale.query.delete()

        # Reset auto-increment
        try:
            db.engine.execute('DELETE FROM sqlite_sequence WHERE name="sales"')
        except:
            pass
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح بيانات المبيعات بنجاح' if language == 'ar' else 'Sales data cleared successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'خطأ في مسح البيانات: {str(e)}' if language == 'ar' else f'Error clearing data: {str(e)}'
        }), 500

@data_management_bp.route('/api/clear-products', methods=['POST'])
@login_required
@permission_required('admin')
def api_clear_products():
    """Clear all products data"""
    try:
        language = get_user_language()
        
        # Verify admin password
        admin_password = request.form.get('admin_password')
        if not current_user.check_password(admin_password):
            return jsonify({
                'success': False,
                'error': 'كلمة مرور المدير غير صحيحة' if language == 'ar' else 'Invalid admin password'
            }), 400
        
        # Clear products and related data
        Product.query.delete()

        # Reset auto-increment
        try:
            db.engine.execute('DELETE FROM sqlite_sequence WHERE name="products"')
        except:
            pass
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح بيانات المنتجات بنجاح' if language == 'ar' else 'Products data cleared successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'خطأ في مسح البيانات: {str(e)}' if language == 'ar' else f'Error clearing data: {str(e)}'
        }), 500

@data_management_bp.route('/api/clear-customers', methods=['POST'])
@login_required
@permission_required('admin')
def api_clear_customers():
    """Clear all customers data"""
    try:
        language = get_user_language()
        
        # Verify admin password
        admin_password = request.form.get('admin_password')
        if not current_user.check_password(admin_password):
            return jsonify({
                'success': False,
                'error': 'كلمة مرور المدير غير صحيحة' if language == 'ar' else 'Invalid admin password'
            }), 400
        
        # Clear customers data
        Customer.query.delete()
        
        # Reset auto-increment
        db.engine.execute('DELETE FROM sqlite_sequence WHERE name="customers"')
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح بيانات العملاء بنجاح' if language == 'ar' else 'Customers data cleared successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'خطأ في مسح البيانات: {str(e)}' if language == 'ar' else f'Error clearing data: {str(e)}'
        }), 500

@data_management_bp.route('/api/clear-suppliers', methods=['POST'])
@login_required
@permission_required('admin')
def api_clear_suppliers():
    """Clear all suppliers data"""
    try:
        language = get_user_language()
        
        # Verify admin password
        admin_password = request.form.get('admin_password')
        if not current_user.check_password(admin_password):
            return jsonify({
                'success': False,
                'error': 'كلمة مرور المدير غير صحيحة' if language == 'ar' else 'Invalid admin password'
            }), 400
        
        # Clear suppliers data
        Supplier.query.delete()
        
        # Reset auto-increment
        db.engine.execute('DELETE FROM sqlite_sequence WHERE name="suppliers"')
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح بيانات الموردين بنجاح' if language == 'ar' else 'Suppliers data cleared successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'خطأ في مسح البيانات: {str(e)}' if language == 'ar' else f'Error clearing data: {str(e)}'
        }), 500

@data_management_bp.route('/api/clear-all', methods=['POST'])
@login_required
@permission_required('admin')
def api_clear_all():
    """Clear all data (except admin user)"""
    try:
        language = get_user_language()
        
        # Verify admin password
        admin_password = request.form.get('admin_password')
        if not current_user.check_password(admin_password):
            return jsonify({
                'success': False,
                'error': 'كلمة مرور المدير غير صحيحة' if language == 'ar' else 'Invalid admin password'
            }), 400
        
        # Clear all data in correct order (respecting foreign keys)
        # Clear sales first (including items through relationship)
        for sale in Sale.query.all():
            try:
                sale.items.delete()
            except:
                pass
        Sale.query.delete()

        # Clear other data
        Product.query.delete()
        Customer.query.delete()
        Supplier.query.delete()
        Category.query.delete()

        # Keep only the current admin user
        User.query.filter(User.id != current_user.id).delete()

        # Reset auto-increment for all tables
        tables = ['sales', 'products', 'customers', 'suppliers', 'categories', 'users']

        for table in tables:
            try:
                db.engine.execute(f'DELETE FROM sqlite_sequence WHERE name="{table}"')
            except:
                pass  # Table might not exist in sqlite_sequence
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح جميع البيانات بنجاح' if language == 'ar' else 'All data cleared successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'خطأ في مسح البيانات: {str(e)}' if language == 'ar' else f'Error clearing data: {str(e)}'
        }), 500

@data_management_bp.route('/api/backup-data', methods=['POST'])
@login_required
@permission_required('admin')
def api_backup_data():
    """Create data backup"""
    try:
        language = get_user_language()
        
        # Create backup directory
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"{backup_dir}/backup_{timestamp}.db"
        
        # Copy database file
        import shutil
        shutil.copy2('qatar_pos.db', backup_file)
        
        return jsonify({
            'success': True,
            'message': f'تم إنشاء نسخة احتياطية: {backup_file}' if language == 'ar' else f'Backup created: {backup_file}',
            'backup_file': backup_file
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}' if language == 'ar' else f'Error creating backup: {str(e)}'
        }), 500
