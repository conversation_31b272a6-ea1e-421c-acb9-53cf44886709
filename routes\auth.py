"""
Authentication routes for Qatar POS System
Handles login, logout, and user session management
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from models.user import User
from extensions import db
from utils.helpers import get_user_language

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = bool(request.form.get('remember_me'))
        language = request.form.get('language', 'ar')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور' if language == 'ar' 
                  else 'Please enter username and password', 'error')
            return render_template('auth/login.html', language=language)
        
        # Find user by username or email
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('حسابك غير مفعل. يرجى الاتصال بالمدير' if language == 'ar'
                      else 'Your account is inactive. Please contact administrator', 'error')
                return render_template('auth/login.html', language=language)
            
            # Login successful
            login_user(user, remember=remember_me)
            user.update_last_login()
            
            # Set user language preference
            session['language'] = language
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            
            flash(f'مرحباً {user.get_full_name("ar")}' if language == 'ar'
                  else f'Welcome {user.get_full_name("en")}', 'success')
            return redirect(url_for('dashboard.index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة' if language == 'ar'
                  else 'Invalid username or password', 'error')
    
    return render_template('auth/login.html', language=session.get('language', 'ar'))

@auth_bp.route('/logout')
@login_required
def logout():
    """User logout"""
    language = get_user_language()
    logout_user()
    flash('تم تسجيل الخروج بنجاح' if language == 'ar' 
          else 'Logged out successfully', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    language = get_user_language()
    
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # Validate current password
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة' if language == 'ar'
                  else 'Current password is incorrect', 'error')
            return render_template('auth/change_password.html', language=language)
        
        # Validate new password
        if len(new_password) < 6:
            flash('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل' if language == 'ar'
                  else 'New password must be at least 6 characters', 'error')
            return render_template('auth/change_password.html', language=language)
        
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة وتأكيدها غير متطابقين' if language == 'ar'
                  else 'New password and confirmation do not match', 'error')
            return render_template('auth/change_password.html', language=language)
        
        # Update password
        current_user.set_password(new_password)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح' if language == 'ar'
              else 'Password changed successfully', 'success')
        return redirect(url_for('dashboard.index'))
    
    return render_template('auth/change_password.html', language=language)

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile management"""
    language = get_user_language()
    
    if request.method == 'POST':
        # Update profile information
        current_user.first_name_ar = request.form.get('first_name_ar')
        current_user.first_name_en = request.form.get('first_name_en')
        current_user.last_name_ar = request.form.get('last_name_ar')
        current_user.last_name_en = request.form.get('last_name_en')
        current_user.phone = request.form.get('phone')
        current_user.qatar_id = request.form.get('qatar_id')
        
        try:
            db.session.commit()
            flash('تم تحديث الملف الشخصي بنجاح' if language == 'ar'
                  else 'Profile updated successfully', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الملف الشخصي' if language == 'ar'
                  else 'Error updating profile', 'error')
    
    return render_template('auth/profile.html', user=current_user, language=language)

@auth_bp.route('/switch-language/<language>')
def switch_language(language):
    """Switch user interface language"""
    if language in ['ar', 'en']:
        session['language'] = language
        
        # If user is logged in, we could save this preference to database
        if current_user.is_authenticated:
            # Could add a language preference field to User model
            pass
    
    # Redirect back to the page they came from
    return redirect(request.referrer or url_for('dashboard.index'))

@auth_bp.route('/unauthorized')
def unauthorized():
    """Unauthorized access page"""
    language = get_user_language()
    return render_template('auth/unauthorized.html', language=language), 403

# Error handlers
@auth_bp.errorhandler(401)
def unauthorized_error(error):
    """Handle unauthorized access"""
    language = get_user_language()
    return render_template('auth/unauthorized.html', language=language), 401

@auth_bp.errorhandler(403)
def forbidden_error(error):
    """Handle forbidden access"""
    language = get_user_language()
    return render_template('auth/unauthorized.html', language=language), 403
