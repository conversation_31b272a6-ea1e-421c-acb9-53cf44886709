#!/usr/bin/env python3
"""
اختبار بسيط لإصلاح AppenderQuery
Simple AppenderQuery fix test
"""

from app import create_app
from extensions import db
from models.product import Product
from models.sale import Sale

def test_fixes():
    """اختبار الإصلاحات"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار إصلاحات AppenderQuery")
        print("=" * 40)
        
        # اختبار المنتجات
        print("\n📦 اختبار المنتجات:")
        products = Product.query.limit(2).all()
        
        for product in products:
            print(f"\n  منتج: {product.name_ar}")
            
            # اختبار الدوال الجديدة
            try:
                sales_count = product.get_sales_count()
                print(f"    ✅ get_sales_count(): {sales_count}")
            except Exception as e:
                print(f"    ❌ get_sales_count(): {e}")
            
            try:
                last_sale = product.get_last_sale()
                if last_sale:
                    print(f"    ✅ get_last_sale(): موجود")
                else:
                    print(f"    ✅ get_last_sale(): لا يوجد")
            except Exception as e:
                print(f"    ❌ get_last_sale(): {e}")
            
            # اختبار AppenderQuery مباشرة
            try:
                count = product.sale_items.count()
                print(f"    ✅ sale_items.count(): {count}")
            except Exception as e:
                print(f"    ❌ sale_items.count(): {e}")
            
            # اختبار len() على AppenderQuery (يجب أن يفشل)
            try:
                length = len(product.sale_items)
                print(f"    ❌ len(sale_items): {length} - لا يجب أن يعمل!")
            except TypeError:
                print(f"    ✅ len(sale_items): خطأ متوقع (AppenderQuery)")
        
        # اختبار المبيعات
        print("\n🛒 اختبار المبيعات:")
        sales = Sale.query.limit(2).all()
        
        if not sales:
            print("    ℹ️ لا توجد مبيعات للاختبار")
        else:
            for sale in sales:
                print(f"\n  مبيعة: {sale.sale_number}")
                
                try:
                    items_count = sale.get_items_count()
                    print(f"    ✅ get_items_count(): {items_count}")
                except Exception as e:
                    print(f"    ❌ get_items_count(): {e}")
                
                try:
                    count = sale.items.count()
                    print(f"    ✅ items.count(): {count}")
                except Exception as e:
                    print(f"    ❌ items.count(): {e}")
        
        print("\n" + "=" * 40)
        print("✅ انتهى الاختبار!")
        print("🎯 النتيجة: إذا لم تظهر أخطاء حمراء، فالإصلاح نجح!")

if __name__ == '__main__':
    test_fixes()
