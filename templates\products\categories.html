{% extends "base.html" %}

{% block title %}
{{ 'إدارة فئات المنتجات - نظام نقاط البيع القطري' if language == 'ar' else 'Product Categories - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-tags"></i>
                {{ 'إدارة فئات المنتجات' if language == 'ar' else 'Product Categories' }}
            </h1>
            {% if current_user.has_permission('products_write') %}
            <a href="{{ url_for('products.create_category') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة فئة جديدة' if language == 'ar' else 'Add New Category' }}
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    {% for category in categories %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="bi bi-tag"></i>
                    {{ category.get_name(language) }}
                </h6>
                {% if category.parent %}
                <span class="badge bg-secondary">{{ 'فرعية' if language == 'ar' else 'Sub-category' }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if category.parent %}
                <p class="text-muted small mb-2">
                    <i class="bi bi-arrow-up"></i>
                    {{ 'الفئة الرئيسية:' if language == 'ar' else 'Parent Category:' }} {{ category.parent.get_name(language) }}
                </p>
                {% endif %}
                
                {% if category.get_description(language) %}
                <p class="text-muted">{{ category.get_description(language) }}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary">{{ category.products.filter_by(is_active=True).count() }}</h5>
                        <small class="text-muted">{{ 'منتج' if language == 'ar' else 'Products' }}</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-info">{{ category.subcategories.filter_by(is_active=True).count() }}</h5>
                        <small class="text-muted">{{ 'فئة فرعية' if language == 'ar' else 'Subcategories' }}</small>
                    </div>
                </div>
                
                {% if category.subcategories.filter_by(is_active=True).count() > 0 %}
                <hr>
                <h6 class="text-muted">{{ 'الفئات الفرعية:' if language == 'ar' else 'Subcategories:' }}</h6>
                <div class="d-flex flex-wrap gap-1">
                    {% for subcategory in category.subcategories.filter_by(is_active=True) %}
                    <span class="badge bg-light text-dark">{{ subcategory.get_name(language) }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        {{ 'تم الإنشاء:' if language == 'ar' else 'Created:' }} {{ category.created_at.strftime('%Y-%m-%d') }}
                    </small>
                    {% if current_user.has_permission('products_write') %}
                    <div class="btn-group btn-group-sm">
                        <a href="{{ url_for('products.edit_category', category_id=category.id) }}" 
                           class="btn btn-outline-warning" title="{{ 'تعديل' if language == 'ar' else 'Edit' }}">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <a href="{{ url_for('products.index', category=category.id) }}" 
                           class="btn btn-outline-primary" title="{{ 'عرض المنتجات' if language == 'ar' else 'View Products' }}">
                            <i class="bi bi-box"></i>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    
    {% if not categories %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-tags display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد فئات' if language == 'ar' else 'No categories found' }}</h5>
            <p class="text-muted">{{ 'ابدأ بإضافة فئة جديدة لتنظيم منتجاتك' if language == 'ar' else 'Start by adding a new category to organize your products' }}</p>
            {% if current_user.has_permission('products_write') %}
            <a href="{{ url_for('products.create_category') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة فئة جديدة' if language == 'ar' else 'Add New Category' }}
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Category Tree View -->
{% if categories %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-diagram-3"></i>
                    {{ 'هيكل الفئات' if language == 'ar' else 'Category Structure' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="category-tree">
                    {% for category in categories if not category.parent %}
                    <div class="category-node">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-folder text-primary me-2"></i>
                            <strong>{{ category.get_name(language) }}</strong>
                            <span class="badge bg-primary ms-2">{{ category.get_active_products_count() }}</span>
                        </div>
                        
                        {% if category.get_active_subcategories_count() > 0 %}
                        <div class="subcategories ms-4">
                            {% for subcategory in category.get_active_subcategories() %}
                            <div class="d-flex align-items-center mb-1">
                                <i class="bi bi-folder-fill text-info me-2"></i>
                                <span>{{ subcategory.get_name(language) }}</span>
                                <span class="badge bg-info ms-2">{{ subcategory.get_active_products_count() }}</span>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% if not loop.last %}<hr>{% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.category-tree {
    font-family: 'Courier New', monospace;
}

.category-node {
    margin-bottom: 1rem;
}

.subcategories {
    border-left: 2px solid #dee2e6;
    padding-left: 1rem;
}

.card-footer {
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.125);
}
</style>
{% endblock %}
