#!/usr/bin/env python3
"""
Enhancement script for manager signature in invoices - Qatar POS System
"""

from app import create_app
from models.setting import Setting
from extensions import db

def enhance_signature_display():
    """Enhance signature display in invoices"""
    app = create_app()
    
    with app.app_context():
        print("🎨 تحسين عرض توقيع المسؤول في الفواتير")
        print("=" * 50)
        
        # Check current signature settings
        signature_settings = {
            'manager_signature': Setting.get_setting('manager_signature'),
            'manager_name': Setting.get_setting('manager_name'),
            'manager_title': Setting.get_setting('manager_title'),
            'signature_width': Setting.get_setting('signature_width'),
            'signature_height': Setting.get_setting('signature_height')
        }
        
        print("📋 الإعدادات الحالية:")
        for key, value in signature_settings.items():
            print(f"   {key}: {value or 'غير محدد'}")
        
        # Set default values if missing
        defaults = {
            'manager_name': 'أحمد المنصوري',
            'manager_title': 'المدير العام',
            'signature_width': '200',
            'signature_height': '80'
        }
        
        updated_count = 0
        for key, default_value in defaults.items():
            current_value = Setting.get_setting(key)
            if not current_value:
                Setting.set_setting(key, default_value, category='company')
                print(f"✅ تم تحديث {key}: {default_value}")
                updated_count += 1
        
        if updated_count > 0:
            print(f"\n🔄 تم تحديث {updated_count} إعدادات")
        else:
            print("\n✅ جميع الإعدادات مكتملة")
        
        return True

def create_signature_css_enhancements():
    """Create CSS enhancements for signature display"""
    
    css_enhancements = """
/* Enhanced Signature Styles for Qatar POS Invoices */

.signature-section {
    text-align: center;
    width: 200px;
    margin: 0 auto;
}

.signature-image {
    max-width: 100%;
    height: auto;
    border: none;
    background: transparent;
}

.signature-placeholder {
    border-bottom: 2px solid #333;
    width: 180px;
    height: 60px;
    margin: 0 auto 10px;
    position: relative;
}

.signature-placeholder::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: #333;
}

.manager-name {
    font-weight: bold;
    font-size: 12px;
    margin-top: 5px;
    color: #333;
}

.manager-title {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

.signature-label {
    font-size: 11px;
    font-weight: bold;
    margin-top: 10px;
    padding-top: 5px;
    border-top: 1px solid #ddd;
    color: #333;
}

/* Print-specific styles */
@media print {
    .signature-section {
        page-break-inside: avoid;
    }
    
    .signature-image {
        max-width: 200px;
        max-height: 80px;
    }
    
    .manager-name {
        font-size: 11px;
    }
    
    .manager-title {
        font-size: 9px;
    }
    
    .signature-label {
        font-size: 10px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .signature-section {
        width: 150px;
    }
    
    .signature-placeholder {
        width: 130px;
        height: 50px;
    }
}
"""
    
    # Save CSS to static folder
    import os
    css_dir = 'static/css'
    os.makedirs(css_dir, exist_ok=True)
    
    css_file = os.path.join(css_dir, 'invoice_signature.css')
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(css_enhancements)
    
    print(f"✅ تم إنشاء ملف CSS للتحسينات: {css_file}")
    return css_file

def test_signature_in_different_scenarios():
    """Test signature display in different scenarios"""
    app = create_app()
    
    with app.app_context():
        print("\n🧪 اختبار التوقيع في سيناريوهات مختلفة")
        print("=" * 50)
        
        # Scenario 1: With signature file
        signature_file = Setting.get_setting('manager_signature')
        if signature_file:
            print("✅ السيناريو 1: مع ملف التوقيع")
            print(f"   📁 الملف: {signature_file}")
            
            # Check file existence
            import os
            file_path = f"static/uploads/signatures/{signature_file}"
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   📊 حجم الملف: {file_size} بايت")
                print("   ✅ سيظهر التوقيع الفعلي في الفاتورة")
            else:
                print("   ❌ الملف غير موجود - سيظهر خط فارغ")
        else:
            print("⚠️ السيناريو 1: بدون ملف التوقيع")
            print("   📝 سيظهر خط فارغ مع اسم المسؤول")
        
        # Scenario 2: Manager name and title
        manager_name = Setting.get_setting('manager_name')
        manager_title = Setting.get_setting('manager_title')
        
        print(f"\n✅ السيناريو 2: معلومات المسؤول")
        print(f"   👤 الاسم: {manager_name or 'غير محدد'}")
        print(f"   🏷️ المنصب: {manager_title or 'غير محدد'}")
        
        if manager_name and manager_title:
            print("   ✅ ستظهر معلومات المسؤول كاملة")
        else:
            print("   ⚠️ ستظهر قيم افتراضية")
        
        # Scenario 3: Signature dimensions
        width = Setting.get_setting('signature_width')
        height = Setting.get_setting('signature_height')
        
        print(f"\n✅ السيناريو 3: أبعاد التوقيع")
        print(f"   📏 العرض: {width or 'افتراضي (200)'} بكسل")
        print(f"   📐 الارتفاع: {height or 'افتراضي (80)'} بكسل")
        
        # Scenario 4: Company stamp alongside signature
        stamp_file = Setting.get_setting('company_stamp')
        print(f"\n✅ السيناريو 4: الختم والتوقيع معاً")
        if stamp_file:
            print("   ✅ سيظهر ختم الشركة والتوقيع جنباً إلى جنب")
        else:
            print("   ⚠️ سيظهر التوقيع فقط (بدون ختم)")
        
        return True

def generate_signature_demo():
    """Generate a demo showing signature placement"""
    
    demo_html = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>عرض توقيع المسؤول - نظام نقاط البيع القطري</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .demo-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
        .signature-demo { display: flex; justify-content: space-between; align-items: end; margin-top: 30px; }
        .stamp-section, .signature-section { text-align: center; }
        .stamp-placeholder { width: 120px; height: 120px; border: 2px solid #007bff; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; }
        .signature-placeholder { width: 200px; height: 80px; border: 1px solid #333; margin: 0 auto; }
    </style>
</head>
<body>
    <h1>عرض توقيع المسؤول في الفواتير</h1>
    
    <div class="demo-section">
        <h2>مع التوقيع الفعلي</h2>
        <div class="signature-demo">
            <div class="stamp-section">
                <div class="stamp-placeholder">
                    <small>ختم الشركة</small>
                </div>
                <div><strong>ختم الشركة</strong></div>
            </div>
            <div class="signature-section">
                <div style="text-align: center; margin-bottom: 10px;">
                    <img src="/static/uploads/signatures/signature_12eb4e5e.png" 
                         alt="توقيع المسؤول" style="max-width: 200px; max-height: 80px;">
                </div>
                <div><strong>أحمد المنصوري</strong></div>
                <div><small>المدير العام</small></div>
                <div style="border-top: 1px solid #ddd; padding-top: 5px; margin-top: 10px;">
                    <strong>توقيع المسؤول</strong>
                </div>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>بدون ملف التوقيع (خط فارغ)</h2>
        <div class="signature-demo">
            <div class="stamp-section">
                <div class="stamp-placeholder">
                    <small>ختم الشركة</small>
                </div>
                <div><strong>ختم الشركة</strong></div>
            </div>
            <div class="signature-section">
                <div class="signature-placeholder"></div>
                <div><strong>أحمد المنصوري</strong></div>
                <div><small>المدير العام</small></div>
                <div style="border-top: 1px solid #ddd; padding-top: 5px; margin-top: 10px;">
                    <strong>توقيع المسؤول</strong>
                </div>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h2>ملاحظات</h2>
        <ul>
            <li>التوقيع يظهر في جميع الفواتير (عادية ومطبوعة)</li>
            <li>يمكن تخصيص أبعاد التوقيع من الإعدادات</li>
            <li>اسم ومنصب المسؤول يظهران تحت التوقيع</li>
            <li>التوقيع يظهر بجانب ختم الشركة</li>
            <li>يمكن تغيير التوقيع في أي وقت من إعدادات الشركة</li>
        </ul>
    </div>
</body>
</html>
"""
    
    # Save demo file
    import os
    demo_dir = 'static/demos'
    os.makedirs(demo_dir, exist_ok=True)
    
    demo_file = os.path.join(demo_dir, 'signature_demo.html')
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(demo_html)
    
    print(f"✅ تم إنشاء عرض توضيحي: {demo_file}")
    print(f"🌐 يمكن عرضه على: http://localhost:2626/static/demos/signature_demo.html")
    
    return demo_file

if __name__ == '__main__':
    print("🎨 تحسين وعرض توقيع المسؤول في الفواتير")
    print("=" * 60)
    
    try:
        # Enhance signature settings
        enhance_signature_display()
        
        # Create CSS enhancements
        create_signature_css_enhancements()
        
        # Test different scenarios
        test_signature_in_different_scenarios()
        
        # Generate demo
        generate_signature_demo()
        
        print("\n🎉 تم تحسين عرض التوقيع بنجاح!")
        print("✅ التوقيع جاهز للاستخدام في جميع الفواتير")
        print("\n📋 الخطوات التالية:")
        print("   1. افتح فاتورة لمشاهدة التوقيع")
        print("   2. اطبع الفاتورة للتأكد من جودة العرض")
        print("   3. عدل إعدادات التوقيع حسب الحاجة")
        
    except Exception as e:
        print(f"\n💥 خطأ في التحسين: {e}")
