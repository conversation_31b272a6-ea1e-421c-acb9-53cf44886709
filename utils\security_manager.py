"""
Advanced Security Manager for Qatar POS System
Handles authentication, authorization, audit logging, and security policies
"""

import hashlib
import secrets
import re
import json
import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import request, session, current_app, g
from werkzeug.security import check_password_hash, generate_password_hash
import ipaddress

class SecurityManager:
    def __init__(self, app=None):
        self.app = app
        self.logger = logging.getLogger(__name__)
        self.failed_attempts = {}  # Track failed login attempts
        self.blocked_ips = set()
        self.audit_log = []
        
    def init_app(self, app):
        """Initialize security manager with Flask app"""
        self.app = app
        
        # Set up security headers
        @app.after_request
        def set_security_headers(response):
            return self._set_security_headers(response)
    
    def _set_security_headers(self, response):
        """Set security headers on all responses"""
        # Prevent clickjacking
        response.headers['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # XSS Protection
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer Policy
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Content Security Policy
        response.headers['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; "
            "img-src 'self' data: blob:; "
            "font-src 'self' cdn.jsdelivr.net; "
            "connect-src 'self';"
        )
        
        # HSTS (if HTTPS)
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response
    
    def validate_password_policy(self, password, settings=None):
        """Validate password against security policy"""
        if not settings:
            # Default settings
            settings = {
                'min_password_length': 8,
                'require_uppercase': True,
                'require_numbers': True,
                'require_special_chars': False
            }
        
        errors = []
        
        # Check minimum length
        min_length = int(settings.get('min_password_length', 8))
        if len(password) < min_length:
            errors.append(f"Password must be at least {min_length} characters long")
        
        # Check for uppercase letters
        if settings.get('require_uppercase') == 'true' and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        # Check for numbers
        if settings.get('require_numbers') == 'true' and not re.search(r'\d', password):
            errors.append("Password must contain at least one number")
        
        # Check for special characters
        if settings.get('require_special_chars') == 'true' and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
        
        # Check for common weak passwords
        weak_passwords = ['password', '123456', 'qwerty', 'admin', 'letmein']
        if password.lower() in weak_passwords:
            errors.append("Password is too common and weak")
        
        return len(errors) == 0, errors
    
    def hash_password(self, password):
        """Generate secure password hash"""
        return generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)
    
    def verify_password(self, password, password_hash):
        """Verify password against hash"""
        return check_password_hash(password_hash, password)
    
    def generate_secure_token(self, length=32):
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    def check_rate_limit(self, identifier, max_attempts=5, window_minutes=15):
        """Check if identifier has exceeded rate limit"""
        now = datetime.now()
        window_start = now - timedelta(minutes=window_minutes)
        
        # Clean old attempts
        if identifier in self.failed_attempts:
            self.failed_attempts[identifier] = [
                attempt for attempt in self.failed_attempts[identifier]
                if attempt > window_start
            ]
        
        # Check current attempts
        attempts = len(self.failed_attempts.get(identifier, []))
        return attempts < max_attempts
    
    def record_failed_attempt(self, identifier):
        """Record a failed login attempt"""
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        self.failed_attempts[identifier].append(datetime.now())
        
        # Log the attempt
        self.log_security_event('failed_login', {
            'identifier': identifier,
            'ip': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', '')
        })
    
    def is_ip_whitelisted(self, ip_address, whitelist=None):
        """Check if IP address is in whitelist"""
        if not whitelist:
            return True  # No whitelist means all IPs allowed
        
        try:
            ip = ipaddress.ip_address(ip_address)
            for allowed_ip in whitelist:
                if ip in ipaddress.ip_network(allowed_ip, strict=False):
                    return True
            return False
        except ValueError:
            return False
    
    def block_ip(self, ip_address, reason="Security violation"):
        """Block an IP address"""
        self.blocked_ips.add(ip_address)
        self.log_security_event('ip_blocked', {
            'ip': ip_address,
            'reason': reason
        })
    
    def is_ip_blocked(self, ip_address):
        """Check if IP address is blocked"""
        return ip_address in self.blocked_ips
    
    def log_security_event(self, event_type, details=None):
        """Log security-related events"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'type': event_type,
            'ip': request.remote_addr if request else None,
            'user_agent': request.headers.get('User-Agent', '') if request else None,
            'user_id': getattr(g, 'current_user', {}).get('id') if hasattr(g, 'current_user') else None,
            'details': details or {}
        }
        
        self.audit_log.append(event)
        self.logger.info(f"Security Event: {event_type} - {json.dumps(details)}")
        
        # Keep only last 1000 events in memory
        if len(self.audit_log) > 1000:
            self.audit_log = self.audit_log[-1000:]
    
    def get_audit_log(self, limit=100, event_type=None):
        """Get audit log entries"""
        logs = self.audit_log
        
        if event_type:
            logs = [log for log in logs if log['type'] == event_type]
        
        return logs[-limit:] if limit else logs
    
    def check_session_security(self, user_id, session_timeout_minutes=480):
        """Check session security and timeout"""
        if 'user_id' not in session:
            return False, "No active session"
        
        if session['user_id'] != user_id:
            return False, "Session user mismatch"
        
        # Check session timeout
        last_activity = session.get('last_activity')
        if last_activity:
            last_activity_time = datetime.fromisoformat(last_activity)
            if datetime.now() - last_activity_time > timedelta(minutes=session_timeout_minutes):
                return False, "Session expired"
        
        # Update last activity
        session['last_activity'] = datetime.now().isoformat()
        
        return True, "Session valid"
    
    def sanitize_input(self, input_string, max_length=1000):
        """Sanitize user input to prevent injection attacks"""
        if not input_string:
            return ""
        
        # Remove null bytes
        sanitized = input_string.replace('\x00', '')
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        # Remove potentially dangerous characters for SQL injection
        dangerous_chars = ['<script', '</script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()
    
    def validate_file_upload(self, file, allowed_extensions=None, max_size_mb=10):
        """Validate file uploads for security"""
        if not file or not file.filename:
            return False, "No file provided"
        
        # Check file extension
        if allowed_extensions:
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            if file_ext not in allowed_extensions:
                return False, f"File type not allowed. Allowed: {', '.join(allowed_extensions)}"
        
        # Check file size
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        
        max_size_bytes = max_size_mb * 1024 * 1024
        if file_size > max_size_bytes:
            return False, f"File too large. Maximum size: {max_size_mb}MB"
        
        # Check for dangerous file signatures
        file_content = file.read(1024)  # Read first 1KB
        file.seek(0)  # Reset
        
        dangerous_signatures = [
            b'<?php',
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'\x4d\x5a'  # PE executable
        ]
        
        for signature in dangerous_signatures:
            if signature in file_content:
                return False, "File contains potentially dangerous content"
        
        return True, "File is valid"
    
    def encrypt_sensitive_data(self, data, key=None):
        """Encrypt sensitive data"""
        if not key:
            key = current_app.config.get('SECRET_KEY', 'default-key')
        
        # Simple encryption using hashlib (in production, use proper encryption)
        data_str = json.dumps(data) if not isinstance(data, str) else data
        encrypted = hashlib.sha256((data_str + key).encode()).hexdigest()
        return encrypted
    
    def generate_csrf_token(self):
        """Generate CSRF token for forms"""
        if 'csrf_token' not in session:
            session['csrf_token'] = self.generate_secure_token()
        return session['csrf_token']
    
    def validate_csrf_token(self, token):
        """Validate CSRF token"""
        return token and session.get('csrf_token') == token
    
    def require_csrf(self, f):
        """Decorator to require CSRF token validation"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'POST':
                token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
                if not self.validate_csrf_token(token):
                    self.log_security_event('csrf_violation', {
                        'endpoint': request.endpoint,
                        'method': request.method
                    })
                    return {'error': 'CSRF token validation failed'}, 403
            return f(*args, **kwargs)
        return decorated_function
    
    def require_ip_whitelist(self, f):
        """Decorator to require IP whitelist"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get IP whitelist from settings
            from models.setting import Setting
            whitelist_enabled = Setting.get_setting('enable_ip_whitelist') == 'true'
            
            if whitelist_enabled:
                # In a real implementation, you'd get the whitelist from settings
                whitelist = ['127.0.0.1', '***********/24']  # Example
                
                if not self.is_ip_whitelisted(request.remote_addr, whitelist):
                    self.log_security_event('ip_whitelist_violation', {
                        'ip': request.remote_addr,
                        'endpoint': request.endpoint
                    })
                    return {'error': 'Access denied'}, 403
            
            return f(*args, **kwargs)
        return decorated_function
    
    def security_audit(self):
        """Perform security audit and return recommendations"""
        recommendations = []
        
        # Check password policy
        from models.setting import Setting
        min_length = int(Setting.get_setting('min_password_length', '8'))
        if min_length < 8:
            recommendations.append("Increase minimum password length to at least 8 characters")
        
        # Check session timeout
        session_timeout = int(Setting.get_setting('session_timeout', '480'))
        if session_timeout > 480:  # 8 hours
            recommendations.append("Consider reducing session timeout for better security")
        
        # Check if HTTPS is enforced
        ssl_only = Setting.get_setting('enable_ssl_only') == 'true'
        if not ssl_only:
            recommendations.append("Enable HTTPS-only mode for production")
        
        # Check audit logging
        audit_enabled = Setting.get_setting('enable_audit_log') == 'true'
        if not audit_enabled:
            recommendations.append("Enable audit logging for security monitoring")
        
        # Check rate limiting
        rate_limiting = Setting.get_setting('enable_rate_limiting') == 'true'
        if not rate_limiting:
            recommendations.append("Enable rate limiting to prevent brute force attacks")
        
        return {
            'status': 'completed',
            'recommendations': recommendations,
            'score': max(0, 100 - len(recommendations) * 20),
            'audit_date': datetime.now().isoformat()
        }

# Global security manager instance
security_manager = SecurityManager()
