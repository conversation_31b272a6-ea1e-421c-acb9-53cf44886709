{% extends "base.html" %}

{% block title %}
{{ 'إعدادات النظام - نظام نقاط البيع القطري' if language == 'ar' else 'System Settings - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.settings-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom: none;
}

.settings-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.btn-save {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-save:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-1px);
}

.alert-info {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #28a745;
}

input:checked + .slider:before {
    transform: translateX(26px);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-gear"></i>
                    {{ 'إعدادات النظام' if language == 'ar' else 'System Settings' }}
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('dashboard.index') }}">
                                {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('settings.index') }}">
                                {{ 'الإعدادات' if language == 'ar' else 'Settings' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            {{ 'النظام' if language == 'ar' else 'System' }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <form method="POST">
        <div class="row">
            <!-- General System Settings -->
            <div class="col-lg-6">
                <div class="card settings-card">
                    <div class="card-header">
                        <h5>
                            <i class="bi bi-gear-fill"></i>
                            {{ 'الإعدادات العامة' if language == 'ar' else 'General Settings' }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">
                                {{ 'اسم النظام' if language == 'ar' else 'System Name' }}
                            </label>
                            <input type="text" class="form-control" name="system_name" 
                                   value="{{ settings.get('system_name', 'Qatar POS System') }}"
                                   placeholder="{{ 'أدخل اسم النظام' if language == 'ar' else 'Enter system name' }}">
                            <div class="form-text">
                                {{ 'اسم النظام الذي يظهر في العنوان والتقارير' if language == 'ar' else 'System name displayed in title and reports' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                {{ 'إصدار النظام' if language == 'ar' else 'System Version' }}
                            </label>
                            <input type="text" class="form-control" name="system_version" 
                                   value="{{ settings.get('system_version', '1.0.0') }}"
                                   placeholder="{{ 'أدخل إصدار النظام' if language == 'ar' else 'Enter system version' }}">
                            <div class="form-text">
                                {{ 'رقم إصدار النظام الحالي' if language == 'ar' else 'Current system version number' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                {{ 'المنطقة الزمنية' if language == 'ar' else 'Timezone' }}
                            </label>
                            <select class="form-select" name="timezone">
                                <option value="Asia/Qatar" {{ 'selected' if settings.get('timezone') == 'Asia/Qatar' else '' }}>
                                    {{ 'قطر (Asia/Qatar)' if language == 'ar' else 'Qatar (Asia/Qatar)' }}
                                </option>
                                <option value="Asia/Riyadh" {{ 'selected' if settings.get('timezone') == 'Asia/Riyadh' else '' }}>
                                    {{ 'الرياض (Asia/Riyadh)' if language == 'ar' else 'Riyadh (Asia/Riyadh)' }}
                                </option>
                                <option value="Asia/Dubai" {{ 'selected' if settings.get('timezone') == 'Asia/Dubai' else '' }}>
                                    {{ 'دبي (Asia/Dubai)' if language == 'ar' else 'Dubai (Asia/Dubai)' }}
                                </option>
                                <option value="UTC" {{ 'selected' if settings.get('timezone') == 'UTC' else '' }}>
                                    UTC
                                </option>
                            </select>
                            <div class="form-text">
                                {{ 'المنطقة الزمنية المستخدمة في النظام' if language == 'ar' else 'Timezone used throughout the system' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                {{ 'تنسيق التاريخ' if language == 'ar' else 'Date Format' }}
                            </label>
                            <select class="form-select" name="date_format">
                                <option value="dd/mm/yyyy" {{ 'selected' if settings.get('date_format') == 'dd/mm/yyyy' else '' }}>
                                    DD/MM/YYYY
                                </option>
                                <option value="mm/dd/yyyy" {{ 'selected' if settings.get('date_format') == 'mm/dd/yyyy' else '' }}>
                                    MM/DD/YYYY
                                </option>
                                <option value="yyyy-mm-dd" {{ 'selected' if settings.get('date_format') == 'yyyy-mm-dd' else '' }}>
                                    YYYY-MM-DD
                                </option>
                            </select>
                            <div class="form-text">
                                {{ 'تنسيق عرض التاريخ في النظام' if language == 'ar' else 'Date display format in the system' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security & Performance -->
            <div class="col-lg-6">
                <div class="card settings-card">
                    <div class="card-header">
                        <h5>
                            <i class="bi bi-shield-check"></i>
                            {{ 'الأمان والأداء' if language == 'ar' else 'Security & Performance' }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">
                                {{ 'مدة انتهاء الجلسة (بالدقائق)' if language == 'ar' else 'Session Timeout (minutes)' }}
                            </label>
                            <input type="number" class="form-control" name="session_timeout" 
                                   value="{{ settings.get('session_timeout', '30') }}" min="5" max="480">
                            <div class="form-text">
                                {{ 'مدة بقاء المستخدم مسجل دخول بدون نشاط' if language == 'ar' else 'How long user stays logged in without activity' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                {{ 'عدد محاولات تسجيل الدخول' if language == 'ar' else 'Login Attempts Limit' }}
                            </label>
                            <input type="number" class="form-control" name="max_login_attempts" 
                                   value="{{ settings.get('max_login_attempts', '5') }}" min="3" max="10">
                            <div class="form-text">
                                {{ 'عدد المحاولات المسموحة قبل حظر الحساب' if language == 'ar' else 'Number of attempts before account lockout' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label d-block">
                                {{ 'تفعيل النسخ الاحتياطي التلقائي' if language == 'ar' else 'Enable Auto Backup' }}
                            </label>
                            <label class="switch">
                                <input type="checkbox" name="auto_backup_enabled" 
                                       {{ 'checked' if settings.get('auto_backup_enabled') == 'true' else '' }}>
                                <span class="slider"></span>
                            </label>
                            <div class="form-text">
                                {{ 'إنشاء نسخة احتياطية تلقائياً بشكل دوري' if language == 'ar' else 'Automatically create periodic backups' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                {{ 'تكرار النسخ الاحتياطي' if language == 'ar' else 'Backup Frequency' }}
                            </label>
                            <select class="form-select" name="backup_frequency">
                                <option value="daily" {{ 'selected' if settings.get('backup_frequency') == 'daily' else '' }}>
                                    {{ 'يومي' if language == 'ar' else 'Daily' }}
                                </option>
                                <option value="weekly" {{ 'selected' if settings.get('backup_frequency') == 'weekly' else '' }}>
                                    {{ 'أسبوعي' if language == 'ar' else 'Weekly' }}
                                </option>
                                <option value="monthly" {{ 'selected' if settings.get('backup_frequency') == 'monthly' else '' }}>
                                    {{ 'شهري' if language == 'ar' else 'Monthly' }}
                                </option>
                            </select>
                            <div class="form-text">
                                {{ 'كم مرة يتم إنشاء النسخة الاحتياطية' if language == 'ar' else 'How often to create backups' }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label d-block">
                                {{ 'تفعيل سجل العمليات' if language == 'ar' else 'Enable Activity Logging' }}
                            </label>
                            <label class="switch">
                                <input type="checkbox" name="activity_logging" 
                                       {{ 'checked' if settings.get('activity_logging') == 'true' else '' }}>
                                <span class="slider"></span>
                            </label>
                            <div class="form-text">
                                {{ 'تسجيل جميع العمليات والأنشطة في النظام' if language == 'ar' else 'Log all operations and activities in the system' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications & Alerts -->
        <div class="row">
            <div class="col-12">
                <div class="card settings-card">
                    <div class="card-header">
                        <h5>
                            <i class="bi bi-bell"></i>
                            {{ 'التنبيهات والإشعارات' if language == 'ar' else 'Notifications & Alerts' }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label d-block">
                                        {{ 'تنبيهات المخزون المنخفض' if language == 'ar' else 'Low Stock Alerts' }}
                                    </label>
                                    <label class="switch">
                                        <input type="checkbox" name="low_stock_alerts" 
                                               {{ 'checked' if settings.get('low_stock_alerts') == 'true' else '' }}>
                                        <span class="slider"></span>
                                    </label>
                                    <div class="form-text">
                                        {{ 'تنبيه عند انخفاض كمية المنتج' if language == 'ar' else 'Alert when product quantity is low' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label d-block">
                                        {{ 'تنبيهات انتهاء الصلاحية' if language == 'ar' else 'Expiry Alerts' }}
                                    </label>
                                    <label class="switch">
                                        <input type="checkbox" name="expiry_alerts" 
                                               {{ 'checked' if settings.get('expiry_alerts') == 'true' else '' }}>
                                        <span class="slider"></span>
                                    </label>
                                    <div class="form-text">
                                        {{ 'تنبيه عند اقتراب انتهاء صلاحية المنتج' if language == 'ar' else 'Alert when product expiry is approaching' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label d-block">
                                        {{ 'تنبيهات المبيعات اليومية' if language == 'ar' else 'Daily Sales Alerts' }}
                                    </label>
                                    <label class="switch">
                                        <input type="checkbox" name="daily_sales_alerts" 
                                               {{ 'checked' if settings.get('daily_sales_alerts') == 'true' else '' }}>
                                        <span class="slider"></span>
                                    </label>
                                    <div class="form-text">
                                        {{ 'تقرير يومي عن المبيعات' if language == 'ar' else 'Daily sales summary report' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row">
            <div class="col-12">
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-save">
                        <i class="bi bi-check-circle"></i>
                        {{ 'حفظ الإعدادات' if language == 'ar' else 'Save Settings' }}
                    </button>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-secondary ms-3">
                        <i class="bi bi-arrow-left"></i>
                        {{ 'العودة' if language == 'ar' else 'Back' }}
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const systemName = document.querySelector('input[name="system_name"]').value.trim();
        const sessionTimeout = document.querySelector('input[name="session_timeout"]').value;
        
        if (!systemName) {
            e.preventDefault();
            alert('{{ "يرجى إدخال اسم النظام" if language == "ar" else "Please enter system name" }}');
            return;
        }
        
        if (sessionTimeout < 5 || sessionTimeout > 480) {
            e.preventDefault();
            alert('{{ "مدة الجلسة يجب أن تكون بين 5 و 480 دقيقة" if language == "ar" else "Session timeout must be between 5 and 480 minutes" }}');
            return;
        }
    });
});
</script>
{% endblock %}
