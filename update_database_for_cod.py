#!/usr/bin/env python3
"""
Update Database for COD - Qatar POS System
Update database schema to support COD payment method and delivery system
"""

from app import create_app
from extensions import db
from models.delivery import DeliveryZone
from sqlalchemy import text

def update_payment_methods_enum():
    """Update payment_methods enum to include COD"""
    app = create_app()
    
    with app.app_context():
        print("🔧 تحديث enum طرق الدفع لإضافة COD")
        print("=" * 50)
        
        try:
            # Check current enum values
            result = db.session.execute(text("""
                SELECT enumlabel 
                FROM pg_enum 
                WHERE enumtypid = (
                    SELECT oid 
                    FROM pg_type 
                    WHERE typname = 'payment_methods'
                )
            """))
            
            current_values = [row[0] for row in result.fetchall()]
            print(f"📋 القيم الحالية: {current_values}")
            
            if 'cod' not in current_values:
                # Add COD to enum
                db.session.execute(text("""
                    ALTER TYPE payment_methods ADD VALUE 'cod'
                """))
                db.session.commit()
                print("   ✅ تم إضافة 'cod' إلى enum طرق الدفع")
            else:
                print("   ✅ 'cod' موجود بالفعل في enum طرق الدفع")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في تحديث enum: {e}")
            db.session.rollback()
            return False

def create_delivery_tables():
    """Create delivery-related tables"""
    app = create_app()
    
    with app.app_context():
        print("\n🗄️ إنشاء جداول التوصيل")
        print("=" * 40)
        
        try:
            # Create all tables
            db.create_all()
            print("   ✅ تم إنشاء جميع الجداول")
            
            # Check if tables exist
            tables_to_check = ['delivery_orders', 'delivery_attempts', 'delivery_zones']
            
            for table_name in tables_to_check:
                result = db.session.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '{table_name}'
                    )
                """))
                
                exists = result.fetchone()[0]
                if exists:
                    print(f"   ✅ جدول {table_name}: موجود")
                else:
                    print(f"   ❌ جدول {table_name}: غير موجود")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الجداول: {e}")
            db.session.rollback()
            return False

def setup_qatar_delivery_zones():
    """Setup delivery zones for Qatar"""
    app = create_app()
    
    with app.app_context():
        print("\n🇶🇦 إعداد مناطق التوصيل في قطر")
        print("=" * 50)
        
        # Qatar delivery zones
        qatar_zones = [
            {
                'zone_code': 'doha_center',
                'zone_name_ar': 'وسط الدوحة',
                'zone_name_en': 'Doha Center',
                'base_fee': 10.00,
                'cod_available': True,
                'same_day_delivery': True
            },
            {
                'zone_code': 'doha_suburbs',
                'zone_name_ar': 'ضواحي الدوحة',
                'zone_name_en': 'Doha Suburbs',
                'base_fee': 15.00,
                'cod_available': True,
                'same_day_delivery': True
            },
            {
                'zone_code': 'al_rayyan',
                'zone_name_ar': 'الريان',
                'zone_name_en': 'Al Rayyan',
                'base_fee': 20.00,
                'cod_available': True,
                'same_day_delivery': False
            },
            {
                'zone_code': 'al_wakrah',
                'zone_name_ar': 'الوكرة',
                'zone_name_en': 'Al Wakrah',
                'base_fee': 25.00,
                'cod_available': True,
                'same_day_delivery': False
            },
            {
                'zone_code': 'al_khor',
                'zone_name_ar': 'الخور',
                'zone_name_en': 'Al Khor',
                'base_fee': 35.00,
                'cod_available': False,  # بعيد جداً
                'same_day_delivery': False
            },
            {
                'zone_code': 'mesaieed',
                'zone_name_ar': 'مسيعيد',
                'zone_name_en': 'Mesaieed',
                'base_fee': 40.00,
                'cod_available': True,
                'same_day_delivery': False
            },
            {
                'zone_code': 'dukhan',
                'zone_name_ar': 'دخان',
                'zone_name_en': 'Dukhan',
                'base_fee': 50.00,
                'cod_available': False,  # منطقة صناعية
                'same_day_delivery': False
            },
            {
                'zone_code': 'lusail',
                'zone_name_ar': 'لوسيل',
                'zone_name_en': 'Lusail',
                'base_fee': 18.00,
                'cod_available': True,
                'same_day_delivery': True
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for zone_data in qatar_zones:
            try:
                # Check if zone already exists
                existing_zone = DeliveryZone.query.filter_by(zone_code=zone_data['zone_code']).first()
                
                if existing_zone:
                    # Update existing zone
                    existing_zone.zone_name_ar = zone_data['zone_name_ar']
                    existing_zone.zone_name_en = zone_data['zone_name_en']
                    existing_zone.base_fee = zone_data['base_fee']
                    existing_zone.cod_available = zone_data['cod_available']
                    existing_zone.same_day_delivery = zone_data['same_day_delivery']
                    updated_count += 1
                    print(f"   🔄 تم تحديث: {zone_data['zone_name_ar']}")
                else:
                    # Create new zone
                    zone = DeliveryZone(
                        zone_code=zone_data['zone_code'],
                        zone_name_ar=zone_data['zone_name_ar'],
                        zone_name_en=zone_data['zone_name_en'],
                        base_fee=zone_data['base_fee'],
                        cod_available=zone_data['cod_available'],
                        standard_delivery=True,
                        express_delivery=True,
                        same_day_delivery=zone_data['same_day_delivery'],
                        delivery_time_slots='["09:00-12:00", "12:00-15:00", "15:00-18:00", "18:00-21:00"]'
                    )
                    
                    db.session.add(zone)
                    created_count += 1
                    print(f"   ✅ تم إنشاء: {zone_data['zone_name_ar']}")
                
                print(f"      💰 رسوم التوصيل: {zone_data['base_fee']} ر.ق")
                print(f"      📦 COD متاح: {'نعم' if zone_data['cod_available'] else 'لا'}")
                
            except Exception as e:
                print(f"   ❌ خطأ في منطقة {zone_data['zone_name_ar']}: {e}")
                db.session.rollback()
                return False
        
        try:
            db.session.commit()
            print(f"\n✅ تم إعداد مناطق التوصيل:")
            print(f"   📊 تم إنشاء: {created_count} منطقة")
            print(f"   🔄 تم تحديث: {updated_count} منطقة")
            print(f"   📋 المجموع: {created_count + updated_count} منطقة")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في حفظ المناطق: {e}")
            db.session.rollback()
            return False

def test_cod_functionality():
    """Test COD functionality after database update"""
    app = create_app()
    
    with app.app_context():
        print("\n🧪 اختبار وظائف COD بعد التحديث")
        print("=" * 50)
        
        try:
            # Test creating a sale with COD
            from models.sale import Sale
            
            test_sale = Sale(
                sale_number='TEST_COD_UPDATE',
                seller_id=1,
                payment_method='cod',
                subtotal=100.00,
                total_amount=100.00,
                payment_status='pending'
            )
            
            print("   ✅ إنشاء بيع بطريقة COD: يعمل")
            
            # Test delivery zones
            zones = DeliveryZone.query.all()
            print(f"   ✅ مناطق التوصيل: {len(zones)} منطقة متاحة")
            
            # Test COD-enabled zones
            cod_zones = DeliveryZone.query.filter_by(cod_available=True).all()
            print(f"   ✅ مناطق COD: {len(cod_zones)} منطقة تدعم COD")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار COD: {e}")
            return False

def generate_update_report():
    """Generate database update report"""
    print("\n📋 إنشاء تقرير تحديث قاعدة البيانات")
    print("=" * 50)
    
    from datetime import datetime
    
    report = f"""# تقرير تحديث قاعدة البيانات للدفع عند الاستلام
تاريخ التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## التحديثات المطبقة

### 1. تحديث enum طرق الدفع
- ✅ إضافة 'cod' إلى payment_methods enum
- ✅ دعم الدفع عند الاستلام في جداول Sales و Payments

### 2. إنشاء جداول التوصيل
- ✅ جدول delivery_orders: طلبات التوصيل
- ✅ جدول delivery_attempts: محاولات التوصيل
- ✅ جدول delivery_zones: مناطق التوصيل

### 3. إعداد مناطق التوصيل في قطر
- ✅ وسط الدوحة (10 ر.ق) - COD متاح
- ✅ ضواحي الدوحة (15 ر.ق) - COD متاح
- ✅ الريان (20 ر.ق) - COD متاح
- ✅ الوكرة (25 ر.ق) - COD متاح
- ✅ الخور (35 ر.ق) - COD غير متاح
- ✅ مسيعيد (40 ر.ق) - COD متاح
- ✅ دخان (50 ر.ق) - COD غير متاح
- ✅ لوسيل (18 ر.ق) - COD متاح

## النتائج
- ✅ قاعدة البيانات محدثة لدعم COD
- ✅ جميع الجداول المطلوبة موجودة
- ✅ مناطق التوصيل في قطر معدة
- ✅ النظام جاهز لاستقبال طلبات COD

## الخطوات التالية
1. إعادة تشغيل الخادم
2. اختبار واجهة نقاط البيع
3. اختبار إنشاء طلبات COD
4. تدريب المستخدمين على النظام الجديد
"""
    
    with open('database_update_cod_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ تم إنشاء التقرير: database_update_cod_report.md")

if __name__ == '__main__':
    print("🔧 تحديث قاعدة البيانات للدفع عند الاستلام - نظام نقاط البيع القطري")
    print("=" * 80)
    
    try:
        # Update payment methods enum
        print("1️⃣ تحديث enum طرق الدفع...")
        enum_ok = update_payment_methods_enum()
        
        # Create delivery tables
        print("\n2️⃣ إنشاء جداول التوصيل...")
        tables_ok = create_delivery_tables()
        
        # Setup Qatar delivery zones
        print("\n3️⃣ إعداد مناطق التوصيل...")
        zones_ok = setup_qatar_delivery_zones()
        
        # Test COD functionality
        print("\n4️⃣ اختبار وظائف COD...")
        test_ok = test_cod_functionality()
        
        # Generate report
        print("\n5️⃣ إنشاء التقرير...")
        generate_update_report()
        
        # Final summary
        print("\n" + "=" * 80)
        if enum_ok and tables_ok and zones_ok and test_ok:
            print("🎉 تم تحديث قاعدة البيانات بنجاح!")
            print("✅ enum طرق الدفع محدث")
            print("✅ جداول التوصيل منشأة")
            print("✅ مناطق قطر معدة")
            print("✅ وظائف COD تعمل")
            print("\n🔄 يرجى إعادة تشغيل الخادم لتطبيق التغييرات")
        else:
            print("⚠️ هناك مشاكل في تحديث قاعدة البيانات")
            print("📝 راجع التفاصيل أعلاه")
        
        print(f"\n🔗 بعد إعادة التشغيل: http://localhost:2626/sales/pos")
        
    except Exception as e:
        print(f"💥 خطأ في التحديث: {e}")
        import traceback
        traceback.print_exc()
