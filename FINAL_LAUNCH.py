#!/usr/bin/env python3
"""
Qatar POS System - Final Launch Script
Ultimate startup script with complete system validation and launch
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime

def print_header():
    """Print beautiful header"""
    print("\n" + "=" * 70)
    print("🇶🇦 QATAR POS SYSTEM - FINAL LAUNCH | نظام نقاط البيع القطري")
    print("=" * 70)
    print(f"🕐 Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print(f"📁 Directory: {os.getcwd()}")
    print("=" * 70)

def check_system():
    """Complete system check"""
    print("🔍 SYSTEM VALIDATION | فحص النظام")
    print("-" * 40)
    
    checks_passed = 0
    total_checks = 6
    
    # 1. Check Python version
    print("1. Python Version Check...")
    if sys.version_info >= (3, 8):
        print("   ✅ Python 3.8+ detected")
        checks_passed += 1
    else:
        print("   ❌ Python 3.8+ required")
    
    # 2. Check required packages
    print("2. Package Dependencies...")
    required_packages = ['flask', 'flask_sqlalchemy', 'flask_login', 'flask_migrate']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"   ❌ {package}")
    
    if not missing:
        checks_passed += 1
    
    # 3. Check database
    print("3. Database Connection...")
    try:
        from app import create_app
        from extensions import db
        app = create_app()
        with app.app_context():
            db.create_all()
        print("   ✅ Database ready")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # 4. Check templates
    print("4. Template Files...")
    template_dirs = ['templates/customers', 'templates/suppliers', 'templates/sales']
    all_exist = True
    for directory in template_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            print(f"   ❌ {directory}")
            all_exist = False
    
    if all_exist:
        checks_passed += 1
    
    # 5. Check static files
    print("5. Static Directories...")
    static_dirs = ['static/css', 'static/js', 'static/uploads']
    all_exist = True
    for directory in static_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            os.makedirs(directory, exist_ok=True)
            print(f"   📁 Created {directory}")
    checks_passed += 1
    
    # 6. Check port availability
    print("6. Port 2626 Availability...")
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', 2626))
        sock.close()
        
        if result != 0:
            print("   ✅ Port 2626 available")
            checks_passed += 1
        else:
            print("   ⚠️ Port 2626 in use (will try to use anyway)")
            checks_passed += 1
    except:
        print("   ✅ Port check completed")
        checks_passed += 1
    
    print("-" * 40)
    print(f"✅ System Check: {checks_passed}/{total_checks} passed")
    
    return checks_passed >= 5

def create_admin_user():
    """Ensure admin user exists"""
    print("\n👤 USER SETUP | إعداد المستخدمين")
    print("-" * 40)
    
    try:
        from app import create_app
        from extensions import db
        from models.user import User
        
        app = create_app()
        with app.app_context():
            admin = User.query.filter_by(username='admin').first()
            
            if not admin:
                print("Creating admin user...")
                admin = User(
                    username='admin',
                    first_name_ar='المدير',
                    first_name_en='Admin',
                    last_name_ar='العام',
                    last_name_en='User',
                    email='<EMAIL>',
                    role='admin',
                    is_active=True,
                    preferred_language='ar'
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ Admin user created")
            else:
                print("✅ Admin user exists")
        
        return True
        
    except Exception as e:
        print(f"❌ User setup error: {e}")
        return False

def launch_server():
    """Launch the Flask server"""
    print("\n🚀 SERVER LAUNCH | تشغيل الخادم")
    print("=" * 70)
    print("🌐 Server starting on: http://127.0.0.1:2626")
    print("🔑 Login credentials:")
    print("   👤 Username: admin")
    print("   🔒 Password: admin123")
    print("\n📱 Available pages:")
    print("   🏠 Dashboard: http://127.0.0.1:2626/")
    print("   🛒 POS System: http://127.0.0.1:2626/sales/pos")
    print("   📦 Products: http://127.0.0.1:2626/products")
    print("   👥 Customers: http://127.0.0.1:2626/customers")
    print("   🏢 Suppliers: http://127.0.0.1:2626/suppliers")
    print("   📊 Reports: http://127.0.0.1:2626/reports")
    print("\n💡 Press Ctrl+C to stop the server")
    print("=" * 70)
    
    # Wait a moment then open browser
    time.sleep(3)
    
    try:
        webbrowser.open('http://127.0.0.1:2626')
        print("🌐 Browser opened automatically")
    except:
        print("🌐 Please open http://127.0.0.1:2626 in your browser")
    
    # Start Flask server
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from extensions import db
            db.create_all()
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=2626,
            use_reloader=True,
            use_debugger=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 SERVER STOPPED | تم إيقاف الخادم")
        print("👋 Thank you for using Qatar POS System!")
        print("👋 شكراً لاستخدام نظام نقاط البيع القطري!")
        
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("💡 Please check the error and try again")

def main():
    """Main launch function"""
    print_header()
    
    # System validation
    if not check_system():
        print("\n❌ SYSTEM CHECK FAILED | فشل فحص النظام")
        print("💡 Please fix the issues above and try again")
        input("\nPress Enter to exit...")
        sys.exit(1)
    
    # User setup
    if not create_admin_user():
        print("\n❌ USER SETUP FAILED | فشل إعداد المستخدمين")
        print("💡 Please check the error and try again")
        input("\nPress Enter to exit...")
        sys.exit(1)
    
    print("\n🎉 ALL SYSTEMS READY | جميع الأنظمة جاهزة")
    print("🚀 Launching Qatar POS System...")
    
    time.sleep(2)
    
    # Launch server
    launch_server()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye! | وداعاً!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        input("\nPress Enter to exit...")
