{% extends "base.html" %}

{% block title %}
{{ 'غير مخول للوصول - نظام نقاط البيع القطري' if language == 'ar' else 'Unauthorized Access - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.unauthorized-container {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.unauthorized-card {
    max-width: 500px;
    width: 100%;
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.unauthorized-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.unauthorized-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.unauthorized-message {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.unauthorized-actions .btn {
    margin: 0.5rem;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-light {
    background: white;
    color: #333;
    border: none;
}

.btn-light:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-outline-light {
    border: 2px solid white;
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: #333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.error-details {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 2rem;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .unauthorized-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .unauthorized-title {
        font-size: 1.5rem;
    }
    
    .unauthorized-icon {
        font-size: 3rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="unauthorized-container">
    <div class="unauthorized-card">
        <div class="unauthorized-icon">
            <i class="bi bi-shield-exclamation"></i>
        </div>
        
        <h1 class="unauthorized-title">
            {{ 'غير مخول للوصول' if language == 'ar' else 'Unauthorized Access' }}
        </h1>
        
        <p class="unauthorized-message">
            {% if language == 'ar' %}
                عذراً، ليس لديك الصلاحية للوصول إلى هذه الصفحة. يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة.
            {% else %}
                Sorry, you don't have permission to access this page. Please contact your administrator for the required permissions.
            {% endif %}
        </p>
        
        <div class="unauthorized-actions">
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-light">
                <i class="bi bi-house"></i>
                {{ 'العودة للرئيسية' if language == 'ar' else 'Go to Dashboard' }}
            </a>
            
            <a href="javascript:history.back()" class="btn btn-outline-light">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة للخلف' if language == 'ar' else 'Go Back' }}
            </a>
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="error-details">
            <strong>{{ 'معلومات المستخدم:' if language == 'ar' else 'User Information:' }}</strong><br>
            {{ 'المستخدم:' if language == 'ar' else 'User:' }} {{ current_user.username }}<br>
            {{ 'الدور:' if language == 'ar' else 'Role:' }} {{ current_user.role }}<br>
            {{ 'الصفحة المطلوبة:' if language == 'ar' else 'Requested Page:' }} {{ request.path }}
        </div>
        {% endif %}
        
        <div class="mt-4">
            <small class="opacity-75">
                {{ 'إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني' if language == 'ar' else 'If you believe this is an error, please contact technical support' }}
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto redirect after 30 seconds if user wants
setTimeout(function() {
    if (confirm('{{ "هل تريد العودة إلى لوحة التحكم؟" if language == "ar" else "Would you like to return to the dashboard?" }}')) {
        window.location.href = '{{ url_for("dashboard.index") }}';
    }
}, 30000);

// Log unauthorized access attempt
console.log('Unauthorized access attempt:', {
    user: '{{ current_user.username if current_user.is_authenticated else "Anonymous" }}',
    role: '{{ current_user.role if current_user.is_authenticated else "None" }}',
    path: '{{ request.path }}',
    timestamp: new Date().toISOString()
});
</script>
{% endblock %}
