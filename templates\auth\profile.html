{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        الملف الشخصي
    {% else %}
        User Profile
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            الملف الشخصي
                        {% else %}
                            User Profile
                        {% endif %}
                    </h3>
                </div>
                
                <form method="POST" class="card-body">
                    <!-- Basic Information -->
                    <h5 class="mb-3">
                        {% if language == 'ar' %}المعلومات الأساسية{% else %}Basic Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name_ar">
                                    {% if language == 'ar' %}الاسم الأول (عربي){% else %}First Name (Arabic){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_ar" name="first_name_ar" 
                                       value="{{ current_user.first_name_ar or '' }}" required dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name_en">
                                    {% if language == 'ar' %}الاسم الأول (إنجليزي){% else %}First Name (English){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_en" name="first_name_en" 
                                       value="{{ current_user.first_name_en or '' }}" required dir="ltr">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name_ar">
                                    {% if language == 'ar' %}اسم العائلة (عربي){% else %}Last Name (Arabic){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_ar" name="last_name_ar" 
                                       value="{{ current_user.last_name_ar or '' }}" required dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name_en">
                                    {% if language == 'ar' %}اسم العائلة (إنجليزي){% else %}Last Name (English){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_en" name="last_name_en" 
                                       value="{{ current_user.last_name_en or '' }}" required dir="ltr">
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات الحساب{% else %}Account Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="username">
                                    {% if language == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ current_user.username }}" readonly>
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}لا يمكن تغيير اسم المستخدم{% else %}Username cannot be changed{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">
                                    {% if language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ current_user.email or '' }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">
                                    {% if language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ current_user.phone or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="role">
                                    {% if language == 'ar' %}الدور{% else %}Role{% endif %}
                                </label>
                                <input type="text" class="form-control" id="role" name="role" 
                                       value="{{ current_user.role }}" readonly>
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}يتم تحديد الدور من قبل المدير{% else %}Role is set by administrator{% endif %}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Language Preference -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="preferred_language">
                                    {% if language == 'ar' %}اللغة المفضلة{% else %}Preferred Language{% endif %}
                                </label>
                                <select class="form-control" id="preferred_language" name="preferred_language">
                                    <option value="ar" {% if current_user.preferred_language == 'ar' %}selected{% endif %}>
                                        العربية
                                    </option>
                                    <option value="en" {% if current_user.preferred_language == 'en' %}selected{% endif %}>
                                        English
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Change Password Section -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}تغيير كلمة المرور{% else %}Change Password{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="current_password">
                                    {% if language == 'ar' %}كلمة المرور الحالية{% else %}Current Password{% endif %}
                                </label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور{% else %}Leave empty if you don't want to change password{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="new_password">
                                    {% if language == 'ar' %}كلمة المرور الجديدة{% else %}New Password{% endif %}
                                </label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="confirm_password">
                                    {% if language == 'ar' %}تأكيد كلمة المرور{% else %}Confirm Password{% endif %}
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>
                    </div>

                    <!-- Account Status -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}حالة الحساب{% else %}Account Status{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>
                                    {% if language == 'ar' %}تاريخ الإنشاء{% else %}Created Date{% endif %}
                                </label>
                                <input type="text" class="form-control" 
                                       value="{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>
                                    {% if language == 'ar' %}آخر تسجيل دخول{% else %}Last Login{% endif %}
                                </label>
                                <input type="text" class="form-control" 
                                       value="{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'Never' }}" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if language == 'ar' %}حفظ التغييرات{% else %}Save Changes{% endif %}
                            </button>
                            <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Password validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('{% if language == "ar" %}كلمات المرور غير متطابقة{% else %}Passwords do not match{% endif %}');
    } else {
        this.setCustomValidity('');
    }
});

// Require current password if new password is entered
document.getElementById('new_password').addEventListener('input', function() {
    const currentPassword = document.getElementById('current_password');
    if (this.value) {
        currentPassword.required = true;
    } else {
        currentPassword.required = false;
    }
});
</script>
{% endblock %}
