@echo off
chcp 65001 >nul
echo ========================================
echo    Qatar POS System Builder
echo    نظام بناء نقاط البيع القطري
echo ========================================
echo.

echo 🚀 بدء عملية بناء التطبيق...
echo.

echo 📦 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo.
echo 🔨 تشغيل سكريپت البناء...
python build_exe.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo 📁 يمكنك العثور على الملف في مجلد dist/
    echo.
    echo هل تريد فتح مجلد النتائج؟ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer dist
    )
) else (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
