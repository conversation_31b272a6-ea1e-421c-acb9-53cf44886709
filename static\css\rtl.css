/* RTL (Right-to-Left) Styles for Arabic Language */

/* Text Alignment */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* Margin and Padding Adjustments */
.mr-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.mr-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.ml-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ml-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

.pr-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
}

.pr-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}

.pl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
}

.pl-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

/* Navigation RTL */
.navbar-nav {
    flex-direction: row-reverse;
}

.navbar-nav .nav-item {
    margin-left: 0.5rem;
    margin-right: 0;
}

.dropdown-menu {
    right: 0;
    left: auto;
}

/* Form RTL */
.form-control {
    text-align: right;
}

.form-select {
    text-align: right;
}

.input-group .form-control {
    border-radius: 0 0.375rem 0.375rem 0;
}

.input-group-text {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* Table RTL */
.table th,
.table td {
    text-align: right;
}

.table th:first-child,
.table td:first-child {
    border-right: none;
}

.table th:last-child,
.table td:last-child {
    border-left: none;
}

/* Card RTL */
.card-header {
    text-align: right;
}

.card-body {
    text-align: right;
}

/* Button Group RTL */
.btn-group .btn:first-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

.btn-group .btn:last-child {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* Pagination RTL */
.pagination {
    flex-direction: row-reverse;
}

.page-link {
    border-radius: 0;
}

.page-item:first-child .page-link {
    border-radius: 0 0.375rem 0.375rem 0;
}

.page-item:last-child .page-link {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* Modal RTL */
.modal-header {
    text-align: right;
}

.modal-body {
    text-align: right;
}

.modal-footer {
    justify-content: flex-start;
}

.modal-footer .btn {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Alert RTL */
.alert {
    text-align: right;
}

.alert-dismissible .btn-close {
    left: 0;
    right: auto;
}

/* Breadcrumb RTL */
.breadcrumb {
    flex-direction: row-reverse;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    padding-right: 0.5rem;
    padding-left: 0;
}

/* List Group RTL */
.list-group-item {
    text-align: right;
}

/* Progress RTL */
.progress {
    direction: ltr;
}

/* Custom RTL Classes */
.rtl-float-right {
    float: left !important;
}

.rtl-float-left {
    float: right !important;
}

.rtl-text-right {
    text-align: right !important;
}

.rtl-text-left {
    text-align: left !important;
}

/* POS Interface RTL */
.pos-cart {
    border-left: none;
    border-right: 1px solid #dee2e6;
}

.search-box .search-icon {
    left: auto;
    right: 0.75rem;
}

.search-box .form-control {
    padding-left: 0.75rem;
    padding-right: 2.5rem;
}

/* Icon Adjustments for RTL */
.bi-arrow-left::before {
    content: "\f13f"; /* arrow-right */
}

.bi-arrow-right::before {
    content: "\f13e"; /* arrow-left */
}

/* Dropdown RTL */
.dropdown-menu-right {
    right: auto;
    left: 0;
}

.dropdown-menu-left {
    right: 0;
    left: auto;
}

/* Flex RTL */
.flex-row-reverse {
    flex-direction: row-reverse !important;
}

/* Border RTL */
.border-left-primary {
    border-left: none !important;
    border-right: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: none !important;
    border-right: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: none !important;
    border-right: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: none !important;
    border-right: 0.25rem solid var(--warning-color) !important;
}

/* Typography RTL */
h1, h2, h3, h4, h5, h6 {
    text-align: right;
}

p {
    text-align: right;
}

/* Custom Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

body[dir="rtl"] {
    font-family: 'Noto Sans Arabic', sans-serif;
}

/* Number formatting for Arabic */
.arabic-numbers {
    font-feature-settings: "lnum";
}

/* Responsive RTL */
@media (max-width: 768px) {
    .pos-cart {
        border-right: none;
        border-top: 1px solid #dee2e6;
    }
}
