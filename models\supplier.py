"""
Supplier model for Qatar POS System
Manages supplier information and purchase tracking
"""

from datetime import datetime
from extensions import db

class Supplier(db.Model):
    """Supplier model with bilingual support"""
    
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Supplier identification
    supplier_code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    
    # Company information
    company_name_ar = db.Column(db.String(200), nullable=False)
    company_name_en = db.Column(db.String(200), nullable=False)
    
    # Contact person
    contact_person_ar = db.Column(db.String(100))
    contact_person_en = db.Column(db.String(100))
    
    # Contact information
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120))
    website = db.Column(db.String(200))
    
    # Address information
    address_ar = db.Column(db.Text)
    address_en = db.Column(db.Text)
    city_ar = db.Column(db.String(100))
    city_en = db.Column(db.String(100))
    country_ar = db.Column(db.String(100), default='قطر')
    country_en = db.Column(db.String(100), default='Qatar')
    postal_code = db.Column(db.String(10))
    
    # Business information
    commercial_registration = db.Column(db.String(20))
    tax_number = db.Column(db.String(20))
    
    # Financial information
    credit_limit = db.Column(db.Numeric(12, 2), default=0)
    current_balance = db.Column(db.Numeric(12, 2), default=0)
    payment_terms = db.Column(db.String(100))  # e.g., "Net 30", "Cash on delivery"
    
    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    last_order_date = db.Column(db.DateTime)
    
    # Relationships
    purchase_orders = db.relationship('PurchaseOrder', backref='supplier', lazy='dynamic')
    
    def get_company_name(self, language='ar'):
        """Get company name in specified language"""
        return self.company_name_ar if language == 'ar' else self.company_name_en
    
    def get_contact_person(self, language='ar'):
        """Get contact person name in specified language"""
        return self.contact_person_ar if language == 'ar' else self.contact_person_en
    
    def get_address(self, language='ar'):
        """Get address in specified language"""
        return self.address_ar if language == 'ar' else self.address_en
    
    def get_city(self, language='ar'):
        """Get city in specified language"""
        return self.city_ar if language == 'ar' else self.city_en
    
    def get_country(self, language='ar'):
        """Get country in specified language"""
        return self.country_ar if language == 'ar' else self.country_en
    
    def get_total_purchases(self):
        """Get total purchase amount from this supplier"""
        from models.purchase import PurchaseOrder
        total = db.session.query(db.func.sum(PurchaseOrder.total_amount)).filter_by(
            supplier_id=self.id,
            status='completed'
        ).scalar()
        return total or 0
    
    def get_purchase_count(self):
        """Get total number of completed purchases"""
        from models.purchase import PurchaseOrder
        return PurchaseOrder.query.filter_by(supplier_id=self.id, status='completed').count()

    def get_pending_orders_count(self):
        """Get number of pending orders"""
        from models.purchase import PurchaseOrder
        return PurchaseOrder.query.filter_by(supplier_id=self.id, status='pending').count()
    
    def get_average_order_value(self):
        """Get average order value"""
        total = self.get_total_purchases()
        count = self.get_purchase_count()
        return total / count if count > 0 else 0
    
    def update_last_order(self):
        """Update last order date"""
        self.last_order_date = datetime.utcnow()
    
    def can_order(self, amount):
        """Check if we can place order within credit limit"""
        if self.credit_limit <= 0:
            return True  # No credit limit
        return (self.current_balance + amount) <= self.credit_limit
    
    def add_to_balance(self, amount):
        """Add amount to supplier balance (what we owe them)"""
        self.current_balance += amount
    
    def to_dict(self, language='ar'):
        """Convert supplier to dictionary"""
        return {
            'id': self.id,
            'supplier_code': self.supplier_code,
            'company_name': self.get_company_name(language),
            'contact_person': self.get_contact_person(language),
            'phone': self.phone,
            'email': self.email,
            'website': self.website,
            'address': self.get_address(language),
            'city': self.get_city(language),
            'country': self.get_country(language),
            'commercial_registration': self.commercial_registration,
            'tax_number': self.tax_number,
            'credit_limit': float(self.credit_limit),
            'current_balance': float(self.current_balance),
            'payment_terms': self.payment_terms,
            'total_purchases': float(self.get_total_purchases()),
            'purchase_count': self.get_purchase_count(),
            'pending_orders': self.get_pending_orders_count(),
            'average_order_value': float(self.get_average_order_value()),
            'last_order_date': self.last_order_date.isoformat() if self.last_order_date else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def generate_supplier_code():
        """Generate unique supplier code"""
        import random
        import string
        
        while True:
            code = 'S' + ''.join(random.choices(string.digits, k=6))
            if not Supplier.query.filter_by(supplier_code=code).first():
                return code
    
    def __repr__(self):
        return f'<Supplier {self.supplier_code}: {self.company_name_en}>'
