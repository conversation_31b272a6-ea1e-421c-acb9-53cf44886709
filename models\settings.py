"""
System Settings model for Qatar POS System
Manages application configuration and preferences
"""

from datetime import datetime
from extensions import db

class SystemSettings(db.Model):
    """System settings and configuration"""
    
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Setting identification
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text)
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    
    # Setting metadata
    category = db.Column(db.String(50), nullable=False, default='general')
    data_type = db.Column(db.String(20), nullable=False, default='string')  # string, integer, float, boolean, json
    is_public = db.Column(db.Bo<PERSON>, default=False)  # Can be accessed by non-admin users
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    def get_value(self):
        """Get typed value based on data_type"""
        if self.value is None:
            return None
            
        if self.data_type == 'integer':
            return int(self.value)
        elif self.data_type == 'float':
            return float(self.value)
        elif self.data_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.data_type == 'json':
            import json
            return json.loads(self.value)
        else:
            return self.value
    
    def set_value(self, value):
        """Set value with proper type conversion"""
        if value is None:
            self.value = None
        elif self.data_type == 'json':
            import json
            self.value = json.dumps(value)
        else:
            self.value = str(value)
    
    def get_description(self, language='ar'):
        """Get description in specified language"""
        return self.description_ar if language == 'ar' else self.description_en
    
    @staticmethod
    def get_setting(key, default=None):
        """Get setting value by key"""
        setting = SystemSettings.query.filter_by(key=key).first()
        if setting:
            return setting.get_value()
        return default
    
    @staticmethod
    def set_setting(key, value, description_ar=None, description_en=None, 
                   category='general', data_type='string', is_public=False):
        """Set or update setting"""
        setting = SystemSettings.query.filter_by(key=key).first()
        if not setting:
            setting = SystemSettings(
                key=key,
                category=category,
                data_type=data_type,
                is_public=is_public,
                description_ar=description_ar,
                description_en=description_en
            )
            db.session.add(setting)
        
        setting.set_value(value)
        if description_ar:
            setting.description_ar = description_ar
        if description_en:
            setting.description_en = description_en
        
        db.session.commit()
        return setting
    
    @staticmethod
    def initialize_default_settings():
        """Initialize default system settings"""
        default_settings = [
            # Company Information
            {
                'key': 'company_name_ar',
                'value': 'شركة نقاط البيع القطرية',
                'description_ar': 'اسم الشركة بالعربية',
                'description_en': 'Company name in Arabic',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'company_name_en',
                'value': 'Qatar POS Company',
                'description_ar': 'اسم الشركة بالإنجليزية',
                'description_en': 'Company name in English',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'company_address_ar',
                'value': 'الدوحة، قطر',
                'description_ar': 'عنوان الشركة بالعربية',
                'description_en': 'Company address in Arabic',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'company_address_en',
                'value': 'Doha, Qatar',
                'description_ar': 'عنوان الشركة بالإنجليزية',
                'description_en': 'Company address in English',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'company_phone',
                'value': '+974-4444-5555',
                'description_ar': 'رقم هاتف الشركة',
                'description_en': 'Company phone number',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'company_email',
                'value': '<EMAIL>',
                'description_ar': 'البريد الإلكتروني للشركة',
                'description_en': 'Company email address',
                'category': 'company',
                'is_public': True
            },
            {
                'key': 'commercial_registration',
                'value': '12345678',
                'description_ar': 'رقم السجل التجاري',
                'description_en': 'Commercial registration number',
                'category': 'company'
            },
            {
                'key': 'tax_number',
                'value': '*********',
                'description_ar': 'الرقم الضريبي',
                'description_en': 'Tax identification number',
                'category': 'company'
            },
            
            # System Configuration
            {
                'key': 'default_language',
                'value': 'ar',
                'description_ar': 'اللغة الافتراضية للنظام',
                'description_en': 'Default system language',
                'category': 'system',
                'is_public': True
            },
            {
                'key': 'default_currency',
                'value': 'QAR',
                'description_ar': 'العملة الافتراضية',
                'description_en': 'Default currency',
                'category': 'system',
                'is_public': True
            },
            {
                'key': 'timezone',
                'value': 'Asia/Qatar',
                'description_ar': 'المنطقة الزمنية',
                'description_en': 'System timezone',
                'category': 'system'
            },
            {
                'key': 'working_days',
                'value': '["saturday", "sunday", "monday", "tuesday", "wednesday", "thursday"]',
                'description_ar': 'أيام العمل',
                'description_en': 'Working days',
                'category': 'system',
                'data_type': 'json'
            },
            {
                'key': 'weekend_days',
                'value': '["friday"]',
                'description_ar': 'أيام الإجازة',
                'description_en': 'Weekend days',
                'category': 'system',
                'data_type': 'json'
            },
            
            # POS Settings
            {
                'key': 'auto_print_receipt',
                'value': 'true',
                'description_ar': 'طباعة الإيصال تلقائياً',
                'description_en': 'Auto print receipt',
                'category': 'pos',
                'data_type': 'boolean'
            },
            {
                'key': 'receipt_footer_ar',
                'value': 'شكراً لزيارتكم',
                'description_ar': 'تذييل الإيصال بالعربية',
                'description_en': 'Receipt footer in Arabic',
                'category': 'pos'
            },
            {
                'key': 'receipt_footer_en',
                'value': 'Thank you for your visit',
                'description_ar': 'تذييل الإيصال بالإنجليزية',
                'description_en': 'Receipt footer in English',
                'category': 'pos'
            },
            
            # Tax Settings
            {
                'key': 'tax_enabled',
                'value': 'false',
                'description_ar': 'تفعيل الضرائب',
                'description_en': 'Enable taxes',
                'category': 'tax',
                'data_type': 'boolean'
            },
            {
                'key': 'default_tax_rate',
                'value': '0.0',
                'description_ar': 'معدل الضريبة الافتراضي',
                'description_en': 'Default tax rate',
                'category': 'tax',
                'data_type': 'float'
            }
        ]
        
        for setting_data in default_settings:
            existing = SystemSettings.query.filter_by(key=setting_data['key']).first()
            if not existing:
                setting = SystemSettings(**setting_data)
                db.session.add(setting)
        
        db.session.commit()
    
    def to_dict(self, language='ar'):
        """Convert setting to dictionary"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.get_value(),
            'description': self.get_description(language),
            'category': self.category,
            'data_type': self.data_type,
            'is_public': self.is_public,
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SystemSettings {self.key}: {self.value}>'
