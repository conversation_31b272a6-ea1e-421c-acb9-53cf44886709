#!/usr/bin/env python3
"""
Qatar POS System - Simple Test Server
Minimal setup for testing the system
"""

from flask import Flask, render_template, redirect, url_for, request, flash, session
import os
import sys

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'dev-secret-key-for-testing'

# Simple in-memory user for testing
TEST_USER = {
    'username': 'admin',
    'password': 'admin123',
    'name_ar': 'المدير العام',
    'name_en': 'System Administrator'
}

@app.route('/')
def index():
    """Home page"""
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == TEST_USER['username'] and password == TEST_USER['password']:
            session['logged_in'] = True
            session['username'] = username
            session['language'] = request.form.get('language', 'ar')
            flash('تم تسجيل الدخول بنجاح!' if session['language'] == 'ar' else 'Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة' if request.form.get('language', 'ar') == 'ar' else 'Invalid username or password', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح!' if request.args.get('lang') == 'ar' else 'Logged out successfully!', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """Dashboard page"""
    if 'logged_in' not in session:
        return redirect(url_for('login'))
    
    language = session.get('language', 'ar')
    return render_template_string(DASHBOARD_TEMPLATE, language=language, user=TEST_USER)

@app.route('/test')
def test():
    """Test page"""
    return render_template_string(TEST_TEMPLATE)

@app.route('/health')
def health():
    """Health check"""
    return {'status': 'ok', 'message': 'Qatar POS System is running', 'version': '1.0.0'}

@app.route('/set-language/<language>')
def set_language(language):
    """Set language"""
    if language in ['ar', 'en']:
        session['language'] = language
    return redirect(request.referrer or url_for('index'))

# Template strings
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام نقاط البيع القطري</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); width: 100%; max-width: 400px; }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #333; font-size: 1.8em; margin-bottom: 5px; }
        .logo p { color: #666; font-size: 0.9em; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #333; font-weight: 500; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #667eea; }
        .btn { width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; transition: background 0.3s; }
        .btn:hover { background: #5a6fd8; }
        .alert { padding: 10px; margin-bottom: 20px; border-radius: 5px; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .language-switch { text-align: center; margin-top: 20px; }
        .language-switch a { color: #667eea; text-decoration: none; margin: 0 10px; }
        .test-info { background: #e7f3ff; padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #2196F3; }
        .test-info h4 { color: #1976D2; margin-bottom: 10px; }
        .test-info p { color: #424242; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🇶🇦 نظام نقاط البيع القطري</h1>
            <p>Qatar POS System</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="form-group">
                <label for="language">اللغة / Language</label>
                <select name="language" id="language">
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="username">اسم المستخدم / Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور / Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn">تسجيل الدخول / Login</button>
        </form>
        
        <div class="test-info">
            <h4>🧪 بيانات الاختبار / Test Credentials</h4>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
        </div>
        
        <div class="language-switch">
            <a href="/test">صفحة الاختبار / Test Page</a> |
            <a href="/health">فحص النظام / Health Check</a>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html dir="{{ 'rtl' if language == 'ar' else 'ltr' }}" lang="{{ language }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'لوحة التحكم - نظام نقاط البيع القطري' if language == 'ar' else 'Dashboard - Qatar POS System' }}</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .header { background: #343a40; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { font-size: 1.5em; }
        .header .user-info { display: flex; align-items: center; gap: 15px; }
        .header a { color: #ffc107; text-decoration: none; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-card h3 { color: #333; margin-bottom: 10px; }
        .stat-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .feature-card { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .feature-card h3 { color: #333; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
        .feature-card p { color: #666; line-height: 1.6; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 15px; }
        .btn:hover { background: #0056b3; }
        .success-message { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #c3e6cb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🇶🇦 {{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}</h1>
        <div class="user-info">
            <span>{{ 'مرحباً' if language == 'ar' else 'Welcome' }}, {{ user.name_ar if language == 'ar' else user.name_en }}</span>
            <a href="/set-language/{{ 'en' if language == 'ar' else 'ar' }}">{{ 'English' if language == 'ar' else 'العربية' }}</a>
            <a href="/logout">{{ 'تسجيل الخروج' if language == 'ar' else 'Logout' }}</a>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="success-message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>{{ 'مبيعات اليوم' if language == 'ar' else "Today's Sales" }}</h3>
                <div class="number">{{ '1,250.00 ر.ق' if language == 'ar' else 'QAR 1,250.00' }}</div>
            </div>
            <div class="stat-card">
                <h3>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</h3>
                <div class="number">15</div>
            </div>
            <div class="stat-card">
                <h3>{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h3>
                <div class="number">245</div>
            </div>
            <div class="stat-card">
                <h3>{{ 'العملاء النشطين' if language == 'ar' else 'Active Customers' }}</h3>
                <div class="number">89</div>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3>🛒 {{ 'نقطة البيع' if language == 'ar' else 'Point of Sale' }}</h3>
                <p>{{ 'واجهة سريعة وسهلة لإجراء عمليات البيع وإصدار الفواتير' if language == 'ar' else 'Fast and easy interface for sales transactions and invoice generation' }}</p>
                <a href="#" class="btn">{{ 'فتح نقطة البيع' if language == 'ar' else 'Open POS' }}</a>
            </div>
            
            <div class="feature-card">
                <h3>📦 {{ 'إدارة المنتجات' if language == 'ar' else 'Product Management' }}</h3>
                <p>{{ 'إضافة وتعديل وإدارة المنتجات والفئات والأسعار' if language == 'ar' else 'Add, edit, and manage products, categories, and prices' }}</p>
                <a href="#" class="btn">{{ 'إدارة المنتجات' if language == 'ar' else 'Manage Products' }}</a>
            </div>
            
            <div class="feature-card">
                <h3>👥 {{ 'إدارة العملاء' if language == 'ar' else 'Customer Management' }}</h3>
                <p>{{ 'إدارة بيانات العملاء وتاريخ المشتريات وبرامج الولاء' if language == 'ar' else 'Manage customer data, purchase history, and loyalty programs' }}</p>
                <a href="#" class="btn">{{ 'إدارة العملاء' if language == 'ar' else 'Manage Customers' }}</a>
            </div>
            
            <div class="feature-card">
                <h3>📊 {{ 'التقارير والتحليلات' if language == 'ar' else 'Reports & Analytics' }}</h3>
                <p>{{ 'تقارير مفصلة للمبيعات والمخزون والأداء المالي' if language == 'ar' else 'Detailed reports for sales, inventory, and financial performance' }}</p>
                <a href="#" class="btn">{{ 'عرض التقارير' if language == 'ar' else 'View Reports' }}</a>
            </div>
        </div>
    </div>
</body>
</html>
'''

TEST_TEMPLATE = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - نظام نقاط البيع القطري</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb; }
        .info { background: #e7f3ff; color: #0c5460; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #b8daff; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature { background: #f8f9fa; padding: 20px; border-radius: 8px; border-right: 4px solid #007bff; }
        .feature h3 { color: #333; margin-bottom: 10px; }
        .feature p { color: #666; }
        .links { text-align: center; margin-top: 30px; }
        .links a { display: inline-block; margin: 10px; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; transition: background 0.3s; }
        .links a:hover { background: #0056b3; }
        .status { display: flex; align-items: center; justify-content: center; gap: 10px; font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        
        <div class="success">
            <div class="status">
                <span>✅</span>
                <strong>النظام يعمل بنجاح!</strong>
                <span>System is running successfully!</span>
            </div>
        </div>
        
        <div class="info">
            <h3>📍 معلومات الخادم / Server Information</h3>
            <p><strong>URL:</strong> http://localhost:5000</p>
            <p><strong>Status:</strong> Active / نشط</p>
            <p><strong>Version:</strong> 1.0.0</p>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🌐 دعم متعدد اللغات</h3>
                <p>Arabic & English Support</p>
            </div>
            <div class="feature">
                <h3>💰 الريال القطري</h3>
                <p>QAR Currency Support</p>
            </div>
            <div class="feature">
                <h3>📅 أسبوع العمل القطري</h3>
                <p>6-Day Work Week</p>
            </div>
            <div class="feature">
                <h3>🆔 الهوية القطرية</h3>
                <p>Qatar ID Support</p>
            </div>
        </div>
        
        <div class="links">
            <a href="/">الصفحة الرئيسية / Home</a>
            <a href="/login">تسجيل الدخول / Login</a>
            <a href="/health">فحص النظام / Health Check</a>
        </div>
    </div>
</body>
</html>
'''

def render_template_string(template_string, **context):
    """Simple template rendering"""
    from jinja2 import Template
    template = Template(template_string)
    return template.render(**context)

if __name__ == '__main__':
    print("🚀 Starting Qatar POS System (Simple Mode)...")
    print("🇶🇦 نظام نقاط البيع القطري")
    print("=" * 50)
    print("📍 Server: http://localhost:5000")
    print("🔑 Login: admin / admin123")
    print("🌐 Test: http://localhost:5000/test")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
