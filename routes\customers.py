"""
Customer management routes for Qatar POS System
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models.customer import Customer
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language, validate_email, validate_qatar_id

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@login_required
@permission_required('customers_read')
def index():
    """List all customers"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    customer_type = request.args.get('type', '')
    
    query = Customer.query
    
    if search:
        query = query.filter(
            (Customer.first_name_ar.contains(search)) |
            (Customer.first_name_en.contains(search)) |
            (Customer.last_name_ar.contains(search)) |
            (Customer.last_name_en.contains(search)) |
            (Customer.company_name_ar.contains(search)) |
            (Customer.company_name_en.contains(search)) |
            (Customer.phone.contains(search)) |
            (Customer.customer_code.contains(search))
        )
    
    if customer_type:
        query = query.filter(Customer.customer_type == customer_type)
    
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('customers/index.html',
                         customers=customers,
                         search=search,
                         customer_type=customer_type,
                         language=language)

@customers_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('customers_write')
def create():
    """Create new customer"""
    language = get_user_language()
    
    if request.method == 'POST':
        customer_type = request.form.get('customer_type', 'individual')
        
        # Common fields
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip() or None
        address_ar = request.form.get('address_ar', '').strip() or None
        address_en = request.form.get('address_en', '').strip() or None
        city_ar = request.form.get('city_ar', '').strip() or None
        city_en = request.form.get('city_en', '').strip() or None
        postal_code = request.form.get('postal_code', '').strip() or None
        qatar_id = request.form.get('qatar_id', '').strip() or None
        
        # Individual or company specific fields
        if customer_type == 'individual':
            first_name_ar = request.form.get('first_name_ar', '').strip()
            first_name_en = request.form.get('first_name_en', '').strip()
            last_name_ar = request.form.get('last_name_ar', '').strip()
            last_name_en = request.form.get('last_name_en', '').strip()
            company_name_ar = None
            company_name_en = None
            commercial_registration = None
            tax_number = None
        else:
            first_name_ar = None
            first_name_en = None
            last_name_ar = None
            last_name_en = None
            company_name_ar = request.form.get('company_name_ar', '').strip()
            company_name_en = request.form.get('company_name_en', '').strip()
            commercial_registration = request.form.get('commercial_registration', '').strip() or None
            tax_number = request.form.get('tax_number', '').strip() or None
        
        # Validation
        errors = []
        
        if customer_type == 'individual':
            if not first_name_ar or not first_name_en:
                errors.append('الاسم الأول مطلوب بالعربية والإنجليزية' if language == 'ar' 
                             else 'First name is required in both Arabic and English')
            if not last_name_ar or not last_name_en:
                errors.append('اسم العائلة مطلوب بالعربية والإنجليزية' if language == 'ar' 
                             else 'Last name is required in both Arabic and English')
        else:
            if not company_name_ar or not company_name_en:
                errors.append('اسم الشركة مطلوب بالعربية والإنجليزية' if language == 'ar' 
                             else 'Company name is required in both Arabic and English')
        
        if not phone:
            errors.append('رقم الهاتف مطلوب' if language == 'ar' else 'Phone number is required')
        
        if email and not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format')
        
        if qatar_id:
            if not validate_qatar_id(qatar_id):
                errors.append('رقم الهوية القطرية غير صحيح' if language == 'ar' 
                             else 'Invalid Qatar ID format')
            elif Customer.query.filter_by(qatar_id=qatar_id).first():
                errors.append('رقم الهوية القطرية موجود بالفعل' if language == 'ar' 
                             else 'Qatar ID already exists')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('customers/create.html', language=language)
        
        try:
            customer = Customer(
                customer_code=Customer.generate_customer_code(),
                customer_type=customer_type,
                first_name_ar=first_name_ar,
                first_name_en=first_name_en,
                last_name_ar=last_name_ar,
                last_name_en=last_name_en,
                company_name_ar=company_name_ar,
                company_name_en=company_name_en,
                phone=phone,
                email=email,
                address_ar=address_ar,
                address_en=address_en,
                city_ar=city_ar,
                city_en=city_en,
                postal_code=postal_code,
                qatar_id=qatar_id,
                commercial_registration=commercial_registration,
                tax_number=tax_number
            )
            
            db.session.add(customer)
            db.session.commit()
            
            flash('تم إنشاء العميل بنجاح' if language == 'ar' 
                  else 'Customer created successfully', 'success')
            return redirect(url_for('customers.view', customer_id=customer.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء العميل' if language == 'ar'
                  else 'Error creating customer', 'error')
    
    return render_template('customers/create.html', language=language)

@customers_bp.route('/<int:customer_id>')
@login_required
@permission_required('customers_read')
def view(customer_id):
    """View customer details"""
    language = get_user_language()
    customer = Customer.query.get_or_404(customer_id)
    
    # Get recent sales
    from models.sale import Sale
    recent_sales = Sale.query.filter_by(customer_id=customer.id).order_by(Sale.created_at.desc()).limit(10).all()
    
    return render_template('customers/view.html',
                         customer=customer,
                         recent_sales=recent_sales,
                         language=language)

@customers_bp.route('/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('customers_write')
def edit(customer_id):
    """Edit customer"""
    language = get_user_language()
    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        customer_type = request.form.get('customer_type', customer.customer_type)

        # Common fields
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip() or None
        address_ar = request.form.get('address_ar', '').strip() or None
        address_en = request.form.get('address_en', '').strip() or None
        city_ar = request.form.get('city_ar', '').strip() or None
        city_en = request.form.get('city_en', '').strip() or None
        postal_code = request.form.get('postal_code', '').strip() or None
        qatar_id = request.form.get('qatar_id', '').strip() or None
        is_active = bool(request.form.get('is_active'))

        # Individual or company specific fields
        if customer_type == 'individual':
            first_name_ar = request.form.get('first_name_ar', '').strip()
            first_name_en = request.form.get('first_name_en', '').strip()
            last_name_ar = request.form.get('last_name_ar', '').strip()
            last_name_en = request.form.get('last_name_en', '').strip()
            company_name_ar = None
            company_name_en = None
            commercial_registration = None
            tax_number = None
        else:
            first_name_ar = None
            first_name_en = None
            last_name_ar = None
            last_name_en = None
            company_name_ar = request.form.get('company_name_ar', '').strip()
            company_name_en = request.form.get('company_name_en', '').strip()
            commercial_registration = request.form.get('commercial_registration', '').strip() or None
            tax_number = request.form.get('tax_number', '').strip() or None

        # Validation
        errors = []

        if customer_type == 'individual':
            if not first_name_ar or not first_name_en:
                errors.append('الاسم الأول مطلوب بالعربية والإنجليزية' if language == 'ar'
                             else 'First name is required in both Arabic and English')
            if not last_name_ar or not last_name_en:
                errors.append('اسم العائلة مطلوب بالعربية والإنجليزية' if language == 'ar'
                             else 'Last name is required in both Arabic and English')
        else:
            if not company_name_ar or not company_name_en:
                errors.append('اسم الشركة مطلوب بالعربية والإنجليزية' if language == 'ar'
                             else 'Company name is required in both Arabic and English')

        if not phone:
            errors.append('رقم الهاتف مطلوب' if language == 'ar' else 'Phone number is required')

        if email and not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format')

        if qatar_id:
            if not validate_qatar_id(qatar_id):
                errors.append('رقم الهوية القطرية غير صحيح' if language == 'ar'
                             else 'Invalid Qatar ID format')
            elif Customer.query.filter(Customer.qatar_id == qatar_id, Customer.id != customer.id).first():
                errors.append('رقم الهوية القطرية موجود بالفعل' if language == 'ar'
                             else 'Qatar ID already exists')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('customers/edit.html', customer=customer, language=language)

        try:
            customer.customer_type = customer_type
            customer.first_name_ar = first_name_ar
            customer.first_name_en = first_name_en
            customer.last_name_ar = last_name_ar
            customer.last_name_en = last_name_en
            customer.company_name_ar = company_name_ar
            customer.company_name_en = company_name_en
            customer.phone = phone
            customer.email = email
            customer.address_ar = address_ar
            customer.address_en = address_en
            customer.city_ar = city_ar
            customer.city_en = city_en
            customer.postal_code = postal_code
            customer.qatar_id = qatar_id
            customer.commercial_registration = commercial_registration
            customer.tax_number = tax_number
            customer.is_active = is_active

            db.session.commit()

            flash('تم تحديث العميل بنجاح' if language == 'ar'
                  else 'Customer updated successfully', 'success')
            return redirect(url_for('customers.view', customer_id=customer.id))

        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث العميل' if language == 'ar'
                  else 'Error updating customer', 'error')

    return render_template('customers/edit.html', customer=customer, language=language)

@customers_bp.route('/api/search')
@login_required
@permission_required('customers_read')
def api_search():
    """API endpoint for customer search"""
    query = request.args.get('q', '')
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify([])
    
    customers = Customer.query.filter(
        (Customer.first_name_ar.contains(query)) |
        (Customer.first_name_en.contains(query)) |
        (Customer.last_name_ar.contains(query)) |
        (Customer.last_name_en.contains(query)) |
        (Customer.company_name_ar.contains(query)) |
        (Customer.company_name_en.contains(query)) |
        (Customer.phone.contains(query)) |
        (Customer.customer_code.contains(query)),
        Customer.is_active == True
    ).limit(limit).all()
    
    language = get_user_language()
    return jsonify([customer.to_dict(language) for customer in customers])
