<!DOCTYPE html>
<html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'فاتورة رقم' if language == 'ar' else 'Invoice #' }} {{ sale.sale_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .invoice {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }

        .company-info {
            text-align: {{ 'right' if language == 'ar' else 'left' }};
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }

        .invoice-title {
            text-align: {{ 'left' if language == 'ar' else 'right' }};
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
        }

        .invoice-number {
            font-size: 20px;
            font-weight: bold;
            margin-top: 5px;
        }

        .details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 20px;
        }

        .customer-info, .invoice-details {
            flex: 1;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }

        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #007bff;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: {{ 'right' if language == 'ar' else 'left' }};
        }

        .items-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }

        .items-table .text-center {
            text-align: center;
        }

        .items-table .text-right {
            text-align: right;
        }

        .totals-section {
            float: {{ 'left' if language == 'ar' else 'right' }};
            width: 300px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }

        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }

        .totals-table .total-row {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }

        .totals-table .total-row td {
            padding: 10px 5px;
        }

        .qr-section {
            text-align: center;
            margin-top: 30px;
            clear: both;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .stamp-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 20px 0;
        }

        .company-stamp {
            text-align: {{ 'right' if language == 'ar' else 'left' }};
            width: 200px;
        }

        .stamp-placeholder {
            width: 150px;
            height: 150px;
            border: 2px dashed #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            background: radial-gradient(circle, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0.05) 70%);
            position: relative;
        }

        .stamp-placeholder::before {
            content: '';
            position: absolute;
            width: 120px;
            height: 120px;
            border: 1px solid #007bff;
            border-radius: 50%;
        }

        .stamp-text {
            text-align: center;
            font-size: 10px;
            color: #007bff;
            font-weight: bold;
            line-height: 1.2;
        }

        .signature-section {
            text-align: {{ 'left' if language == 'ar' else 'right' }};
            width: 200px;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            width: 150px;
            height: 50px;
            margin: 0 auto 10px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                font-size: 11px;
            }

            .invoice {
                max-width: none;
                padding: 0;
                margin: 0;
            }

            .details-section {
                display: block;
            }

            .customer-info, .invoice-details {
                margin-bottom: 15px;
                page-break-inside: avoid;
            }

            .items-table {
                page-break-inside: avoid;
            }

            .totals-section {
                float: none;
                width: 100%;
                margin-top: 20px;
            }
        }

        @page {
            margin: 1cm;
            size: A4;
        }
    </style>
</head>
<body>
    <button onclick="window.print()" class="no-print">
        {{ 'طباعة' if language == 'ar' else 'Print' }}
    </button>

    <div class="invoice">
        <!-- Header -->
        <div class="header">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 60%;">
                        <div class="company-info">
                            {% if settings.get('company_logo') %}
                            <div class="company-logo" style="margin-bottom: 10px;">
                                <img src="{{ url_for('static', filename='uploads/logos/' + settings.get('company_logo')) }}"
                                     alt="Company Logo"
                                     style="max-width: {{ settings.get('logo_width', '150') }}px; max-height: {{ settings.get('logo_height', '80') }}px;">
                            </div>
                            {% endif %}
                            <div class="company-name">
                                {{ settings.get('company_name_ar') if language == 'ar' else settings.get('company_name_en') }}
                            </div>
                            <div>{{ settings.get('company_address_ar') if language == 'ar' else settings.get('company_address_en') }}</div>
                            <div><strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong> {{ settings.get('company_phone') }}</div>
                            <div><strong>{{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}</strong> {{ settings.get('company_email') }}</div>

                            {% if settings.get('company_cr_number') %}
                            <div><strong>{{ 'رقم السجل التجاري:' if language == 'ar' else 'CR Number:' }}</strong> {{ settings.get('company_cr_number') }}</div>
                            {% endif %}
                        </div>
                    </td>
                    <td style="width: 40%; text-align: {{ 'left' if language == 'ar' else 'right' }};">
                        <div class="invoice-title">{{ 'فاتورة' if language == 'ar' else 'INVOICE' }}</div>
                        <div class="invoice-number">{{ sale.sale_number }}</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Details Section -->
        <div class="details-section">
            <div class="customer-info">
                <div class="section-title">{{ 'بيانات العميل' if language == 'ar' else 'Customer Information' }}</div>
                {% if sale.customer %}
                    <div><strong>{{ 'الاسم:' if language == 'ar' else 'Name:' }}</strong> {{ sale.customer.get_display_name(language) }}</div>
                    {% if sale.customer.phone %}
                    <div><strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong> {{ sale.customer.phone }}</div>
                    {% endif %}
                    {% if sale.customer.email %}
                    <div><strong>{{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}</strong> {{ sale.customer.email }}</div>
                    {% endif %}
                    {% if sale.customer.address %}
                    <div><strong>{{ 'العنوان:' if language == 'ar' else 'Address:' }}</strong> {{ sale.customer.address }}</div>
                    {% endif %}
                {% else %}
                    <div>{{ 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</div>
                {% endif %}
            </div>

            <div class="invoice-details">
                <div class="section-title">{{ 'تفاصيل الفاتورة' if language == 'ar' else 'Invoice Details' }}</div>
                <div><strong>{{ 'رقم الفاتورة:' if language == 'ar' else 'Invoice Number:' }}</strong> {{ sale.sale_number }}</div>
                <div><strong>{{ 'تاريخ الفاتورة:' if language == 'ar' else 'Invoice Date:' }}</strong> {{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</div>
                <div><strong>{{ 'البائع:' if language == 'ar' else 'Seller:' }}</strong> {{ sale.seller.get_full_name(language) }}</div>
                <div><strong>{{ 'طريقة الدفع:' if language == 'ar' else 'Payment Method:' }}</strong>
                    {% if sale.payment_method == 'cash' %}
                        {{ 'نقدي' if language == 'ar' else 'Cash' }}
                    {% elif sale.payment_method == 'card' %}
                        {{ 'بطاقة' if language == 'ar' else 'Card' }}
                    {% elif sale.payment_method == 'bank_transfer' %}
                        {{ 'تحويل بنكي' if language == 'ar' else 'Bank Transfer' }}
                    {% else %}
                        {{ sale.payment_method }}
                    {% endif %}
                </div>
                <div><strong>{{ 'حالة الدفع:' if language == 'ar' else 'Payment Status:' }}</strong>
                    {% if sale.payment_status == 'paid' %}
                        {{ 'مدفوع' if language == 'ar' else 'Paid' }}
                    {% elif sale.payment_status == 'partial' %}
                        {{ 'مدفوع جزئياً' if language == 'ar' else 'Partially Paid' }}
                    {% else %}
                        {{ 'غير مدفوع' if language == 'ar' else 'Unpaid' }}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 15%;">{{ 'رمز المنتج' if language == 'ar' else 'SKU' }}</th>
                    <th style="width: 35%;">{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                    <th style="width: 10%;">{{ 'الكمية' if language == 'ar' else 'Qty' }}</th>
                    <th style="width: 15%;">{{ 'سعر الوحدة' if language == 'ar' else 'Unit Price' }}</th>
                    <th style="width: 10%;">{{ 'الخصم' if language == 'ar' else 'Discount' }}</th>
                    <th style="width: 15%;">{{ 'المجموع' if language == 'ar' else 'Total' }}</th>
                </tr>
            </thead>
            <tbody>
                {% for item in sale.items %}
                <tr>
                    <td class="text-center">{{ loop.index }}</td>
                    <td>{{ item.product.sku }}</td>
                    <td>{{ item.product.get_name(language) }}</td>
                    <td class="text-center">{{ item.quantity }}</td>
                    <td class="text-right">{{ '%.2f'|format(item.unit_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                    <td class="text-right">{{ '%.2f'|format(item.discount_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                    <td class="text-right"><strong>{{ '%.2f'|format(item.total_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td><strong>{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</strong></td>
                    <td class="text-right"><strong>{{ '%.2f'|format(sale.subtotal) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% if sale.discount_amount > 0 %}
                <tr>
                    <td><strong>{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</strong></td>
                    <td class="text-right"><strong>-{{ '%.2f'|format(sale.discount_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% endif %}
                {% if sale.tax_amount > 0 %}
                <tr>
                    <td><strong>{{ 'ضريبة القيمة المضافة:' if language == 'ar' else 'VAT:' }}</strong></td>
                    <td class="text-right"><strong>{{ '%.2f'|format(sale.tax_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% endif %}
                <tr class="total-row">
                    <td><strong>{{ 'المجموع الكلي:' if language == 'ar' else 'Total Amount:' }}</strong></td>
                    <td class="text-right"><strong>{{ '%.2f'|format(sale.total_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                <tr>
                    <td><strong>{{ 'المبلغ المدفوع:' if language == 'ar' else 'Amount Paid:' }}</strong></td>
                    <td class="text-right"><strong>{{ '%.2f'|format(sale.amount_paid) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% if sale.amount_due > 0 %}
                <tr>
                    <td><strong>{{ 'المبلغ المستحق:' if language == 'ar' else 'Amount Due:' }}</strong></td>
                    <td class="text-right"><strong>{{ '%.2f'|format(sale.amount_due) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong></td>
                </tr>
                {% endif %}
            </table>
        </div>

        <div style="clear: both;"></div>

        <!-- Notes Section -->
        {% if sale.notes %}
        <div style="margin-top: 20px;">
            <div class="section-title">{{ 'ملاحظات:' if language == 'ar' else 'Notes:' }}</div>
            <div>{{ sale.notes }}</div>
        </div>
        {% endif %}

        <!-- Stamp and Signature Section -->
        <div class="stamp-section">
            <div class="company-stamp">
                {% if settings.get('company_stamp') %}
                    <!-- Real Company Stamp -->
                    <div style="text-align: center; margin-bottom: 10px;">
                        <img src="{{ url_for('static', filename='uploads/stamps/' + settings.get('company_stamp')) }}"
                             alt="Company Stamp"
                             style="max-width: {{ settings.get('stamp_width', '120') }}px; max-height: {{ settings.get('stamp_height', '120') }}px;">
                    </div>
                {% else %}
                    <!-- Placeholder Stamp -->
                    <div class="stamp-placeholder">
                        <div class="stamp-text">
                            {{ settings.get('company_name_ar') if language == 'ar' else settings.get('company_name_en') }}<br>
                            {{ 'ختم الشركة' if language == 'ar' else 'COMPANY SEAL' }}
                        </div>
                    </div>
                {% endif %}
                <div style="text-align: center; font-size: 12px; font-weight: bold;">
                    {{ 'ختم الشركة' if language == 'ar' else 'Company Stamp' }}
                </div>
            </div>

            <div class="signature-section">
                {% if settings.get('manager_signature') %}
                    <!-- Real Manager Signature -->
                    <div style="text-align: center; margin-bottom: 10px;">
                        <img src="{{ url_for('static', filename='uploads/signatures/' + settings.get('manager_signature')) }}"
                             alt="Manager Signature"
                             style="max-width: {{ settings.get('signature_width', '200') }}px; max-height: {{ settings.get('signature_height', '80') }}px;">
                    </div>
                    <div style="text-align: center; font-size: 12px; font-weight: bold; margin-top: 5px;">
                        {{ settings.get('manager_name', 'المدير العام' if language == 'ar' else 'General Manager') }}
                    </div>
                    <div style="text-align: center; font-size: 10px; color: #666; margin-top: 2px;">
                        {{ settings.get('manager_title', 'المدير العام' if language == 'ar' else 'General Manager') }}
                    </div>
                {% else %}
                    <!-- Placeholder Signature -->
                    <div class="signature-line"></div>
                    <div style="text-align: center; font-size: 12px; font-weight: bold; margin-top: 10px;">
                        {{ settings.get('manager_name', 'المدير العام' if language == 'ar' else 'General Manager') }}
                    </div>
                    <div style="text-align: center; font-size: 10px; color: #666; margin-top: 2px;">
                        {{ settings.get('manager_title', 'المدير العام' if language == 'ar' else 'General Manager') }}
                    </div>
                {% endif %}
                <div style="text-align: center; font-size: 11px; font-weight: bold; margin-top: 10px; border-top: 1px solid #ddd; padding-top: 5px;">
                    {{ 'توقيع المسؤول' if language == 'ar' else 'Authorized Signature' }}
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="font-weight: bold; margin-bottom: 10px;">
                {{ 'شكراً لتعاملكم معنا' if language == 'ar' else 'Thank you for your business!' }}
            </div>
            {% if settings.get('receipt_footer_ar') and language == 'ar' %}
                <div style="font-size: 10px;">{{ settings.get('receipt_footer_ar') }}</div>
            {% elif settings.get('receipt_footer_en') and language == 'en' %}
                <div style="font-size: 10px;">{{ settings.get('receipt_footer_en') }}</div>
            {% endif %}
        </div>
    </div>



    <script>
    // Auto print when page loads (optional)
    // window.addEventListener('load', function() {
    //     setTimeout(function() {
    //         window.print();
    //     }, 1000);
    // });
    </script>
</body>
</html>