#!/usr/bin/env python3
"""
🇶🇦 نظام نقاط البيع القطري - ابدأ من هنا
Qatar POS System - START HERE

This is the main entry point for the Qatar POS System.
Run this file to automatically set up and start the system.
"""

import sys
import os
import subprocess
import time

def print_welcome():
    """Print welcome message"""
    print("=" * 60)
    print("🇶🇦 مرحباً بك في نظام نقاط البيع القطري")
    print("   Welcome to Qatar POS System")
    print("=" * 60)
    print()
    print("هذا الملف سيقوم بإعداد وتشغيل النظام تلقائياً")
    print("This file will automatically set up and run the system")
    print()

def check_python():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    print(f"   Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3:
        print("❌ Python 3 is required")
        print("💡 Download from: https://python.org/downloads/")
        return False
    
    print("✅ Python version OK")
    return True

def install_flask():
    """Install Flask if not available"""
    print("\n📦 Checking Flask...")
    
    try:
        import flask
        print(f"✅ Flask {flask.__version__} found")
        return True
    except ImportError:
        print("⚠️ Flask not found, installing...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 'flask'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Test import again
            import flask
            print(f"✅ Flask {flask.__version__} installed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install Flask: {e}")
            print("💡 Try manually: pip install flask")
            return False

def find_working_server():
    """Find a working server file"""
    print("\n🔍 Finding best server file...")
    
    # List of server files in order of preference
    server_files = [
        ('test_server.py', 'Simple test server (recommended for first run)'),
        ('simple_run.py', 'Basic POS system with login'),
        ('run.py', 'Full POS system'),
        ('app.py', 'Main application file')
    ]
    
    for filename, description in server_files:
        if os.path.exists(filename):
            print(f"✅ Found {filename} - {description}")
            return filename
    
    print("❌ No server files found")
    return None

def create_minimal_server():
    """Create a minimal server if none exists"""
    print("\n🛠️ Creating minimal server...")
    
    server_code = '''#!/usr/bin/env python3
"""
Qatar POS System - Minimal Server
Created automatically by start_here.py
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>نظام نقاط البيع القطري</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                margin: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }
            h1 { font-size: 2.5em; margin-bottom: 20px; }
            .success { font-size: 1.5em; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🇶🇦 نظام نقاط البيع القطري</h1>
            <h2>Qatar POS System</h2>
            <div class="success">✅ النظام يعمل بنجاح!</div>
            <div class="success">✅ System Running Successfully!</div>
            <p>تم إنشاء هذا الخادم تلقائياً بواسطة start_here.py</p>
            <p>This server was created automatically by start_here.py</p>
        </div>
    </body>
    </html>
    """

@app.route('/health')
def health():
    return {
        'status': 'ok',
        'message': 'Qatar POS System is running',
        'created_by': 'start_here.py'
    }

if __name__ == '__main__':
    print("🚀 Starting Qatar POS System...")
    print("🌐 Open: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
    
    try:
        with open('minimal_server.py', 'w', encoding='utf-8') as f:
            f.write(server_code)
        print("✅ Created minimal_server.py")
        return 'minimal_server.py'
    except Exception as e:
        print(f"❌ Failed to create server: {e}")
        return None

def start_server(filename):
    """Start the server"""
    print(f"\n🚀 Starting {filename}...")
    print("=" * 40)
    print("📍 Server URL: http://localhost:5000")
    print("🌐 Test page: http://localhost:5000/test")
    print("💚 Health check: http://localhost:5000/health")
    print("=" * 40)
    print("Press Ctrl+C to stop the server")
    print("=" * 40)
    
    try:
        subprocess.run([sys.executable, filename])
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Thank you for using Qatar POS System!")
        print("👋 تم إيقاف الخادم. شكراً لاستخدام نظام نقاط البيع القطري!")

def main():
    """Main function"""
    print_welcome()
    
    # Step 1: Check Python
    if not check_python():
        input("Press Enter to exit...")
        return
    
    # Step 2: Install Flask
    if not install_flask():
        input("Press Enter to exit...")
        return
    
    # Step 3: Find working server
    server_file = find_working_server()
    
    # Step 4: Create minimal server if needed
    if not server_file:
        server_file = create_minimal_server()
    
    if not server_file:
        print("❌ Cannot create or find server file")
        input("Press Enter to exit...")
        return
    
    # Step 5: Start server
    print(f"\n🎉 Setup complete! Starting {server_file}...")
    time.sleep(2)
    start_server(server_file)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("💡 Try running one of these files manually:")
        print("   python test_server.py")
        print("   python simple_run.py")
        print("   python run.py")
        input("Press Enter to exit...")
