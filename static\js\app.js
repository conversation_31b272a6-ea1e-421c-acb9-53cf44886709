// Qatar POS System - Main JavaScript File

// Global variables
let currentLanguage = document.documentElement.lang || 'ar';
let isRTL = currentLanguage === 'ar';

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize number formatting
    initializeNumberFormatting();
    
    // Initialize auto-refresh for dashboard
    if (window.location.pathname === '/') {
        initializeDashboardRefresh();
    }
}

// Tooltip initialization
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Form validation
function initializeFormValidation() {
    // Bootstrap form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // Custom validation rules
    addCustomValidationRules();
}

function addCustomValidationRules() {
    // Qatar ID validation
    const qatarIdInputs = document.querySelectorAll('input[name="qatar_id"]');
    qatarIdInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validateQatarId(this);
        });
    });
    
    // Phone number validation
    const phoneInputs = document.querySelectorAll('input[type="tel"], input[name*="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validatePhoneNumber(this);
        });
    });
    
    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validateEmail(this);
        });
    });
}

// Validation functions
function validateQatarId(input) {
    const value = input.value.replace(/\D/g, '');
    const isValid = value.length === 11;
    
    input.setCustomValidity(isValid ? '' : 
        (currentLanguage === 'ar' ? 'رقم الهوية القطرية يجب أن يكون 11 رقم' : 'Qatar ID must be 11 digits'));
}

function validatePhoneNumber(input) {
    const value = input.value.replace(/\D/g, '');
    const isValid = value.length >= 8 && value.length <= 12;
    
    input.setCustomValidity(isValid ? '' : 
        (currentLanguage === 'ar' ? 'رقم الهاتف غير صحيح' : 'Invalid phone number'));
}

function validateEmail(input) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(input.value);
    
    input.setCustomValidity(isValid ? '' : 
        (currentLanguage === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email address'));
}

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(function(input) {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performSearch(this);
            }, 300);
        });
    });
}

function performSearch(input) {
    const searchTerm = input.value.trim();
    const targetTable = input.getAttribute('data-target');
    
    if (targetTable) {
        filterTable(targetTable, searchTerm);
    }
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        const text = row.textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());
        row.style.display = matches ? '' : 'none';
    });
}

// Number formatting
function initializeNumberFormatting() {
    const numberInputs = document.querySelectorAll('input[type="number"], .format-number');
    numberInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            formatNumber(this);
        });
    });
    
    // Format existing numbers on page load
    const formattedNumbers = document.querySelectorAll('.currency, .number-format');
    formattedNumbers.forEach(function(element) {
        const value = parseFloat(element.textContent);
        if (!isNaN(value)) {
            element.textContent = formatCurrency(value);
        }
    });
}

function formatNumber(input) {
    const value = parseFloat(input.value);
    if (!isNaN(value)) {
        input.value = value.toLocaleString();
    }
}

function formatCurrency(amount, currency = 'QAR') {
    const formatted = amount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    
    if (currentLanguage === 'ar') {
        return `${formatted} ر.ق`;
    } else {
        return `${currency} ${formatted}`;
    }
}

// Dashboard refresh
function initializeDashboardRefresh() {
    // Refresh dashboard stats every 5 minutes
    setInterval(refreshDashboardStats, 300000);
}

function refreshDashboardStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            updateDashboardStats(data);
        })
        .catch(error => {
            console.error('Error refreshing dashboard stats:', error);
        });
}

function updateDashboardStats(stats) {
    // Update stat cards
    const statElements = {
        'today_sales': document.querySelector('[data-stat="today_sales"]'),
        'today_transactions': document.querySelector('[data-stat="today_transactions"]'),
        'total_products': document.querySelector('[data-stat="total_products"]'),
        'low_stock_count': document.querySelector('[data-stat="low_stock_count"]')
    };
    
    Object.keys(statElements).forEach(key => {
        const element = statElements[key];
        if (element && stats[key] !== undefined) {
            if (key === 'today_sales') {
                element.textContent = formatCurrency(stats[key]);
            } else {
                element.textContent = stats[key].toLocaleString();
            }
        }
    });
}

// Utility functions
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || document.body;
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLoading(element) {
    const spinner = document.createElement('div');
    spinner.className = 'spinner-border spinner-border-sm';
    spinner.setAttribute('role', 'status');
    
    const originalContent = element.innerHTML;
    element.innerHTML = '';
    element.appendChild(spinner);
    element.disabled = true;
    
    return function hideLoading() {
        element.innerHTML = originalContent;
        element.disabled = false;
    };
}

function confirmAction(message, callback) {
    const confirmMessage = message || 
        (currentLanguage === 'ar' ? 'هل أنت متأكد؟' : 'Are you sure?');
    
    if (confirm(confirmMessage)) {
        callback();
    }
}

// AJAX helper functions
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    return fetch(url, mergedOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
}

function postData(url, data) {
    return makeRequest(url, {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

// Export functions for global use
window.POS = {
    showAlert,
    showLoading,
    confirmAction,
    makeRequest,
    postData,
    formatCurrency,
    validateQatarId,
    validatePhoneNumber,
    validateEmail
};
