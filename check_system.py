#!/usr/bin/env python3
"""
Qatar POS System - System Check
Check if all requirements are met
"""

import sys
import os
import subprocess
import importlib

def print_header():
    print("🇶🇦 نظام نقاط البيع القطري - فحص النظام")
    print("Qatar POS System - System Check")
    print("=" * 50)

def check_python():
    print("\n🐍 Checking Python...")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version OK")
        return True

def check_module(module_name, package_name=None):
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name}")
        return True
    except ImportError:
        print(f"❌ {module_name} (install: pip install {package_name or module_name})")
        return False

def check_modules():
    print("\n📦 Checking Python modules...")
    
    modules = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('flask_migrate', 'Flask-Migrate'),
        ('flask_wtf', 'Flask-WTF'),
        ('werkzeug', 'Werkzeug'),
        ('jinja2', 'Jinja2'),
        ('wtforms', 'WTForms'),
    ]
    
    all_ok = True
    for module, package in modules:
        if not check_module(module, package):
            all_ok = False
    
    return all_ok

def check_files():
    print("\n📁 Checking project files...")
    
    required_files = [
        'app.py',
        'config.py',
        'extensions.py',
        'run.py',
        'simple_run.py',
        'test_server.py'
    ]
    
    all_ok = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            all_ok = False
    
    return all_ok

def check_directories():
    print("\n📂 Checking directories...")
    
    required_dirs = [
        'templates',
        'static',
        'models',
        'routes',
        'utils'
    ]
    
    all_ok = True
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/")
            all_ok = False
    
    return all_ok

def test_flask():
    print("\n🧪 Testing Flask...")
    try:
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/test')
        def test():
            return "OK"
        
        with app.test_client() as client:
            response = client.get('/test')
            if response.status_code == 200:
                print("✅ Flask test passed")
                return True
            else:
                print("❌ Flask test failed")
                return False
    except Exception as e:
        print(f"❌ Flask test error: {e}")
        return False

def check_port():
    print("\n🌐 Checking port 5000...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("⚠️ Port 5000 is already in use")
            return False
        else:
            print("✅ Port 5000 is available")
            return True
    except Exception as e:
        print(f"❌ Port check error: {e}")
        return False

def provide_solutions():
    print("\n💡 Solutions / الحلول:")
    print("\n1. Install missing modules:")
    print("   pip install flask flask-sqlalchemy flask-login flask-migrate flask-wtf")
    
    print("\n2. Quick test:")
    print("   python test_server.py")
    
    print("\n3. Simple system:")
    print("   python simple_run.py")
    
    print("\n4. Full system:")
    print("   python run.py")
    
    print("\n5. Windows users:")
    print("   install.bat")
    print("   start.bat")

def main():
    print_header()
    
    checks = [
        ("Python", check_python),
        ("Modules", check_modules),
        ("Files", check_files),
        ("Directories", check_directories),
        ("Flask", test_flask),
        ("Port", check_port)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} check failed: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📊 Summary / الملخص:")
    
    all_passed = True
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 All checks passed! System is ready!")
        print("🎉 جميع الفحوصات نجحت! النظام جاهز!")
        print("\nRun: python test_server.py")
        print("Then open: http://localhost:5000")
    else:
        print("⚠️ Some checks failed. See solutions below.")
        print("⚠️ بعض الفحوصات فشلت. راجع الحلول أدناه.")
        provide_solutions()

if __name__ == '__main__':
    main()
