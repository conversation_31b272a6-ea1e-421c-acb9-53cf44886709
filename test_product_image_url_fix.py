"""
اختبار إصلاح مشكلة image_url في نموذج Product
Test Product image_url fix
"""

import sys
import os

def test_product_model():
    """اختبار نموذج Product"""
    print("🔍 اختبار نموذج Product...")
    print("🔍 Testing Product model...")
    
    try:
        # استيراد النموذج
        from models.product import Product
        
        print("✅ تم استيراد نموذج Product بنجاح")
        print("✅ Product model imported successfully")
        
        # فحص وجود خاصية image_url
        if hasattr(Product, 'image_url'):
            print("✅ خاصية image_url موجودة")
            print("✅ image_url property exists")
        else:
            print("❌ خاصية image_url غير موجودة")
            print("❌ image_url property missing")
            return False
        
        # فحص وجود دالة get_image_url
        if hasattr(Product, 'get_image_url'):
            print("✅ دالة get_image_url موجودة")
            print("✅ get_image_url method exists")
        else:
            print("❌ دالة get_image_url غير موجودة")
            print("❌ get_image_url method missing")
            return False
        
        # إنشاء منتج تجريبي
        test_product = Product(
            sku='TEST001',
            name_ar='منتج تجريبي',
            name_en='Test Product',
            selling_price=10.0,
            cost_price=5.0,
            category_id=1
        )
        
        # اختبار image_url بدون صورة
        print(f"📝 image_url بدون صورة: {test_product.image_url}")
        
        # اختبار image_url مع صورة
        test_product.image_filename = 'test_image.jpg'
        print(f"📝 image_url مع صورة: {test_product.image_url}")
        
        # اختبار get_image_url
        print(f"📝 get_image_url(): {test_product.get_image_url()}")
        
        # اختبار to_dict
        product_dict = test_product.to_dict()
        if 'image_url' in product_dict:
            print(f"✅ to_dict() يحتوي على image_url: {product_dict['image_url']}")
            print(f"✅ to_dict() contains image_url: {product_dict['image_url']}")
        else:
            print("❌ to_dict() لا يحتوي على image_url")
            print("❌ to_dict() missing image_url")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {str(e)}")
        print(f"❌ Model test error: {str(e)}")
        return False

def test_cart_routes():
    """اختبار routes/cart.py"""
    print("\n🔍 اختبار routes/cart.py...")
    print("🔍 Testing routes/cart.py...")
    
    try:
        # قراءة ملف cart.py
        with open('routes/cart.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص استخدام image_url
        if 'product.image_url' in content:
            print("✅ routes/cart.py يستخدم product.image_url")
            print("✅ routes/cart.py uses product.image_url")
        else:
            print("❌ routes/cart.py لا يستخدم product.image_url")
            print("❌ routes/cart.py doesn't use product.image_url")
            return False
        
        # عد مرات الاستخدام
        count = content.count('product.image_url')
        print(f"📊 عدد مرات استخدام product.image_url: {count}")
        print(f"📊 product.image_url usage count: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار cart routes: {str(e)}")
        print(f"❌ Cart routes test error: {str(e)}")
        return False

def check_directories():
    """فحص المجلدات المطلوبة"""
    print("\n📁 فحص المجلدات...")
    print("📁 Checking directories...")
    
    directories = [
        'static',
        'static/images',
        'static/uploads',
        'static/uploads/products'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"⚠️ {directory} غير موجود - سيتم إنشاؤه")
            print(f"⚠️ {directory} missing - will be created")
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ تم إنشاء {directory}")
                print(f"✅ Created {directory}")
            except Exception as e:
                print(f"❌ فشل في إنشاء {directory}: {str(e)}")
                print(f"❌ Failed to create {directory}: {str(e)}")

def create_default_image():
    """إنشاء صورة افتراضية"""
    print("\n🖼️ إنشاء صورة افتراضية...")
    print("🖼️ Creating default image...")
    
    try:
        default_image_path = 'static/images/no-image.png'
        
        if not os.path.exists(default_image_path):
            # إنشاء ملف نصي كبديل مؤقت
            placeholder_path = 'static/images/no-image.txt'
            with open(placeholder_path, 'w', encoding='utf-8') as f:
                f.write("Default image placeholder\nصورة افتراضية")
            
            print(f"✅ تم إنشاء ملف بديل: {placeholder_path}")
            print(f"✅ Created placeholder file: {placeholder_path}")
        else:
            print(f"✅ الصورة الافتراضية موجودة: {default_image_path}")
            print(f"✅ Default image exists: {default_image_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة الافتراضية: {str(e)}")
        print(f"❌ Error creating default image: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة image_url")
    print("🔧 Testing image_url fix")
    print("=" * 50)
    
    success = True
    
    # فحص المجلدات
    check_directories()
    
    # إنشاء الصورة الافتراضية
    create_default_image()
    
    # اختبار نموذج Product
    if not test_product_model():
        success = False
    
    # اختبار cart routes
    if not test_cart_routes():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("🎉 All tests passed!")
        print("\n✅ تم إصلاح مشكلة image_url بنجاح")
        print("✅ image_url issue fixed successfully")
        
        print("\n📋 الملخص:")
        print("📋 Summary:")
        print("- تم إضافة خاصية image_url إلى نموذج Product")
        print("- Added image_url property to Product model")
        print("- تم إضافة دالة get_image_url()")
        print("- Added get_image_url() method")
        print("- تم تحديث to_dict() لتشمل image_url")
        print("- Updated to_dict() to include image_url")
        print("- routes/cart.py يستخدم image_url بشكل صحيح")
        print("- routes/cart.py uses image_url correctly")
        
        print("\n🎯 الآن يمكن استخدام:")
        print("🎯 Now you can use:")
        print("   - product.image_url")
        print("   - product.get_image_url()")
        print("   - product.to_dict()['image_url']")
        
    else:
        print("❌ بعض الاختبارات فشلت!")
        print("❌ Some tests failed!")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
        print("⏹️ Operation cancelled")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        print(f"💥 Unexpected error: {str(e)}")
        sys.exit(1)
    
    input("\nاضغط Enter للخروج...")
