"""
Test cases for Qatar POS System models
"""

import unittest
from datetime import datetime, timedelta
from decimal import Decimal

from app import create_app, db
from app.models import (
    User, Customer, Product, Category, Sale, SaleItem, 
    InventoryTransaction, Supplier, SystemSettings
)


class ModelTestCase(unittest.TestCase):
    """Base test case for model tests"""
    
    def setUp(self):
        """Set up test environment"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
    def tearDown(self):
        """Clean up test environment"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()


class UserModelTest(ModelTestCase):
    """Test cases for User model"""
    
    def test_user_creation(self):
        """Test user creation with required fields"""
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name_ar='أحمد',
            first_name_en='Ahmed',
            last_name_ar='محمد',
            last_name_en='Mohammed',
            role='seller'
        )
        user.set_password('password123')
        db.session.add(user)
        db.session.commit()
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('password123'))
        self.assertFalse(user.check_password('wrongpassword'))
        
    def test_user_full_name(self):
        """Test user full name methods"""
        user = User(
            username='testuser',
            first_name_ar='أحمد',
            first_name_en='Ahmed',
            last_name_ar='محمد',
            last_name_en='Mohammed'
        )
        
        self.assertEqual(user.get_full_name('ar'), 'أحمد محمد')
        self.assertEqual(user.get_full_name('en'), 'Ahmed Mohammed')
        
    def test_user_permissions(self):
        """Test user permission system"""
        # Manager should have all permissions
        manager = User(username='manager', role='manager')
        self.assertTrue(manager.has_permission('sales'))
        self.assertTrue(manager.has_permission('products_write'))
        self.assertTrue(manager.has_permission('users_write'))
        
        # Seller should have limited permissions
        seller = User(username='seller', role='seller')
        self.assertTrue(seller.has_permission('sales'))
        self.assertFalse(seller.has_permission('users_write'))
        self.assertFalse(seller.has_permission('settings'))


class CustomerModelTest(ModelTestCase):
    """Test cases for Customer model"""
    
    def test_individual_customer_creation(self):
        """Test individual customer creation"""
        customer = Customer(
            customer_type='individual',
            first_name_ar='فاطمة',
            first_name_en='Fatima',
            last_name_ar='علي',
            last_name_en='Ali',
            phone='+974-5555-1234',
            email='<EMAIL>'
        )
        db.session.add(customer)
        db.session.commit()
        
        self.assertEqual(customer.customer_type, 'individual')
        self.assertEqual(customer.phone, '+974-5555-1234')
        self.assertTrue(customer.customer_code.startswith('CUST'))
        
    def test_company_customer_creation(self):
        """Test company customer creation"""
        customer = Customer(
            customer_type='company',
            company_name_ar='شركة قطر للتجارة',
            company_name_en='Qatar Trading Company',
            contact_person_ar='محمد أحمد',
            contact_person_en='Mohammed Ahmed',
            phone='+974-4444-5678',
            commercial_registration='12345678'
        )
        db.session.add(customer)
        db.session.commit()
        
        self.assertEqual(customer.customer_type, 'company')
        self.assertEqual(customer.commercial_registration, '12345678')
        
    def test_customer_display_name(self):
        """Test customer display name methods"""
        # Individual customer
        individual = Customer(
            customer_type='individual',
            first_name_ar='سارة',
            first_name_en='Sarah',
            last_name_ar='محمد',
            last_name_en='Mohammed'
        )
        
        self.assertEqual(individual.get_display_name('ar'), 'سارة محمد')
        self.assertEqual(individual.get_display_name('en'), 'Sarah Mohammed')
        
        # Company customer
        company = Customer(
            customer_type='company',
            company_name_ar='شركة الخليج',
            company_name_en='Gulf Company'
        )
        
        self.assertEqual(company.get_display_name('ar'), 'شركة الخليج')
        self.assertEqual(company.get_display_name('en'), 'Gulf Company')


class ProductModelTest(ModelTestCase):
    """Test cases for Product model"""
    
    def test_product_creation(self):
        """Test product creation with required fields"""
        category = Category(name_ar='إلكترونيات', name_en='Electronics')
        db.session.add(category)
        db.session.flush()
        
        product = Product(
            name_ar='هاتف ذكي',
            name_en='Smartphone',
            sku='PHONE001',
            category_id=category.id,
            cost_price=Decimal('500.00'),
            selling_price=Decimal('750.00'),
            current_stock=10,
            minimum_stock=2
        )
        db.session.add(product)
        db.session.commit()
        
        self.assertEqual(product.name_ar, 'هاتف ذكي')
        self.assertEqual(product.sku, 'PHONE001')
        self.assertEqual(product.cost_price, Decimal('500.00'))
        self.assertEqual(product.selling_price, Decimal('750.00'))
        
    def test_product_stock_status(self):
        """Test product stock status methods"""
        product = Product(
            name_en='Test Product',
            current_stock=0,
            minimum_stock=5
        )
        
        # Out of stock
        self.assertTrue(product.is_out_of_stock())
        self.assertFalse(product.is_low_stock())
        
        # Low stock
        product.current_stock = 3
        self.assertFalse(product.is_out_of_stock())
        self.assertTrue(product.is_low_stock())
        
        # Normal stock
        product.current_stock = 10
        self.assertFalse(product.is_out_of_stock())
        self.assertFalse(product.is_low_stock())
        
    def test_product_profit_calculation(self):
        """Test product profit calculation"""
        product = Product(
            name_en='Test Product',
            cost_price=Decimal('100.00'),
            selling_price=Decimal('150.00')
        )
        
        profit = product.get_profit()
        profit_margin = product.get_profit_margin()
        
        self.assertEqual(profit, Decimal('50.00'))
        self.assertEqual(profit_margin, Decimal('33.33'))


class SaleModelTest(ModelTestCase):
    """Test cases for Sale model"""
    
    def test_sale_creation(self):
        """Test sale creation with items"""
        # Create user
        user = User(username='seller', role='seller')
        user.set_password('password')
        db.session.add(user)
        db.session.flush()
        
        # Create customer
        customer = Customer(
            customer_type='individual',
            first_name_en='John',
            last_name_en='Doe',
            phone='+974-5555-0000'
        )
        db.session.add(customer)
        db.session.flush()
        
        # Create category and product
        category = Category(name_en='Test Category')
        db.session.add(category)
        db.session.flush()
        
        product = Product(
            name_en='Test Product',
            sku='TEST001',
            category_id=category.id,
            cost_price=Decimal('50.00'),
            selling_price=Decimal('100.00'),
            current_stock=10
        )
        db.session.add(product)
        db.session.flush()
        
        # Create sale
        sale = Sale(
            seller_id=user.id,
            customer_id=customer.id,
            payment_method='cash',
            subtotal_amount=Decimal('200.00'),
            tax_amount=Decimal('0.00'),
            discount_amount=Decimal('10.00'),
            total_amount=Decimal('190.00'),
            payment_amount=Decimal('200.00'),
            change_amount=Decimal('10.00')
        )
        db.session.add(sale)
        db.session.flush()
        
        # Add sale items
        sale_item = SaleItem(
            sale_id=sale.id,
            product_id=product.id,
            quantity=2,
            unit_price=Decimal('100.00'),
            total_price=Decimal('200.00')
        )
        db.session.add(sale_item)
        db.session.commit()
        
        self.assertEqual(sale.total_amount, Decimal('190.00'))
        self.assertEqual(sale.items.count(), 1)
        self.assertEqual(sale.items.first().quantity, 2)
        self.assertTrue(sale.sale_number.startswith('INV'))
        
    def test_sale_status_transitions(self):
        """Test sale status transitions"""
        sale = Sale(
            payment_method='cash',
            total_amount=Decimal('100.00'),
            status='pending'
        )
        
        self.assertEqual(sale.status, 'pending')
        
        # Complete sale
        sale.status = 'completed'
        self.assertEqual(sale.status, 'completed')


class InventoryTransactionTest(ModelTestCase):
    """Test cases for InventoryTransaction model"""
    
    def test_inventory_transaction_creation(self):
        """Test inventory transaction creation"""
        # Create product
        category = Category(name_en='Test Category')
        db.session.add(category)
        db.session.flush()
        
        product = Product(
            name_en='Test Product',
            sku='TEST001',
            category_id=category.id,
            current_stock=10
        )
        db.session.add(product)
        db.session.flush()
        
        # Create transaction
        transaction = InventoryTransaction(
            product_id=product.id,
            transaction_type='sale',
            quantity_change=-2,
            old_quantity=10,
            new_quantity=8,
            reference_type='sale',
            reference_id=1
        )
        db.session.add(transaction)
        db.session.commit()
        
        self.assertEqual(transaction.quantity_change, -2)
        self.assertEqual(transaction.old_quantity, 10)
        self.assertEqual(transaction.new_quantity, 8)
        self.assertEqual(transaction.transaction_type, 'sale')


class SystemSettingsTest(ModelTestCase):
    """Test cases for SystemSettings model"""
    
    def test_settings_creation(self):
        """Test system settings creation"""
        settings = SystemSettings(
            key='company_name_ar',
            value='شركة قطر لنقاط البيع',
            data_type='string'
        )
        db.session.add(settings)
        db.session.commit()
        
        self.assertEqual(settings.key, 'company_name_ar')
        self.assertEqual(settings.value, 'شركة قطر لنقاط البيع')
        self.assertEqual(settings.data_type, 'string')
        
    def test_settings_get_value(self):
        """Test settings value retrieval with type conversion"""
        # String setting
        string_setting = SystemSettings(
            key='system_name',
            value='Qatar POS',
            data_type='string'
        )
        db.session.add(string_setting)
        
        # Boolean setting
        bool_setting = SystemSettings(
            key='maintenance_mode',
            value='true',
            data_type='boolean'
        )
        db.session.add(bool_setting)
        
        # Integer setting
        int_setting = SystemSettings(
            key='session_timeout',
            value='3600',
            data_type='integer'
        )
        db.session.add(int_setting)
        
        # Float setting
        float_setting = SystemSettings(
            key='tax_rate',
            value='5.5',
            data_type='float'
        )
        db.session.add(float_setting)
        
        db.session.commit()
        
        self.assertEqual(string_setting.get_value(), 'Qatar POS')
        self.assertEqual(bool_setting.get_value(), True)
        self.assertEqual(int_setting.get_value(), 3600)
        self.assertEqual(float_setting.get_value(), 5.5)


if __name__ == '__main__':
    unittest.main()
