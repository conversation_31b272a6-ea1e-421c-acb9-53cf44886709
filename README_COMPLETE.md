# 🇶🇦 Qatar POS System | نظام نقاط البيع القطري

A comprehensive Point of Sale system designed specifically for the Qatar market with full Arabic/English bilingual support and Qatar Tax Authority compliance.

نظام نقاط بيع شامل مصمم خصيصاً للسوق القطري مع دعم كامل للغتين العربية والإنجليزية والامتثال لهيئة الضرائب القطرية.

## 🌟 Features | المميزات

### 🇶🇦 Qatar-Specific Features | المميزات القطرية
- ✅ **Bilingual Support** | دعم اللغتين العربية والإنجليزية
- ✅ **Qatar Riyal (QAR) Currency** | عملة الريال القطري
- ✅ **6-Day Work Week** | أسبوع عمل 6 أيام (إغلاق الجمعة)
- ✅ **Qatar ID Support** | دعم رقم الهوية القطرية (11 رقم)
- ✅ **Commercial Registration** | دعم السجل التجاري
- ✅ **Tax Number Support** | دعم الرقم الضريبي
- ✅ **Qatar Tax Authority Compliance** | الامتثال لهيئة الضرائب القطرية

### 💼 Business Features | المميزات التجارية
- 🛒 **Advanced POS System** | نقطة بيع متطورة
- 📦 **Inventory Management** | إدارة المخزون
- 👥 **Customer Management** | إدارة العملاء (أفراد وشركات)
- 🏢 **Supplier Management** | إدارة الموردين
- 📊 **Reports & Analytics** | تقارير وإحصائيات
- 👤 **User Management** | إدارة المستخدمين والصلاحيات
- 💳 **Multiple Payment Methods** | طرق دفع متعددة
- 🧾 **Invoice Generation** | إنشاء الفواتير
- 📱 **Responsive Design** | تصميم متجاوب

## 🚀 Quick Start | البدء السريع

### Prerequisites | المتطلبات
- Python 3.8+ | بايثون 3.8 أو أحدث
- pip | مدير حزم بايثون

### Installation | التثبيت

1. **Install dependencies | تثبيت التبعيات**
```bash
pip install -r requirements.txt
```

2. **Start the system | تشغيل النظام**
```bash
python start_qatar_pos.py
```

### Alternative Start Methods | طرق تشغيل بديلة
```bash
# Method 1: Enhanced startup script
python start_qatar_pos.py

# Method 2: Direct app run
python app.py

# Method 3: Simple server
python server_2626.py

# Method 4: Windows batch file
start_2626.bat
```

## 🌐 Access Information | معلومات الوصول

- **URL**: http://127.0.0.1:2626
- **Username | اسم المستخدم**: admin
- **Password | كلمة المرور**: admin123

## 📱 System Pages | صفحات النظام

| Page | Arabic | URL |
|------|--------|-----|
| Dashboard | لوحة التحكم | `/` |
| POS System | نقطة البيع | `/sales/pos` |
| Products | المنتجات | `/products` |
| Customers | العملاء | `/customers` |
| Suppliers | الموردين | `/suppliers` |
| Sales | المبيعات | `/sales` |
| Inventory | المخزون | `/inventory` |
| Reports | التقارير | `/reports` |
| Users | المستخدمين | `/users` |
| Settings | الإعدادات | `/settings` |

## 🛠️ Technical Stack | المكدس التقني

- **Backend**: Flask (Python)
- **Database**: SQLite (Development) / PostgreSQL (Production)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 4
- **Authentication**: Flask-Login
- **Database ORM**: SQLAlchemy
- **Migrations**: Flask-Migrate
- **Internationalization**: Flask-Babel

## 📁 Project Structure | هيكل المشروع

```
qatar-pos-system/
├── app.py                 # Main application entry point
├── config.py              # Configuration settings
├── extensions.py          # Flask extensions
├── requirements.txt       # Python dependencies
├── start_qatar_pos.py     # Enhanced startup script
├── models/               # Database models
│   ├── __init__.py
│   ├── user.py
│   ├── product.py
│   ├── customer.py
│   ├── supplier.py
│   ├── sale.py
│   └── ...
├── routes/               # Application routes
│   ├── auth.py
│   ├── dashboard.py
│   ├── products.py
│   ├── customers.py
│   ├── suppliers.py
│   ├── sales.py
│   └── ...
├── templates/            # HTML templates
│   ├── base.html
│   ├── customers/
│   ├── suppliers/
│   ├── sales/
│   └── ...
├── static/               # Static files
│   ├── css/
│   ├── js/
│   └── uploads/
└── utils/                # Utility functions
    ├── decorators.py
    ├── helpers.py
    └── validators.py
```

## 👥 User Roles | أدوار المستخدمين

| Role | Arabic | Permissions |
|------|--------|-------------|
| Admin | مدير | Full system access |
| Manager | مدير فرع | Most features except user management |
| Cashier | كاشير | POS, customers, basic sales |
| Employee | موظف | Limited access to products and customers |

## 🧪 Testing | الاختبار

### Run Tests | تشغيل الاختبارات
```bash
# Check all systems
python fix_all_errors.py

# Test templates
python fix_templates.py

# Test models
python fix_models.py
```

## 📊 Reports Available | التقارير المتاحة

- 📈 **Sales Reports** | تقارير المبيعات
- 📦 **Inventory Reports** | تقارير المخزون
- 👥 **Customer Reports** | تقارير العملاء
- 🏢 **Supplier Reports** | تقارير الموردين
- 💰 **Financial Reports** | التقارير المالية
- 📅 **Daily/Monthly/Yearly Reports** | تقارير يومية/شهرية/سنوية

## 🔒 Security Features | مميزات الأمان

- 🔐 **User Authentication** | مصادقة المستخدمين
- 🛡️ **Role-Based Access Control** | التحكم في الوصول حسب الدور
- 🔑 **Password Hashing** | تشفير كلمات المرور
- 🚫 **Session Management** | إدارة الجلسات
- 📝 **Audit Logging** | سجل المراجعة

## 🌍 Internationalization | التدويل

The system supports:
- **Arabic (ar)** - العربية
- **English (en)** - English

Users can switch between languages in their profile settings.
يمكن للمستخدمين التبديل بين اللغات في إعدادات الملف الشخصي.

## 🔄 Updates | التحديثات

- **Version 1.0.0** - Initial release with full Qatar market features
- **الإصدار 1.0.0** - الإصدار الأولي مع جميع مميزات السوق القطري

---

**🇶🇦 Made with ❤️ for Qatar | صُنع بـ ❤️ لقطر**
