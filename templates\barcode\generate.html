{% extends "base.html" %}

{% block title %}
    {{ 'إنشاء باركود' if language == 'ar' else 'Generate Barcode' }} - {{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-upc-scan"></i>
                    {{ 'إنشاء باركود' if language == 'ar' else 'Generate Barcode' }}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('main.dashboard') }}">
                                {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('barcode.index') }}">
                                {{ 'إدارة الباركود' if language == 'ar' else 'Barcode Management' }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            {{ 'إنشاء باركود' if language == 'ar' else 'Generate Barcode' }}
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear"></i>
                        {{ 'إعدادات الباركود' if language == 'ar' else 'Barcode Settings' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form id="barcodeForm" method="POST">
                        <div class="mb-3">
                            <label for="barcode_type" class="form-label">
                                {{ 'نوع الباركود' if language == 'ar' else 'Barcode Type' }}
                            </label>
                            <select class="form-select" id="barcode_type" name="barcode_type" required>
                                <option value="">{{ 'اختر نوع الباركود' if language == 'ar' else 'Select Barcode Type' }}</option>
                                <option value="code128" {{ 'selected' if barcode_type == 'code128' else '' }}>Code 128</option>
                                <option value="code39" {{ 'selected' if barcode_type == 'code39' else '' }}>Code 39</option>
                                <option value="ean13" {{ 'selected' if barcode_type == 'ean13' else '' }}>EAN-13</option>
                                <option value="ean8" {{ 'selected' if barcode_type == 'ean8' else '' }}>EAN-8</option>
                                <option value="upca" {{ 'selected' if barcode_type == 'upca' else '' }}>UPC-A</option>
                                <option value="qr" {{ 'selected' if barcode_type == 'qr' else '' }}>QR Code</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="barcode_data" class="form-label">
                                {{ 'البيانات' if language == 'ar' else 'Data' }}
                            </label>
                            <input type="text" class="form-control" id="barcode_data" name="barcode_data" 
                                   value="{{ barcode_data or '' }}" required
                                   placeholder="{{ 'أدخل البيانات المراد تشفيرها' if language == 'ar' else 'Enter data to encode' }}">
                            <div class="form-text">
                                {{ 'أدخل النص أو الرقم المراد تحويله إلى باركود' if language == 'ar' else 'Enter text or number to convert to barcode' }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="width" class="form-label">
                                        {{ 'العرض (بكسل)' if language == 'ar' else 'Width (pixels)' }}
                                    </label>
                                    <input type="number" class="form-control" id="width" name="width" 
                                           value="{{ width or 200 }}" min="50" max="1000">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="height" class="form-label">
                                        {{ 'الارتفاع (بكسل)' if language == 'ar' else 'Height (pixels)' }}
                                    </label>
                                    <input type="number" class="form-control" id="height" name="height" 
                                           value="{{ height or 100 }}" min="30" max="500">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_text" name="show_text" 
                                       {{ 'checked' if show_text else '' }}>
                                <label class="form-check-label" for="show_text">
                                    {{ 'إظهار النص أسفل الباركود' if language == 'ar' else 'Show text below barcode' }}
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-magic"></i>
                                {{ 'إنشاء الباركود' if language == 'ar' else 'Generate Barcode' }}
                            </button>
                            <a href="{{ url_for('barcode.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i>
                                {{ 'العودة' if language == 'ar' else 'Back' }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-eye"></i>
                        {{ 'معاينة الباركود' if language == 'ar' else 'Barcode Preview' }}
                    </h5>
                </div>
                <div class="card-body text-center">
                    {% if barcode_image %}
                        <div class="mb-3">
                            <img src="data:image/png;base64,{{ barcode_image }}" 
                                 alt="Generated Barcode" class="img-fluid border rounded p-2">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="data:image/png;base64,{{ barcode_image }}" 
                               download="barcode_{{ barcode_data }}.png" 
                               class="btn btn-success">
                                <i class="bi bi-download"></i>
                                {{ 'تحميل الصورة' if language == 'ar' else 'Download Image' }}
                            </a>
                            
                            <button type="button" class="btn btn-info" onclick="printBarcode()">
                                <i class="bi bi-printer"></i>
                                {{ 'طباعة' if language == 'ar' else 'Print' }}
                            </button>
                        </div>
                    {% else %}
                        <div class="text-muted py-5">
                            <i class="bi bi-upc-scan display-1 opacity-25"></i>
                            <p class="mt-3">
                                {{ 'املأ النموذج لإنشاء الباركود' if language == 'ar' else 'Fill the form to generate barcode' }}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if barcode_image %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i>
                        {{ 'معلومات الباركود' if language == 'ar' else 'Barcode Information' }}
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>{{ 'النوع:' if language == 'ar' else 'Type:' }}</strong></td>
                            <td>{{ barcode_type.upper() if barcode_type else '' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ 'البيانات:' if language == 'ar' else 'Data:' }}</strong></td>
                            <td><code>{{ barcode_data }}</code></td>
                        </tr>
                        <tr>
                            <td><strong>{{ 'الأبعاد:' if language == 'ar' else 'Dimensions:' }}</strong></td>
                            <td>{{ width }}x{{ height }} {{ 'بكسل' if language == 'ar' else 'pixels' }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ 'إظهار النص:' if language == 'ar' else 'Show Text:' }}</strong></td>
                            <td>
                                {% if show_text %}
                                    <span class="badge bg-success">{{ 'نعم' if language == 'ar' else 'Yes' }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ 'لا' if language == 'ar' else 'No' }}</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function printBarcode() {
    const barcodeImage = document.querySelector('img[alt="Generated Barcode"]');
    if (barcodeImage) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>{{ 'طباعة الباركود' if language == 'ar' else 'Print Barcode' }}</title>
                    <style>
                        body { 
                            margin: 0; 
                            padding: 20px; 
                            text-align: center; 
                            font-family: Arial, sans-serif;
                        }
                        img { 
                            max-width: 100%; 
                            height: auto; 
                        }
                        .info {
                            margin-top: 20px;
                            font-size: 12px;
                            color: #666;
                        }
                        @media print {
                            body { margin: 0; padding: 10px; }
                        }
                    </style>
                </head>
                <body>
                    <img src="${barcodeImage.src}" alt="Barcode">
                    <div class="info">
                        <p>{{ 'النوع:' if language == 'ar' else 'Type:' }} {{ barcode_type.upper() if barcode_type else '' }}</p>
                        <p>{{ 'البيانات:' if language == 'ar' else 'Data:' }} {{ barcode_data }}</p>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            };
                        };
                    </script>
                </body>
            </html>
        `);
        printWindow.document.close();
    }
}

// Auto-submit form when barcode type changes (for better UX)
document.getElementById('barcode_type').addEventListener('change', function() {
    const form = document.getElementById('barcodeForm');
    const data = document.getElementById('barcode_data').value;
    if (data.trim()) {
        form.submit();
    }
});
</script>
{% endblock %}
