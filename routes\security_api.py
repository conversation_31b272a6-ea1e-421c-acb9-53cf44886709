"""
Security API Routes for Qatar POS System
Handles security settings, audit logs, and security monitoring
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from utils.decorators import permission_required
from utils.security_manager import security_manager
from models.setting import Setting
from models.user import User
from extensions import db
import logging
from datetime import datetime, timedelta

security_api_bp = Blueprint('security_api', __name__, url_prefix='/api/security')
logger = logging.getLogger(__name__)

@security_api_bp.route('/audit-log', methods=['GET'])
@login_required
@permission_required('admin')
def get_audit_log():
    """Get security audit log"""
    try:
        limit = request.args.get('limit', 100, type=int)
        event_type = request.args.get('type')
        
        logs = security_manager.get_audit_log(limit=limit, event_type=event_type)
        
        return jsonify({
            'success': True,
            'logs': logs,
            'total': len(logs)
        })
        
    except Exception as e:
        logger.error(f"Failed to get audit log: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/audit', methods=['POST'])
@login_required
@permission_required('admin')
def perform_security_audit():
    """Perform comprehensive security audit"""
    try:
        audit_results = security_manager.security_audit()
        
        # Log the audit
        security_manager.log_security_event('security_audit_performed', {
            'performed_by': current_user.username,
            'score': audit_results['score'],
            'recommendations_count': len(audit_results['recommendations'])
        })
        
        return jsonify({
            'success': True,
            'audit': audit_results
        })
        
    except Exception as e:
        logger.error(f"Security audit failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/password-policy/validate', methods=['POST'])
@login_required
def validate_password():
    """Validate password against security policy"""
    try:
        data = request.get_json() or {}
        password = data.get('password', '')
        
        if not password:
            return jsonify({
                'success': False,
                'error': 'Password is required'
            }), 400
        
        # Get password policy settings
        settings = {
            'min_password_length': Setting.get_setting('min_password_length', '8'),
            'require_uppercase': Setting.get_setting('require_uppercase', 'true'),
            'require_numbers': Setting.get_setting('require_numbers', 'true'),
            'require_special_chars': Setting.get_setting('require_special_chars', 'false')
        }
        
        is_valid, errors = security_manager.validate_password_policy(password, settings)
        
        return jsonify({
            'success': True,
            'valid': is_valid,
            'errors': errors,
            'strength': calculate_password_strength(password)
        })
        
    except Exception as e:
        logger.error(f"Password validation failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def calculate_password_strength(password):
    """Calculate password strength score"""
    score = 0
    
    # Length bonus
    if len(password) >= 8:
        score += 20
    if len(password) >= 12:
        score += 10
    
    # Character variety
    if any(c.islower() for c in password):
        score += 10
    if any(c.isupper() for c in password):
        score += 15
    if any(c.isdigit() for c in password):
        score += 15
    if any(c in '!@#$%^&*(),.?":{}|<>' for c in password):
        score += 20
    
    # Complexity bonus
    if len(set(password)) > len(password) * 0.7:  # High character diversity
        score += 10
    
    # Determine strength level
    if score >= 80:
        return {'score': score, 'level': 'strong', 'color': 'success'}
    elif score >= 60:
        return {'score': score, 'level': 'medium', 'color': 'warning'}
    else:
        return {'score': score, 'level': 'weak', 'color': 'danger'}

@security_api_bp.route('/sessions', methods=['GET'])
@login_required
@permission_required('admin')
def get_active_sessions():
    """Get active user sessions"""
    try:
        # This is a simplified version - in production you'd track sessions in database
        active_sessions = []
        
        # Get users who have logged in recently
        recent_login_threshold = datetime.now() - timedelta(hours=24)
        recent_users = User.query.filter(
            User.last_login >= recent_login_threshold
        ).all()
        
        for user in recent_users:
            session_info = {
                'user_id': user.id,
                'username': user.username,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'role': user.role,
                'is_active': user.is_active,
                'session_duration': str(datetime.now() - user.last_login) if user.last_login else None
            }
            active_sessions.append(session_info)
        
        return jsonify({
            'success': True,
            'sessions': active_sessions,
            'total': len(active_sessions)
        })
        
    except Exception as e:
        logger.error(f"Failed to get active sessions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/failed-attempts', methods=['GET'])
@login_required
@permission_required('admin')
def get_failed_attempts():
    """Get failed login attempts"""
    try:
        failed_attempts = []
        
        for identifier, attempts in security_manager.failed_attempts.items():
            failed_attempts.append({
                'identifier': identifier,
                'attempts': len(attempts),
                'last_attempt': max(attempts).isoformat() if attempts else None,
                'blocked': len(attempts) >= 5  # Assuming 5 is the limit
            })
        
        # Sort by last attempt time
        failed_attempts.sort(key=lambda x: x['last_attempt'] or '', reverse=True)
        
        return jsonify({
            'success': True,
            'failed_attempts': failed_attempts,
            'total': len(failed_attempts)
        })
        
    except Exception as e:
        logger.error(f"Failed to get failed attempts: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/blocked-ips', methods=['GET'])
@login_required
@permission_required('admin')
def get_blocked_ips():
    """Get blocked IP addresses"""
    try:
        blocked_ips = list(security_manager.blocked_ips)
        
        return jsonify({
            'success': True,
            'blocked_ips': blocked_ips,
            'total': len(blocked_ips)
        })
        
    except Exception as e:
        logger.error(f"Failed to get blocked IPs: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/block-ip', methods=['POST'])
@login_required
@permission_required('admin')
def block_ip():
    """Block an IP address"""
    try:
        data = request.get_json() or {}
        ip_address = data.get('ip_address')
        reason = data.get('reason', 'Manually blocked by admin')
        
        if not ip_address:
            return jsonify({
                'success': False,
                'error': 'IP address is required'
            }), 400
        
        security_manager.block_ip(ip_address, reason)
        
        return jsonify({
            'success': True,
            'message': f'IP {ip_address} has been blocked'
        })
        
    except Exception as e:
        logger.error(f"Failed to block IP: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/unblock-ip', methods=['POST'])
@login_required
@permission_required('admin')
def unblock_ip():
    """Unblock an IP address"""
    try:
        data = request.get_json() or {}
        ip_address = data.get('ip_address')
        
        if not ip_address:
            return jsonify({
                'success': False,
                'error': 'IP address is required'
            }), 400
        
        if ip_address in security_manager.blocked_ips:
            security_manager.blocked_ips.remove(ip_address)
            
            security_manager.log_security_event('ip_unblocked', {
                'ip': ip_address,
                'unblocked_by': current_user.username
            })
            
            return jsonify({
                'success': True,
                'message': f'IP {ip_address} has been unblocked'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'IP address is not blocked'
            }), 400
        
    except Exception as e:
        logger.error(f"Failed to unblock IP: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/security-status', methods=['GET'])
@login_required
@permission_required('admin')
def get_security_status():
    """Get overall security status"""
    try:
        # Get security settings
        settings = {
            'two_factor_enabled': Setting.get_setting('two_factor_enabled') == 'true',
            'audit_log_enabled': Setting.get_setting('enable_audit_log') == 'true',
            'rate_limiting_enabled': Setting.get_setting('enable_rate_limiting') == 'true',
            'ip_whitelist_enabled': Setting.get_setting('enable_ip_whitelist') == 'true',
            'ssl_only_enabled': Setting.get_setting('enable_ssl_only') == 'true',
            'data_encryption_enabled': Setting.get_setting('enable_data_encryption') == 'true',
            'backup_encryption_enabled': Setting.get_setting('enable_backup_encryption') == 'true'
        }
        
        # Calculate security score
        enabled_features = sum(1 for enabled in settings.values() if enabled)
        security_score = (enabled_features / len(settings)) * 100
        
        # Get recent security events
        recent_events = security_manager.get_audit_log(limit=10)
        
        # Get failed attempts count
        total_failed_attempts = sum(len(attempts) for attempts in security_manager.failed_attempts.values())
        
        # Get blocked IPs count
        blocked_ips_count = len(security_manager.blocked_ips)
        
        status = {
            'security_score': round(security_score, 1),
            'settings': settings,
            'recent_events': recent_events,
            'failed_attempts_count': total_failed_attempts,
            'blocked_ips_count': blocked_ips_count,
            'last_audit': None,  # Would be stored in database in production
            'recommendations': []
        }
        
        # Add recommendations based on disabled features
        if not settings['two_factor_enabled']:
            status['recommendations'].append('Enable two-factor authentication for enhanced security')
        
        if not settings['audit_log_enabled']:
            status['recommendations'].append('Enable audit logging to track security events')
        
        if not settings['rate_limiting_enabled']:
            status['recommendations'].append('Enable rate limiting to prevent brute force attacks')
        
        if not settings['ssl_only_enabled']:
            status['recommendations'].append('Enable HTTPS-only mode for production')
        
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"Failed to get security status: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/generate-csrf-token', methods=['GET'])
@login_required
def generate_csrf_token():
    """Generate CSRF token for forms"""
    try:
        token = security_manager.generate_csrf_token()
        
        return jsonify({
            'success': True,
            'csrf_token': token
        })
        
    except Exception as e:
        logger.error(f"Failed to generate CSRF token: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@security_api_bp.route('/test-security', methods=['POST'])
@login_required
@permission_required('admin')
def test_security_features():
    """Test security features"""
    try:
        test_results = []
        
        # Test 1: Password policy
        test_password = "TestPass123!"
        settings = {
            'min_password_length': Setting.get_setting('min_password_length', '8'),
            'require_uppercase': Setting.get_setting('require_uppercase', 'true'),
            'require_numbers': Setting.get_setting('require_numbers', 'true'),
            'require_special_chars': Setting.get_setting('require_special_chars', 'false')
        }
        
        is_valid, errors = security_manager.validate_password_policy(test_password, settings)
        test_results.append({
            'test': 'Password Policy',
            'status': 'passed' if is_valid else 'failed',
            'message': 'Policy validation working' if is_valid else f'Errors: {", ".join(errors)}'
        })
        
        # Test 2: CSRF token generation
        try:
            token = security_manager.generate_csrf_token()
            test_results.append({
                'test': 'CSRF Token Generation',
                'status': 'passed' if token else 'failed',
                'message': 'Token generated successfully' if token else 'Failed to generate token'
            })
        except Exception as e:
            test_results.append({
                'test': 'CSRF Token Generation',
                'status': 'failed',
                'message': str(e)
            })
        
        # Test 3: Audit logging
        try:
            security_manager.log_security_event('test_event', {'test': True})
            test_results.append({
                'test': 'Audit Logging',
                'status': 'passed',
                'message': 'Event logged successfully'
            })
        except Exception as e:
            test_results.append({
                'test': 'Audit Logging',
                'status': 'failed',
                'message': str(e)
            })
        
        # Test 4: Rate limiting check
        try:
            can_proceed = security_manager.check_rate_limit('test_user', max_attempts=5)
            test_results.append({
                'test': 'Rate Limiting',
                'status': 'passed',
                'message': f'Rate limit check working (result: {can_proceed})'
            })
        except Exception as e:
            test_results.append({
                'test': 'Rate Limiting',
                'status': 'failed',
                'message': str(e)
            })
        
        # Calculate overall status
        failed_tests = [t for t in test_results if t['status'] == 'failed']
        overall_status = 'failed' if failed_tests else 'passed'
        
        return jsonify({
            'success': True,
            'overall_status': overall_status,
            'test_results': test_results
        })
        
    except Exception as e:
        logger.error(f"Security test failed: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
