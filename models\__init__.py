"""
Models package for Qatar POS System
Import all models in the correct order to avoid circular imports
"""

# Import base models first
# from .role import Role, Permission  # Temporarily disabled
from .user import User
from .category import Category
from .product import Product
from .customer import Customer
from .supplier import Supplier

# Import transaction models
from .sale import Sale, SaleItem
from .purchase import PurchaseOrder, PurchaseOrderItem
from .inventory import InventoryTransaction

# Import settings
from .settings import SystemSettings
from .setting import Setting, SystemInfo

# Export all models
__all__ = [
    'Role',
    'Permission',
    'User',
    'Category',
    'Product',
    'Customer',
    'Supplier',
    'Sale',
    'SaleItem',
    'PurchaseOrder',
    'PurchaseOrderItem',
    'InventoryTransaction',
    'SystemSettings',
    'Setting',
    'SystemInfo'
]
