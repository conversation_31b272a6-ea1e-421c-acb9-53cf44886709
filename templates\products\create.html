{% extends "base.html" %}

{% block title %}
{{ 'إضافة منتج جديد - نظام نقاط البيع القطري' if language == 'ar' else 'Add New Product - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة منتج جديد' if language == 'ar' else 'Add New Product' }}
            </h1>
            <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة للقائمة' if language == 'ar' else 'Back to List' }}
            </a>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i>
                        {{ 'المعلومات الأساسية' if language == 'ar' else 'Basic Information' }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sku" class="form-label">
                                {{ 'رمز المنتج (SKU)' if language == 'ar' else 'Product SKU' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="sku" name="sku" required
                                   placeholder="{{ 'مثال: PROD001' if language == 'ar' else 'e.g., PROD001' }}">
                            <div class="invalid-feedback">
                                {{ 'رمز المنتج مطلوب' if language == 'ar' else 'Product SKU is required' }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="barcode" class="form-label">
                                {{ 'الباركود' if language == 'ar' else 'Barcode' }}
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcode" name="barcode"
                                       placeholder="{{ 'اتركه فارغاً للإنشاء التلقائي' if language == 'ar' else 'Leave empty for auto-generation' }}">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">
                                {{ 'اسم المنتج بالعربية' if language == 'ar' else 'Product Name (Arabic)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name_ar" name="name_ar" required
                                   placeholder="{{ 'أدخل اسم المنتج بالعربية' if language == 'ar' else 'Enter product name in Arabic' }}">
                            <div class="invalid-feedback">
                                {{ 'اسم المنتج بالعربية مطلوب' if language == 'ar' else 'Arabic product name is required' }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">
                                {{ 'اسم المنتج بالإنجليزية' if language == 'ar' else 'Product Name (English)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name_en" name="name_en" required
                                   placeholder="{{ 'أدخل اسم المنتج بالإنجليزية' if language == 'ar' else 'Enter product name in English' }}">
                            <div class="invalid-feedback">
                                {{ 'اسم المنتج بالإنجليزية مطلوب' if language == 'ar' else 'English product name is required' }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="description_ar" class="form-label">
                                {{ 'الوصف بالعربية' if language == 'ar' else 'Description (Arabic)' }}
                            </label>
                            <textarea class="form-control" id="description_ar" name="description_ar" rows="3"
                                      placeholder="{{ 'وصف المنتج بالعربية' if language == 'ar' else 'Product description in Arabic' }}"></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="description_en" class="form-label">
                                {{ 'الوصف بالإنجليزية' if language == 'ar' else 'Description (English)' }}
                            </label>
                            <textarea class="form-control" id="description_en" name="description_en" rows="3"
                                      placeholder="{{ 'وصف المنتج بالإنجليزية' if language == 'ar' else 'Product description in English' }}"></textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category_id" class="form-label">
                            {{ 'الفئة' if language == 'ar' else 'Category' }}
                            <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="category_id" name="category_id" required>
                            <option value="">{{ 'اختر الفئة' if language == 'ar' else 'Select Category' }}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.get_name(language) }}</option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            {{ 'يرجى اختيار فئة المنتج' if language == 'ar' else 'Please select a product category' }}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pricing -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-currency-dollar"></i>
                        {{ 'التسعير' if language == 'ar' else 'Pricing' }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="cost_price" class="form-label">
                                {{ 'سعر التكلفة (ر.ق)' if language == 'ar' else 'Cost Price (QAR)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                   step="0.001" min="0" required>
                            <div class="invalid-feedback">
                                {{ 'سعر التكلفة مطلوب' if language == 'ar' else 'Cost price is required' }}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="selling_price" class="form-label">
                                {{ 'سعر البيع (ر.ق)' if language == 'ar' else 'Selling Price (QAR)' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                   step="0.01" min="0" required>
                            <div class="invalid-feedback">
                                {{ 'سعر البيع مطلوب' if language == 'ar' else 'Selling price is required' }}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="discount_percentage" class="form-label">
                                {{ 'نسبة الخصم (%)' if language == 'ar' else 'Discount Percentage (%)' }}
                            </label>
                            <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" 
                                   step="0.01" min="0" max="100" value="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <strong>{{ 'هامش الربح:' if language == 'ar' else 'Profit Margin:' }}</strong>
                                <span id="profit_margin">0%</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <strong>{{ 'السعر النهائي:' if language == 'ar' else 'Final Price:' }}</strong>
                                <span id="final_price">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Inventory -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-boxes"></i>
                        {{ 'إدارة المخزون' if language == 'ar' else 'Inventory Management' }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="current_stock" class="form-label">
                                {{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}
                            </label>
                            <input type="number" class="form-control" id="current_stock" name="current_stock" 
                                   min="0" value="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="minimum_stock" class="form-label">
                                {{ 'الحد الأدنى للمخزون' if language == 'ar' else 'Minimum Stock' }}
                            </label>
                            <input type="number" class="form-control" id="minimum_stock" name="minimum_stock" 
                                   min="0" value="5">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="maximum_stock" class="form-label">
                                {{ 'الحد الأقصى للمخزون' if language == 'ar' else 'Maximum Stock' }}
                            </label>
                            <input type="number" class="form-control" id="maximum_stock" name="maximum_stock" 
                                   min="0" value="1000">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="unit_of_measure" class="form-label">
                                {{ 'وحدة القياس' if language == 'ar' else 'Unit of Measure' }}
                            </label>
                            <select class="form-select" id="unit_of_measure" name="unit_of_measure">
                                <option value="piece">{{ 'قطعة' if language == 'ar' else 'Piece' }}</option>
                                <option value="kg">{{ 'كيلوجرام' if language == 'ar' else 'Kilogram' }}</option>
                                <option value="liter">{{ 'لتر' if language == 'ar' else 'Liter' }}</option>
                                <option value="meter">{{ 'متر' if language == 'ar' else 'Meter' }}</option>
                                <option value="pack">{{ 'علبة' if language == 'ar' else 'Pack' }}</option>
                                <option value="box">{{ 'صندوق' if language == 'ar' else 'Box' }}</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="weight" class="form-label">
                                {{ 'الوزن (كجم)' if language == 'ar' else 'Weight (kg)' }}
                            </label>
                            <input type="number" class="form-control" id="weight" name="weight" 
                                   step="0.001" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="dimensions" class="form-label">
                                {{ 'الأبعاد (طول × عرض × ارتفاع سم)' if language == 'ar' else 'Dimensions (L × W × H cm)' }}
                            </label>
                            <input type="text" class="form-control" id="dimensions" name="dimensions"
                                   placeholder="{{ '20 × 15 × 10' if language == 'ar' else '20 × 15 × 10' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" checked>
                                <label class="form-check-label" for="track_inventory">
                                    {{ 'تتبع المخزون' if language == 'ar' else 'Track Inventory' }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_taxable" name="is_taxable">
                                <label class="form-check-label" for="is_taxable">
                                    {{ 'خاضع للضريبة' if language == 'ar' else 'Taxable' }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Image and Actions -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-image"></i>
                        {{ 'صورة المنتج' if language == 'ar' else 'Product Image' }}
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="image_preview" class="border rounded p-4 mb-3" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                            <div class="text-muted">
                                <i class="bi bi-image display-4"></i>
                                <p class="mt-2">{{ 'لا توجد صورة' if language == 'ar' else 'No image selected' }}</p>
                            </div>
                        </div>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                        <small class="text-muted">
                            {{ 'الحد الأقصى: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF' if language == 'ar' else 'Max: 5MB. Supported: JPG, PNG, GIF' }}
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear"></i>
                        {{ 'الإعدادات' if language == 'ar' else 'Settings' }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            {{ 'منتج نشط' if language == 'ar' else 'Active Product' }}
                        </label>
                        <small class="form-text text-muted d-block">
                            {{ 'المنتجات النشطة فقط تظهر في نقطة البيع' if language == 'ar' else 'Only active products appear in POS' }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ المنتج' if language == 'ar' else 'Save Product' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Calculate profit margin and final price
function calculatePricing() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    const discountPercentage = parseFloat(document.getElementById('discount_percentage').value) || 0;
    
    // Calculate profit margin
    let profitMargin = 0;
    if (costPrice > 0) {
        profitMargin = ((sellingPrice - costPrice) / costPrice) * 100;
    }
    
    // Calculate final price after discount
    const discountAmount = sellingPrice * (discountPercentage / 100);
    const finalPrice = sellingPrice - discountAmount;
    
    // Update display
    document.getElementById('profit_margin').textContent = profitMargin.toFixed(1) + '%';
    document.getElementById('final_price').textContent = 
        '{{ "ر.ق " if language == "ar" else "QAR " }}' + finalPrice.toFixed(2);
}

// Add event listeners for pricing calculations
document.getElementById('cost_price').addEventListener('input', calculatePricing);
document.getElementById('selling_price').addEventListener('input', calculatePricing);
document.getElementById('discount_percentage').addEventListener('input', calculatePricing);

// Generate barcode
function generateBarcode() {
    const barcode = Math.floor(Math.random() * ************) + 100000000000;
    document.getElementById('barcode').value = barcode.toString();
}

// Image preview
function previewImage(input) {
    const preview = document.getElementById('image_preview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;">`;
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = `
            <div class="text-muted">
                <i class="bi bi-image display-4"></i>
                <p class="mt-2">${'{{ "لا توجد صورة" if language == "ar" else "No image selected" }}'}</p>
            </div>
        `;
    }
}

// Auto-generate SKU based on category and name
document.getElementById('category_id').addEventListener('change', generateSKU);
document.getElementById('name_en').addEventListener('input', generateSKU);

function generateSKU() {
    const category = document.getElementById('category_id');
    const nameEn = document.getElementById('name_en').value;
    const skuField = document.getElementById('sku');
    
    if (category.value && nameEn && !skuField.value) {
        const categoryText = category.options[category.selectedIndex].text;
        const categoryCode = categoryText.substring(0, 3).toUpperCase();
        const nameCode = nameEn.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '');
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        skuField.value = `${categoryCode}${nameCode}${randomNum}`;
    }
}
</script>
{% endblock %}
