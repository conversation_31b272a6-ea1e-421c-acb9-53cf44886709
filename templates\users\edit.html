{% extends "base.html" %}

{% block title %}
{{ 'تعديل المستخدم - نظام نقاط البيع القطري' if language == 'ar' else 'Edit User - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.user-edit-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-section {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.form-section h5 {
    color: #8B1538;
    border-bottom: 2px solid #8B1538;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.required {
    color: #dc3545;
}

.form-control:focus {
    border-color: #8B1538;
    box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.25);
}

.btn-primary {
    background-color: #8B1538;
    border-color: #8B1538;
}

.btn-primary:hover {
    background-color: #6d1028;
    border-color: #6d1028;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="bi bi-person-gear"></i>
                {{ 'تعديل المستخدم' if language == 'ar' else 'Edit User' }}
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('dashboard.index') }}">
                            {{ 'الرئيسية' if language == 'ar' else 'Dashboard' }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('users.index') }}">
                            {{ 'المستخدمون' if language == 'ar' else 'Users' }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('users.view', user_id=user.id) }}">
                            {{ user.get_display_name(language) }}
                        </a>
                    </li>
                    <li class="breadcrumb-item active">
                        {{ 'تعديل' if language == 'ar' else 'Edit' }}
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('users.view', user_id=user.id) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card user-edit-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-gear"></i>
                        {{ 'تعديل بيانات المستخدم' if language == 'ar' else 'Edit User Information' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="editUserForm">
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5>
                                <i class="bi bi-person"></i>
                                {{ 'المعلومات الأساسية' if language == 'ar' else 'Basic Information' }}
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="username" class="form-label">
                                            {{ 'اسم المستخدم' if language == 'ar' else 'Username' }}
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               value="{{ user.username }}" readonly>
                                        <small class="form-text text-muted">
                                            {{ 'لا يمكن تغيير اسم المستخدم' if language == 'ar' else 'Username cannot be changed' }}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="email" class="form-label">
                                            {{ 'البريد الإلكتروني' if language == 'ar' else 'Email' }}
                                            <span class="required">*</span>
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="{{ user.email }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="form-section">
                            <h5>
                                <i class="bi bi-card-text"></i>
                                {{ 'المعلومات الشخصية' if language == 'ar' else 'Personal Information' }}
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="first_name_ar" class="form-label">
                                            {{ 'الاسم الأول (عربي)' if language == 'ar' else 'First Name (Arabic)' }}
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="first_name_ar" name="first_name_ar"
                                               value="{{ user.first_name_ar }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="first_name_en" class="form-label">
                                            {{ 'الاسم الأول (إنجليزي)' if language == 'ar' else 'First Name (English)' }}
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="first_name_en" name="first_name_en"
                                               value="{{ user.first_name_en }}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="last_name_ar" class="form-label">
                                            {{ 'اسم العائلة (عربي)' if language == 'ar' else 'Last Name (Arabic)' }}
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="last_name_ar" name="last_name_ar"
                                               value="{{ user.last_name_ar }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="last_name_en" class="form-label">
                                            {{ 'اسم العائلة (إنجليزي)' if language == 'ar' else 'Last Name (English)' }}
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="last_name_en" name="last_name_en"
                                               value="{{ user.last_name_en }}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="phone" class="form-label">
                                            {{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="{{ user.phone or '' }}" placeholder="+974 XXXX XXXX">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="role" class="form-label">
                                            {{ 'الدور' if language == 'ar' else 'Role' }}
                                            <span class="required">*</span>
                                        </label>
                                        <select class="form-control" id="role" name="role" required>
                                            <option value="admin" {{ 'selected' if user.role == 'admin' else '' }}>
                                                {{ 'مدير النظام' if language == 'ar' else 'System Administrator' }}
                                            </option>
                                            <option value="manager" {{ 'selected' if user.role == 'manager' else '' }}>
                                                {{ 'مدير' if language == 'ar' else 'Manager' }}
                                            </option>
                                            <option value="seller" {{ 'selected' if user.role == 'seller' else '' }}>
                                                {{ 'بائع' if language == 'ar' else 'Seller' }}
                                            </option>
                                            <option value="accountant" {{ 'selected' if user.role == 'accountant' else '' }}>
                                                {{ 'محاسب' if language == 'ar' else 'Accountant' }}
                                            </option>
                                            <option value="inventory_manager" {{ 'selected' if user.role == 'inventory_manager' else '' }}>
                                                {{ 'مدير مخزون' if language == 'ar' else 'Inventory Manager' }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="form-section">
                            <h5>
                                <i class="bi bi-toggle-on"></i>
                                {{ 'حالة المستخدم' if language == 'ar' else 'User Status' }}
                            </h5>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       {{ 'checked' if user.is_active else '' }}>
                                <label class="form-check-label" for="is_active">
                                    {{ 'المستخدم نشط' if language == 'ar' else 'User is active' }}
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-check-circle"></i>
                                    {{ 'حفظ التغييرات' if language == 'ar' else 'Save Changes' }}
                                </button>
                                <a href="{{ url_for('users.view', user_id=user.id) }}" class="btn btn-secondary btn-lg ms-2">
                                    <i class="bi bi-x-circle"></i>
                                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- User Info Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i>
                        {{ 'معلومات المستخدم' if language == 'ar' else 'User Information' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>{{ 'تاريخ الإنشاء:' if language == 'ar' else 'Created:' }}</strong><br>
                        <small class="text-muted">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>

                    {% if user.last_login %}
                    <div class="mb-3">
                        <strong>{{ 'آخر دخول:' if language == 'ar' else 'Last Login:' }}</strong><br>
                        <small class="text-muted">{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <strong>{{ 'الحالة الحالية:' if language == 'ar' else 'Current Status:' }}</strong><br>
                        <span class="badge {{ 'bg-success' if user.is_active else 'bg-danger' }}">
                            {{ 'نشط' if user.is_active else 'غير نشط' if language == 'ar' else 'Active' if user.is_active else 'Inactive' }}
                        </span>
                    </div>

                    <div class="mb-3">
                        <strong>{{ 'الدور الحالي:' if language == 'ar' else 'Current Role:' }}</strong><br>
                        <span class="badge bg-primary">
                            {% if user.role == 'admin' %}
                                {{ 'مدير النظام' if language == 'ar' else 'System Administrator' }}
                            {% elif user.role == 'manager' %}
                                {{ 'مدير' if language == 'ar' else 'Manager' }}
                            {% elif user.role == 'seller' %}
                                {{ 'بائع' if language == 'ar' else 'Seller' }}
                            {% elif user.role == 'accountant' %}
                                {{ 'محاسب' if language == 'ar' else 'Accountant' }}
                            {% elif user.role == 'inventory_manager' %}
                                {{ 'مدير مخزون' if language == 'ar' else 'Inventory Manager' }}
                            {% else %}
                                {{ user.role }}
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    const requiredFields = ['email', 'first_name_ar', 'first_name_en', 'last_name_ar', 'last_name_en', 'role'];
    let isValid = true;

    requiredFields.forEach(function(fieldName) {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('{{ "يرجى ملء جميع الحقول المطلوبة" if language == "ar" else "Please fill in all required fields" }}');
    }
});

// Remove validation styling on input
document.querySelectorAll('.form-control').forEach(function(input) {
    input.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});
</script>
{% endblock %}