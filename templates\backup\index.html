{% extends "base.html" %}

{% block title %}
{{ 'النسخ الاحتياطي والاستعادة - نظام نقاط البيع القطري' if language == 'ar' else 'Backup & Restore - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-cloud-download"></i>
                {{ 'النسخ الاحتياطي والاستعادة' if language == 'ar' else 'Backup & Restore' }}
            </h1>
            <div>
                <button type="button" class="btn btn-primary" onclick="createBackup()">
                    <i class="bi bi-cloud-arrow-up"></i>
                    {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Backup Status -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'آخر نسخة احتياطية' if language == 'ar' else 'Last Backup' }}
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {% if last_backup %}
                            {{ last_backup.created_at.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                            {{ 'لا توجد' if language == 'ar' else 'None' }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'إجمالي النسخ' if language == 'ar' else 'Total Backups' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ backups.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-archive fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'حجم النسخ' if language == 'ar' else 'Total Size' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:.1f} MB'.format(total_backup_size / 1024 / 1024) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'النسخ التلقائية' if language == 'ar' else 'Auto Backup' }}
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {% if auto_backup_enabled %}
                            <span class="text-success">{{ 'مفعل' if language == 'ar' else 'Enabled' }}</span>
                            {% else %}
                            <span class="text-danger">{{ 'معطل' if language == 'ar' else 'Disabled' }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-gear fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Backup Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-tools"></i>
                    {{ 'إجراءات النسخ الاحتياطي' if language == 'ar' else 'Backup Actions' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="createBackup()">
                        <i class="bi bi-cloud-arrow-up"></i>
                        {{ 'إنشاء نسخة احتياطية كاملة' if language == 'ar' else 'Create Full Backup' }}
                    </button>

                    <button type="button" class="btn btn-outline-primary" onclick="createDatabaseBackup()">
                        <i class="bi bi-database"></i>
                        {{ 'نسخ احتياطي لقاعدة البيانات فقط' if language == 'ar' else 'Database Backup Only' }}
                    </button>

                    <button type="button" class="btn btn-outline-info" onclick="showRestoreModal()">
                        <i class="bi bi-cloud-arrow-down"></i>
                        {{ 'استعادة من نسخة احتياطية' if language == 'ar' else 'Restore from Backup' }}
                    </button>

                    <hr>

                    <button type="button" class="btn btn-outline-warning" onclick="showAutoBackupSettings()">
                        <i class="bi bi-gear"></i>
                        {{ 'إعدادات النسخ التلقائي' if language == 'ar' else 'Auto Backup Settings' }}
                    </button>

                    <button type="button" class="btn btn-outline-danger" onclick="cleanupOldBackups()">
                        <i class="bi bi-trash"></i>
                        {{ 'تنظيف النسخ القديمة' if language == 'ar' else 'Cleanup Old Backups' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Backup Progress -->
        <div class="card mt-4" id="backupProgressCard" style="display: none;">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-hourglass-split"></i>
                    {{ 'تقدم النسخ الاحتياطي' if language == 'ar' else 'Backup Progress' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         id="backupProgressBar" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="backupStatus" class="text-center">
                    {{ 'جاري التحضير...' if language == 'ar' else 'Preparing...' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {{ 'قائمة النسخ الاحتياطية' if language == 'ar' else 'Backup List' }}
                </h6>
            </div>
            <div class="card-body p-0">
                {% if backups.items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>{{ 'اسم الملف' if language == 'ar' else 'Filename' }}</th>
                                <th>{{ 'النوع' if language == 'ar' else 'Type' }}</th>
                                <th>{{ 'الحجم' if language == 'ar' else 'Size' }}</th>
                                <th>{{ 'تاريخ الإنشاء' if language == 'ar' else 'Created' }}</th>
                                <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups.items %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ backup.filename }}</strong>
                                        {% if backup.description %}
                                        <br>
                                        <small class="text-muted">{{ backup.description }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if backup.backup_type == 'full' %}
                                    <span class="badge bg-primary">{{ 'كامل' if language == 'ar' else 'Full' }}</span>
                                    {% elif backup.backup_type == 'database' %}
                                    <span class="badge bg-info">{{ 'قاعدة بيانات' if language == 'ar' else 'Database' }}</span>
                                    {% elif backup.backup_type == 'files' %}
                                    <span class="badge bg-warning">{{ 'ملفات' if language == 'ar' else 'Files' }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ '{:.1f} MB'.format(backup.file_size / 1024 / 1024) }}</td>
                                <td>
                                    <div>{{ backup.created_at.strftime('%Y-%m-%d') }}</div>
                                    <small class="text-muted">{{ backup.created_at.strftime('%H:%M') }}</small>
                                </td>
                                <td>
                                    {% if backup.status == 'completed' %}
                                    <span class="badge bg-success">{{ 'مكتمل' if language == 'ar' else 'Completed' }}</span>
                                    {% elif backup.status == 'in_progress' %}
                                    <span class="badge bg-warning">{{ 'جاري' if language == 'ar' else 'In Progress' }}</span>
                                    {% elif backup.status == 'failed' %}
                                    <span class="badge bg-danger">{{ 'فشل' if language == 'ar' else 'Failed' }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if backup.status == 'completed' %}
                                        <a href="{{ url_for('backup.download', backup_id=backup.id) }}"
                                           class="btn btn-outline-primary" title="{{ 'تحميل' if language == 'ar' else 'Download' }}">
                                            <i class="bi bi-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-success"
                                                onclick="restoreBackup({{ backup.id }}, '{{ backup.filename }}')"
                                                title="{{ 'استعادة' if language == 'ar' else 'Restore' }}">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="deleteBackup({{ backup.id }}, '{{ backup.filename }}')"
                                                title="{{ 'حذف' if language == 'ar' else 'Delete' }}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if backups.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Backups pagination">
                        <ul class="pagination pagination-sm justify-content-center mb-0">
                            {% if backups.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('backup.index', page=backups.prev_num) }}">
                                    {{ 'السابق' if language == 'ar' else 'Previous' }}
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in backups.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != backups.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('backup.index', page=page_num) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if backups.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('backup.index', page=backups.next_num) }}">
                                    {{ 'التالي' if language == 'ar' else 'Next' }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-cloud-download display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'لا توجد نسخ احتياطية' if language == 'ar' else 'No backups found' }}</h5>
                    <p class="text-muted">{{ 'ابدأ بإنشاء نسخة احتياطية لحماية بياناتك' if language == 'ar' else 'Start by creating a backup to protect your data' }}</p>
                    <button type="button" class="btn btn-primary" onclick="createBackup()">
                        <i class="bi bi-cloud-arrow-up"></i>
                        {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Restore Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'استعادة من نسخة احتياطية' if language == 'ar' else 'Restore from Backup' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ 'تحذير: هذا الإجراء سيستبدل جميع البيانات الحالية' if language == 'ar' else 'Warning: This action will replace all current data' }}
                </div>
                <p id="restoreMessage"></p>
                <div class="mb-3">
                    <label class="form-label">{{ 'كلمة مرور التأكيد' if language == 'ar' else 'Confirmation Password' }}</label>
                    <input type="password" class="form-control" id="restorePassword" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-warning" id="confirmRestore">
                    {{ 'تأكيد الاستعادة' if language == 'ar' else 'Confirm Restore' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Backup Modal -->
<div class="modal fade" id="deleteBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'حذف النسخة الاحتياطية' if language == 'ar' else 'Delete Backup' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="deleteBackupMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBackup">
                    {{ 'حذف' if language == 'ar' else 'Delete' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Auto Backup Settings Modal -->
<div class="modal fade" id="autoBackupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'إعدادات النسخ التلقائي' if language == 'ar' else 'Auto Backup Settings' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="autoBackupForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableAutoBackup"
                                   {{ 'checked' if auto_backup_enabled }}>
                            <label class="form-check-label" for="enableAutoBackup">
                                {{ 'تفعيل النسخ التلقائي' if language == 'ar' else 'Enable Auto Backup' }}
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ 'تكرار النسخ' if language == 'ar' else 'Backup Frequency' }}</label>
                        <select class="form-select" name="backup_frequency">
                            <option value="daily" {{ 'selected' if backup_frequency == 'daily' }}>
                                {{ 'يومي' if language == 'ar' else 'Daily' }}
                            </option>
                            <option value="weekly" {{ 'selected' if backup_frequency == 'weekly' }}>
                                {{ 'أسبوعي' if language == 'ar' else 'Weekly' }}
                            </option>
                            <option value="monthly" {{ 'selected' if backup_frequency == 'monthly' }}>
                                {{ 'شهري' if language == 'ar' else 'Monthly' }}
                            </option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ 'وقت النسخ' if language == 'ar' else 'Backup Time' }}</label>
                        <input type="time" class="form-control" name="backup_time"
                               value="{{ backup_time or '02:00' }}">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ 'الاحتفاظ بالنسخ (أيام)' if language == 'ar' else 'Retention Period (days)' }}</label>
                        <input type="number" class="form-control" name="retention_days" min="1" max="365"
                               value="{{ retention_days or 30 }}">
                        <div class="form-text">
                            {{ 'سيتم حذف النسخ الأقدم من هذه المدة تلقائياً' if language == 'ar' else 'Backups older than this period will be automatically deleted' }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {{ 'حفظ الإعدادات' if language == 'ar' else 'Save Settings' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentBackupId = null;
let currentAction = null;

// Create backup
function createBackup() {
    showBackupProgress();

    fetch('/backup/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'full',
            description: 'Manual full backup'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            pollBackupStatus(data.backup_id);
        } else {
            hideBackupProgress();
            alert(data.error || 'حدث خطأ في إنشاء النسخة الاحتياطية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        hideBackupProgress();
        alert('حدث خطأ في إنشاء النسخة الاحتياطية');
    });
}

// Create database backup
function createDatabaseBackup() {
    showBackupProgress();

    fetch('/backup/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'database',
            description: 'Manual database backup'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            pollBackupStatus(data.backup_id);
        } else {
            hideBackupProgress();
            alert(data.error || 'حدث خطأ في إنشاء النسخة الاحتياطية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        hideBackupProgress();
        alert('حدث خطأ في إنشاء النسخة الاحتياطية');
    });
}

// Show backup progress
function showBackupProgress() {
    document.getElementById('backupProgressCard').style.display = 'block';
    document.getElementById('backupProgressBar').style.width = '0%';
    document.getElementById('backupStatus').textContent = '{{ "جاري التحضير..." if language == "ar" else "Preparing..." }}';
}

// Hide backup progress
function hideBackupProgress() {
    document.getElementById('backupProgressCard').style.display = 'none';
}

// Poll backup status
function pollBackupStatus(backupId) {
    const interval = setInterval(() => {
        fetch(`/backup/status/${backupId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'completed') {
                clearInterval(interval);
                document.getElementById('backupProgressBar').style.width = '100%';
                document.getElementById('backupStatus').textContent = '{{ "تم بنجاح!" if language == "ar" else "Completed successfully!" }}';
                setTimeout(() => {
                    hideBackupProgress();
                    location.reload();
                }, 2000);
            } else if (data.status === 'failed') {
                clearInterval(interval);
                hideBackupProgress();
                alert('{{ "فشل في إنشاء النسخة الاحتياطية" if language == "ar" else "Backup creation failed" }}');
            } else {
                document.getElementById('backupProgressBar').style.width = `${data.progress || 0}%`;
                document.getElementById('backupStatus').textContent = data.message || '{{ "جاري العمل..." if language == "ar" else "Working..." }}';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            clearInterval(interval);
            hideBackupProgress();
        });
    }, 2000);
}

// Restore backup
function restoreBackup(backupId, filename) {
    currentBackupId = backupId;
    currentAction = 'restore';

    document.getElementById('restoreMessage').textContent =
        '{{ "هل أنت متأكد من استعادة النسخة الاحتياطية" if language == "ar" else "Are you sure you want to restore backup" }}' +
        ` "${filename}"?`;

    const modal = new bootstrap.Modal(document.getElementById('restoreModal'));
    modal.show();
}

// Delete backup
function deleteBackup(backupId, filename) {
    currentBackupId = backupId;
    currentAction = 'delete';

    document.getElementById('deleteBackupMessage').textContent =
        '{{ "هل أنت متأكد من حذف النسخة الاحتياطية" if language == "ar" else "Are you sure you want to delete backup" }}' +
        ` "${filename}"?`;

    const modal = new bootstrap.Modal(document.getElementById('deleteBackupModal'));
    modal.show();
}

// Show auto backup settings
function showAutoBackupSettings() {
    const modal = new bootstrap.Modal(document.getElementById('autoBackupModal'));
    modal.show();
}

// Cleanup old backups
function cleanupOldBackups() {
    if (confirm('{{ "هل أنت متأكد من حذف النسخ القديمة؟" if language == "ar" else "Are you sure you want to cleanup old backups?" }}')) {
        fetch('/backup/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`{{ "تم حذف" if language == "ar" else "Deleted" }} ${data.deleted_count} {{ "نسخة احتياطية" if language == "ar" else "backups" }}`);
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ');
        });
    }
}

// Confirm restore
document.getElementById('confirmRestore').addEventListener('click', function() {
    const password = document.getElementById('restorePassword').value;

    if (!password) {
        alert('{{ "يرجى إدخال كلمة المرور" if language == "ar" else "Please enter password" }}');
        return;
    }

    if (currentBackupId && currentAction === 'restore') {
        fetch(`/backup/restore/${currentBackupId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                password: password
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ "تم بدء عملية الاستعادة بنجاح" if language == "ar" else "Restore process started successfully" }}');
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ');
        });

        bootstrap.Modal.getInstance(document.getElementById('restoreModal')).hide();
    }
});

// Confirm delete backup
document.getElementById('confirmDeleteBackup').addEventListener('click', function() {
    if (currentBackupId && currentAction === 'delete') {
        fetch(`/backup/delete/${currentBackupId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ');
        });

        bootstrap.Modal.getInstance(document.getElementById('deleteBackupModal')).hide();
    }
});

// Auto backup form
document.getElementById('autoBackupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    data.enabled = document.getElementById('enableAutoBackup').checked;

    fetch('/backup/auto-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('{{ "تم حفظ الإعدادات بنجاح" if language == "ar" else "Settings saved successfully" }}');
            location.reload();
        } else {
            alert(data.error || 'حدث خطأ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ');
    });

    bootstrap.Modal.getInstance(document.getElementById('autoBackupModal')).hide();
});
</script>
{% endblock %}