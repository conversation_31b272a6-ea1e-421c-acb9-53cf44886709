#!/bin/bash

# Qatar POS System - Startup Script for Linux/Mac

echo "========================================"
echo "🇶🇦 نظام نقاط البيع القطري"
echo "   Qatar POS System"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

echo "✅ Python found: $(python3 --version)"
echo

# Install/update requirements
echo "📦 Installing/updating requirements..."
python3 -m pip install --upgrade pip
python3 -m pip install Flask Flask-SQLAlchemy Flask-Login Flask-Migrate Flask-WTF python-dotenv

echo
echo "🚀 Starting Qatar POS System..."
echo
echo "📍 Server will be available at: http://localhost:5000"
echo "🔑 Default login: admin / admin123"
echo "🌐 Test page: http://localhost:5000/test"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo

# Start the server
python3 run.py
