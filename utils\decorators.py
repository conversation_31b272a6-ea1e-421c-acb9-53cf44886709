"""
Custom decorators for Qatar POS System
Includes role-based access control and other utility decorators
"""

from functools import wraps
from flask import abort, redirect, url_for, flash, request
from flask_login import current_user
from utils.helpers import get_user_language

def role_required(*roles):
    """
    Decorator to require specific user roles
    Usage: @role_required('manager', 'accountant')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            
            if current_user.role not in roles:
                language = get_user_language()
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة' if language == 'ar'
                      else 'You do not have permission to access this page', 'error')
                return redirect(url_for('auth.unauthorized'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def permission_required(permission):
    """
    Decorator to require specific permission
    Usage: @permission_required('sales')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            
            if not current_user.has_permission(permission):
                language = get_user_language()
                flash('ليس لديك صلاحية لتنفيذ هذا الإجراء' if language == 'ar'
                      else 'You do not have permission to perform this action', 'error')
                return redirect(url_for('auth.unauthorized'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def manager_required(f):
    """Decorator to require manager role"""
    return role_required('manager')(f)

def seller_or_manager_required(f):
    """Decorator to require seller or manager role"""
    return role_required('seller', 'manager')(f)

def accountant_or_manager_required(f):
    """Decorator to require accountant or manager role"""
    return role_required('accountant', 'manager')(f)

def ajax_required(f):
    """Decorator to require AJAX requests"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not request.is_xhr:
            abort(400)
        return f(*args, **kwargs)
    return decorated_function

def validate_json(required_fields=None):
    """
    Decorator to validate JSON request data
    Usage: @validate_json(['name', 'email'])
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return {'error': 'Request must be JSON'}, 400
            
            data = request.get_json()
            if not data:
                return {'error': 'No JSON data provided'}, 400
            
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return {'error': f'Missing required fields: {", ".join(missing_fields)}'}, 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
