#!/usr/bin/env python3
"""
Qatar POS System - Server on Port 3000
Another alternative port
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import sys

def open_browser():
    """Open browser after server starts"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:3000')
        print("🌐 Browser opened automatically")
    except:
        print("🌐 Please open browser manually: http://localhost:3000")

class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 60px;
            border-radius: 30px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            max-width: 700px;
            width: 90%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 4em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.4);
        }
        .success {
            background: rgba(40, 167, 69, 0.4);
            border: 3px solid rgba(40, 167, 69, 0.7);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
            font-size: 1.5em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .port-info {
            background: rgba(0, 123, 255, 0.3);
            border: 2px solid rgba(0, 123, 255, 0.6);
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            font-weight: bold;
        }
        .status {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(40, 167, 69, 0.9);
            padding: 15px 25px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="status">🟢 PORT 3000</div>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        
        <div class="success">
            🎉 النظام يعمل على المنفذ 3000!<br>
            🎉 System Running on Port 3000!
        </div>
        
        <div class="port-info">
            🔧 منفذ بديل آخر!<br>
            🔧 Another Alternative Port!<br>
            📍 URL: http://localhost:3000
        </div>
        
        <div style="margin-top: 50px; font-size: 1.3em;">
            <p><strong>✅ النظام يعمل بنجاح!</strong></p>
            <p><strong>✅ System Working Successfully!</strong></p>
        </div>
    </div>
</body>
</html>'''
        
        self.wfile.write(html.encode('utf-8'))

def main():
    print("🇶🇦 Qatar POS System - Port 3000 Server")
    print("=" * 60)
    print("📍 Server URL: http://localhost:3000")
    print("🌐 Opening browser automatically...")
    print("=" * 60)
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", 3000), QatarPOSHandler) as httpd:
            print("✅ Server started successfully on port 3000!")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
