@echo off
chcp 65001 >nul
title Qatar POS System - Desktop Application Launcher

echo ========================================
echo    Qatar POS System
echo    نظام نقاط البيع القطري
echo    Desktop Application Launcher
echo    مشغل تطبيق سطح المكتب
echo ========================================
echo.

echo 🔍 التحقق من النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من الملفات
if not exist "desktop_app.py" (
    echo ❌ ملف desktop_app.py غير موجود
    pause
    exit /b 1
)

if not exist "simple_desktop.py" (
    echo ❌ ملف simple_desktop.py غير موجود
    pause
    exit /b 1
)

echo ✅ ملفات التطبيق موجودة

REM التحقق من Flask
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Flask غير مثبت، جاري التثبيت...
    pip install flask flask-sqlalchemy flask-login flask-migrate flask-babel flask-wtf
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)

echo ✅ Flask متوفر
echo.

echo 🎯 اختر نوع التطبيق:
echo.
echo 1. التطبيق المبسط (Simple Desktop)
echo 2. التطبيق الكامل (Full Desktop)
echo 3. اختبار النظام (System Test)
echo 4. بناء ملف EXE (Build EXE)
echo 5. إظهار الدليل (Show Guide)
echo.

set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل التطبيق المبسط...
    echo.
    python simple_desktop.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل التطبيق الكامل...
    echo.
    python desktop_app.py
) else if "%choice%"=="3" (
    echo.
    echo 🔍 تشغيل اختبار النظام...
    echo.
    python quick_desktop_test.py
) else if "%choice%"=="4" (
    echo.
    echo 🔨 بناء ملف EXE...
    echo.
    python build_exe.py
) else if "%choice%"=="5" (
    echo.
    echo 📖 عرض الدليل...
    echo.
    if exist "DESKTOP_QUICK_GUIDE.md" (
        notepad DESKTOP_QUICK_GUIDE.md
    ) else (
        echo ❌ ملف الدليل غير موجود
    )
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo.
)

echo.
echo 👋 انتهى التشغيل
pause
