{% extends "base.html" %}

{% block title %}
{{ 'تقرير المنتجات الأكثر مبيعاً - نظام نقاط البيع القطري' if language == 'ar' else 'Top Selling Products Report - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-trophy"></i>
                {{ 'تقرير المنتجات الأكثر مبيعاً' if language == 'ar' else 'Top Selling Products Report' }}
            </h1>
            <div>
                <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                    <i class="bi bi-file-pdf"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="bi bi-file-excel"></i>
                    Excel
                </button>
                <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للتقارير' if language == 'ar' else 'Back to Reports' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'تصفية التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                        <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                        <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">{{ 'عدد المنتجات' if language == 'ar' else 'Number of Products' }}</label>
                        <select class="form-select" name="limit">
                            <option value="10" {% if limit == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if limit == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if limit == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if limit == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                                {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي الكمية المباعة' if language == 'ar' else 'Total Quantity Sold' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,}'.format(total_quantity) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'إجمالي الإيرادات' if language == 'ar' else 'Total Revenue' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(total_revenue) if language == 'ar' else 'QAR {:,.2f}'.format(total_revenue) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'عدد المنتجات' if language == 'ar' else 'Number of Products' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ top_products|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {{ 'رسم بياني للمنتجات الأكثر مبيعاً' if language == 'ar' else 'Top Selling Products Chart' }}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="topProductsChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'تفاصيل المنتجات الأكثر مبيعاً' if language == 'ar' else 'Top Selling Products Details' }}
                </h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'الترتيب' if language == 'ar' else 'Rank' }}</th>
                                <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                                <th>{{ 'الكمية المباعة' if language == 'ar' else 'Quantity Sold' }}</th>
                                <th>{{ 'إجمالي الإيرادات' if language == 'ar' else 'Total Revenue' }}</th>
                                <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</th>
                                <th>{{ 'متوسط الكمية' if language == 'ar' else 'Avg Quantity' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in top_products %}
                            <tr>
                                <td>
                                    {% if loop.index <= 3 %}
                                    <span class="badge bg-{{ 'warning' if loop.index == 1 else 'secondary' if loop.index == 2 else 'info' }}">
                                        #{{ loop.index }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">#{{ loop.index }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if product.Product.image_filename %}
                                        <img src="{{ url_for('static', filename='uploads/products/' + product.Product.image_filename) }}" 
                                             alt="{{ product.Product.get_name(language) }}" class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                            <i class="bi bi-image text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <strong>{{ product.Product.get_name(language) }}</strong>
                                            <br>
                                            <small class="text-muted">{{ 'الرمز:' if language == 'ar' else 'SKU:' }} {{ product.Product.sku }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary fs-6">{{ '{:,}'.format(product.total_sold) }}</span>
                                </td>
                                <td>{{ '{:,.2f} ر.ق'.format(product.total_revenue) if language == 'ar' else 'QAR {:,.2f}'.format(product.total_revenue) }}</td>
                                <td>{{ '{:,}'.format(product.transaction_count) }}</td>
                                <td>{{ '{:.1f}'.format(product.avg_quantity) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'لا توجد بيانات للفترة المحددة' if language == 'ar' else 'No data found for the selected period' }}</h5>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Top Products Chart
const ctx = document.getElementById('topProductsChart').getContext('2d');
const topProductsChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: [
            {% for product in top_products[:10] %}
            '{{ product.Product.get_name(language)[:20] }}...'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ 'الكمية المباعة' if language == 'ar' else 'Quantity Sold' }}',
            data: [
                {% for product in top_products[:10] %}
                {{ product.total_sold }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{{ 'أفضل 10 منتجات مبيعاً' if language == 'ar' else 'Top 10 Selling Products' }}'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            },
            x: {
                ticks: {
                    maxRotation: 45
                }
            }
        }
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    window.open(`{{ url_for('reports.top_selling') }}?${params.toString()}`);
}
</script>
{% endblock %}
