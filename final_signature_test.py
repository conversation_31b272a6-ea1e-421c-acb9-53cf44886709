#!/usr/bin/env python3
"""
Final comprehensive test for manager signature in invoices - Qatar POS System
"""

from app import create_app
from models.setting import Setting
from models.sale import Sale
from models.user import User
from extensions import db
import os

def test_signature_complete_setup():
    """Complete test of signature functionality"""
    app = create_app()
    
    with app.app_context():
        print("🔍 اختبار شامل لتوقيع المسؤول في الفواتير")
        print("=" * 60)
        
        # Test 1: Check all signature settings
        print("1️⃣ فحص إعدادات التوقيع الكاملة...")
        
        signature_settings = {
            'manager_signature': Setting.get_setting('manager_signature'),
            'manager_name': Setting.get_setting('manager_name'),
            'manager_title': Setting.get_setting('manager_title'),
            'signature_width': Setting.get_setting('signature_width'),
            'signature_height': Setting.get_setting('signature_height')
        }
        
        all_complete = True
        for key, value in signature_settings.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value or 'غير محدد'}")
            if not value:
                all_complete = False
        
        # Test 2: Check signature file
        print("\n2️⃣ فحص ملف التوقيع...")
        
        signature_file = signature_settings['manager_signature']
        if signature_file:
            signature_path = f"static/uploads/signatures/{signature_file}"
            if os.path.exists(signature_path):
                file_size = os.path.getsize(signature_path)
                print(f"   ✅ ملف التوقيع موجود: {signature_path}")
                print(f"   📊 حجم الملف: {file_size:,} بايت")
                
                # Check file type
                file_ext = os.path.splitext(signature_file)[1].lower()
                if file_ext in ['.png', '.jpg', '.jpeg', '.gif']:
                    print(f"   ✅ نوع الملف صحيح: {file_ext}")
                else:
                    print(f"   ⚠️ نوع الملف غير مدعوم: {file_ext}")
                    all_complete = False
            else:
                print(f"   ❌ ملف التوقيع غير موجود: {signature_path}")
                all_complete = False
        else:
            print("   ⚠️ لم يتم تحديد ملف توقيع")
        
        # Test 3: Check template files
        print("\n3️⃣ فحص ملفات القوالب...")
        
        template_files = [
            'templates/sales/invoice.html',
            'templates/sales/print_invoice.html'
        ]
        
        for template_file in template_files:
            if os.path.exists(template_file):
                print(f"   ✅ قالب موجود: {template_file}")
                
                # Check if signature code exists in template
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                signature_elements = [
                    'manager_signature',
                    'توقيع المسؤول',
                    'Authorized Signature'
                ]
                
                found_elements = sum(1 for element in signature_elements if element in content)
                print(f"      📋 عناصر التوقيع: {found_elements}/{len(signature_elements)}")
                
                if found_elements < len(signature_elements):
                    all_complete = False
            else:
                print(f"   ❌ قالب غير موجود: {template_file}")
                all_complete = False
        
        # Test 4: Check sales data
        print("\n4️⃣ فحص بيانات المبيعات...")
        
        sales_count = Sale.query.count()
        if sales_count > 0:
            print(f"   ✅ عدد المبيعات: {sales_count}")
            
            # Get a sample sale
            sample_sale = Sale.query.first()
            print(f"   📄 مبيعة تجريبية: {sample_sale.sale_number}")
            print(f"   💰 المبلغ: {sample_sale.total_amount} ر.ق")
        else:
            print("   ⚠️ لا توجد مبيعات للاختبار")
        
        # Test 5: Check user authentication
        print("\n5️⃣ فحص المستخدمين...")
        
        users_count = User.query.count()
        if users_count > 0:
            print(f"   ✅ عدد المستخدمين: {users_count}")
            
            admin_user = User.query.filter_by(role='admin').first()
            if admin_user:
                print(f"   👤 مدير النظام: {admin_user.username}")
            else:
                print("   ⚠️ لا يوجد مدير نظام")
        else:
            print("   ❌ لا يوجد مستخدمين")
            all_complete = False
        
        # Test 6: Generate test invoice HTML
        print("\n6️⃣ إنشاء فاتورة تجريبية...")
        
        if sales_count > 0 and signature_file:
            try:
                # Create a test invoice HTML
                test_invoice_html = generate_test_invoice_html(sample_sale, signature_settings)
                
                # Save test invoice
                test_file = 'static/test_invoice_with_signature.html'
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(test_invoice_html)
                
                print(f"   ✅ تم إنشاء فاتورة تجريبية: {test_file}")
                print(f"   🌐 يمكن عرضها على: http://localhost:2626/static/test_invoice_with_signature.html")
                
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء الفاتورة التجريبية: {e}")
                all_complete = False
        
        # Test 7: Overall assessment
        print("\n7️⃣ التقييم النهائي...")
        
        if all_complete:
            print("   🎉 ممتاز! جميع إعدادات التوقيع مكتملة وجاهزة")
            print("   ✅ التوقيع سيظهر في جميع الفواتير")
            print("   🚀 النظام جاهز للاستخدام التجاري")
        else:
            print("   ⚠️ بعض الإعدادات تحتاج إلى مراجعة")
            print("   📝 راجع النقاط المذكورة أعلاه")
        
        return all_complete

def generate_test_invoice_html(sale, signature_settings):
    """Generate test invoice HTML with signature"""
    
    html_template = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة تجريبية مع التوقيع - {sale.sale_number}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .invoice-container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .company-name {{
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }}
        .invoice-title {{
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
        }}
        .invoice-details {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }}
        .detail-box {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            width: 45%;
        }}
        .items-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }}
        .items-table th, .items-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }}
        .items-table th {{
            background: #007bff;
            color: white;
        }}
        .totals {{
            text-align: right;
            margin-bottom: 40px;
        }}
        .total-row {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        .final-total {{
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            border-top: 2px solid #007bff;
            padding-top: 10px;
        }}
        .signature-section {{
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #ddd;
        }}
        .stamp-area, .signature-area {{
            text-align: center;
            width: 45%;
        }}
        .signature-image {{
            max-width: 200px;
            max-height: 80px;
            margin-bottom: 10px;
        }}
        .signature-placeholder {{
            border-bottom: 2px solid #333;
            width: 200px;
            height: 60px;
            margin: 0 auto 10px;
        }}
        .manager-info {{
            margin-top: 10px;
        }}
        .manager-name {{
            font-weight: bold;
            font-size: 14px;
        }}
        .manager-title {{
            font-size: 12px;
            color: #666;
        }}
        .signature-label {{
            font-weight: bold;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">شركة قطر لنقاط البيع</div>
            <div class="invoice-title">فاتورة مبيعات</div>
        </div>
        
        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="detail-box">
                <strong>تفاصيل الفاتورة:</strong><br>
                رقم الفاتورة: {sale.sale_number}<br>
                التاريخ: {sale.sale_date.strftime('%Y-%m-%d')}<br>
                البائع: {sale.seller.get_full_name('ar') if sale.seller else 'غير محدد'}
            </div>
            <div class="detail-box">
                <strong>تفاصيل العميل:</strong><br>
                العميل: {sale.customer.get_name('ar') if sale.customer else 'عميل نقدي'}<br>
                الهاتف: {sale.customer.phone if sale.customer and sale.customer.phone else 'غير محدد'}<br>
                البريد: {sale.customer.email if sale.customer and sale.customer.email else 'غير محدد'}
            </div>
        </div>
        
        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>منتج تجريبي</td>
                    <td>1</td>
                    <td>{sale.total_amount:.2f} ر.ق</td>
                    <td>{sale.total_amount:.2f} ر.ق</td>
                </tr>
            </tbody>
        </table>
        
        <!-- Totals -->
        <div class="totals">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>{sale.subtotal:.2f} ر.ق</span>
            </div>
            <div class="total-row">
                <span>الخصم:</span>
                <span>{sale.discount_amount:.2f} ر.ق</span>
            </div>
            <div class="total-row">
                <span>الضريبة:</span>
                <span>{sale.tax_amount:.2f} ر.ق</span>
            </div>
            <div class="total-row final-total">
                <span>المجموع الكلي:</span>
                <span>{sale.total_amount:.2f} ر.ق</span>
            </div>
        </div>
        
        <!-- Signature Section -->
        <div class="signature-section">
            <div class="stamp-area">
                <div style="width: 120px; height: 120px; border: 2px solid #007bff; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                    <small style="text-align: center; color: #007bff; font-weight: bold;">
                        ختم الشركة<br>
                        <span style="font-size: 10px;">COMPANY SEAL</span>
                    </small>
                </div>
                <div style="margin-top: 10px; font-weight: bold;">ختم الشركة</div>
            </div>
            
            <div class="signature-area">
                {"<img src='/static/uploads/signatures/" + signature_settings['manager_signature'] + "' alt='توقيع المسؤول' class='signature-image'>" if signature_settings['manager_signature'] else "<div class='signature-placeholder'></div>"}
                <div class="manager-info">
                    <div class="manager-name">{signature_settings['manager_name'] or 'المدير العام'}</div>
                    <div class="manager-title">{signature_settings['manager_title'] or 'المدير العام'}</div>
                </div>
                <div class="signature-label">توقيع المسؤول</div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <strong>شكراً لتعاملكم معنا</strong><br>
            <small>نظام نقاط البيع القطري - تم إنشاء هذه الفاتورة تلقائياً</small>
        </div>
    </div>
</body>
</html>
"""
    
    return html_template

def create_signature_usage_guide():
    """Create a comprehensive usage guide"""
    
    guide_content = """
# دليل استخدام توقيع المسؤول في الفواتير

## 🎯 نظرة عامة
تم إضافة ميزة توقيع المسؤول إلى جميع الفواتير في نظام نقاط البيع القطري.

## 📋 المواقع التي يظهر فيها التوقيع:
✅ الفواتير العادية (/sales/{id}/invoice)
✅ الفواتير المطبوعة (/sales/{id}/print)
✅ جميع أنواع الفواتير والإيصالات

## ⚙️ إعداد التوقيع:

### 1. الوصول إلى الإعدادات:
- اذهب إلى: http://localhost:2626/settings/company
- ابحث عن قسم "توقيع المسؤول"

### 2. رفع ملف التوقيع:
- اختر ملف التوقيع (PNG, JPG, GIF)
- الحد الأقصى: 2MB
- الأبعاد المقترحة: 200x80 بكسل
- يُفضل خلفية شفافة

### 3. إدخال معلومات المسؤول:
- اسم المسؤول (مثال: أحمد المنصوري)
- منصب المسؤول (مثال: المدير العام)

### 4. تحديد أبعاد التوقيع:
- العرض: 200 بكسل (افتراضي)
- الارتفاع: 80 بكسل (افتراضي)

## 🎨 مظهر التوقيع في الفاتورة:

### مع ملف التوقيع:
```
┌─────────────────────────────────────┐
│  ختم الشركة    │    توقيع المسؤول   │
│      🏢         │        ✍️         │
│   ختم الشركة    │   [صورة التوقيع]   │
│                │   أحمد المنصوري   │
│                │    المدير العام    │
│                │   توقيع المسؤول    │
└─────────────────────────────────────┘
```

### بدون ملف التوقيع:
```
┌─────────────────────────────────────┐
│  ختم الشركة    │    توقيع المسؤول   │
│      🏢         │    ____________   │
│   ختم الشركة    │   أحمد المنصوري   │
│                │    المدير العام    │
│                │   توقيع المسؤول    │
└─────────────────────────────────────┘
```

## 🔧 استكشاف الأخطاء:

### المشكلة: التوقيع لا يظهر
✅ تأكد من رفع ملف التوقيع
✅ تحقق من صيغة الملف (PNG/JPG/GIF)
✅ تأكد من حجم الملف (أقل من 2MB)

### المشكلة: التوقيع صغير جداً أو كبير جداً
✅ عدل أبعاد التوقيع من الإعدادات
✅ جرب أبعاد مختلفة (150x60 أو 250x100)

### المشكلة: اسم المسؤول لا يظهر
✅ أدخل اسم المسؤول في الإعدادات
✅ أدخل منصب المسؤول في الإعدادات

## 💡 نصائح للحصول على أفضل النتائج:

1. **جودة التوقيع:**
   - استخدم صورة عالية الجودة
   - تأكد من وضوح التوقيع
   - استخدم خلفية شفافة (PNG)

2. **الأبعاد:**
   - النسبة المثالية: 2.5:1 (العرض:الارتفاع)
   - للتوقيعات الطويلة: 250x80
   - للتوقيعات المدورة: 150x100

3. **الألوان:**
   - استخدم ألوان داكنة للتوقيع
   - تجنب الألوان الفاتحة جداً
   - الأسود أو الأزرق الداكن مثالي

## 🚀 الاستخدام:

بعد إعداد التوقيع، سيظهر تلقائياً في:
- جميع الفواتير الجديدة
- الفواتير المطبوعة
- إيصالات المبيعات

لا حاجة لأي إعدادات إضافية!

## 📞 الدعم:

إذا واجهت أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع إعدادات الشركة
3. تأكد من صحة ملف التوقيع
4. اتصل بالدعم الفني إذا لزم الأمر
"""
    
    # Save guide
    with open('static/signature_usage_guide.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 تم إنشاء دليل الاستخدام: static/signature_usage_guide.md")

if __name__ == '__main__':
    print("🔬 الاختبار النهائي الشامل لتوقيع المسؤول")
    print("=" * 70)
    
    try:
        # Run comprehensive test
        success = test_signature_complete_setup()
        
        # Create usage guide
        create_signature_usage_guide()
        
        print("\n" + "=" * 70)
        if success:
            print("🎉 تهانينا! جميع اختبارات التوقيع نجحت بامتياز!")
            print("✅ توقيع المسؤول جاهز للاستخدام في الإنتاج")
            print("🚀 النظام مكتمل ومجهز للعمل التجاري")
        else:
            print("⚠️ هناك بعض النقاط التي تحتاج إلى مراجعة")
            print("📝 راجع التفاصيل أعلاه وأكمل الإعدادات المطلوبة")
        
        print("\n📚 الموارد المتاحة:")
        print("   📖 دليل الاستخدام: static/signature_usage_guide.md")
        print("   🌐 فاتورة تجريبية: http://localhost:2626/static/test_invoice_with_signature.html")
        print("   ⚙️ إعدادات الشركة: http://localhost:2626/settings/company")
        
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار النهائي: {e}")
        import traceback
        traceback.print_exc()
