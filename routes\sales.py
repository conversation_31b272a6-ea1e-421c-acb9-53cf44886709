"""
Sales routes for Qatar POS System
Handles POS interface, sales transactions, and invoice generation
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models.sale import Sale, SaleItem, Payment
from models.product import Product
from models.customer import Customer
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language

sales_bp = Blueprint('sales', __name__)

@sales_bp.route('/')
@login_required
@permission_required('sales')
def index():
    """List all sales"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    query = Sale.query
    
    # Apply filters
    if search:
        query = query.filter(Sale.sale_number.contains(search))
    
    if status_filter:
        query = query.filter(Sale.status == status_filter)
    
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.sale_date >= from_date)
        except ValueError:
            pass
    
    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Sale.sale_date <= to_date)
        except ValueError:
            pass
    
    sales = query.order_by(Sale.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('sales/index.html',
                         sales=sales,
                         search=search,
                         status_filter=status_filter,
                         date_from=date_from,
                         date_to=date_to,
                         language=language)

@sales_bp.route('/pos')
@login_required
@permission_required('sales')
def pos():
    """Point of Sale interface"""
    language = get_user_language()
    
    # Get recent customers for quick selection
    recent_customers = Customer.query.filter_by(is_active=True).order_by(
        Customer.last_purchase_date.desc().nullslast()
    ).limit(10).all()
    
    return render_template('sales/pos.html',
                         recent_customers=recent_customers,
                         language=language)

@sales_bp.route('/api/create', methods=['POST'])
@login_required
@permission_required('sales')
def api_create_sale():
    """API endpoint to create a new sale"""
    data = request.get_json()
    
    if not data or 'items' not in data or not data['items']:
        return jsonify({'error': 'No items provided'}), 400
    
    try:
        # Create sale
        sale = Sale(
            sale_number=Sale.generate_sale_number(),
            seller_id=current_user.id,
            customer_id=data.get('customer_id'),
            payment_method=data.get('payment_method', 'cash'),
            notes=data.get('notes', '')
        )
        
        db.session.add(sale)
        db.session.flush()  # Get sale ID
        
        # Add items
        for item_data in data['items']:
            product = Product.query.get(item_data['product_id'])
            if not product:
                return jsonify({'error': f'Product {item_data["product_id"]} not found'}), 400
            
            # Check stock availability
            if product.track_inventory and not product.can_sell_quantity(item_data['quantity']):
                return jsonify({'error': f'Insufficient stock for {product.get_name()}'}), 400
            
            # Create sale item
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product.id,
                quantity=item_data['quantity'],
                unit_price=item_data.get('unit_price', product.get_final_price())
            )
            sale_item.calculate_total()
            db.session.add(sale_item)
            
            # Update product stock
            if product.track_inventory:
                product.update_stock(-item_data['quantity'], 'sale')
        
        # Calculate totals
        sale.calculate_totals()
        
        # Apply discount if provided
        if data.get('discount_amount'):
            sale.apply_discount(data['discount_amount'])
        
        # Process payment
        payment_amount = data.get('payment_amount', sale.total_amount)
        sale.process_payment(payment_amount, data.get('payment_method', 'cash'))
        
        # Generate QR code for Qatar Tax Authority
        sale.generate_qr_code()
        
        # Update customer last purchase date
        if sale.customer:
            sale.customer.update_last_purchase()
        
        db.session.commit()
        
        language = get_user_language()
        return jsonify({
            'success': True,
            'sale': sale.to_dict(language),
            'sale_id': sale.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@sales_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('sales')
def create():
    """Create new sale (manual entry)"""
    language = get_user_language()

    if request.method == 'POST':
        customer_id = request.form.get('customer_id', type=int)
        payment_method = request.form.get('payment_method', 'cash')
        notes = request.form.get('notes', '').strip()

        try:
            # Create sale
            sale = Sale(
                customer_id=customer_id,
                payment_method=payment_method,
                notes=notes,
                status='completed'
            )

            db.session.add(sale)
            db.session.flush()  # Get sale ID

            # Add items (this would be more complex in real implementation)
            # For now, redirect to POS to add items
            db.session.commit()

            flash('تم إنشاء المبيعة بنجاح' if language == 'ar'
                  else 'Sale created successfully', 'success')
            return redirect(url_for('sales.view', sale_id=sale.id))

        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المبيعة' if language == 'ar'
                  else 'Error creating sale', 'error')

    # Get customers for dropdown
    from models.customer import Customer
    customers = Customer.query.filter_by(is_active=True).all()

    return render_template('sales/create.html', customers=customers, language=language)

@sales_bp.route('/<int:sale_id>')
@login_required
@permission_required('sales')
def view(sale_id):
    """View sale details"""
    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)

    return render_template('sales/view.html', sale=sale, language=language)

@sales_bp.route('/<int:sale_id>/invoice')
@login_required
@permission_required('sales')
def invoice(sale_id):
    """Generate and display invoice"""
    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)
    
    return render_template('sales/invoice.html', sale=sale, language=language)

@sales_bp.route('/<int:sale_id>/print')
@login_required
@permission_required('sales')
def print_invoice(sale_id):
    """Print-friendly invoice page"""
    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)
    
    return render_template('sales/print_invoice.html', sale=sale, language=language)

@sales_bp.route('/<int:sale_id>/refund', methods=['GET', 'POST'])
@login_required
@permission_required('sales')
def refund(sale_id):
    """Process sale refund"""
    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)
    
    if sale.status == 'refunded':
        flash('هذه المبيعة مسترجعة بالفعل' if language == 'ar' 
              else 'This sale is already refunded', 'error')
        return redirect(url_for('sales.view', sale_id=sale.id))
    
    if request.method == 'POST':
        refund_reason = request.form.get('refund_reason', '')
        
        try:
            # Update sale status
            sale.status = 'refunded'
            sale.notes = f"Refunded: {refund_reason}"
            
            # Restore product stock
            for item in sale.items:
                if item.product.track_inventory:
                    item.product.update_stock(item.quantity, 'return')
            
            db.session.commit()
            
            flash('تم استرجاع المبيعة بنجاح' if language == 'ar' 
                  else 'Sale refunded successfully', 'success')
            return redirect(url_for('sales.view', sale_id=sale.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء استرجاع المبيعة' if language == 'ar' 
                  else 'Error processing refund', 'error')
    
    return render_template('sales/refund.html', sale=sale, language=language)
