"""
Settings model for Qatar POS System
Stores system configuration and preferences
"""

from extensions import db
from datetime import datetime
from sqlalchemy import text

class Setting(db.Model):
    """System settings model"""
    __tablename__ = 'settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text)
    value_type = db.Column(db.String(20), default='string')  # string, integer, float, boolean, json
    category = db.Column(db.String(50), default='general')
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    is_public = db.Column(db.Bo<PERSON>an, default=False)  # Can be accessed without login
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Setting {self.key}: {self.value}>'
    
    def get_value(self):
        """Get typed value based on value_type"""
        if not self.value:
            return None
            
        if self.value_type == 'integer':
            try:
                return int(self.value)
            except (ValueError, TypeError):
                return 0
        elif self.value_type == 'float':
            try:
                return float(self.value)
            except (ValueError, TypeError):
                return 0.0
        elif self.value_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.value_type == 'json':
            try:
                import json
                return json.loads(self.value)
            except (ValueError, TypeError):
                return {}
        else:
            return self.value
    
    def set_value(self, value):
        """Set value with automatic type conversion"""
        if self.value_type == 'json':
            import json
            self.value = json.dumps(value)
        else:
            self.value = str(value)
    
    def get_description(self, language='ar'):
        """Get description in specified language"""
        if language == 'en':
            return self.description_en or self.description_ar
        return self.description_ar or self.description_en
    
    @staticmethod
    def get_setting(key, default=None):
        """Get setting value by key"""
        setting = Setting.query.filter_by(key=key).first()
        if setting:
            return setting.get_value()
        return default
    
    @staticmethod
    def set_setting(key, value, value_type='string', category='general', 
                   description_ar=None, description_en=None, is_public=False):
        """Set or update setting"""
        setting = Setting.query.filter_by(key=key).first()
        if not setting:
            setting = Setting(
                key=key,
                value_type=value_type,
                category=category,
                description_ar=description_ar,
                description_en=description_en,
                is_public=is_public
            )
            db.session.add(setting)
        
        setting.set_value(value)
        setting.updated_at = datetime.utcnow()
        db.session.commit()
        return setting
    
    @staticmethod
    def get_category_settings(category):
        """Get all settings in a category"""
        return Setting.query.filter_by(category=category).all()
    
    @staticmethod
    def get_public_settings():
        """Get all public settings (for frontend)"""
        settings = Setting.query.filter_by(is_public=True).all()
        return {setting.key: setting.get_value() for setting in settings}

class SystemInfo(db.Model):
    """System information and metadata"""
    __tablename__ = 'system_info'
    
    id = db.Column(db.Integer, primary_key=True)
    version = db.Column(db.String(20), default='1.0.0')
    installation_date = db.Column(db.DateTime, default=datetime.utcnow)
    last_backup = db.Column(db.DateTime)
    database_version = db.Column(db.String(20))
    total_sales = db.Column(db.Integer, default=0)
    total_products = db.Column(db.Integer, default=0)
    total_customers = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    @staticmethod
    def get_info():
        """Get system info, create if not exists"""
        info = SystemInfo.query.first()
        if not info:
            info = SystemInfo()
            db.session.add(info)
            db.session.commit()
        return info
    
    def update_stats(self):
        """Update system statistics"""
        from models.sale import Sale
        from models.product import Product
        from models.customer import Customer
        
        self.total_sales = Sale.query.count()
        self.total_products = Product.query.filter_by(is_active=True).count()
        self.total_customers = Customer.query.filter_by(is_active=True).count()
        self.updated_at = datetime.utcnow()
        db.session.commit()

# Default settings that should be created on system initialization
DEFAULT_SETTINGS = [
    # Company Information
    {
        'key': 'company_name_ar',
        'value': 'شركة نقاط البيع القطرية',
        'category': 'company',
        'description_ar': 'اسم الشركة بالعربية',
        'description_en': 'Company name in Arabic',
        'is_public': True
    },
    {
        'key': 'company_name_en',
        'value': 'Qatar POS Company',
        'category': 'company',
        'description_ar': 'اسم الشركة بالإنجليزية',
        'description_en': 'Company name in English',
        'is_public': True
    },
    {
        'key': 'company_address_ar',
        'value': 'الدوحة، قطر',
        'category': 'company',
        'description_ar': 'عنوان الشركة بالعربية',
        'description_en': 'Company address in Arabic'
    },
    {
        'key': 'company_address_en',
        'value': 'Doha, Qatar',
        'category': 'company',
        'description_ar': 'عنوان الشركة بالإنجليزية',
        'description_en': 'Company address in English'
    },
    {
        'key': 'company_phone',
        'value': '+974 4444 4444',
        'category': 'company',
        'description_ar': 'رقم هاتف الشركة',
        'description_en': 'Company phone number'
    },
    {
        'key': 'company_email',
        'value': '<EMAIL>',
        'category': 'company',
        'description_ar': 'البريد الإلكتروني للشركة',
        'description_en': 'Company email address'
    },
    {
        'key': 'company_tax_number',
        'value': '*********',
        'category': 'company',
        'description_ar': 'الرقم الضريبي للشركة',
        'description_en': 'Company tax number'
    },
    {
        'key': 'company_cr_number',
        'value': 'CR-123456',
        'category': 'company',
        'description_ar': 'رقم السجل التجاري',
        'description_en': 'Commercial registration number'
    },
    {
        'key': 'company_logo',
        'value': '',
        'category': 'company',
        'description_ar': 'شعار الشركة',
        'description_en': 'Company Logo'
    },
    {
        'key': 'logo_width',
        'value': '150',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'عرض الشعار (بكسل)',
        'description_en': 'Logo Width (pixels)'
    },
    {
        'key': 'logo_height',
        'value': '80',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'ارتفاع الشعار (بكسل)',
        'description_en': 'Logo Height (pixels)'
    },
    {
        'key': 'company_stamp',
        'value': '',
        'category': 'company',
        'description_ar': 'ختم الشركة',
        'description_en': 'Company Stamp'
    },
    {
        'key': 'stamp_width',
        'value': '120',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'عرض الختم (بكسل)',
        'description_en': 'Stamp Width (pixels)'
    },
    {
        'key': 'stamp_height',
        'value': '120',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'ارتفاع الختم (بكسل)',
        'description_en': 'Stamp Height (pixels)'
    },
    {
        'key': 'manager_signature',
        'value': '',
        'category': 'company',
        'description_ar': 'توقيع المسؤول',
        'description_en': 'Manager Signature'
    },
    {
        'key': 'signature_width',
        'value': '200',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'عرض التوقيع (بكسل)',
        'description_en': 'Signature Width (pixels)'
    },
    {
        'key': 'signature_height',
        'value': '80',
        'value_type': 'integer',
        'category': 'company',
        'description_ar': 'ارتفاع التوقيع (بكسل)',
        'description_en': 'Signature Height (pixels)'
    },
    {
        'key': 'manager_name',
        'value': '',
        'category': 'company',
        'description_ar': 'اسم المسؤول',
        'description_en': 'Manager Name'
    },
    {
        'key': 'manager_title',
        'value': 'المدير العام',
        'category': 'company',
        'description_ar': 'منصب المسؤول',
        'description_en': 'Manager Title'
    },
    
    # System Settings
    {
        'key': 'default_language',
        'value': 'ar',
        'category': 'system',
        'description_ar': 'اللغة الافتراضية للنظام',
        'description_en': 'Default system language',
        'is_public': True
    },
    {
        'key': 'currency_code',
        'value': 'QAR',
        'category': 'system',
        'description_ar': 'رمز العملة',
        'description_en': 'Currency code',
        'is_public': True
    },
    {
        'key': 'currency_symbol',
        'value': 'ر.ق',
        'category': 'system',
        'description_ar': 'رمز العملة',
        'description_en': 'Currency symbol',
        'is_public': True
    },
    {
        'key': 'timezone',
        'value': 'Asia/Qatar',
        'category': 'system',
        'description_ar': 'المنطقة الزمنية',
        'description_en': 'System timezone'
    },
    {
        'key': 'date_format',
        'value': 'DD/MM/YYYY',
        'category': 'system',
        'description_ar': 'تنسيق التاريخ',
        'description_en': 'Date format'
    },
    {
        'key': 'working_days',
        'value': '6',
        'value_type': 'integer',
        'category': 'system',
        'description_ar': 'عدد أيام العمل في الأسبوع',
        'description_en': 'Working days per week'
    },
    {
        'key': 'weekend_day',
        'value': 'friday',
        'category': 'system',
        'description_ar': 'يوم العطلة الأسبوعية',
        'description_en': 'Weekend day'
    },
    
    # POS Settings
    {
        'key': 'auto_print_invoice',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'pos',
        'description_ar': 'طباعة الفاتورة تلقائياً',
        'description_en': 'Auto print invoice'
    },
    {
        'key': 'receipt_footer_ar',
        'value': 'شكراً لزيارتكم',
        'category': 'pos',
        'description_ar': 'تذييل الفاتورة بالعربية',
        'description_en': 'Receipt footer in Arabic'
    },
    {
        'key': 'receipt_footer_en',
        'value': 'Thank you for your visit',
        'category': 'pos',
        'description_ar': 'تذييل الفاتورة بالإنجليزية',
        'description_en': 'Receipt footer in English'
    },
    {
        'key': 'low_stock_threshold',
        'value': '10',
        'value_type': 'integer',
        'category': 'inventory',
        'description_ar': 'حد المخزون المنخفض',
        'description_en': 'Low stock threshold'
    },
    
    # Tax Settings
    {
        'key': 'vat_rate',
        'value': '0',
        'value_type': 'float',
        'category': 'tax',
        'description_ar': 'معدل ضريبة القيمة المضافة',
        'description_en': 'VAT rate percentage'
    },
    {
        'key': 'tax_enabled',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'tax',
        'description_ar': 'تفعيل الضرائب',
        'description_en': 'Enable taxes'
    },

    # Sound Settings
    {
        'key': 'sounds_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'تفعيل الأصوات',
        'description_en': 'Enable sounds'
    },
    {
        'key': 'sound_volume',
        'value': '0.5',
        'value_type': 'float',
        'category': 'sounds',
        'description_ar': 'مستوى الصوت',
        'description_en': 'Sound volume'
    },
    {
        'key': 'sale_sound_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت إتمام البيع',
        'description_en': 'Sale completion sound'
    },
    {
        'key': 'error_sound_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت الأخطاء',
        'description_en': 'Error sound'
    },
    {
        'key': 'notification_sound_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت الإشعارات',
        'description_en': 'Notification sound'
    },
    {
        'key': 'button_click_sound_enabled',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت النقر على الأزرار',
        'description_en': 'Button click sound'
    },
    {
        'key': 'payment_sound_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت استلام الدفع',
        'description_en': 'Payment received sound'
    },

    # Notification Settings
    {
        'key': 'notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'تفعيل الإشعارات',
        'description_en': 'Enable notifications'
    },
    {
        'key': 'notification_duration',
        'value': '5',
        'value_type': 'integer',
        'category': 'notifications',
        'description_ar': 'مدة عرض الإشعار بالثواني',
        'description_en': 'Notification display duration in seconds'
    },
    {
        'key': 'notification_position',
        'value': 'top-right',
        'value_type': 'string',
        'category': 'notifications',
        'description_ar': 'موضع الإشعارات',
        'description_en': 'Notification position'
    },
    {
        'key': 'sale_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات البيع',
        'description_en': 'Sale notifications'
    },
    {
        'key': 'payment_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات الدفع',
        'description_en': 'Payment notifications'
    },
    {
        'key': 'low_stock_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات نفاد المخزون',
        'description_en': 'Low stock notifications'
    },
    {
        'key': 'error_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات الأخطاء',
        'description_en': 'Error notifications'
    },
    {
        'key': 'system_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات النظام',
        'description_en': 'System notifications'
    },
    {
        'key': 'backup_notifications_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات النسخ الاحتياطي',
        'description_en': 'Backup notifications'
    },
    {
        'key': 'low_stock_threshold',
        'value': '10',
        'value_type': 'integer',
        'category': 'notifications',
        'description_ar': 'حد المخزون المنخفض',
        'description_en': 'Low stock threshold'
    },
    {
        'key': 'desktop_notifications_enabled',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات سطح المكتب',
        'description_en': 'Desktop notifications'
    },
    {
        'key': 'email_notifications_enabled',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'notifications',
        'description_ar': 'إشعارات البريد الإلكتروني',
        'description_en': 'Email notifications'
    },

    # Security Settings
    {
        'key': 'force_password_change',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'إجبار تغيير كلمة المرور',
        'description_en': 'Force password change'
    },
    {
        'key': 'session_timeout',
        'value': '480',
        'value_type': 'integer',
        'category': 'security',
        'description_ar': 'مدة انتهاء الجلسة بالدقائق',
        'description_en': 'Session timeout in minutes'
    },
    {
        'key': 'two_factor_enabled',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'المصادقة الثنائية',
        'description_en': 'Two-factor authentication'
    },
    {
        'key': 'max_login_attempts',
        'value': '5',
        'value_type': 'integer',
        'category': 'security',
        'description_ar': 'عدد محاولات تسجيل الدخول',
        'description_en': 'Maximum login attempts'
    },
    {
        'key': 'min_password_length',
        'value': '8',
        'value_type': 'integer',
        'category': 'security',
        'description_ar': 'الحد الأدنى لطول كلمة المرور',
        'description_en': 'Minimum password length'
    },
    {
        'key': 'require_uppercase',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'يتطلب أحرف كبيرة',
        'description_en': 'Require uppercase letters'
    },
    {
        'key': 'require_numbers',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'يتطلب أرقام',
        'description_en': 'Require numbers'
    },
    {
        'key': 'require_special_chars',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'يتطلب رموز خاصة',
        'description_en': 'Require special characters'
    },
    {
        'key': 'password_expiry_days',
        'value': '0',
        'value_type': 'integer',
        'category': 'security',
        'description_ar': 'انتهاء صلاحية كلمة المرور بالأيام',
        'description_en': 'Password expiry in days'
    },
    {
        'key': 'enable_audit_log',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'تفعيل سجل المراجعة',
        'description_en': 'Enable audit log'
    },
    {
        'key': 'enable_ip_whitelist',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'تفعيل القائمة البيضاء للـ IP',
        'description_en': 'Enable IP whitelist'
    },
    {
        'key': 'enable_ssl_only',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'HTTPS فقط',
        'description_en': 'HTTPS only'
    },
    {
        'key': 'enable_rate_limiting',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'تحديد معدل الطلبات',
        'description_en': 'Enable rate limiting'
    },
    {
        'key': 'enable_data_encryption',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'تشفير البيانات الحساسة',
        'description_en': 'Encrypt sensitive data'
    },
    {
        'key': 'enable_backup_encryption',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'تشفير النسخ الاحتياطية',
        'description_en': 'Encrypt backups'
    },
    {
        'key': 'log_retention_months',
        'value': '6',
        'value_type': 'integer',
        'category': 'security',
        'description_ar': 'مدة الاحتفاظ بالسجلات بالأشهر',
        'description_en': 'Log retention period in months'
    },
    {
        'key': 'enable_gdpr_compliance',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'security',
        'description_ar': 'الامتثال لحماية البيانات',
        'description_en': 'GDPR compliance'
    },

    # Backup Settings
    {
        'key': 'auto_backup_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'backup',
        'description_ar': 'تفعيل النسخ الاحتياطي التلقائي',
        'description_en': 'Enable automated backup'
    },
    {
        'key': 'backup_frequency',
        'value': 'daily',
        'value_type': 'string',
        'category': 'backup',
        'description_ar': 'تكرار النسخ الاحتياطي',
        'description_en': 'Backup frequency'
    },
    {
        'key': 'backup_time',
        'value': '02:00',
        'value_type': 'string',
        'category': 'backup',
        'description_ar': 'وقت النسخ الاحتياطي',
        'description_en': 'Backup time'
    },
    {
        'key': 'backup_retention_days',
        'value': '30',
        'value_type': 'integer',
        'category': 'backup',
        'description_ar': 'مدة الاحتفاظ بالنسخ الاحتياطية بالأيام',
        'description_en': 'Backup retention period in days'
    },
    {
        'key': 'backup_compression',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'backup',
        'description_ar': 'ضغط النسخ الاحتياطية',
        'description_en': 'Compress backups'
    },
    {
        'key': 'backup_cloud_storage',
        'value': 'false',
        'value_type': 'boolean',
        'category': 'backup',
        'description_ar': 'رفع للتخزين السحابي',
        'description_en': 'Upload to cloud storage'
    },
    {
        'key': 'low_stock_sound_enabled',
        'value': 'true',
        'value_type': 'boolean',
        'category': 'sounds',
        'description_ar': 'صوت تنبيه نفاد المخزون',
        'description_en': 'Low stock alert sound'
    }
]
