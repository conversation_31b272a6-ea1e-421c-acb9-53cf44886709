{% extends "base.html" %}

{% block title %}مولد التقارير السريع - نظام نقاط البيع القطري{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .report-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .report-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        color: #007bff;
    }
    
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
    }
    
    .report-results {
        display: none;
        margin-top: 30px;
    }
    
    .chart-container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn-generate {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line text-primary"></i>
                        مولد التقارير السريع
                    </h1>
                    <p class="text-muted mb-0">احصل على رؤى فورية لأعمالك</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats" id="quickStats">
        <div class="stat-card">
            <div class="stat-value" id="todaySales">--</div>
            <div class="stat-label">مبيعات اليوم</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="todayTransactions">--</div>
            <div class="stat-label">معاملات اليوم</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="avgTransaction">--</div>
            <div class="stat-label">متوسط المعاملة</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="activeCustomers">--</div>
            <div class="stat-label">عملاء نشطون</div>
        </div>
    </div>

    <!-- Report Generator -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog"></i>
                        إعدادات التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form id="reportForm">
                        <!-- نوع التقرير -->
                        <div class="form-group mb-3">
                            <label for="reportType" class="form-label">نوع التقرير</label>
                            <select class="form-control" id="reportType" required>
                                <option value="">اختر نوع التقرير</option>
                                <option value="sales_summary">ملخص المبيعات</option>
                                <option value="top_products">أفضل المنتجات</option>
                                <option value="customer_activity">نشاط العملاء</option>
                                <option value="inventory_status">حالة المخزون</option>
                                <option value="payment_methods">طرق الدفع</option>
                                <option value="hourly_sales">المبيعات بالساعة</option>
                                <option value="category_performance">أداء الفئات</option>
                            </select>
                        </div>

                        <!-- نطاق التاريخ -->
                        <div class="form-group mb-3">
                            <label for="dateRange" class="form-label">نطاق التاريخ</label>
                            <select class="form-control" id="dateRange">
                                <option value="today">اليوم</option>
                                <option value="yesterday">أمس</option>
                                <option value="this_week">هذا الأسبوع</option>
                                <option value="this_month">هذا الشهر</option>
                                <option value="last_7_days">آخر 7 أيام</option>
                                <option value="last_30_days">آخر 30 يوم</option>
                            </select>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="form-group mb-3" id="additionalOptions">
                            <label for="recordLimit" class="form-label">عدد السجلات</label>
                            <select class="form-control" id="recordLimit">
                                <option value="10">10 سجلات</option>
                                <option value="20">20 سجل</option>
                                <option value="50">50 سجل</option>
                                <option value="100">100 سجل</option>
                            </select>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-generate">
                                <i class="fas fa-chart-bar"></i>
                                توليد التقرير
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearResults()">
                                <i class="fas fa-eraser"></i>
                                مسح النتائج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري توليد التقرير...</p>
            </div>

            <!-- Report Results -->
            <div class="report-results" id="reportResults">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="reportTitle">نتائج التقرير</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="exportReport()">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="reportContent">
                            <!-- سيتم ملء المحتوى هنا بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// متغيرات عامة
let currentReportData = null;
let currentChart = null;

// تحميل الإحصائيات السريعة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadQuickStats();
});

// تحميل الإحصائيات السريعة
function loadQuickStats() {
    fetch('/quick_reports/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            report_type: 'sales_summary',
            date_range: 'today'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.data;
            document.getElementById('todaySales').textContent = formatCurrency(stats.total_sales);
            document.getElementById('todayTransactions').textContent = stats.transaction_count;
            document.getElementById('avgTransaction').textContent = formatCurrency(stats.avg_transaction);
            document.getElementById('activeCustomers').textContent = stats.unique_customers;
        }
    })
    .catch(error => {
        console.error('خطأ في تحميل الإحصائيات:', error);
    });
}

// معالج إرسال النموذج
document.getElementById('reportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    generateReport();
});

// توليد التقرير
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const dateRange = document.getElementById('dateRange').value;
    const recordLimit = document.getElementById('recordLimit').value;

    if (!reportType) {
        alert('يرجى اختيار نوع التقرير');
        return;
    }

    // إظهار مؤشر التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('reportResults').style.display = 'none';

    // إرسال طلب التقرير
    fetch('/quick_reports/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            report_type: reportType,
            date_range: dateRange,
            filters: {
                limit: parseInt(recordLimit)
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingSpinner').style.display = 'none';
        
        if (data.success) {
            currentReportData = data;
            displayReport(data);
        } else {
            alert('خطأ في توليد التقرير: ' + data.error);
        }
    })
    .catch(error => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.error('خطأ:', error);
        alert('حدث خطأ في توليد التقرير');
    });
}

// عرض التقرير
function displayReport(data) {
    const reportType = document.getElementById('reportType').value;
    const reportTitle = document.getElementById('reportType').selectedOptions[0].text;
    
    document.getElementById('reportTitle').textContent = reportTitle;
    document.getElementById('reportResults').style.display = 'block';
    
    const contentDiv = document.getElementById('reportContent');
    
    // تنظيف المحتوى السابق
    contentDiv.innerHTML = '';
    if (currentChart) {
        currentChart.destroy();
        currentChart = null;
    }
    
    // عرض التقرير حسب النوع
    switch(reportType) {
        case 'sales_summary':
            displaySalesSummary(data.data, contentDiv);
            break;
        case 'top_products':
            displayTopProducts(data.data, contentDiv);
            break;
        case 'customer_activity':
            displayCustomerActivity(data.data, contentDiv);
            break;
        case 'inventory_status':
            displayInventoryStatus(data.data, contentDiv);
            break;
        case 'payment_methods':
            displayPaymentMethods(data.data, contentDiv);
            break;
        case 'hourly_sales':
            displayHourlySales(data.data, contentDiv);
            break;
        case 'category_performance':
            displayCategoryPerformance(data.data, contentDiv);
            break;
    }
}

// عرض ملخص المبيعات
function displaySalesSummary(data, container) {
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-value">${formatCurrency(data.total_sales)}</div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-value">${data.transaction_count}</div>
                    <div class="stat-label">عدد المعاملات</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-value">${formatCurrency(data.avg_transaction)}</div>
                    <div class="stat-label">متوسط المعاملة</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-value">${data.unique_customers}</div>
                    <div class="stat-label">عملاء فريدون</div>
                </div>
            </div>
        </div>
    `;
}

// عرض أفضل المنتجات
function displayTopProducts(data, container) {
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>المنتج</th><th>الكمية المباعة</th><th>الإيرادات</th></tr></thead><tbody>';

    data.forEach(product => {
        html += `<tr>
            <td>${product.name_ar || product.name_en}</td>
            <td>${product.quantity_sold}</td>
            <td>${formatCurrency(product.revenue)}</td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// عرض نشاط العملاء
function displayCustomerActivity(data, container) {
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>العميل</th><th>عدد المشتريات</th><th>إجمالي الإنفاق</th><th>آخر شراء</th></tr></thead><tbody>';

    data.forEach(customer => {
        const lastPurchase = customer.last_purchase ? new Date(customer.last_purchase).toLocaleDateString('ar-QA') : 'غير محدد';
        html += `<tr>
            <td>${customer.name || customer.customer_code}</td>
            <td>${customer.purchase_count}</td>
            <td>${formatCurrency(customer.total_spent)}</td>
            <td>${lastPurchase}</td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// عرض حالة المخزون
function displayInventoryStatus(data, container) {
    let html = '<div class="row">';

    // المنتجات منخفضة المخزون
    html += '<div class="col-md-6"><h6>منتجات منخفضة المخزون</h6>';
    html += '<div class="table-responsive"><table class="table table-sm table-warning">';
    html += '<thead><tr><th>المنتج</th><th>المخزون الحالي</th><th>الحد الأدنى</th></tr></thead><tbody>';

    data.low_stock_products.forEach(product => {
        html += `<tr>
            <td>${product.name_ar || product.name_en}</td>
            <td>${product.current_stock}</td>
            <td>${product.minimum_stock}</td>
        </tr>`;
    });

    html += '</tbody></table></div></div>';

    // المنتجات نفدت من المخزون
    html += '<div class="col-md-6"><h6>منتجات نفدت من المخزون</h6>';
    html += '<div class="table-responsive"><table class="table table-sm table-danger">';
    html += '<thead><tr><th>المنتج</th><th>المخزون الحالي</th></tr></thead><tbody>';

    data.out_of_stock_products.forEach(product => {
        html += `<tr>
            <td>${product.name_ar || product.name_en}</td>
            <td>${product.current_stock}</td>
        </tr>`;
    });

    html += '</tbody></table></div></div>';
    html += '</div>';

    // إجمالي قيمة المخزون
    html += `<div class="alert alert-info mt-3">
        <strong>إجمالي قيمة المخزون: ${formatCurrency(data.total_inventory_value)}</strong>
    </div>`;

    container.innerHTML = html;
}

// عرض طرق الدفع
function displayPaymentMethods(data, container) {
    // جدول البيانات
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>طريقة الدفع</th><th>عدد المعاملات</th><th>المبلغ الإجمالي</th><th>النسبة</th></tr></thead><tbody>';

    data.forEach(method => {
        html += `<tr>
            <td>${method.payment_method}</td>
            <td>${method.transaction_count}</td>
            <td>${formatCurrency(method.total_amount)}</td>
            <td>${method.percentage.toFixed(1)}%</td>
        </tr>`;
    });

    html += '</tbody></table></div>';

    // رسم بياني دائري
    html += '<div class="chart-container"><canvas id="paymentChart" width="400" height="200"></canvas></div>';

    container.innerHTML = html;

    // إنشاء الرسم البياني
    const ctx = document.getElementById('paymentChart').getContext('2d');
    currentChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: data.map(method => method.payment_method),
            datasets: [{
                data: data.map(method => method.total_amount),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// عرض المبيعات بالساعة
function displayHourlySales(data, container) {
    let html = '<div class="chart-container"><canvas id="hourlyChart" width="400" height="200"></canvas></div>';
    container.innerHTML = html;

    const ctx = document.getElementById('hourlyChart').getContext('2d');
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(hour => `${hour.hour}:00`),
            datasets: [{
                label: 'المبيعات',
                data: data.map(hour => hour.total_amount),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// عرض أداء الفئات
function displayCategoryPerformance(data, container) {
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>الفئة</th><th>الكمية المباعة</th><th>الإيرادات</th></tr></thead><tbody>';

    data.forEach(category => {
        html += `<tr>
            <td>${category.name_ar || category.name_en}</td>
            <td>${category.total_quantity}</td>
            <td>${formatCurrency(category.total_revenue)}</td>
        </tr>`;
    });

    html += '</tbody></table></div>';

    // رسم بياني عمودي
    html += '<div class="chart-container"><canvas id="categoryChart" width="400" height="200"></canvas></div>';

    container.innerHTML = html;

    const ctx = document.getElementById('categoryChart').getContext('2d');
    currentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.map(category => category.name_ar || category.name_en),
            datasets: [{
                label: 'الإيرادات',
                data: data.map(category => category.total_revenue),
                backgroundColor: '#36A2EB'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// وظائف مساعدة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-QA', {
        style: 'currency',
        currency: 'QAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function clearResults() {
    document.getElementById('reportResults').style.display = 'none';
    currentReportData = null;
    if (currentChart) {
        currentChart.destroy();
        currentChart = null;
    }
}

function refreshDashboard() {
    loadQuickStats();
    if (currentReportData) {
        generateReport();
    }
}

function exportReport() {
    if (!currentReportData) {
        alert('لا يوجد تقرير لتصديره');
        return;
    }

    const reportType = document.getElementById('reportType').value;
    const dateRange = document.getElementById('dateRange').value;

    // إنشاء رابط التصدير
    const exportUrl = `/quick_reports/export/${reportType}?date_range=${dateRange}&format=excel`;

    // فتح رابط التحميل
    window.open(exportUrl, '_blank');
}

function printReport() {
    if (!currentReportData) {
        alert('لا يوجد تقرير للطباعة');
        return;
    }
    
    window.print();
}
</script>
{% endblock %}
