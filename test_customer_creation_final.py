#!/usr/bin/env python3
"""
اختبار نهائي لإنشاء العميل
Final test for customer creation
"""

from app import create_app
from models.user import User
from models.customer import Customer
from extensions import db

def test_customer_creation_comprehensive():
    """اختبار شامل لإنشاء العميل"""
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("🧪 اختبار شامل لإنشاء العميل")
            print("=" * 50)
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = client.post('/auth/login', data=login_data, follow_redirects=True)
            
            if response.status_code != 200:
                print(f"  ❌ فشل تسجيل الدخول")
                return
            
            print("  ✅ تم تسجيل الدخول بنجاح")
            
            # اختبار 1: عميل فرد بسيط
            print("\n📝 اختبار 1: عميل فرد بسيط")
            individual_data = {
                'customer_type': 'individual',
                'first_name_ar': 'سارة',
                'first_name_en': 'Sarah',
                'last_name_ar': 'أحمد',
                'last_name_en': 'Ahmed',
                'phone': '+974-5555-1111',
                'email': '<EMAIL>'
            }
            
            response = client.post('/customers/create', data=individual_data, follow_redirects=True)
            
            if response.status_code == 200 and 'تم إنشاء العميل بنجاح' in response.get_data(as_text=True):
                print("    ✅ تم إنشاء عميل فرد بسيط بنجاح")
            else:
                print("    ❌ فشل في إنشاء عميل فرد بسيط")
            
            # اختبار 2: عميل فرد مع جميع البيانات
            print("\n📝 اختبار 2: عميل فرد مع جميع البيانات")
            individual_full_data = {
                'customer_type': 'individual',
                'first_name_ar': 'محمد',
                'first_name_en': 'Mohammed',
                'last_name_ar': 'علي',
                'last_name_en': 'Ali',
                'phone': '+974-5555-2222',
                'email': '<EMAIL>',
                'address_ar': 'شارع الكورنيش، الدوحة',
                'address_en': 'Corniche Street, Doha',
                'city_ar': 'الدوحة',
                'city_en': 'Doha',
                'postal_code': '12345',
                'qatar_id': '12345678901'
            }
            
            response = client.post('/customers/create', data=individual_full_data, follow_redirects=True)
            
            if response.status_code == 200 and 'تم إنشاء العميل بنجاح' in response.get_data(as_text=True):
                print("    ✅ تم إنشاء عميل فرد مع جميع البيانات بنجاح")
            else:
                print("    ❌ فشل في إنشاء عميل فرد مع جميع البيانات")
            
            # اختبار 3: عميل شركة
            print("\n📝 اختبار 3: عميل شركة")
            company_data = {
                'customer_type': 'company',
                'company_name_ar': 'شركة التجارة المتقدمة',
                'company_name_en': 'Advanced Trading Company',
                'phone': '+974-4444-3333',
                'email': '<EMAIL>',
                'address_ar': 'المنطقة الصناعية، الدوحة',
                'address_en': 'Industrial Area, Doha',
                'city_ar': 'الدوحة',
                'city_en': 'Doha',
                'commercial_registration': 'CR123456',
                'tax_number': 'TAX789012'
            }
            
            response = client.post('/customers/create', data=company_data, follow_redirects=True)
            
            if response.status_code == 200 and 'تم إنشاء العميل بنجاح' in response.get_data(as_text=True):
                print("    ✅ تم إنشاء عميل شركة بنجاح")
            else:
                print("    ❌ فشل في إنشاء عميل شركة")
            
            # اختبار 4: بيانات ناقصة (يجب أن يفشل)
            print("\n📝 اختبار 4: بيانات ناقصة (يجب أن يفشل)")
            incomplete_data = {
                'customer_type': 'individual',
                'first_name_ar': 'أحمد',
                # missing first_name_en
                'last_name_ar': 'محمد',
                'last_name_en': 'Mohammed',
                'phone': '+974-5555-4444'
            }
            
            response = client.post('/customers/create', data=incomplete_data, follow_redirects=False)
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                if 'الاسم الأول مطلوب' in content or 'First name is required' in content:
                    print("    ✅ التحقق من البيانات الناقصة يعمل بشكل صحيح")
                else:
                    print("    ⚠️ التحقق من البيانات الناقصة قد لا يعمل")
            else:
                print("    ❌ خطأ في اختبار البيانات الناقصة")
            
            # اختبار 5: رقم هوية مكرر (يجب أن يفشل)
            print("\n📝 اختبار 5: رقم هوية مكرر (يجب أن يفشل)")
            duplicate_qatar_id_data = {
                'customer_type': 'individual',
                'first_name_ar': 'فاطمة',
                'first_name_en': 'Fatima',
                'last_name_ar': 'سالم',
                'last_name_en': 'Salem',
                'phone': '+974-5555-5555',
                'email': '<EMAIL>',
                'qatar_id': '12345678901'  # نفس الرقم المستخدم في الاختبار 2
            }
            
            response = client.post('/customers/create', data=duplicate_qatar_id_data, follow_redirects=False)
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                if 'رقم الهوية القطرية موجود بالفعل' in content or 'Qatar ID already exists' in content:
                    print("    ✅ التحقق من رقم الهوية المكرر يعمل بشكل صحيح")
                else:
                    print("    ⚠️ التحقق من رقم الهوية المكرر قد لا يعمل")
            else:
                print("    ❌ خطأ في اختبار رقم الهوية المكرر")

def test_customer_database():
    """اختبار قاعدة البيانات"""
    app = create_app()
    
    with app.app_context():
        print("\n💾 اختبار قاعدة البيانات")
        print("=" * 50)
        
        # عد العملاء الحاليين
        customer_count = Customer.query.count()
        print(f"    📊 عدد العملاء الحاليين: {customer_count}")
        
        # عرض آخر 3 عملاء
        recent_customers = Customer.query.order_by(Customer.created_at.desc()).limit(3).all()
        
        print(f"    📋 آخر 3 عملاء:")
        for i, customer in enumerate(recent_customers, 1):
            print(f"      {i}. {customer.customer_code}: {customer.get_display_name('ar')}")
            print(f"         الهاتف: {customer.phone}")
            print(f"         البريد: {customer.email or 'غير محدد'}")
            print(f"         النوع: {'فرد' if customer.customer_type == 'individual' else 'شركة'}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار نهائي لإنشاء العميل")
    print("=" * 70)
    
    test_customer_creation_comprehensive()
    test_customer_database()
    
    print("\n" + "=" * 70)
    print("✅ انتهى الاختبار النهائي!")
    print("🎯 إذا نجحت جميع الاختبارات، فقد تم إصلاح مشكلة إنشاء العميل بالكامل!")
