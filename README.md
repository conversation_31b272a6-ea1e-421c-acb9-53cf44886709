# Qatar POS System | نظام نقاط البيع القطري

A comprehensive Point of Sale (POS) system designed specifically for the Qatar market with full Arabic and English language support, Qatar Tax Authority compliance, and local business requirements.

## Features | المميزات

### Core Features | المميزات الأساسية
- ✅ Bilingual support (Arabic/English) | دعم ثنائي اللغة
- ✅ Qatar Riyal (QAR) currency | عملة الريال القطري
- ✅ Qatar Tax Authority compliance | التوافق مع الهيئة العامة للضرائب
- ✅ Electronic invoice generation with QR codes | إنتاج الفواتير الإلكترونية مع رموز QR
- ✅ Multi-user role management | إدارة أدوار المستخدمين المتعددة
- ✅ Comprehensive reporting | تقارير شاملة

### System Modules | وحدات النظام
1. **Authentication & User Management** | المصادقة وإدارة المستخدمين
2. **Dashboard** | لوحة التحكم
3. **Sales & POS Interface** | واجهة المبيعات ونقاط البيع
4. **Product Management** | إدارة المنتجات
5. **Inventory Management** | إدارة المخزون
6. **Customer Management** | إدارة العملاء
7. **Supplier Management** | إدارة الموردين
8. **Reports & Analytics** | التقارير والتحليلات
9. **Invoice Generation** | إنتاج الفواتير

## Installation | التثبيت

### Prerequisites | المتطلبات المسبقة
- Python 3.8 or higher
- pip (Python package manager)

### Setup Steps | خطوات الإعداد

1. **Clone the repository | استنساخ المستودع**
```bash
git clone <repository-url>
cd qatar-pos-system
```

2. **Create virtual environment | إنشاء بيئة افتراضية**
```bash
python -m venv venv
# On Windows
venv\Scripts\activate
# On Linux/Mac
source venv/bin/activate
```

3. **Install dependencies | تثبيت التبعيات**
```bash
pip install -r requirements.txt
```

4. **Environment setup | إعداد البيئة**
```bash
cp .env.example .env
# Edit .env file with your configuration
```

5. **Initialize database | تهيئة قاعدة البيانات**
```bash
python app.py
```

6. **Run the application | تشغيل التطبيق**
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## User Roles | أدوار المستخدمين

- **Manager | المدير**: Full system access
- **Seller | البائع**: Sales operations and basic reporting
- **Accountant | المحاسب**: Financial reports and inventory management

## Qatar Compliance | التوافق القطري

- Qatar Tax Authority electronic invoice requirements
- Arabic language support with RTL layout
- Qatar Riyal currency formatting
- Local business hours (Sunday-Thursday)
- Commercial registration integration

## Technology Stack | المكدس التقني

- **Backend**: Flask (Python)
- **Database**: SQLite/PostgreSQL
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **PDF Generation**: ReportLab, WeasyPrint
- **Internationalization**: Flask-Babel
- **Authentication**: Flask-Login

## License | الترخيص

This project is licensed under the MIT License.

## Support | الدعم

For support and questions, please contact the development team.
