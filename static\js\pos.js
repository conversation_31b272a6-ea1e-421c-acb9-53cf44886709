// Qatar POS System - Point of Sale JavaScript

class POSSystem {
    constructor() {
        this.cart = [];
        this.products = [];
        this.categories = [];
        this.currentSale = null;
        this.language = document.documentElement.lang || 'ar';
        
        this.init();
    }
    
    init() {
        this.loadCategories();
        this.loadProducts();
        this.setupEventListeners();
        this.updateCartDisplay();
    }
    
    setupEventListeners() {
        // Search functionality
        document.getElementById('product_search').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });
        
        // Category filter
        document.getElementById('category_filter').addEventListener('change', (e) => {
            this.filterByCategory(e.target.value);
        });
        
        // Discount calculation
        document.getElementById('discount_amount').addEventListener('input', () => {
            this.calculateTotals();
        });
        
        // Cart actions
        document.getElementById('complete_sale').addEventListener('click', () => {
            this.completeSale();
        });
        
        document.getElementById('clear_cart').addEventListener('click', () => {
            this.clearCart();
        });
        
        document.getElementById('hold_sale').addEventListener('click', () => {
            this.holdSale();
        });
        
        // Barcode scanner
        document.getElementById('barcode_input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchByBarcode();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    async loadCategories() {
        try {
            const response = await fetch('/api/categories');
            const data = await response.json();
            this.categories = data.categories || [];
            console.log('Categories loaded:', this.categories);
            this.renderCategoryFilter();
        } catch (error) {
            console.error('Error loading categories:', error);
            this.categories = [];
        }
    }
    
    async loadProducts(search = '', category = '') {
        try {
            document.getElementById('products_loading').style.display = 'block';

            let url = '/products/api/search?limit=50';

            // Always add a query parameter to ensure we get results
            // If no search term, use empty string which will return all products
            url += `&q=${encodeURIComponent(search)}`;

            if (category) {
                url += `&category=${category}`;
            }

            console.log('Loading products with URL:', url);

            const response = await fetch(url);
            this.products = await response.json();
            console.log('Loaded products:', this.products.length);
            this.renderProducts();
        } catch (error) {
            console.error('Error loading products:', error);
            this.showAlert('خطأ في تحميل المنتجات', 'error');
        } finally {
            document.getElementById('products_loading').style.display = 'none';
        }
    }
    
    renderCategoryFilter() {
        const select = document.getElementById('category_filter');
        if (!select) {
            console.error('Category filter element not found');
            return;
        }

        const currentValue = select.value;

        // Clear existing options except first
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        console.log('Rendering categories:', this.categories);

        if (this.categories && this.categories.length > 0) {
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
                console.log('Added category:', category.name);
            });
        }

        select.value = currentValue;
    }
    
    renderProducts() {
        const grid = document.getElementById('products_grid');
        grid.innerHTML = '';
        
        if (this.products.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-search display-4 text-muted"></i>
                    <p class="mt-2 text-muted">${this.language === 'ar' ? 'لا توجد منتجات' : 'No products found'}</p>
                </div>
            `;
            return;
        }
        
        this.products.forEach(product => {
            const productCard = this.createProductCard(product);
            grid.appendChild(productCard);
        });
    }
    
    createProductCard(product) {
        const col = document.createElement('div');
        col.className = 'col-lg-4 col-md-6 mb-3';
        
        const stockStatus = product.is_out_of_stock ? 'danger' : 
                           product.is_low_stock ? 'warning' : 'success';
        const stockText = product.is_out_of_stock ? 
                         (this.language === 'ar' ? 'نفد المخزون' : 'Out of Stock') :
                         product.current_stock;
        
        col.innerHTML = `
            <div class="card product-card h-100" onclick="pos.addToCart(${product.id})">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0 small">${product.name}</h6>
                        <span class="badge bg-${stockStatus}">${stockText}</span>
                    </div>
                    <p class="card-text text-muted small mb-2">${product.sku}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <strong class="text-primary">${this.formatCurrency(product.final_price)}</strong>
                        <button class="btn btn-primary btn-sm" ${product.is_out_of_stock ? 'disabled' : ''}>
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product || product.is_out_of_stock) {
            this.showAlert(this.language === 'ar' ? 'المنتج غير متوفر' : 'Product not available', 'warning');

            // Play warning sound
            if (window.playSound) {
                window.playSound('warning');
            }
            return;
        }

        const existingItem = this.cart.find(item => item.product_id === productId);

        if (existingItem) {
            if (product.current_stock >= existingItem.quantity + 1) {
                existingItem.quantity += 1;
                existingItem.total_price = existingItem.quantity * existingItem.unit_price;

                // Play success sound for adding item
                if (window.playSound) {
                    window.playSound('success');
                }
            } else {
                this.showAlert(this.language === 'ar' ? 'لا يوجد مخزون كافي' : 'Insufficient stock', 'warning');

                // Play warning sound for insufficient stock
                if (window.playSound) {
                    window.playSound('warning');
                }

                // Check if low stock alert should be triggered
                if (product.current_stock <= 5) {
                    document.dispatchEvent(new CustomEvent('lowStockAlert', {
                        detail: { product: product }
                    }));
                }
                return;
            }
        } else {
            this.cart.push({
                product_id: productId,
                product_name: product.name,
                product_sku: product.sku,
                quantity: 1,
                unit_price: product.final_price,
                total_price: product.final_price
            });

            // Play success sound for adding new item
            if (window.playSound) {
                window.playSound('success');
            }
        }

        this.updateCartDisplay();
        this.calculateTotals();
    }
    
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.product_id !== productId);
        this.updateCartDisplay();
        this.calculateTotals();
    }
    
    updateCartItemQuantity(productId, quantity) {
        const item = this.cart.find(item => item.product_id === productId);
        const product = this.products.find(p => p.id === productId);

        if (item && product) {
            // Don't allow quantity changes for suspended items
            if (item.suspended) {
                this.showAlert(this.language === 'ar' ? 'لا يمكن تعديل العناصر المعلقة' : 'Cannot modify suspended items', 'warning');
                return;
            }

            if (quantity <= 0) {
                this.removeFromCart(productId);
                return;
            }

            if (quantity > product.current_stock) {
                this.showAlert(this.language === 'ar' ? 'لا يوجد مخزون كافي' : 'Insufficient stock', 'warning');
                return;
            }

            item.quantity = quantity;
            item.total_price = quantity * item.unit_price;
            this.updateCartDisplay();
            this.calculateTotals();
        }
    }

    toggleItemSuspension(productId) {
        const item = this.cart.find(item => item.product_id === productId);
        if (item) {
            item.suspended = !item.suspended;
            this.updateCartDisplay();
            this.calculateTotals();

            const message = item.suspended
                ? (this.language === 'ar' ? 'تم تعليق العنصر' : 'Item suspended')
                : (this.language === 'ar' ? 'تم إلغاء تعليق العنصر' : 'Item unsuspended');
            this.showAlert(message, 'info');
        }
    }

    addItemNote(productId) {
        const item = this.cart.find(item => item.product_id === productId);
        if (item) {
            const currentNote = item.note || '';
            const prompt = this.language === 'ar' ? 'أدخل ملاحظة للعنصر:' : 'Enter note for item:';

            const note = window.prompt(prompt, currentNote);
            if (note !== null) {
                item.note = note.trim();
                this.updateCartDisplay();

                const message = note.trim()
                    ? (this.language === 'ar' ? 'تم إضافة الملاحظة' : 'Note added')
                    : (this.language === 'ar' ? 'تم حذف الملاحظة' : 'Note removed');
                this.showAlert(message, 'success');
            }
        }
    }

    unsuspendAllItems() {
        const suspendedItems = this.cart.filter(item => item.suspended);

        if (suspendedItems.length === 0) {
            this.showAlert(this.language === 'ar' ? 'لا توجد عناصر معلقة' : 'No suspended items', 'info');
            return;
        }

        const message = this.language === 'ar'
            ? `هل تريد إلغاء تعليق ${suspendedItems.length} عنصر؟`
            : `Unsuspend ${suspendedItems.length} items?`;

        if (confirm(message)) {
            suspendedItems.forEach(item => {
                item.suspended = false;
            });

            this.updateCartDisplay();
            this.calculateTotals();

            const successMessage = this.language === 'ar'
                ? `تم إلغاء تعليق ${suspendedItems.length} عنصر`
                : `${suspendedItems.length} items unsuspended`;
            this.showAlert(successMessage, 'success');
        }
    }
    
    updateCartDisplay() {
        const cartItems = document.getElementById('cart_items');
        const cartCount = document.getElementById('cart_count');
        
        cartCount.textContent = this.cart.length;
        
        if (this.cart.length === 0) {
            cartItems.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-cart display-4"></i>
                    <p class="mt-2">${this.language === 'ar' ? 'السلة فارغة' : 'Cart is empty'}</p>
                    <small>${this.language === 'ar' ? 'اختر منتجات لإضافتها للسلة' : 'Select products to add to cart'}</small>
                </div>
            `;
            document.getElementById('complete_sale').disabled = true;
            return;
        }
        
        cartItems.innerHTML = '';
        this.cart.forEach(item => {
            const cartItem = this.createCartItem(item);
            cartItems.appendChild(cartItem);
        });
        
        document.getElementById('complete_sale').disabled = false;
    }
    
    createCartItem(item) {
        const div = document.createElement('div');
        div.className = 'cart-item';

        // Check if item is suspended
        const isSuspended = item.suspended || false;
        const suspendedClass = isSuspended ? 'suspended-item' : '';

        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="flex-grow-1">
                    <h6 class="mb-1 ${isSuspended ? 'text-muted' : ''}">${item.product_name}</h6>
                    <small class="text-muted">${item.product_sku}</small>
                    ${item.note ? `<div class="item-note"><small class="text-info"><i class="bi bi-chat-left-text"></i> ${item.note}</small></div>` : ''}
                    ${isSuspended ? `<div class="suspended-badge"><small class="badge bg-warning text-dark"><i class="bi bi-pause-circle"></i> ${this.language === 'ar' ? 'معلق' : 'Suspended'}</small></div>` : ''}
                </div>
                <div class="btn-group-vertical btn-group-sm">
                    <button class="btn btn-outline-info btn-sm" onclick="pos.addItemNote(${item.product_id})" title="${this.language === 'ar' ? 'إضافة ملاحظة' : 'Add Note'}">
                        <i class="bi bi-chat-left-text"></i>
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="pos.toggleItemSuspension(${item.product_id})" title="${this.language === 'ar' ? (isSuspended ? 'إلغاء التعليق' : 'تعليق') : (isSuspended ? 'Unsuspend' : 'Suspend')}">
                        <i class="bi bi-${isSuspended ? 'play-circle' : 'pause-circle'}"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="pos.removeFromCart(${item.product_id})" title="${this.language === 'ar' ? 'حذف' : 'Remove'}">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center ${isSuspended ? 'opacity-50' : ''}">
                <div class="input-group input-group-sm" style="width: 120px;">
                    <button class="btn btn-outline-secondary" onclick="pos.updateCartItemQuantity(${item.product_id}, ${item.quantity - 1})" ${isSuspended ? 'disabled' : ''}>-</button>
                    <input type="number" class="form-control text-center" value="${item.quantity}"
                           onchange="pos.updateCartItemQuantity(${item.product_id}, parseInt(this.value))" ${isSuspended ? 'disabled' : ''}>
                    <button class="btn btn-outline-secondary" onclick="pos.updateCartItemQuantity(${item.product_id}, ${item.quantity + 1})" ${isSuspended ? 'disabled' : ''}>+</button>
                </div>
                <strong class="${isSuspended ? 'text-muted' : ''}">${this.formatCurrency(item.total_price)}</strong>
            </div>
        `;

        return div;
    }
    
    calculateTotals() {
        // Only include non-suspended items in calculations
        const activeItems = this.cart.filter(item => !item.suspended);
        const subtotal = activeItems.reduce((sum, item) => sum + item.total_price, 0);
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        const taxAmount = 0; // Qatar VAT rate
        const total = subtotal - discountAmount + taxAmount;

        document.getElementById('subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('tax_amount').textContent = this.formatCurrency(taxAmount);
        document.getElementById('total_amount').textContent = this.formatCurrency(total);

        // Show suspended items info if any
        const suspendedItems = this.cart.filter(item => item.suspended);
        if (suspendedItems.length > 0) {
            const suspendedTotal = suspendedItems.reduce((sum, item) => sum + item.total_price, 0);
            const suspendedInfo = document.getElementById('suspended_info');
            if (suspendedInfo) {
                suspendedInfo.innerHTML = `
                    <small class="text-warning">
                        <i class="bi bi-pause-circle"></i>
                        ${this.language === 'ar' ? `${suspendedItems.length} عنصر معلق (${this.formatCurrency(suspendedTotal)})` : `${suspendedItems.length} suspended items (${this.formatCurrency(suspendedTotal)})`}
                    </small>
                `;
                suspendedInfo.style.display = 'block';
            }
        } else {
            const suspendedInfo = document.getElementById('suspended_info');
            if (suspendedInfo) {
                suspendedInfo.style.display = 'none';
            }
        }
    }
    
    async completeSale() {
        const activeItems = this.cart.filter(item => !item.suspended);

        if (activeItems.length === 0) {
            this.showAlert(this.language === 'ar' ? 'لا توجد عناصر نشطة في السلة' : 'No active items in cart', 'warning');
            return;
        }

        const saleData = {
            items: activeItems, // Only include non-suspended items
            customer_id: document.getElementById('customer_select').value || null,
            payment_method: document.querySelector('input[name="payment_method"]:checked').value,
            discount_amount: parseFloat(document.getElementById('discount_amount').value) || 0,
            payment_amount: this.getTotalAmount(),
            notes: ''
        };
        
        try {
            const response = await fetch('/sales/api/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(saleData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentSale = result.sale;
                this.showSaleCompletion(result.sale);
                this.clearCart();

                // Play sale completion sound
                if (window.playSound) {
                    window.playSound('sale');
                }

                // Trigger sale completed event
                document.dispatchEvent(new CustomEvent('saleCompleted', {
                    detail: { sale: result.sale }
                }));
            } else {
                this.showAlert(result.error || 'خطأ في إتمام البيع', 'error');

                // Play error sound
                if (window.playSound) {
                    window.playSound('error');
                }

                // Trigger error event
                document.dispatchEvent(new CustomEvent('errorOccurred', {
                    detail: { error: result.error }
                }));
            }
        } catch (error) {
            console.error('Error completing sale:', error);
            this.showAlert(this.language === 'ar' ? 'خطأ في إتمام البيع' : 'Error completing sale', 'error');
        }
    }
    
    showSaleCompletion(sale) {
        document.getElementById('sale_number_display').textContent = sale.sale_number;
        document.getElementById('sale_total_display').textContent = this.formatCurrency(sale.total_amount);
        document.getElementById('sale_items_display').textContent = sale.items_count;
        
        const modal = new bootstrap.Modal(document.getElementById('saleCompletionModal'));
        modal.show();
    }
    
    clearCart() {
        // Check if there are suspended items
        const suspendedItems = this.cart.filter(item => item.suspended);

        if (suspendedItems.length > 0) {
            const message = this.language === 'ar'
                ? `هناك ${suspendedItems.length} عنصر معلق. هل تريد حذف جميع العناصر؟`
                : `There are ${suspendedItems.length} suspended items. Clear all items?`;

            if (!confirm(message)) {
                return;
            }
        }

        this.cart = [];
        this.updateCartDisplay();
        this.calculateTotals();
        document.getElementById('discount_amount').value = 0;
        document.getElementById('customer_select').value = '';
    }
    
    searchProducts(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadProducts(query, document.getElementById('category_filter').value);
        }, 300);
    }
    
    filterByCategory(categoryId) {
        this.loadProducts(document.getElementById('product_search').value, categoryId);
    }
    
    async searchByBarcode() {
        const barcode = document.getElementById('barcode_input').value.trim();
        if (!barcode) return;
        
        try {
            const response = await fetch(`/products/api/barcode/${barcode}`);
            if (response.ok) {
                const product = await response.json();
                this.addToCart(product.id);
                bootstrap.Modal.getInstance(document.getElementById('barcodeScannerModal')).hide();
                document.getElementById('barcode_input').value = '';
            } else {
                this.showAlert(this.language === 'ar' ? 'المنتج غير موجود' : 'Product not found', 'warning');
            }
        } catch (error) {
            console.error('Error searching by barcode:', error);
            this.showAlert(this.language === 'ar' ? 'خطأ في البحث' : 'Search error', 'error');
        }
    }
    
    getTotalAmount() {
        // Only include non-suspended items in total
        const activeItems = this.cart.filter(item => !item.suspended);
        const subtotal = activeItems.reduce((sum, item) => sum + item.total_price, 0);
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        return subtotal - discountAmount;
    }
    
    formatCurrency(amount) {
        const formatted = amount.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        return this.language === 'ar' ? `${formatted} ر.ق` : `QAR ${formatted}`;
    }
    
    showAlert(message, type = 'info') {
        // Use the global POS.showAlert function
        if (window.POS && window.POS.showAlert) {
            window.POS.showAlert(message, type);
        } else {
            alert(message);
        }
    }
    
    handleKeyboardShortcuts(e) {
        // F1 - Complete Sale
        if (e.key === 'F1') {
            e.preventDefault();
            this.completeSale();
        }
        // F2 - Clear Cart
        else if (e.key === 'F2') {
            e.preventDefault();
            this.clearCart();
        }
        // F3 - Barcode Scanner
        else if (e.key === 'F3') {
            e.preventDefault();
            openBarcodeScanner();
        }
        // Escape - Clear search
        else if (e.key === 'Escape') {
            document.getElementById('product_search').value = '';
            this.loadProducts();
        }
    }
}

// Global functions
function openBarcodeScanner() {
    const modal = new bootstrap.Modal(document.getElementById('barcodeScannerModal'));
    modal.show();
    setTimeout(() => {
        document.getElementById('barcode_input').focus();
    }, 500);
}

function searchByBarcode() {
    pos.searchByBarcode();
}

function printInvoice() {
    if (pos.currentSale) {
        window.open(`/sales/${pos.currentSale.id}/print`, '_blank');
    }
}

function newSale() {
    bootstrap.Modal.getInstance(document.getElementById('saleCompletionModal')).hide();
    pos.clearCart();
}

// Initialize POS system
let pos;
document.addEventListener('DOMContentLoaded', function() {
    pos = new POSSystem();
});
