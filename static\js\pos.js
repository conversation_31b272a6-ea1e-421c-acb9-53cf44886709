// Qatar POS System - Point of Sale JavaScript

class POSSystem {
    constructor() {
        this.cart = [];
        this.products = [];
        this.categories = [];
        this.currentSale = null;
        this.language = document.documentElement.lang || 'ar';
        
        this.init();
    }
    
    init() {
        this.loadCategories();
        this.loadProducts();
        this.setupEventListeners();
        this.updateCartDisplay();
    }
    
    setupEventListeners() {
        // Search functionality
        document.getElementById('product_search').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });
        
        // Category filter
        document.getElementById('category_filter').addEventListener('change', (e) => {
            this.filterByCategory(e.target.value);
        });
        
        // Discount calculation
        document.getElementById('discount_amount').addEventListener('input', () => {
            this.calculateTotals();
        });
        
        // Cart actions
        document.getElementById('complete_sale').addEventListener('click', () => {
            this.completeSale();
        });
        
        document.getElementById('clear_cart').addEventListener('click', () => {
            this.clearCart();
        });
        
        document.getElementById('hold_sale').addEventListener('click', () => {
            this.holdSale();
        });
        
        // Barcode scanner
        document.getElementById('barcode_input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchByBarcode();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    async loadCategories() {
        try {
            const response = await fetch('/api/categories');
            const data = await response.json();
            this.categories = data.categories || [];
            console.log('Categories loaded:', this.categories);
            this.renderCategoryFilter();
        } catch (error) {
            console.error('Error loading categories:', error);
            this.categories = [];
        }
    }
    
    async loadProducts(search = '', category = '') {
        try {
            document.getElementById('products_loading').style.display = 'block';
            
            let url = '/products/api/search?limit=50';
            if (search) url += `&q=${encodeURIComponent(search)}`;
            if (category) url += `&category=${category}`;
            
            const response = await fetch(url);
            this.products = await response.json();
            this.renderProducts();
        } catch (error) {
            console.error('Error loading products:', error);
            this.showAlert('خطأ في تحميل المنتجات', 'error');
        } finally {
            document.getElementById('products_loading').style.display = 'none';
        }
    }
    
    renderCategoryFilter() {
        const select = document.getElementById('category_filter');
        if (!select) {
            console.error('Category filter element not found');
            return;
        }

        const currentValue = select.value;

        // Clear existing options except first
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        console.log('Rendering categories:', this.categories);

        if (this.categories && this.categories.length > 0) {
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
                console.log('Added category:', category.name);
            });
        }

        select.value = currentValue;
    }
    
    renderProducts() {
        const grid = document.getElementById('products_grid');
        grid.innerHTML = '';
        
        if (this.products.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-search display-4 text-muted"></i>
                    <p class="mt-2 text-muted">${this.language === 'ar' ? 'لا توجد منتجات' : 'No products found'}</p>
                </div>
            `;
            return;
        }
        
        this.products.forEach(product => {
            const productCard = this.createProductCard(product);
            grid.appendChild(productCard);
        });
    }
    
    createProductCard(product) {
        const col = document.createElement('div');
        col.className = 'col-lg-4 col-md-6 mb-3';
        
        const stockStatus = product.is_out_of_stock ? 'danger' : 
                           product.is_low_stock ? 'warning' : 'success';
        const stockText = product.is_out_of_stock ? 
                         (this.language === 'ar' ? 'نفد المخزون' : 'Out of Stock') :
                         product.current_stock;
        
        col.innerHTML = `
            <div class="card product-card h-100" onclick="pos.addToCart(${product.id})">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0 small">${product.name}</h6>
                        <span class="badge bg-${stockStatus}">${stockText}</span>
                    </div>
                    <p class="card-text text-muted small mb-2">${product.sku}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <strong class="text-primary">${this.formatCurrency(product.final_price)}</strong>
                        <button class="btn btn-primary btn-sm" ${product.is_out_of_stock ? 'disabled' : ''}>
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product || product.is_out_of_stock) {
            this.showAlert(this.language === 'ar' ? 'المنتج غير متوفر' : 'Product not available', 'warning');
            return;
        }
        
        const existingItem = this.cart.find(item => item.product_id === productId);
        
        if (existingItem) {
            if (product.current_stock >= existingItem.quantity + 1) {
                existingItem.quantity += 1;
                existingItem.total_price = existingItem.quantity * existingItem.unit_price;
            } else {
                this.showAlert(this.language === 'ar' ? 'لا يوجد مخزون كافي' : 'Insufficient stock', 'warning');
                return;
            }
        } else {
            this.cart.push({
                product_id: productId,
                product_name: product.name,
                product_sku: product.sku,
                quantity: 1,
                unit_price: product.final_price,
                total_price: product.final_price
            });
        }
        
        this.updateCartDisplay();
        this.calculateTotals();
    }
    
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.product_id !== productId);
        this.updateCartDisplay();
        this.calculateTotals();
    }
    
    updateCartItemQuantity(productId, quantity) {
        const item = this.cart.find(item => item.product_id === productId);
        const product = this.products.find(p => p.id === productId);
        
        if (item && product) {
            if (quantity <= 0) {
                this.removeFromCart(productId);
                return;
            }
            
            if (quantity > product.current_stock) {
                this.showAlert(this.language === 'ar' ? 'لا يوجد مخزون كافي' : 'Insufficient stock', 'warning');
                return;
            }
            
            item.quantity = quantity;
            item.total_price = quantity * item.unit_price;
            this.updateCartDisplay();
            this.calculateTotals();
        }
    }
    
    updateCartDisplay() {
        const cartItems = document.getElementById('cart_items');
        const cartCount = document.getElementById('cart_count');
        
        cartCount.textContent = this.cart.length;
        
        if (this.cart.length === 0) {
            cartItems.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-cart display-4"></i>
                    <p class="mt-2">${this.language === 'ar' ? 'السلة فارغة' : 'Cart is empty'}</p>
                    <small>${this.language === 'ar' ? 'اختر منتجات لإضافتها للسلة' : 'Select products to add to cart'}</small>
                </div>
            `;
            document.getElementById('complete_sale').disabled = true;
            return;
        }
        
        cartItems.innerHTML = '';
        this.cart.forEach(item => {
            const cartItem = this.createCartItem(item);
            cartItems.appendChild(cartItem);
        });
        
        document.getElementById('complete_sale').disabled = false;
    }
    
    createCartItem(item) {
        const div = document.createElement('div');
        div.className = 'cart-item';
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${item.product_name}</h6>
                    <small class="text-muted">${item.product_sku}</small>
                </div>
                <button class="btn btn-outline-danger btn-sm" onclick="pos.removeFromCart(${item.product_id})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <div class="input-group input-group-sm" style="width: 120px;">
                    <button class="btn btn-outline-secondary" onclick="pos.updateCartItemQuantity(${item.product_id}, ${item.quantity - 1})">-</button>
                    <input type="number" class="form-control text-center" value="${item.quantity}" 
                           onchange="pos.updateCartItemQuantity(${item.product_id}, parseInt(this.value))">
                    <button class="btn btn-outline-secondary" onclick="pos.updateCartItemQuantity(${item.product_id}, ${item.quantity + 1})">+</button>
                </div>
                <strong>${this.formatCurrency(item.total_price)}</strong>
            </div>
        `;
        
        return div;
    }
    
    calculateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total_price, 0);
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        const taxAmount = 0; // Qatar VAT rate
        const total = subtotal - discountAmount + taxAmount;
        
        document.getElementById('subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('tax_amount').textContent = this.formatCurrency(taxAmount);
        document.getElementById('total_amount').textContent = this.formatCurrency(total);
    }
    
    async completeSale() {
        if (this.cart.length === 0) {
            this.showAlert(this.language === 'ar' ? 'السلة فارغة' : 'Cart is empty', 'warning');
            return;
        }
        
        const saleData = {
            items: this.cart,
            customer_id: document.getElementById('customer_select').value || null,
            payment_method: document.querySelector('input[name="payment_method"]:checked').value,
            discount_amount: parseFloat(document.getElementById('discount_amount').value) || 0,
            payment_amount: this.getTotalAmount(),
            notes: ''
        };
        
        try {
            const response = await fetch('/sales/api/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(saleData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentSale = result.sale;
                this.showSaleCompletion(result.sale);
                this.clearCart();
            } else {
                this.showAlert(result.error || 'خطأ في إتمام البيع', 'error');
            }
        } catch (error) {
            console.error('Error completing sale:', error);
            this.showAlert(this.language === 'ar' ? 'خطأ في إتمام البيع' : 'Error completing sale', 'error');
        }
    }
    
    showSaleCompletion(sale) {
        document.getElementById('sale_number_display').textContent = sale.sale_number;
        document.getElementById('sale_total_display').textContent = this.formatCurrency(sale.total_amount);
        document.getElementById('sale_items_display').textContent = sale.items_count;
        
        const modal = new bootstrap.Modal(document.getElementById('saleCompletionModal'));
        modal.show();
    }
    
    clearCart() {
        this.cart = [];
        this.updateCartDisplay();
        this.calculateTotals();
        document.getElementById('discount_amount').value = 0;
        document.getElementById('customer_select').value = '';
    }
    
    searchProducts(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadProducts(query, document.getElementById('category_filter').value);
        }, 300);
    }
    
    filterByCategory(categoryId) {
        this.loadProducts(document.getElementById('product_search').value, categoryId);
    }
    
    async searchByBarcode() {
        const barcode = document.getElementById('barcode_input').value.trim();
        if (!barcode) return;
        
        try {
            const response = await fetch(`/products/api/barcode/${barcode}`);
            if (response.ok) {
                const product = await response.json();
                this.addToCart(product.id);
                bootstrap.Modal.getInstance(document.getElementById('barcodeScannerModal')).hide();
                document.getElementById('barcode_input').value = '';
            } else {
                this.showAlert(this.language === 'ar' ? 'المنتج غير موجود' : 'Product not found', 'warning');
            }
        } catch (error) {
            console.error('Error searching by barcode:', error);
            this.showAlert(this.language === 'ar' ? 'خطأ في البحث' : 'Search error', 'error');
        }
    }
    
    getTotalAmount() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total_price, 0);
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        return subtotal - discountAmount;
    }
    
    formatCurrency(amount) {
        const formatted = amount.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        return this.language === 'ar' ? `${formatted} ر.ق` : `QAR ${formatted}`;
    }
    
    showAlert(message, type = 'info') {
        // Use the global POS.showAlert function
        if (window.POS && window.POS.showAlert) {
            window.POS.showAlert(message, type);
        } else {
            alert(message);
        }
    }
    
    handleKeyboardShortcuts(e) {
        // F1 - Complete Sale
        if (e.key === 'F1') {
            e.preventDefault();
            this.completeSale();
        }
        // F2 - Clear Cart
        else if (e.key === 'F2') {
            e.preventDefault();
            this.clearCart();
        }
        // F3 - Barcode Scanner
        else if (e.key === 'F3') {
            e.preventDefault();
            openBarcodeScanner();
        }
        // Escape - Clear search
        else if (e.key === 'Escape') {
            document.getElementById('product_search').value = '';
            this.loadProducts();
        }
    }
}

// Global functions
function openBarcodeScanner() {
    const modal = new bootstrap.Modal(document.getElementById('barcodeScannerModal'));
    modal.show();
    setTimeout(() => {
        document.getElementById('barcode_input').focus();
    }, 500);
}

function searchByBarcode() {
    pos.searchByBarcode();
}

function printInvoice() {
    if (pos.currentSale) {
        window.open(`/sales/${pos.currentSale.id}/print`, '_blank');
    }
}

function newSale() {
    bootstrap.Modal.getInstance(document.getElementById('saleCompletionModal')).hide();
    pos.clearCart();
}

// Initialize POS system
let pos;
document.addEventListener('DOMContentLoaded', function() {
    pos = new POSSystem();
});
