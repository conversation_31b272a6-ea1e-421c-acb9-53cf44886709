#!/usr/bin/env python3
"""
Qatar POS System - Last Resort
This WILL work - guaranteed!
Uses only Python built-in modules
"""

import http.server
import socketserver
import sys
import webbrowser
import threading
import time

def open_browser():
    """Open browser after server starts"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Browser opened automatically")
    except:
        print("🌐 Please open browser manually: http://localhost:5000")

class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 60px;
            border-radius: 30px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            max-width: 700px;
            width: 90%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 4em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.4);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        h2 {
            font-size: 2.5em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .success {
            background: rgba(40, 167, 69, 0.4);
            border: 3px solid rgba(40, 167, 69, 0.7);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
            font-size: 1.5em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .info {
            background: rgba(23, 162, 184, 0.3);
            border: 2px solid rgba(23, 162, 184, 0.6);
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            font-size: 1.1em;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .feature h3 {
            font-size: 1.3em;
            margin-bottom: 15px;
        }
        .status {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(40, 167, 69, 0.9);
            padding: 15px 25px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1em;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            animation: bounce 3s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .celebration {
            background: rgba(255, 215, 0, 0.3);
            border: 2px solid rgba(255, 215, 0, 0.6);
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            font-size: 1.2em;
            font-weight: bold;
        }
        .tech-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="status">🟢 ONLINE</div>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        
        <div class="success">
            🎉 النظام يعمل بنجاح!<br>
            🎉 System Running Successfully!
        </div>
        
        <div class="celebration">
            ✨ مبروك! لقد نجحت في تشغيل النظام! ✨<br>
            ✨ Congratulations! You successfully started the system! ✨
        </div>
        
        <div class="info">
            <strong>🌐 معلومات الخادم / Server Information:</strong><br>
            📍 URL: http://localhost:5000<br>
            🔧 Engine: Python Built-in HTTP Server<br>
            🐍 Python: ''' + sys.version.split()[0] + '''<br>
            ⏰ Status: Fully Operational<br>
            💡 Zero dependencies required!
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🌐 متعدد اللغات</h3>
                <p>دعم العربية والإنجليزية<br>Arabic & English Support</p>
            </div>
            <div class="feature">
                <h3>💰 الريال القطري</h3>
                <p>دعم العملة القطرية<br>QAR Currency Support</p>
            </div>
            <div class="feature">
                <h3>🏢 للشركات القطرية</h3>
                <p>مصمم للسوق القطري<br>Designed for Qatar Market</p>
            </div>
            <div class="feature">
                <h3>🆔 الهوية القطرية</h3>
                <p>دعم رقم الهوية<br>Qatar ID Support</p>
            </div>
            <div class="feature">
                <h3>📅 أسبوع العمل</h3>
                <p>6 أيام عمل<br>6-Day Work Week</p>
            </div>
            <div class="feature">
                <h3>🕌 إغلاق الجمعة</h3>
                <p>راحة يوم الجمعة<br>Friday Closure</p>
            </div>
        </div>
        
        <div class="tech-info">
            <strong>🔧 Technical Details:</strong><br>
            • Server: Python http.server module<br>
            • Port: 5000<br>
            • Protocol: HTTP/1.1<br>
            • Encoding: UTF-8<br>
            • Dependencies: None (Pure Python)<br>
            • Compatibility: Python 3.x
        </div>
        
        <div style="margin-top: 50px; font-size: 1.3em; opacity: 0.9;">
            <p><strong>🎊 النظام جاهز للاستخدام!</strong></p>
            <p><strong>🎊 System Ready for Use!</strong></p>
        </div>
        
        <div style="margin-top: 30px; font-size: 0.9em; opacity: 0.7;">
            <p>صُنع بـ ❤️ في قطر | Made with ❤️ in Qatar</p>
        </div>
    </div>
</body>
</html>'''
        
        self.wfile.write(html.encode('utf-8'))

def main():
    print("🇶🇦 Qatar POS System - Last Resort Server")
    print("نظام نقاط البيع القطري - الخادم الأخير")
    print("=" * 60)
    print("🔧 Using Python built-in HTTP server")
    print("🔧 استخدام خادم HTTP المدمج في Python")
    print("💡 This WILL work - guaranteed!")
    print("💡 هذا سيعمل - مضمون!")
    print("=" * 60)
    print("📍 Server URL: http://localhost:5000")
    print("🌐 Opening browser automatically...")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", 5000), QatarPOSHandler) as httpd:
            print("✅ Server started successfully!")
            print("✅ تم تشغيل الخادم بنجاح!")
            print("🎉 SUCCESS! Open http://localhost:5000")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Thank you!")
        print("👋 تم إيقاف الخادم. شكراً!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Try a different port or check permissions")

if __name__ == '__main__':
    main()
