{% extends "base.html" %}

{% block title %}{{ 'النسخ الاحتياطي' if language == 'ar' else 'Backup & Restore' }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-cloud-download"></i>
                {{ 'النسخ الاحتياطي والاستعادة' if language == 'ar' else 'Backup & Restore' }}
            </h1>
            <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة' if language == 'ar' else 'Back' }}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Create Backup -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-download"></i>
                    {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    {{ 'إنشاء نسخة احتياطية من قاعدة البيانات والملفات المهمة' if language == 'ar' 
                       else 'Create a backup of the database and important files' }}
                </p>
                
                <div class="mb-3">
                    <label class="form-label">{{ 'نوع النسخة الاحتياطية' if language == 'ar' else 'Backup Type' }}</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="backup_type" id="backup_full" value="full" checked>
                        <label class="form-check-label" for="backup_full">
                            {{ 'نسخة كاملة' if language == 'ar' else 'Full Backup' }}
                        </label>
                        <div class="form-text">{{ 'تشمل جميع البيانات والملفات' if language == 'ar' else 'Includes all data and files' }}</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="backup_type" id="backup_data" value="data">
                        <label class="form-check-label" for="backup_data">
                            {{ 'البيانات فقط' if language == 'ar' else 'Data Only' }}
                        </label>
                        <div class="form-text">{{ 'قاعدة البيانات فقط' if language == 'ar' else 'Database only' }}</div>
                    </div>
                </div>
                
                <button type="button" class="btn btn-success" onclick="createBackup()">
                    <i class="bi bi-download"></i>
                    {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                </button>
                
                <div id="backup-progress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted">{{ 'جاري إنشاء النسخة الاحتياطية...' if language == 'ar' else 'Creating backup...' }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Backup -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-upload"></i>
                    {{ 'استعادة نسخة احتياطية' if language == 'ar' else 'Restore Backup' }}
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    {{ 'استعادة البيانات من نسخة احتياطية سابقة' if language == 'ar' 
                       else 'Restore data from a previous backup' }}
                </p>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ 'تحذير: ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية' if language == 'ar' 
                       else 'Warning: Restore will replace all current data' }}
                </div>
                
                <div class="mb-3">
                    <label for="backup_file" class="form-label">{{ 'اختر ملف النسخة الاحتياطية' if language == 'ar' else 'Select Backup File' }}</label>
                    <input type="file" class="form-control" id="backup_file" accept=".sql,.db,.zip">
                </div>
                
                <button type="button" class="btn btn-warning" onclick="restoreBackup()" disabled>
                    <i class="bi bi-upload"></i>
                    {{ 'استعادة النسخة الاحتياطية' if language == 'ar' else 'Restore Backup' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Backup History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i>
                    {{ 'سجل النسخ الاحتياطية' if language == 'ar' else 'Backup History' }}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                                <th>{{ 'النوع' if language == 'ar' else 'Type' }}</th>
                                <th>{{ 'الحجم' if language == 'ar' else 'Size' }}</th>
                                <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                            </tr>
                        </thead>
                        <tbody id="backup-history">
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    {{ 'لا توجد نسخ احتياطية' if language == 'ar' else 'No backups found' }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Automated Backup Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    {{ 'إعدادات النسخ الاحتياطي التلقائي' if language == 'ar' else 'Automated Backup Settings' }}
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_backup_enabled">
                                <label class="form-check-label" for="auto_backup_enabled">
                                    {{ 'تفعيل النسخ الاحتياطي التلقائي' if language == 'ar' else 'Enable Automated Backup' }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="backup_frequency" class="form-label">{{ 'تكرار النسخ' if language == 'ar' else 'Backup Frequency' }}</label>
                            <select class="form-select" id="backup_frequency">
                                <option value="daily">{{ 'يومي' if language == 'ar' else 'Daily' }}</option>
                                <option value="weekly">{{ 'أسبوعي' if language == 'ar' else 'Weekly' }}</option>
                                <option value="monthly">{{ 'شهري' if language == 'ar' else 'Monthly' }}</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="backup_time" class="form-label">{{ 'وقت النسخ' if language == 'ar' else 'Backup Time' }}</label>
                            <input type="time" class="form-control" id="backup_time" value="02:00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="backup_retention" class="form-label">{{ 'الاحتفاظ بالنسخ (أيام)' if language == 'ar' else 'Retention Period (days)' }}</label>
                            <input type="number" class="form-control" id="backup_retention" value="30" min="1" max="365">
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">
                        <i class="bi bi-check-circle"></i>
                        {{ 'حفظ الإعدادات' if language == 'ar' else 'Save Settings' }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
async function createBackup() {
    const backupType = document.querySelector('input[name="backup_type"]:checked').value;
    const progressDiv = document.getElementById('backup-progress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    
    // Show progress
    progressDiv.style.display = 'block';
    progressBar.style.width = '10%';
    
    try {
        const response = await fetch('/settings/api/backup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ type: backupType })
        });
        
        progressBar.style.width = '50%';
        
        const result = await response.json();
        
        progressBar.style.width = '100%';
        
        if (result.success) {
            setTimeout(() => {
                progressDiv.style.display = 'none';
                progressBar.style.width = '0%';
                
                // Show success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                alertDiv.innerHTML = `
                    <i class="bi bi-check-circle"></i>
                    ${result.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.querySelector('.card-body').appendChild(alertDiv);
                
                // Refresh backup history
                loadBackupHistory();
            }, 1000);
        } else {
            throw new Error(result.error);
        }
        
    } catch (error) {
        progressDiv.style.display = 'none';
        progressBar.style.width = '0%';
        
        // Show error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle"></i>
            ${error.message || 'حدث خطأ أثناء إنشاء النسخة الاحتياطية'}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.card-body').appendChild(alertDiv);
    }
}

function restoreBackup() {
    const fileInput = document.getElementById('backup_file');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('{{ "يرجى اختيار ملف النسخة الاحتياطية" if language == "ar" else "Please select a backup file" }}');
        return;
    }
    
    if (confirm('{{ "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ ستفقد جميع البيانات الحالية!" if language == "ar" else "Are you sure you want to restore this backup? You will lose all current data!" }}')) {
        // Implementation for restore functionality
        alert('{{ "ميزة الاستعادة قيد التطوير" if language == "ar" else "Restore functionality is under development" }}');
    }
}

function saveBackupSettings() {
    const settings = {
        auto_backup_enabled: document.getElementById('auto_backup_enabled').checked,
        backup_frequency: document.getElementById('backup_frequency').value,
        backup_time: document.getElementById('backup_time').value,
        backup_retention: document.getElementById('backup_retention').value
    };
    
    // Save settings (implementation needed)
    alert('{{ "تم حفظ الإعدادات" if language == "ar" else "Settings saved" }}');
}

function loadBackupHistory() {
    // Load backup history (implementation needed)
    console.log('Loading backup history...');
}

// Enable/disable restore button based on file selection
document.getElementById('backup_file').addEventListener('change', function() {
    const restoreBtn = document.querySelector('button[onclick="restoreBackup()"]');
    restoreBtn.disabled = !this.files.length;
});

// Load backup history on page load
document.addEventListener('DOMContentLoaded', function() {
    loadBackupHistory();
});
</script>
{% endblock %}
