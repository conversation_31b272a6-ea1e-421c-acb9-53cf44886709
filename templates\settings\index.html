{% extends "base.html" %}

{% block title %}
{{ 'إعدادات النظام - نظام نقاط البيع القطري' if language == 'ar' else 'System Settings - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-gear"></i>
            {{ 'إعدادات النظام' if language == 'ar' else 'System Settings' }}
        </h1>
    </div>
</div>

<div class="row">
    <!-- Settings Navigation -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {{ 'فئات الإعدادات' if language == 'ar' else 'Settings Categories' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="bi bi-gear"></i>
                    {{ 'إعدادات عامة' if language == 'ar' else 'General Settings' }}
                </a>
                <a href="#company" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-building"></i>
                    {{ 'معلومات الشركة' if language == 'ar' else 'Company Information' }}
                </a>
                <a href="#pos" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-cash-register"></i>
                    {{ 'إعدادات نقطة البيع' if language == 'ar' else 'POS Settings' }}
                </a>
                <a href="#tax" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-receipt"></i>
                    {{ 'الإعدادات الضريبية' if language == 'ar' else 'Tax Settings' }}
                </a>
                <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-bell"></i>
                    {{ 'إعدادات الإشعارات' if language == 'ar' else 'Notification Settings' }}
                </a>
                <a href="#sounds" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-volume-up"></i>
                    {{ 'إعدادات الأصوات' if language == 'ar' else 'Sound Settings' }}
                </a>
                <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-shield-check"></i>
                    {{ 'إعدادات الأمان' if language == 'ar' else 'Security Settings' }}
                </a>
                <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-cloud-download"></i>
                    {{ 'النسخ الاحتياطي' if language == 'ar' else 'Backup & Restore' }}
                </a>
                <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-shield-check"></i>
                    {{ 'إعدادات الأمان' if language == 'ar' else 'Security Settings' }}
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Content -->
    <div class="col-lg-9">
        <div class="tab-content">
            <!-- General Settings -->
            <div class="tab-pane fade show active" id="general">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-gear"></i>
                            {{ 'الإعدادات العامة' if language == 'ar' else 'General Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='system') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم النظام' if language == 'ar' else 'System Name' }}</label>
                                    <input type="text" class="form-control" name="system_name" 
                                           value="{{ settings.system_name or 'Qatar POS System' }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اللغة الافتراضية' if language == 'ar' else 'Default Language' }}</label>
                                    <select class="form-select" name="default_language">
                                        <option value="ar" {{ 'selected' if settings.default_language == 'ar' }}>
                                            {{ 'العربية' if language == 'ar' else 'Arabic' }}
                                        </option>
                                        <option value="en" {{ 'selected' if settings.default_language == 'en' }}>
                                            {{ 'الإنجليزية' if language == 'ar' else 'English' }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العملة' if language == 'ar' else 'Currency' }}</label>
                                    <select class="form-select" name="currency">
                                        <option value="QAR" {{ 'selected' if settings.currency == 'QAR' }}>
                                            {{ 'ريال قطري (QAR)' if language == 'ar' else 'Qatari Riyal (QAR)' }}
                                        </option>
                                        <option value="USD" {{ 'selected' if settings.currency == 'USD' }}>
                                            {{ 'دولار أمريكي (USD)' if language == 'ar' else 'US Dollar (USD)' }}
                                        </option>
                                        <option value="EUR" {{ 'selected' if settings.currency == 'EUR' }}>
                                            {{ 'يورو (EUR)' if language == 'ar' else 'Euro (EUR)' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'المنطقة الزمنية' if language == 'ar' else 'Timezone' }}</label>
                                    <select class="form-select" name="timezone">
                                        <option value="Asia/Qatar" {{ 'selected' if settings.timezone == 'Asia/Qatar' }}>
                                            {{ 'قطر (Asia/Qatar)' if language == 'ar' else 'Qatar (Asia/Qatar)' }}
                                        </option>
                                        <option value="UTC" {{ 'selected' if settings.timezone == 'UTC' }}>UTC</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'تنسيق التاريخ' if language == 'ar' else 'Date Format' }}</label>
                                    <select class="form-select" name="date_format">
                                        <option value="DD/MM/YYYY" {{ 'selected' if settings.date_format == 'DD/MM/YYYY' }}>DD/MM/YYYY</option>
                                        <option value="MM/DD/YYYY" {{ 'selected' if settings.date_format == 'MM/DD/YYYY' }}>MM/DD/YYYY</option>
                                        <option value="YYYY-MM-DD" {{ 'selected' if settings.date_format == 'YYYY-MM-DD' }}>YYYY-MM-DD</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'تنسيق الوقت' if language == 'ar' else 'Time Format' }}</label>
                                    <select class="form-select" name="time_format">
                                        <option value="24" {{ 'selected' if settings.time_format == '24' }}>24 {{ 'ساعة' if language == 'ar' else 'Hour' }}</option>
                                        <option value="12" {{ 'selected' if settings.time_format == '12' }}>12 {{ 'ساعة' if language == 'ar' else 'Hour' }}</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="maintenance_mode" 
                                           {{ 'checked' if settings.maintenance_mode }}>
                                    <label class="form-check-label">
                                        {{ 'وضع الصيانة' if language == 'ar' else 'Maintenance Mode' }}
                                    </label>
                                    <div class="form-text">
                                        {{ 'تفعيل وضع الصيانة سيمنع المستخدمين من الوصول للنظام' if language == 'ar' else 'Enabling maintenance mode will prevent users from accessing the system' }}
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ الإعدادات' if language == 'ar' else 'Save Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="tab-pane fade" id="company">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-building"></i>
                            {{ 'معلومات الشركة' if language == 'ar' else 'Company Information' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='company') }}" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم الشركة (عربي)' if language == 'ar' else 'Company Name (Arabic)' }}</label>
                                    <input type="text" class="form-control" name="company_name_ar" 
                                           value="{{ settings.company_name_ar }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم الشركة (إنجليزي)' if language == 'ar' else 'Company Name (English)' }}</label>
                                    <input type="text" class="form-control" name="company_name_en" 
                                           value="{{ settings.company_name_en }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'السجل التجاري' if language == 'ar' else 'Commercial Registration' }}</label>
                                    <input type="text" class="form-control" name="commercial_registration" 
                                           value="{{ settings.commercial_registration }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'الرقم الضريبي' if language == 'ar' else 'Tax Number' }}</label>
                                    <input type="text" class="form-control" name="tax_number" 
                                           value="{{ settings.tax_number }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}</label>
                                    <input type="tel" class="form-control" name="phone" 
                                           value="{{ settings.phone }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'البريد الإلكتروني' if language == 'ar' else 'Email Address' }}</label>
                                    <input type="email" class="form-control" name="email" 
                                           value="{{ settings.email }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العنوان (عربي)' if language == 'ar' else 'Address (Arabic)' }}</label>
                                    <textarea class="form-control" name="address_ar" rows="3">{{ settings.address_ar }}</textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العنوان (إنجليزي)' if language == 'ar' else 'Address (English)' }}</label>
                                    <textarea class="form-control" name="address_en" rows="3">{{ settings.address_en }}</textarea>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}</label>
                                    <input type="file" class="form-control" name="logo" accept="image/*">
                                    {% if settings.logo %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename='uploads/logos/' + settings.logo) }}" 
                                             alt="Company Logo" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'الموقع الإلكتروني' if language == 'ar' else 'Website' }}</label>
                                    <input type="url" class="form-control" name="website" 
                                           value="{{ settings.website }}">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ معلومات الشركة' if language == 'ar' else 'Save Company Information' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- POS Settings -->
            <div class="tab-pane fade" id="pos">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-cash-register"></i>
                            {{ 'إعدادات نقطة البيع' if language == 'ar' else 'POS Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='pos') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'طابعة الإيصالات' if language == 'ar' else 'Receipt Printer' }}</label>
                                    <select class="form-select" name="receipt_printer">
                                        <option value="">{{ 'بدون طابعة' if language == 'ar' else 'No Printer' }}</option>
                                        <option value="thermal" {{ 'selected' if settings.receipt_printer == 'thermal' }}>
                                            {{ 'طابعة حرارية' if language == 'ar' else 'Thermal Printer' }}
                                        </option>
                                        <option value="inkjet" {{ 'selected' if settings.receipt_printer == 'inkjet' }}>
                                            {{ 'طابعة نافثة للحبر' if language == 'ar' else 'Inkjet Printer' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'حجم الإيصال' if language == 'ar' else 'Receipt Size' }}</label>
                                    <select class="form-select" name="receipt_size">
                                        <option value="80mm" {{ 'selected' if settings.receipt_size == '80mm' }}>80mm</option>
                                        <option value="58mm" {{ 'selected' if settings.receipt_size == '58mm' }}>58mm</option>
                                        <option value="A4" {{ 'selected' if settings.receipt_size == 'A4' }}>A4</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'ماسح الباركود' if language == 'ar' else 'Barcode Scanner' }}</label>
                                    <select class="form-select" name="barcode_scanner">
                                        <option value="disabled" {{ 'selected' if settings.barcode_scanner == 'disabled' }}>
                                            {{ 'معطل' if language == 'ar' else 'Disabled' }}
                                        </option>
                                        <option value="enabled" {{ 'selected' if settings.barcode_scanner == 'enabled' }}>
                                            {{ 'مفعل' if language == 'ar' else 'Enabled' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'درج النقود' if language == 'ar' else 'Cash Drawer' }}</label>
                                    <select class="form-select" name="cash_drawer">
                                        <option value="disabled" {{ 'selected' if settings.cash_drawer == 'disabled' }}>
                                            {{ 'معطل' if language == 'ar' else 'Disabled' }}
                                        </option>
                                        <option value="enabled" {{ 'selected' if settings.cash_drawer == 'enabled' }}>
                                            {{ 'مفعل' if language == 'ar' else 'Enabled' }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_print_receipt" 
                                           {{ 'checked' if settings.auto_print_receipt }}>
                                    <label class="form-check-label">
                                        {{ 'طباعة الإيصال تلقائياً' if language == 'ar' else 'Auto Print Receipt' }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="show_customer_display" 
                                           {{ 'checked' if settings.show_customer_display }}>
                                    <label class="form-check-label">
                                        {{ 'عرض شاشة العميل' if language == 'ar' else 'Show Customer Display' }}
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ إعدادات نقطة البيع' if language == 'ar' else 'Save POS Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tax Settings -->
            <div class="tab-pane fade" id="tax">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-receipt"></i>
                            {{ 'الإعدادات الضريبية' if language == 'ar' else 'Tax Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='tax') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'معدل الضريبة (%)' if language == 'ar' else 'Tax Rate (%)' }}</label>
                                    <input type="number" class="form-control" name="tax_rate" step="0.01" min="0" max="100"
                                           value="{{ settings.tax_rate or 0 }}">
                                    <div class="form-text">
                                        {{ 'قطر لا تطبق ضريبة القيمة المضافة حالياً' if language == 'ar' else 'Qatar currently does not apply VAT' }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'رقم التسجيل الضريبي' if language == 'ar' else 'Tax Registration Number' }}</label>
                                    <input type="text" class="form-control" name="tax_registration_number" 
                                           value="{{ settings.tax_registration_number }}">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="tax_inclusive" 
                                           {{ 'checked' if settings.tax_inclusive }}>
                                    <label class="form-check-label">
                                        {{ 'الأسعار تشمل الضريبة' if language == 'ar' else 'Prices Include Tax' }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="show_tax_on_receipt" 
                                           {{ 'checked' if settings.show_tax_on_receipt }}>
                                    <label class="form-check-label">
                                        {{ 'إظهار الضريبة في الإيصال' if language == 'ar' else 'Show Tax on Receipt' }}
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ الإعدادات الضريبية' if language == 'ar' else 'Save Tax Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="tab-pane fade" id="notifications">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-bell"></i>
                            {{ 'إعدادات الإشعارات' if language == 'ar' else 'Notification Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='notifications') }}">
                            <!-- Master Notification Control -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="notifications_enabled"
                                                               id="notifications_enabled" {{ 'checked' if settings.get('notifications_enabled') == 'true' }}>
                                                        <label class="form-check-label fw-bold" for="notifications_enabled">
                                                            {{ 'تفعيل الإشعارات' if language == 'ar' else 'Enable Notifications' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">
                                                        {{ 'تفعيل أو إلغاء جميع الإشعارات في النظام' if language == 'ar' else 'Enable or disable all system notifications' }}
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">{{ 'مدة عرض الإشعار (ثواني)' if language == 'ar' else 'Notification Duration (seconds)' }}</label>
                                                    <select class="form-select" name="notification_duration" id="notification_duration">
                                                        <option value="3" {{ 'selected' if settings.get('notification_duration') == '3' }}>3 {{ 'ثواني' if language == 'ar' else 'seconds' }}</option>
                                                        <option value="5" {{ 'selected' if settings.get('notification_duration', '5') == '5' }}>5 {{ 'ثواني' if language == 'ar' else 'seconds' }}</option>
                                                        <option value="7" {{ 'selected' if settings.get('notification_duration') == '7' }}>7 {{ 'ثواني' if language == 'ar' else 'seconds' }}</option>
                                                        <option value="10" {{ 'selected' if settings.get('notification_duration') == '10' }}>10 {{ 'ثواني' if language == 'ar' else 'seconds' }}</option>
                                                        <option value="0" {{ 'selected' if settings.get('notification_duration') == '0' }}>{{ 'دائم (يدوي)' if language == 'ar' else 'Permanent (manual)' }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Types -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="sale_notifications_enabled"
                                               id="sale_notifications_enabled" {{ 'checked' if settings.get('sale_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="sale_notifications_enabled">
                                            <i class="bi bi-cash-coin text-success me-2"></i>
                                            {{ 'إشعارات البيع' if language == 'ar' else 'Sale Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعار عند إتمام عملية بيع' if language == 'ar' else 'Notify when a sale is completed' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="payment_notifications_enabled"
                                               id="payment_notifications_enabled" {{ 'checked' if settings.get('payment_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="payment_notifications_enabled">
                                            <i class="bi bi-credit-card text-info me-2"></i>
                                            {{ 'إشعارات الدفع' if language == 'ar' else 'Payment Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعار عند استلام الدفع' if language == 'ar' else 'Notify when payment is received' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="low_stock_notifications_enabled"
                                               id="low_stock_notifications_enabled" {{ 'checked' if settings.get('low_stock_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="low_stock_notifications_enabled">
                                            <i class="bi bi-box text-warning me-2"></i>
                                            {{ 'إشعارات نفاد المخزون' if language == 'ar' else 'Low Stock Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعار عند انخفاض المخزون' if language == 'ar' else 'Notify when stock is low' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="error_notifications_enabled"
                                               id="error_notifications_enabled" {{ 'checked' if settings.get('error_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="error_notifications_enabled">
                                            <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                                            {{ 'إشعارات الأخطاء' if language == 'ar' else 'Error Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعار عند حدوث خطأ' if language == 'ar' else 'Notify when an error occurs' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="system_notifications_enabled"
                                               id="system_notifications_enabled" {{ 'checked' if settings.get('system_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="system_notifications_enabled">
                                            <i class="bi bi-gear text-secondary me-2"></i>
                                            {{ 'إشعارات النظام' if language == 'ar' else 'System Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعارات عامة للنظام' if language == 'ar' else 'General system notifications' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="backup_notifications_enabled"
                                               id="backup_notifications_enabled" {{ 'checked' if settings.get('backup_notifications_enabled') == 'true' }}>
                                        <label class="form-check-label" for="backup_notifications_enabled">
                                            <i class="bi bi-cloud-download text-primary me-2"></i>
                                            {{ 'إشعارات النسخ الاحتياطي' if language == 'ar' else 'Backup Notifications' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'إشعار عند إنشاء نسخة احتياطية' if language == 'ar' else 'Notify when backup is created' }}</small>
                                </div>
                            </div>

                            <!-- Advanced Notification Settings -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-sliders"></i>
                                                {{ 'إعدادات متقدمة' if language == 'ar' else 'Advanced Settings' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'موضع الإشعارات' if language == 'ar' else 'Notification Position' }}</label>
                                                    <select class="form-select" name="notification_position" id="notification_position">
                                                        <option value="top-right" {{ 'selected' if settings.get('notification_position', 'top-right') == 'top-right' }}>{{ 'أعلى يمين' if language == 'ar' else 'Top Right' }}</option>
                                                        <option value="top-left" {{ 'selected' if settings.get('notification_position') == 'top-left' }}>{{ 'أعلى يسار' if language == 'ar' else 'Top Left' }}</option>
                                                        <option value="bottom-right" {{ 'selected' if settings.get('notification_position') == 'bottom-right' }}>{{ 'أسفل يمين' if language == 'ar' else 'Bottom Right' }}</option>
                                                        <option value="bottom-left" {{ 'selected' if settings.get('notification_position') == 'bottom-left' }}>{{ 'أسفل يسار' if language == 'ar' else 'Bottom Left' }}</option>
                                                        <option value="top-center" {{ 'selected' if settings.get('notification_position') == 'top-center' }}>{{ 'أعلى وسط' if language == 'ar' else 'Top Center' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'حد المخزون المنخفض' if language == 'ar' else 'Low Stock Threshold' }}</label>
                                                    <input type="number" class="form-control" name="low_stock_threshold"
                                                           value="{{ settings.get('low_stock_threshold', '10') }}" min="1" max="100">
                                                    <small class="text-muted">{{ 'عدد القطع التي تؤدي لتنبيه نفاد المخزون' if language == 'ar' else 'Number of items that trigger low stock alert' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="desktop_notifications_enabled"
                                                               id="desktop_notifications_enabled" {{ 'checked' if settings.get('desktop_notifications_enabled') == 'true' }}>
                                                        <label class="form-check-label" for="desktop_notifications_enabled">
                                                            <i class="bi bi-display text-info me-2"></i>
                                                            {{ 'إشعارات سطح المكتب' if language == 'ar' else 'Desktop Notifications' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'إشعارات خارج المتصفح' if language == 'ar' else 'Browser notifications' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="email_notifications_enabled"
                                                               id="email_notifications_enabled" {{ 'checked' if settings.get('email_notifications_enabled') == 'true' }}>
                                                        <label class="form-check-label" for="email_notifications_enabled">
                                                            <i class="bi bi-envelope text-warning me-2"></i>
                                                            {{ 'إشعارات البريد الإلكتروني' if language == 'ar' else 'Email Notifications' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'إرسال إشعارات عبر البريد' if language == 'ar' else 'Send notifications via email' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Notifications Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-bell-fill"></i>
                                                {{ 'اختبار الإشعارات' if language == 'ar' else 'Test Notifications' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted mb-3">
                                                {{ 'انقر على الأزرار أدناه لاختبار الإشعارات المختلفة' if language == 'ar' else 'Click the buttons below to test different notifications' }}
                                            </p>
                                            <div class="row">
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-success btn-sm w-100" onclick="testNotification('success', '{{ \"تم إتمام البيع بنجاح\" if language == \"ar\" else \"Sale completed successfully\" }}')">
                                                        <i class="bi bi-check-circle"></i> {{ 'نجاح' if language == 'ar' else 'Success' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-info btn-sm w-100" onclick="testNotification('info', '{{ \"معلومات عامة\" if language == \"ar\" else \"General information\" }}')">
                                                        <i class="bi bi-info-circle"></i> {{ 'معلومات' if language == 'ar' else 'Info' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-warning btn-sm w-100" onclick="testNotification('warning', '{{ \"تحذير: المخزون منخفض\" if language == \"ar\" else \"Warning: Low stock\" }}')">
                                                        <i class="bi bi-exclamation-triangle"></i> {{ 'تحذير' if language == 'ar' else 'Warning' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-danger btn-sm w-100" onclick="testNotification('error', '{{ \"حدث خطأ في النظام\" if language == \"ar\" else \"System error occurred\" }}')">
                                                        <i class="bi bi-x-circle"></i> {{ 'خطأ' if language == 'ar' else 'Error' }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i>
                                    {{ 'حفظ إعدادات الإشعارات' if language == 'ar' else 'Save Notification Settings' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="tab-pane fade" id="security">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-shield-check"></i>
                            {{ 'إعدادات الأمان' if language == 'ar' else 'Security Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='security') }}">
                            <!-- Authentication Settings -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="bi bi-key"></i>
                                                {{ 'إعدادات المصادقة' if language == 'ar' else 'Authentication Settings' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="force_password_change"
                                                               id="force_password_change" {{ 'checked' if settings.get('force_password_change') == 'true' }}>
                                                        <label class="form-check-label" for="force_password_change">
                                                            {{ 'إجبار تغيير كلمة المرور' if language == 'ar' else 'Force Password Change' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'إجبار المستخدمين على تغيير كلمة المرور عند أول تسجيل دخول' if language == 'ar' else 'Force users to change password on first login' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'مدة انتهاء الجلسة (دقائق)' if language == 'ar' else 'Session Timeout (minutes)' }}</label>
                                                    <select class="form-select" name="session_timeout" id="session_timeout">
                                                        <option value="30" {{ 'selected' if settings.get('session_timeout') == '30' }}>30 {{ 'دقيقة' if language == 'ar' else 'minutes' }}</option>
                                                        <option value="60" {{ 'selected' if settings.get('session_timeout') == '60' }}>1 {{ 'ساعة' if language == 'ar' else 'hour' }}</option>
                                                        <option value="120" {{ 'selected' if settings.get('session_timeout') == '120' }}>2 {{ 'ساعة' if language == 'ar' else 'hours' }}</option>
                                                        <option value="480" {{ 'selected' if settings.get('session_timeout', '480') == '480' }}>8 {{ 'ساعات' if language == 'ar' else 'hours' }}</option>
                                                        <option value="1440" {{ 'selected' if settings.get('session_timeout') == '1440' }}>24 {{ 'ساعة' if language == 'ar' else 'hours' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="two_factor_enabled"
                                                               id="two_factor_enabled" {{ 'checked' if settings.get('two_factor_enabled') == 'true' }}>
                                                        <label class="form-check-label" for="two_factor_enabled">
                                                            {{ 'المصادقة الثنائية' if language == 'ar' else 'Two-Factor Authentication' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'تفعيل المصادقة الثنائية للحسابات' if language == 'ar' else 'Enable 2FA for user accounts' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'عدد محاولات تسجيل الدخول' if language == 'ar' else 'Max Login Attempts' }}</label>
                                                    <select class="form-select" name="max_login_attempts" id="max_login_attempts">
                                                        <option value="3" {{ 'selected' if settings.get('max_login_attempts') == '3' }}>3 {{ 'محاولات' if language == 'ar' else 'attempts' }}</option>
                                                        <option value="5" {{ 'selected' if settings.get('max_login_attempts', '5') == '5' }}>5 {{ 'محاولات' if language == 'ar' else 'attempts' }}</option>
                                                        <option value="10" {{ 'selected' if settings.get('max_login_attempts') == '10' }}>10 {{ 'محاولات' if language == 'ar' else 'attempts' }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Password Policy -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="bi bi-lock"></i>
                                                {{ 'سياسة كلمات المرور' if language == 'ar' else 'Password Policy' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'الحد الأدنى لطول كلمة المرور' if language == 'ar' else 'Minimum Password Length' }}</label>
                                                    <select class="form-select" name="min_password_length" id="min_password_length">
                                                        <option value="6" {{ 'selected' if settings.get('min_password_length') == '6' }}>6 {{ 'أحرف' if language == 'ar' else 'characters' }}</option>
                                                        <option value="8" {{ 'selected' if settings.get('min_password_length', '8') == '8' }}>8 {{ 'أحرف' if language == 'ar' else 'characters' }}</option>
                                                        <option value="12" {{ 'selected' if settings.get('min_password_length') == '12' }}>12 {{ 'حرف' if language == 'ar' else 'characters' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="require_uppercase"
                                                               id="require_uppercase" {{ 'checked' if settings.get('require_uppercase') == 'true' }}>
                                                        <label class="form-check-label" for="require_uppercase">
                                                            {{ 'يتطلب أحرف كبيرة' if language == 'ar' else 'Require Uppercase' }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="require_numbers"
                                                               id="require_numbers" {{ 'checked' if settings.get('require_numbers') == 'true' }}>
                                                        <label class="form-check-label" for="require_numbers">
                                                            {{ 'يتطلب أرقام' if language == 'ar' else 'Require Numbers' }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="require_special_chars"
                                                               id="require_special_chars" {{ 'checked' if settings.get('require_special_chars') == 'true' }}>
                                                        <label class="form-check-label" for="require_special_chars">
                                                            {{ 'يتطلب رموز خاصة' if language == 'ar' else 'Require Special Characters' }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'انتهاء صلاحية كلمة المرور (أيام)' if language == 'ar' else 'Password Expiry (days)' }}</label>
                                                    <select class="form-select" name="password_expiry_days" id="password_expiry_days">
                                                        <option value="0" {{ 'selected' if settings.get('password_expiry_days', '0') == '0' }}>{{ 'لا تنتهي' if language == 'ar' else 'Never' }}</option>
                                                        <option value="30" {{ 'selected' if settings.get('password_expiry_days') == '30' }}>30 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                        <option value="60" {{ 'selected' if settings.get('password_expiry_days') == '60' }}>60 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                        <option value="90" {{ 'selected' if settings.get('password_expiry_days') == '90' }}>90 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Security -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-shield-exclamation"></i>
                                                {{ 'أمان النظام' if language == 'ar' else 'System Security' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_audit_log"
                                                               id="enable_audit_log" {{ 'checked' if settings.get('enable_audit_log') == 'true' }}>
                                                        <label class="form-check-label" for="enable_audit_log">
                                                            {{ 'تفعيل سجل المراجعة' if language == 'ar' else 'Enable Audit Log' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'تسجيل جميع العمليات المهمة' if language == 'ar' else 'Log all important operations' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_ip_whitelist"
                                                               id="enable_ip_whitelist" {{ 'checked' if settings.get('enable_ip_whitelist') == 'true' }}>
                                                        <label class="form-check-label" for="enable_ip_whitelist">
                                                            {{ 'تفعيل القائمة البيضاء للـ IP' if language == 'ar' else 'Enable IP Whitelist' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'السماح لعناوين IP محددة فقط' if language == 'ar' else 'Allow only specific IP addresses' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_ssl_only"
                                                               id="enable_ssl_only" {{ 'checked' if settings.get('enable_ssl_only') == 'true' }}>
                                                        <label class="form-check-label" for="enable_ssl_only">
                                                            {{ 'HTTPS فقط' if language == 'ar' else 'HTTPS Only' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'إجبار استخدام HTTPS' if language == 'ar' else 'Force HTTPS usage' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_rate_limiting"
                                                               id="enable_rate_limiting" {{ 'checked' if settings.get('enable_rate_limiting') == 'true' }}>
                                                        <label class="form-check-label" for="enable_rate_limiting">
                                                            {{ 'تحديد معدل الطلبات' if language == 'ar' else 'Rate Limiting' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'منع الطلبات المفرطة' if language == 'ar' else 'Prevent excessive requests' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Protection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-database-lock"></i>
                                                {{ 'حماية البيانات' if language == 'ar' else 'Data Protection' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_data_encryption"
                                                               id="enable_data_encryption" {{ 'checked' if settings.get('enable_data_encryption') == 'true' }}>
                                                        <label class="form-check-label" for="enable_data_encryption">
                                                            {{ 'تشفير البيانات الحساسة' if language == 'ar' else 'Encrypt Sensitive Data' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'تشفير البيانات المالية والشخصية' if language == 'ar' else 'Encrypt financial and personal data' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_backup_encryption"
                                                               id="enable_backup_encryption" {{ 'checked' if settings.get('enable_backup_encryption') == 'true' }}>
                                                        <label class="form-check-label" for="enable_backup_encryption">
                                                            {{ 'تشفير النسخ الاحتياطية' if language == 'ar' else 'Encrypt Backups' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'تشفير ملفات النسخ الاحتياطية' if language == 'ar' else 'Encrypt backup files' }}</small>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'مدة الاحتفاظ بالسجلات (أشهر)' if language == 'ar' else 'Log Retention Period (months)' }}</label>
                                                    <select class="form-select" name="log_retention_months" id="log_retention_months">
                                                        <option value="3" {{ 'selected' if settings.get('log_retention_months') == '3' }}>3 {{ 'أشهر' if language == 'ar' else 'months' }}</option>
                                                        <option value="6" {{ 'selected' if settings.get('log_retention_months', '6') == '6' }}>6 {{ 'أشهر' if language == 'ar' else 'months' }}</option>
                                                        <option value="12" {{ 'selected' if settings.get('log_retention_months') == '12' }}>12 {{ 'شهر' if language == 'ar' else 'months' }}</option>
                                                        <option value="24" {{ 'selected' if settings.get('log_retention_months') == '24' }}>24 {{ 'شهر' if language == 'ar' else 'months' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="enable_gdpr_compliance"
                                                               id="enable_gdpr_compliance" {{ 'checked' if settings.get('enable_gdpr_compliance') == 'true' }}>
                                                        <label class="form-check-label" for="enable_gdpr_compliance">
                                                            {{ 'الامتثال لحماية البيانات' if language == 'ar' else 'GDPR Compliance' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted ms-4">{{ 'تفعيل ميزات حماية البيانات الشخصية' if language == 'ar' else 'Enable personal data protection features' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-shield-check"></i>
                                    {{ 'حفظ إعدادات الأمان' if language == 'ar' else 'Save Security Settings' }}
                                </button>
                                <button type="button" class="btn btn-outline-info ms-2" onclick="testSecuritySettings()">
                                    <i class="bi bi-shield-exclamation"></i>
                                    {{ 'اختبار الأمان' if language == 'ar' else 'Test Security' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sound Settings -->
            <div class="tab-pane fade" id="sounds">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-volume-up"></i>
                            {{ 'إعدادات الأصوات' if language == 'ar' else 'Sound Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='sounds') }}">
                            <!-- Master Sound Control -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="sounds_enabled"
                                                               id="sounds_enabled" {{ 'checked' if settings.get('sounds_enabled') == 'true' }}>
                                                        <label class="form-check-label fw-bold" for="sounds_enabled">
                                                            {{ 'تفعيل الأصوات' if language == 'ar' else 'Enable Sounds' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">
                                                        {{ 'تفعيل أو إلغاء جميع الأصوات في النظام' if language == 'ar' else 'Enable or disable all system sounds' }}
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">{{ 'مستوى الصوت' if language == 'ar' else 'Volume Level' }}</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range me-3" name="sound_volume"
                                                               id="sound_volume" min="0" max="1" step="0.1"
                                                               value="{{ settings.get('sound_volume', '0.5') }}">
                                                        <span id="volume_display" class="badge bg-primary">{{ (settings.get('sound_volume', '0.5')|float * 100)|int }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Individual Sound Settings -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="sale_sound_enabled"
                                               id="sale_sound_enabled" {{ 'checked' if settings.get('sale_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="sale_sound_enabled">
                                            <i class="bi bi-cash-coin text-success me-2"></i>
                                            {{ 'صوت إتمام البيع' if language == 'ar' else 'Sale Completion Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند إتمام عملية بيع' if language == 'ar' else 'Plays when a sale is completed' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="payment_sound_enabled"
                                               id="payment_sound_enabled" {{ 'checked' if settings.get('payment_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="payment_sound_enabled">
                                            <i class="bi bi-credit-card text-info me-2"></i>
                                            {{ 'صوت استلام الدفع' if language == 'ar' else 'Payment Received Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند استلام الدفع' if language == 'ar' else 'Plays when payment is received' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="error_sound_enabled"
                                               id="error_sound_enabled" {{ 'checked' if settings.get('error_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="error_sound_enabled">
                                            <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                                            {{ 'صوت الأخطاء' if language == 'ar' else 'Error Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند حدوث خطأ' if language == 'ar' else 'Plays when an error occurs' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="notification_sound_enabled"
                                               id="notification_sound_enabled" {{ 'checked' if settings.get('notification_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="notification_sound_enabled">
                                            <i class="bi bi-bell text-warning me-2"></i>
                                            {{ 'صوت الإشعارات' if language == 'ar' else 'Notification Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله مع الإشعارات' if language == 'ar' else 'Plays with notifications' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="low_stock_sound_enabled"
                                               id="low_stock_sound_enabled" {{ 'checked' if settings.get('low_stock_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="low_stock_sound_enabled">
                                            <i class="bi bi-box text-danger me-2"></i>
                                            {{ 'صوت تنبيه نفاد المخزون' if language == 'ar' else 'Low Stock Alert Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند انخفاض المخزون' if language == 'ar' else 'Plays when stock is low' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="button_click_sound_enabled"
                                               id="button_click_sound_enabled" {{ 'checked' if settings.get('button_click_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="button_click_sound_enabled">
                                            <i class="bi bi-mouse text-secondary me-2"></i>
                                            {{ 'صوت النقر على الأزرار' if language == 'ar' else 'Button Click Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند النقر على الأزرار' if language == 'ar' else 'Plays when buttons are clicked' }}</small>
                                </div>
                            </div>

                            <!-- Test Sounds Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-play-circle"></i>
                                                {{ 'اختبار الأصوات' if language == 'ar' else 'Test Sounds' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted mb-3">
                                                {{ 'انقر على الأزرار أدناه لاختبار الأصوات المختلفة' if language == 'ar' else 'Click the buttons below to test different sounds' }}
                                            </p>
                                            <div class="row">
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-success btn-sm w-100" onclick="window.playSound('sale')">
                                                        <i class="bi bi-cash-coin"></i> {{ 'بيع' if language == 'ar' else 'Sale' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-info btn-sm w-100" onclick="window.playSound('payment')">
                                                        <i class="bi bi-credit-card"></i> {{ 'دفع' if language == 'ar' else 'Payment' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-danger btn-sm w-100" onclick="window.playSound('error')">
                                                        <i class="bi bi-exclamation-triangle"></i> {{ 'خطأ' if language == 'ar' else 'Error' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-warning btn-sm w-100" onclick="window.playSound('notification')">
                                                        <i class="bi bi-bell"></i> {{ 'إشعار' if language == 'ar' else 'Notification' }}
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-6 mb-2">
                                                    <button type="button" class="btn btn-secondary btn-sm w-100" onclick="window.testSounds()">
                                                        <i class="bi bi-play-circle"></i> {{ 'اختبار جميع الأصوات' if language == 'ar' else 'Test All Sounds' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-6 mb-2">
                                                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="window.playSound('lowStock')">
                                                        <i class="bi bi-box"></i> {{ 'تنبيه مخزون' if language == 'ar' else 'Low Stock' }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i>
                                    {{ 'حفظ إعدادات الأصوات' if language == 'ar' else 'Save Sound Settings' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Backup Settings -->
            <div class="tab-pane fade" id="backup">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-cloud-download"></i>
                            {{ 'النسخ الاحتياطي والاستعادة' if language == 'ar' else 'Backup & Restore' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Quick Backup Actions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="bi bi-download"></i>
                                            {{ 'إنشاء نسخة احتياطية' if language == 'ar' else 'Create Backup' }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted">{{ 'إنشاء نسخة احتياطية فورية من النظام' if language == 'ar' else 'Create an immediate system backup' }}</p>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="backup_type" id="backup_full" value="full" checked>
                                                <label class="form-check-label" for="backup_full">
                                                    {{ 'نسخة كاملة' if language == 'ar' else 'Full Backup' }}
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="backup_type" id="backup_database" value="database">
                                                <label class="form-check-label" for="backup_database">
                                                    {{ 'قاعدة البيانات فقط' if language == 'ar' else 'Database Only' }}
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="backup_type" id="backup_files" value="files">
                                                <label class="form-check-label" for="backup_files">
                                                    {{ 'الملفات فقط' if language == 'ar' else 'Files Only' }}
                                                </label>
                                            </div>
                                        </div>

                                        <button type="button" class="btn btn-success w-100" onclick="createManualBackup()">
                                            <i class="bi bi-download"></i>
                                            {{ 'إنشاء نسخة احتياطية الآن' if language == 'ar' else 'Create Backup Now' }}
                                        </button>

                                        <div id="backup-progress" class="mt-3" style="display: none;">
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted">{{ 'جاري إنشاء النسخة الاحتياطية...' if language == 'ar' else 'Creating backup...' }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="bi bi-upload"></i>
                                            {{ 'استعادة نسخة احتياطية' if language == 'ar' else 'Restore Backup' }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            {{ 'تحذير: ستؤدي الاستعادة إلى استبدال البيانات الحالية' if language == 'ar' else 'Warning: Restore will replace current data' }}
                                        </div>

                                        <div class="mb-3">
                                            <label for="restore_file" class="form-label">{{ 'اختر ملف النسخة الاحتياطية' if language == 'ar' else 'Select Backup File' }}</label>
                                            <input type="file" class="form-control" id="restore_file" accept=".zip,.sql,.db">
                                        </div>

                                        <button type="button" class="btn btn-warning w-100" onclick="restoreBackup()" disabled>
                                            <i class="bi bi-upload"></i>
                                            {{ 'استعادة النسخة الاحتياطية' if language == 'ar' else 'Restore Backup' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Automated Backup Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="bi bi-clock"></i>
                                            {{ 'إعدادات النسخ الاحتياطي التلقائي' if language == 'ar' else 'Automated Backup Settings' }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='backup') }}">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="auto_backup_enabled"
                                                               id="auto_backup_enabled" {{ 'checked' if settings.get('auto_backup_enabled') == 'true' }}>
                                                        <label class="form-check-label" for="auto_backup_enabled">
                                                            {{ 'تفعيل النسخ الاحتياطي التلقائي' if language == 'ar' else 'Enable Automated Backup' }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'تكرار النسخ' if language == 'ar' else 'Backup Frequency' }}</label>
                                                    <select class="form-select" name="backup_frequency" id="backup_frequency">
                                                        <option value="daily" {{ 'selected' if settings.get('backup_frequency', 'daily') == 'daily' }}>{{ 'يومي' if language == 'ar' else 'Daily' }}</option>
                                                        <option value="weekly" {{ 'selected' if settings.get('backup_frequency') == 'weekly' }}>{{ 'أسبوعي' if language == 'ar' else 'Weekly' }}</option>
                                                        <option value="monthly" {{ 'selected' if settings.get('backup_frequency') == 'monthly' }}>{{ 'شهري' if language == 'ar' else 'Monthly' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'وقت النسخ' if language == 'ar' else 'Backup Time' }}</label>
                                                    <input type="time" class="form-control" name="backup_time" id="backup_time"
                                                           value="{{ settings.get('backup_time', '02:00') }}">
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">{{ 'الاحتفاظ بالنسخ (أيام)' if language == 'ar' else 'Retention Period (days)' }}</label>
                                                    <select class="form-select" name="backup_retention_days" id="backup_retention_days">
                                                        <option value="7" {{ 'selected' if settings.get('backup_retention_days') == '7' }}>7 {{ 'أيام' if language == 'ar' else 'days' }}</option>
                                                        <option value="14" {{ 'selected' if settings.get('backup_retention_days') == '14' }}>14 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                        <option value="30" {{ 'selected' if settings.get('backup_retention_days', '30') == '30' }}>30 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                        <option value="60" {{ 'selected' if settings.get('backup_retention_days') == '60' }}>60 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                        <option value="90" {{ 'selected' if settings.get('backup_retention_days') == '90' }}>90 {{ 'يوم' if language == 'ar' else 'days' }}</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="backup_compression"
                                                               id="backup_compression" {{ 'checked' if settings.get('backup_compression') == 'true' }}>
                                                        <label class="form-check-label" for="backup_compression">
                                                            {{ 'ضغط النسخ الاحتياطية' if language == 'ar' else 'Compress Backups' }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="backup_cloud_storage"
                                                               id="backup_cloud_storage" {{ 'checked' if settings.get('backup_cloud_storage') == 'true' }}>
                                                        <label class="form-check-label" for="backup_cloud_storage">
                                                            {{ 'رفع للتخزين السحابي' if language == 'ar' else 'Upload to Cloud Storage' }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <button type="submit" class="btn btn-info">
                                                <i class="bi bi-check-circle"></i>
                                                {{ 'حفظ إعدادات النسخ الاحتياطي' if language == 'ar' else 'Save Backup Settings' }}
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Backup History -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-clock-history"></i>
                                            {{ 'سجل النسخ الاحتياطية' if language == 'ar' else 'Backup History' }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                                                        <th>{{ 'النوع' if language == 'ar' else 'Type' }}</th>
                                                        <th>{{ 'الحجم' if language == 'ar' else 'Size' }}</th>
                                                        <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                                        <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="backup-history">
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted">
                                                            {{ 'جاري تحميل سجل النسخ الاحتياطية...' if language == 'ar' else 'Loading backup history...' }}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other tabs would continue here... -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Handle tab switching
document.querySelectorAll('[data-bs-toggle="pill"]').forEach(function(tab) {
    tab.addEventListener('shown.bs.tab', function(e) {
        // Update URL hash
        window.location.hash = e.target.getAttribute('href');
    });
});

// Set active tab from URL hash
document.addEventListener('DOMContentLoaded', function() {
    const hash = window.location.hash;
    if (hash) {
        const tab = document.querySelector(`[href="${hash}"]`);
        if (tab) {
            const tabInstance = new bootstrap.Tab(tab);
            tabInstance.show();
        }
    }

    // Sound volume slider
    const volumeSlider = document.getElementById('sound_volume');
    const volumeDisplay = document.getElementById('volume_display');

    if (volumeSlider && volumeDisplay) {
        volumeSlider.addEventListener('input', function() {
            const volume = Math.round(this.value * 100);
            volumeDisplay.textContent = volume + '%';

            // Update sound manager volume if available
            if (window.soundManager) {
                window.soundManager.setVolume(this.value);
            }
        });
    }

    // Master sound toggle
    const soundsEnabled = document.getElementById('sounds_enabled');
    if (soundsEnabled) {
        soundsEnabled.addEventListener('change', function() {
            if (window.soundManager) {
                window.soundManager.setEnabled(this.checked);
            }

            // Enable/disable all other sound checkboxes
            const soundCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_sound_enabled"]');
            soundCheckboxes.forEach(checkbox => {
                checkbox.disabled = !this.checked;
            });
        });

        // Initial state
        const soundCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_sound_enabled"]');
        soundCheckboxes.forEach(checkbox => {
            checkbox.disabled = !soundsEnabled.checked;
        });
    }

    // Master notification toggle
    const notificationsEnabled = document.getElementById('notifications_enabled');
    if (notificationsEnabled) {
        notificationsEnabled.addEventListener('change', function() {
            // Enable/disable all other notification checkboxes
            const notificationCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_notifications_enabled"]');
            notificationCheckboxes.forEach(checkbox => {
                checkbox.disabled = !this.checked;
            });
        });

        // Initial state
        const notificationCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_notifications_enabled"]');
        notificationCheckboxes.forEach(checkbox => {
            checkbox.disabled = !notificationsEnabled.checked;
        });
    }
});

// Test notification function
function testNotification(type, message) {
    if (window.showAlert) {
        window.showAlert(message, type);
    } else {
        // Fallback alert
        alert(`${type.toUpperCase()}: ${message}`);
    }

    // Also test desktop notification if enabled
    const desktopEnabled = document.getElementById('desktop_notifications_enabled');
    if (desktopEnabled && desktopEnabled.checked && 'Notification' in window) {
        if (Notification.permission === 'granted') {
            new Notification('Qatar POS System', {
                body: message,
                icon: '/static/images/logo.png'
            });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification('Qatar POS System', {
                        body: message,
                        icon: '/static/images/logo.png'
                    });
                }
            });
        }
    }
}

// Security Settings Functions
function testSecuritySettings() {
    const tests = [
        { name: 'Password Policy', test: testPasswordPolicy },
        { name: 'Session Security', test: testSessionSecurity },
        { name: 'System Security', test: testSystemSecurity }
    ];

    let results = [];

    tests.forEach(test => {
        try {
            const result = test.test();
            results.push({ name: test.name, status: result ? 'passed' : 'failed', result });
        } catch (error) {
            results.push({ name: test.name, status: 'error', error: error.message });
        }
    });

    showSecurityTestResults(results);
}

function testPasswordPolicy() {
    const minLength = parseInt(document.getElementById('min_password_length').value);
    const requireUppercase = document.getElementById('require_uppercase').checked;
    const requireNumbers = document.getElementById('require_numbers').checked;
    const requireSpecialChars = document.getElementById('require_special_chars').checked;

    return minLength >= 8 && (requireUppercase || requireNumbers || requireSpecialChars);
}

function testSessionSecurity() {
    const sessionTimeout = parseInt(document.getElementById('session_timeout').value);
    const maxLoginAttempts = parseInt(document.getElementById('max_login_attempts').value);

    return sessionTimeout <= 480 && maxLoginAttempts <= 5;
}

function testSystemSecurity() {
    const auditLog = document.getElementById('enable_audit_log').checked;
    const rateLimiting = document.getElementById('enable_rate_limiting').checked;

    return auditLog && rateLimiting;
}

function showSecurityTestResults(results) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ 'نتائج اختبار الأمان' if language == 'ar' else 'Security Test Results' }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${results.map(result => `
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi ${result.status === 'passed' ? 'bi-check-circle text-success' : 'bi-x-circle text-danger'} me-2"></i>
                            <span>${result.name}: ${result.status === 'passed' ? 'مُجتاز' : 'فاشل'}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ 'إغلاق' if language == 'ar' else 'Close' }}</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// Backup Functions
async function createManualBackup() {
    const backupType = document.querySelector('input[name="backup_type"]:checked').value;
    const progressDiv = document.getElementById('backup-progress');
    const progressBar = progressDiv.querySelector('.progress-bar');

    // Show progress
    progressDiv.style.display = 'block';
    progressBar.style.width = '10%';

    try {
        const response = await fetch('/api/backup/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: backupType,
                manual: true
            })
        });

        progressBar.style.width = '50%';

        const result = await response.json();

        progressBar.style.width = '100%';

        if (result.success) {
            setTimeout(() => {
                progressDiv.style.display = 'none';
                progressBar.style.width = '0%';

                showAlert('{{ "تم إنشاء النسخة الاحتياطية بنجاح" if language == "ar" else "Backup created successfully" }}', 'success');
                loadBackupHistory();
            }, 1000);
        } else {
            throw new Error(result.error);
        }

    } catch (error) {
        progressDiv.style.display = 'none';
        progressBar.style.width = '0%';
        showAlert(error.message || '{{ "حدث خطأ أثناء إنشاء النسخة الاحتياطية" if language == "ar" else "Error creating backup" }}', 'error');
    }
}

function restoreBackup() {
    const fileInput = document.getElementById('restore_file');
    const file = fileInput.files[0];

    if (!file) {
        showAlert('{{ "يرجى اختيار ملف النسخة الاحتياطية" if language == "ar" else "Please select a backup file" }}', 'warning');
        return;
    }

    if (confirm('{{ "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ ستفقد جميع البيانات الحالية!" if language == "ar" else "Are you sure you want to restore this backup? You will lose all current data!" }}')) {
        const formData = new FormData();
        formData.append('backup_file', file);

        fetch('/api/backup/restore', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('{{ "تم استعادة النسخة الاحتياطية بنجاح" if language == "ar" else "Backup restored successfully" }}', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showAlert(result.error || '{{ "حدث خطأ أثناء استعادة النسخة الاحتياطية" if language == "ar" else "Error restoring backup" }}', 'error');
            }
        })
        .catch(error => {
            showAlert('{{ "حدث خطأ أثناء استعادة النسخة الاحتياطية" if language == "ar" else "Error restoring backup" }}', 'error');
        });
    }
}

async function loadBackupHistory() {
    try {
        const response = await fetch('/api/backup/history');
        const result = await response.json();

        const tbody = document.getElementById('backup-history');

        if (result.success && result.backups.length > 0) {
            tbody.innerHTML = result.backups.map(backup => `
                <tr>
                    <td>${new Date(backup.created_at).toLocaleString()}</td>
                    <td>
                        <span class="badge bg-${backup.type === 'full' ? 'primary' : backup.type === 'database' ? 'info' : 'secondary'}">
                            ${backup.type}
                        </span>
                    </td>
                    <td>${formatFileSize(backup.size)}</td>
                    <td>
                        <span class="badge bg-${backup.status === 'completed' ? 'success' : backup.status === 'failed' ? 'danger' : 'warning'}">
                            ${backup.status}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="downloadBackup('${backup.id}')" title="{{ 'تحميل' if language == 'ar' else 'Download' }}">
                                <i class="bi bi-download"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteBackup('${backup.id}')" title="{{ 'حذف' if language == 'ar' else 'Delete' }}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        } else {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted">
                        {{ 'لا توجد نسخ احتياطية' if language == 'ar' else 'No backups found' }}
                    </td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('Error loading backup history:', error);
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function downloadBackup(backupId) {
    window.open(`/api/backup/download/${backupId}`, '_blank');
}

function deleteBackup(backupId) {
    if (confirm('{{ "هل أنت متأكد من حذف هذه النسخة الاحتياطية؟" if language == "ar" else "Are you sure you want to delete this backup?" }}')) {
        fetch(`/api/backup/delete/${backupId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('{{ "تم حذف النسخة الاحتياطية" if language == "ar" else "Backup deleted" }}', 'success');
                loadBackupHistory();
            } else {
                showAlert(result.error || '{{ "حدث خطأ أثناء حذف النسخة الاحتياطية" if language == "ar" else "Error deleting backup" }}', 'error');
            }
        })
        .catch(error => {
            showAlert('{{ "حدث خطأ أثناء حذف النسخة الاحتياطية" if language == "ar" else "Error deleting backup" }}', 'error');
        });
    }
}

// Enable/disable restore button based on file selection
document.addEventListener('DOMContentLoaded', function() {
    const restoreFileInput = document.getElementById('restore_file');
    if (restoreFileInput) {
        restoreFileInput.addEventListener('change', function() {
            const restoreBtn = document.querySelector('button[onclick="restoreBackup()"]');
            if (restoreBtn) {
                restoreBtn.disabled = !this.files.length;
            }
        });
    }

    // Load backup history
    loadBackupHistory();
});
</script>
{% endblock %}
