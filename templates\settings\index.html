{% extends "base.html" %}

{% block title %}
{{ 'إعدادات النظام - نظام نقاط البيع القطري' if language == 'ar' else 'System Settings - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-gear"></i>
            {{ 'إعدادات النظام' if language == 'ar' else 'System Settings' }}
        </h1>
    </div>
</div>

<div class="row">
    <!-- Settings Navigation -->
    <div class="col-lg-3 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {{ 'فئات الإعدادات' if language == 'ar' else 'Settings Categories' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="bi bi-gear"></i>
                    {{ 'إعدادات عامة' if language == 'ar' else 'General Settings' }}
                </a>
                <a href="#company" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-building"></i>
                    {{ 'معلومات الشركة' if language == 'ar' else 'Company Information' }}
                </a>
                <a href="#pos" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-cash-register"></i>
                    {{ 'إعدادات نقطة البيع' if language == 'ar' else 'POS Settings' }}
                </a>
                <a href="#tax" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-receipt"></i>
                    {{ 'الإعدادات الضريبية' if language == 'ar' else 'Tax Settings' }}
                </a>
                <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-bell"></i>
                    {{ 'إعدادات الإشعارات' if language == 'ar' else 'Notification Settings' }}
                </a>
                <a href="#sounds" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-volume-up"></i>
                    {{ 'إعدادات الأصوات' if language == 'ar' else 'Sound Settings' }}
                </a>
                <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-cloud-download"></i>
                    {{ 'النسخ الاحتياطي' if language == 'ar' else 'Backup & Restore' }}
                </a>
                <a href="#security" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="bi bi-shield-check"></i>
                    {{ 'إعدادات الأمان' if language == 'ar' else 'Security Settings' }}
                </a>
            </div>
        </div>
    </div>

    <!-- Settings Content -->
    <div class="col-lg-9">
        <div class="tab-content">
            <!-- General Settings -->
            <div class="tab-pane fade show active" id="general">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-gear"></i>
                            {{ 'الإعدادات العامة' if language == 'ar' else 'General Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='system') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم النظام' if language == 'ar' else 'System Name' }}</label>
                                    <input type="text" class="form-control" name="system_name" 
                                           value="{{ settings.system_name or 'Qatar POS System' }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اللغة الافتراضية' if language == 'ar' else 'Default Language' }}</label>
                                    <select class="form-select" name="default_language">
                                        <option value="ar" {{ 'selected' if settings.default_language == 'ar' }}>
                                            {{ 'العربية' if language == 'ar' else 'Arabic' }}
                                        </option>
                                        <option value="en" {{ 'selected' if settings.default_language == 'en' }}>
                                            {{ 'الإنجليزية' if language == 'ar' else 'English' }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العملة' if language == 'ar' else 'Currency' }}</label>
                                    <select class="form-select" name="currency">
                                        <option value="QAR" {{ 'selected' if settings.currency == 'QAR' }}>
                                            {{ 'ريال قطري (QAR)' if language == 'ar' else 'Qatari Riyal (QAR)' }}
                                        </option>
                                        <option value="USD" {{ 'selected' if settings.currency == 'USD' }}>
                                            {{ 'دولار أمريكي (USD)' if language == 'ar' else 'US Dollar (USD)' }}
                                        </option>
                                        <option value="EUR" {{ 'selected' if settings.currency == 'EUR' }}>
                                            {{ 'يورو (EUR)' if language == 'ar' else 'Euro (EUR)' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'المنطقة الزمنية' if language == 'ar' else 'Timezone' }}</label>
                                    <select class="form-select" name="timezone">
                                        <option value="Asia/Qatar" {{ 'selected' if settings.timezone == 'Asia/Qatar' }}>
                                            {{ 'قطر (Asia/Qatar)' if language == 'ar' else 'Qatar (Asia/Qatar)' }}
                                        </option>
                                        <option value="UTC" {{ 'selected' if settings.timezone == 'UTC' }}>UTC</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'تنسيق التاريخ' if language == 'ar' else 'Date Format' }}</label>
                                    <select class="form-select" name="date_format">
                                        <option value="DD/MM/YYYY" {{ 'selected' if settings.date_format == 'DD/MM/YYYY' }}>DD/MM/YYYY</option>
                                        <option value="MM/DD/YYYY" {{ 'selected' if settings.date_format == 'MM/DD/YYYY' }}>MM/DD/YYYY</option>
                                        <option value="YYYY-MM-DD" {{ 'selected' if settings.date_format == 'YYYY-MM-DD' }}>YYYY-MM-DD</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'تنسيق الوقت' if language == 'ar' else 'Time Format' }}</label>
                                    <select class="form-select" name="time_format">
                                        <option value="24" {{ 'selected' if settings.time_format == '24' }}>24 {{ 'ساعة' if language == 'ar' else 'Hour' }}</option>
                                        <option value="12" {{ 'selected' if settings.time_format == '12' }}>12 {{ 'ساعة' if language == 'ar' else 'Hour' }}</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="maintenance_mode" 
                                           {{ 'checked' if settings.maintenance_mode }}>
                                    <label class="form-check-label">
                                        {{ 'وضع الصيانة' if language == 'ar' else 'Maintenance Mode' }}
                                    </label>
                                    <div class="form-text">
                                        {{ 'تفعيل وضع الصيانة سيمنع المستخدمين من الوصول للنظام' if language == 'ar' else 'Enabling maintenance mode will prevent users from accessing the system' }}
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ الإعدادات' if language == 'ar' else 'Save Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="tab-pane fade" id="company">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-building"></i>
                            {{ 'معلومات الشركة' if language == 'ar' else 'Company Information' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='company') }}" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم الشركة (عربي)' if language == 'ar' else 'Company Name (Arabic)' }}</label>
                                    <input type="text" class="form-control" name="company_name_ar" 
                                           value="{{ settings.company_name_ar }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'اسم الشركة (إنجليزي)' if language == 'ar' else 'Company Name (English)' }}</label>
                                    <input type="text" class="form-control" name="company_name_en" 
                                           value="{{ settings.company_name_en }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'السجل التجاري' if language == 'ar' else 'Commercial Registration' }}</label>
                                    <input type="text" class="form-control" name="commercial_registration" 
                                           value="{{ settings.commercial_registration }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'الرقم الضريبي' if language == 'ar' else 'Tax Number' }}</label>
                                    <input type="text" class="form-control" name="tax_number" 
                                           value="{{ settings.tax_number }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}</label>
                                    <input type="tel" class="form-control" name="phone" 
                                           value="{{ settings.phone }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'البريد الإلكتروني' if language == 'ar' else 'Email Address' }}</label>
                                    <input type="email" class="form-control" name="email" 
                                           value="{{ settings.email }}">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العنوان (عربي)' if language == 'ar' else 'Address (Arabic)' }}</label>
                                    <textarea class="form-control" name="address_ar" rows="3">{{ settings.address_ar }}</textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'العنوان (إنجليزي)' if language == 'ar' else 'Address (English)' }}</label>
                                    <textarea class="form-control" name="address_en" rows="3">{{ settings.address_en }}</textarea>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'شعار الشركة' if language == 'ar' else 'Company Logo' }}</label>
                                    <input type="file" class="form-control" name="logo" accept="image/*">
                                    {% if settings.logo %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename='uploads/logos/' + settings.logo) }}" 
                                             alt="Company Logo" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'الموقع الإلكتروني' if language == 'ar' else 'Website' }}</label>
                                    <input type="url" class="form-control" name="website" 
                                           value="{{ settings.website }}">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ معلومات الشركة' if language == 'ar' else 'Save Company Information' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- POS Settings -->
            <div class="tab-pane fade" id="pos">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-cash-register"></i>
                            {{ 'إعدادات نقطة البيع' if language == 'ar' else 'POS Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='pos') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'طابعة الإيصالات' if language == 'ar' else 'Receipt Printer' }}</label>
                                    <select class="form-select" name="receipt_printer">
                                        <option value="">{{ 'بدون طابعة' if language == 'ar' else 'No Printer' }}</option>
                                        <option value="thermal" {{ 'selected' if settings.receipt_printer == 'thermal' }}>
                                            {{ 'طابعة حرارية' if language == 'ar' else 'Thermal Printer' }}
                                        </option>
                                        <option value="inkjet" {{ 'selected' if settings.receipt_printer == 'inkjet' }}>
                                            {{ 'طابعة نافثة للحبر' if language == 'ar' else 'Inkjet Printer' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'حجم الإيصال' if language == 'ar' else 'Receipt Size' }}</label>
                                    <select class="form-select" name="receipt_size">
                                        <option value="80mm" {{ 'selected' if settings.receipt_size == '80mm' }}>80mm</option>
                                        <option value="58mm" {{ 'selected' if settings.receipt_size == '58mm' }}>58mm</option>
                                        <option value="A4" {{ 'selected' if settings.receipt_size == 'A4' }}>A4</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'ماسح الباركود' if language == 'ar' else 'Barcode Scanner' }}</label>
                                    <select class="form-select" name="barcode_scanner">
                                        <option value="disabled" {{ 'selected' if settings.barcode_scanner == 'disabled' }}>
                                            {{ 'معطل' if language == 'ar' else 'Disabled' }}
                                        </option>
                                        <option value="enabled" {{ 'selected' if settings.barcode_scanner == 'enabled' }}>
                                            {{ 'مفعل' if language == 'ar' else 'Enabled' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'درج النقود' if language == 'ar' else 'Cash Drawer' }}</label>
                                    <select class="form-select" name="cash_drawer">
                                        <option value="disabled" {{ 'selected' if settings.cash_drawer == 'disabled' }}>
                                            {{ 'معطل' if language == 'ar' else 'Disabled' }}
                                        </option>
                                        <option value="enabled" {{ 'selected' if settings.cash_drawer == 'enabled' }}>
                                            {{ 'مفعل' if language == 'ar' else 'Enabled' }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_print_receipt" 
                                           {{ 'checked' if settings.auto_print_receipt }}>
                                    <label class="form-check-label">
                                        {{ 'طباعة الإيصال تلقائياً' if language == 'ar' else 'Auto Print Receipt' }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="show_customer_display" 
                                           {{ 'checked' if settings.show_customer_display }}>
                                    <label class="form-check-label">
                                        {{ 'عرض شاشة العميل' if language == 'ar' else 'Show Customer Display' }}
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ إعدادات نقطة البيع' if language == 'ar' else 'Save POS Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tax Settings -->
            <div class="tab-pane fade" id="tax">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-receipt"></i>
                            {{ 'الإعدادات الضريبية' if language == 'ar' else 'Tax Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='tax') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'معدل الضريبة (%)' if language == 'ar' else 'Tax Rate (%)' }}</label>
                                    <input type="number" class="form-control" name="tax_rate" step="0.01" min="0" max="100"
                                           value="{{ settings.tax_rate or 0 }}">
                                    <div class="form-text">
                                        {{ 'قطر لا تطبق ضريبة القيمة المضافة حالياً' if language == 'ar' else 'Qatar currently does not apply VAT' }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">{{ 'رقم التسجيل الضريبي' if language == 'ar' else 'Tax Registration Number' }}</label>
                                    <input type="text" class="form-control" name="tax_registration_number" 
                                           value="{{ settings.tax_registration_number }}">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="tax_inclusive" 
                                           {{ 'checked' if settings.tax_inclusive }}>
                                    <label class="form-check-label">
                                        {{ 'الأسعار تشمل الضريبة' if language == 'ar' else 'Prices Include Tax' }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="show_tax_on_receipt" 
                                           {{ 'checked' if settings.show_tax_on_receipt }}>
                                    <label class="form-check-label">
                                        {{ 'إظهار الضريبة في الإيصال' if language == 'ar' else 'Show Tax on Receipt' }}
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {{ 'حفظ الإعدادات الضريبية' if language == 'ar' else 'Save Tax Settings' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sound Settings -->
            <div class="tab-pane fade" id="sounds">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-volume-up"></i>
                            {{ 'إعدادات الأصوات' if language == 'ar' else 'Sound Settings' }}
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('settings.api_update_settings', category='sounds') }}">
                            <!-- Master Sound Control -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" name="sounds_enabled"
                                                               id="sounds_enabled" {{ 'checked' if settings.get('sounds_enabled') == 'true' }}>
                                                        <label class="form-check-label fw-bold" for="sounds_enabled">
                                                            {{ 'تفعيل الأصوات' if language == 'ar' else 'Enable Sounds' }}
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">
                                                        {{ 'تفعيل أو إلغاء جميع الأصوات في النظام' if language == 'ar' else 'Enable or disable all system sounds' }}
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">{{ 'مستوى الصوت' if language == 'ar' else 'Volume Level' }}</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range me-3" name="sound_volume"
                                                               id="sound_volume" min="0" max="1" step="0.1"
                                                               value="{{ settings.get('sound_volume', '0.5') }}">
                                                        <span id="volume_display" class="badge bg-primary">{{ (settings.get('sound_volume', '0.5')|float * 100)|int }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Individual Sound Settings -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="sale_sound_enabled"
                                               id="sale_sound_enabled" {{ 'checked' if settings.get('sale_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="sale_sound_enabled">
                                            <i class="bi bi-cash-coin text-success me-2"></i>
                                            {{ 'صوت إتمام البيع' if language == 'ar' else 'Sale Completion Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند إتمام عملية بيع' if language == 'ar' else 'Plays when a sale is completed' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="payment_sound_enabled"
                                               id="payment_sound_enabled" {{ 'checked' if settings.get('payment_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="payment_sound_enabled">
                                            <i class="bi bi-credit-card text-info me-2"></i>
                                            {{ 'صوت استلام الدفع' if language == 'ar' else 'Payment Received Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند استلام الدفع' if language == 'ar' else 'Plays when payment is received' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="error_sound_enabled"
                                               id="error_sound_enabled" {{ 'checked' if settings.get('error_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="error_sound_enabled">
                                            <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                                            {{ 'صوت الأخطاء' if language == 'ar' else 'Error Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند حدوث خطأ' if language == 'ar' else 'Plays when an error occurs' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="notification_sound_enabled"
                                               id="notification_sound_enabled" {{ 'checked' if settings.get('notification_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="notification_sound_enabled">
                                            <i class="bi bi-bell text-warning me-2"></i>
                                            {{ 'صوت الإشعارات' if language == 'ar' else 'Notification Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله مع الإشعارات' if language == 'ar' else 'Plays with notifications' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="low_stock_sound_enabled"
                                               id="low_stock_sound_enabled" {{ 'checked' if settings.get('low_stock_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="low_stock_sound_enabled">
                                            <i class="bi bi-box text-danger me-2"></i>
                                            {{ 'صوت تنبيه نفاد المخزون' if language == 'ar' else 'Low Stock Alert Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند انخفاض المخزون' if language == 'ar' else 'Plays when stock is low' }}</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="button_click_sound_enabled"
                                               id="button_click_sound_enabled" {{ 'checked' if settings.get('button_click_sound_enabled') == 'true' }}>
                                        <label class="form-check-label" for="button_click_sound_enabled">
                                            <i class="bi bi-mouse text-secondary me-2"></i>
                                            {{ 'صوت النقر على الأزرار' if language == 'ar' else 'Button Click Sound' }}
                                        </label>
                                    </div>
                                    <small class="text-muted ms-4">{{ 'يتم تشغيله عند النقر على الأزرار' if language == 'ar' else 'Plays when buttons are clicked' }}</small>
                                </div>
                            </div>

                            <!-- Test Sounds Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="bi bi-play-circle"></i>
                                                {{ 'اختبار الأصوات' if language == 'ar' else 'Test Sounds' }}
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted mb-3">
                                                {{ 'انقر على الأزرار أدناه لاختبار الأصوات المختلفة' if language == 'ar' else 'Click the buttons below to test different sounds' }}
                                            </p>
                                            <div class="row">
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-success btn-sm w-100" onclick="window.playSound('sale')">
                                                        <i class="bi bi-cash-coin"></i> {{ 'بيع' if language == 'ar' else 'Sale' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-info btn-sm w-100" onclick="window.playSound('payment')">
                                                        <i class="bi bi-credit-card"></i> {{ 'دفع' if language == 'ar' else 'Payment' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-danger btn-sm w-100" onclick="window.playSound('error')">
                                                        <i class="bi bi-exclamation-triangle"></i> {{ 'خطأ' if language == 'ar' else 'Error' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <button type="button" class="btn btn-warning btn-sm w-100" onclick="window.playSound('notification')">
                                                        <i class="bi bi-bell"></i> {{ 'إشعار' if language == 'ar' else 'Notification' }}
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-6 mb-2">
                                                    <button type="button" class="btn btn-secondary btn-sm w-100" onclick="window.testSounds()">
                                                        <i class="bi bi-play-circle"></i> {{ 'اختبار جميع الأصوات' if language == 'ar' else 'Test All Sounds' }}
                                                    </button>
                                                </div>
                                                <div class="col-md-6 mb-2">
                                                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="window.playSound('lowStock')">
                                                        <i class="bi bi-box"></i> {{ 'تنبيه مخزون' if language == 'ar' else 'Low Stock' }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i>
                                    {{ 'حفظ إعدادات الأصوات' if language == 'ar' else 'Save Sound Settings' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Other tabs would continue here... -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Handle tab switching
document.querySelectorAll('[data-bs-toggle="pill"]').forEach(function(tab) {
    tab.addEventListener('shown.bs.tab', function(e) {
        // Update URL hash
        window.location.hash = e.target.getAttribute('href');
    });
});

// Set active tab from URL hash
document.addEventListener('DOMContentLoaded', function() {
    const hash = window.location.hash;
    if (hash) {
        const tab = document.querySelector(`[href="${hash}"]`);
        if (tab) {
            const tabInstance = new bootstrap.Tab(tab);
            tabInstance.show();
        }
    }

    // Sound volume slider
    const volumeSlider = document.getElementById('sound_volume');
    const volumeDisplay = document.getElementById('volume_display');

    if (volumeSlider && volumeDisplay) {
        volumeSlider.addEventListener('input', function() {
            const volume = Math.round(this.value * 100);
            volumeDisplay.textContent = volume + '%';

            // Update sound manager volume if available
            if (window.soundManager) {
                window.soundManager.setVolume(this.value);
            }
        });
    }

    // Master sound toggle
    const soundsEnabled = document.getElementById('sounds_enabled');
    if (soundsEnabled) {
        soundsEnabled.addEventListener('change', function() {
            if (window.soundManager) {
                window.soundManager.setEnabled(this.checked);
            }

            // Enable/disable all other sound checkboxes
            const soundCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_sound_enabled"]');
            soundCheckboxes.forEach(checkbox => {
                checkbox.disabled = !this.checked;
            });
        });

        // Initial state
        const soundCheckboxes = document.querySelectorAll('input[type="checkbox"][name$="_sound_enabled"]');
        soundCheckboxes.forEach(checkbox => {
            checkbox.disabled = !soundsEnabled.checked;
        });
    }
});
</script>
{% endblock %}
