{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        إنشاء مبيعة جديدة
    {% else %}
        Create New Sale
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            إنشاء مبيعة جديدة
                        {% else %}
                            Create New Sale
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('sales.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <form method="POST" class="card-body">
                    <!-- Customer Selection -->
                    <h5 class="mb-3">
                        {% if language == 'ar' %}معلومات العميل{% else %}Customer Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="customer_id">
                                    {% if language == 'ar' %}العميل{% else %}Customer{% endif %}
                                </label>
                                <select class="form-control" id="customer_id" name="customer_id">
                                    <option value="">{% if language == 'ar' %}عميل نقدي (بدون حساب){% else %}Cash Customer (No Account){% endif %}</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}">
                                        {{ customer.get_display_name(language) }} - {{ customer.customer_code }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}اختياري - اتركه فارغاً للمبيعات النقدية{% else %}Optional - leave empty for cash sales{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="payment_method">
                                    {% if language == 'ar' %}طريقة الدفع{% else %}Payment Method{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="payment_method" name="payment_method" required>
                                    <option value="cash">{% if language == 'ar' %}نقدي{% else %}Cash{% endif %}</option>
                                    <option value="card">{% if language == 'ar' %}بطاقة{% else %}Card{% endif %}</option>
                                    <option value="bank_transfer">{% if language == 'ar' %}تحويل بنكي{% else %}Bank Transfer{% endif %}</option>
                                    <option value="credit">{% if language == 'ar' %}آجل{% else %}Credit{% endif %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Sale Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات المبيعة{% else %}Sale Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="notes">
                                    {% if language == 'ar' %}ملاحظات{% else %}Notes{% endif %}
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="{% if language == 'ar' %}أي ملاحظات إضافية...{% else %}Any additional notes...{% endif %}"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Information Notice -->
                    <div class="alert alert-info">
                        <h6>
                            <i class="fas fa-info-circle"></i>
                            {% if language == 'ar' %}ملاحظة مهمة{% else %}Important Note{% endif %}
                        </h6>
                        <p class="mb-0">
                            {% if language == 'ar' %}
                                بعد إنشاء المبيعة، ستحتاج إلى إضافة المنتجات من خلال نقطة البيع أو تعديل المبيعة.
                                يمكنك أيضاً استخدام نقطة البيع مباشرة لإنشاء مبيعة كاملة مع المنتجات.
                            {% else %}
                                After creating the sale, you'll need to add products through the POS system or by editing the sale.
                                You can also use the POS system directly to create a complete sale with products.
                            {% endif %}
                        </p>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group w-100" role="group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    {% if language == 'ar' %}إنشاء المبيعة{% else %}Create Sale{% endif %}
                                </button>
                                <a href="{{ url_for('sales.pos') }}" class="btn btn-success">
                                    <i class="fas fa-cash-register"></i>
                                    {% if language == 'ar' %}استخدام نقطة البيع{% else %}Use POS System{% endif %}
                                </a>
                                <a href="{{ url_for('sales.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Customer search functionality
document.getElementById('customer_id').addEventListener('change', function() {
    const customerId = this.value;
    const paymentMethod = document.getElementById('payment_method');
    
    if (customerId) {
        // If customer is selected, suggest credit payment for registered customers
        paymentMethod.value = 'credit';
    } else {
        // If no customer (cash customer), suggest cash payment
        paymentMethod.value = 'cash';
    }
});
</script>
{% endblock %}
