{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        عرض المبيعة #{{ sale.sale_number }}
    {% else %}
        View Sale #{{ sale.sale_number }}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            تفاصيل المبيعة #{{ sale.sale_number }}
                        {% else %}
                            Sale Details #{{ sale.sale_number }}
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('sales.invoice', sale_id=sale.id) }}" class="btn btn-info">
                            <i class="fas fa-file-invoice"></i>
                            {% if language == 'ar' %}الفاتورة{% else %}Invoice{% endif %}
                        </a>
                        <a href="{{ url_for('sales.print_invoice', sale_id=sale.id) }}" class="btn btn-secondary" target="_blank">
                            <i class="fas fa-print"></i>
                            {% if language == 'ar' %}طباعة{% else %}Print{% endif %}
                        </a>
                        <a href="{{ url_for('sales.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Sale Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{% if language == 'ar' %}معلومات المبيعة{% else %}Sale Information{% endif %}</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>{% if language == 'ar' %}رقم المبيعة:{% else %}Sale Number:{% endif %}</strong></td>
                                    <td>{{ sale.sale_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% if language == 'ar' %}التاريخ:{% else %}Date:{% endif %}</strong></td>
                                    <td>{{ sale.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% if language == 'ar' %}الحالة:{% else %}Status:{% endif %}</strong></td>
                                    <td>
                                        {% if sale.status == 'completed' %}
                                            <span class="badge badge-success">
                                                {% if language == 'ar' %}مكتملة{% else %}Completed{% endif %}
                                            </span>
                                        {% elif sale.status == 'pending' %}
                                            <span class="badge badge-warning">
                                                {% if language == 'ar' %}معلقة{% else %}Pending{% endif %}
                                            </span>
                                        {% elif sale.status == 'refunded' %}
                                            <span class="badge badge-danger">
                                                {% if language == 'ar' %}مسترجعة{% else %}Refunded{% endif %}
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% if language == 'ar' %}طريقة الدفع:{% else %}Payment Method:{% endif %}</strong></td>
                                    <td>
                                        {% if sale.payment_method == 'cash' %}
                                            {% if language == 'ar' %}نقدي{% else %}Cash{% endif %}
                                        {% elif sale.payment_method == 'card' %}
                                            {% if language == 'ar' %}بطاقة{% else %}Card{% endif %}
                                        {% elif sale.payment_method == 'bank_transfer' %}
                                            {% if language == 'ar' %}تحويل بنكي{% else %}Bank Transfer{% endif %}
                                        {% elif sale.payment_method == 'credit' %}
                                            {% if language == 'ar' %}آجل{% else %}Credit{% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>{% if language == 'ar' %}معلومات العميل{% else %}Customer Information{% endif %}</h5>
                            {% if sale.customer %}
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>{% if language == 'ar' %}الاسم:{% else %}Name:{% endif %}</strong></td>
                                        <td>{{ sale.customer.get_display_name(language) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{% if language == 'ar' %}كود العميل:{% else %}Customer Code:{% endif %}</strong></td>
                                        <td>{{ sale.customer.customer_code }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{% if language == 'ar' %}الهاتف:{% else %}Phone:{% endif %}</strong></td>
                                        <td>{{ sale.customer.phone or '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{% if language == 'ar' %}البريد الإلكتروني:{% else %}Email:{% endif %}</strong></td>
                                        <td>{{ sale.customer.email or '-' }}</td>
                                    </tr>
                                </table>
                            {% else %}
                                <p class="text-muted">
                                    {% if language == 'ar' %}عميل نقدي (بدون حساب){% else %}Cash Customer (No Account){% endif %}
                                </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Sale Items -->
                    <h5 class="mt-4 mb-3">{% if language == 'ar' %}عناصر المبيعة{% else %}Sale Items{% endif %}</h5>
                    
                    {% if sale.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% if language == 'ar' %}المنتج{% else %}Product{% endif %}</th>
                                    <th>{% if language == 'ar' %}الكمية{% else %}Quantity{% endif %}</th>
                                    <th>{% if language == 'ar' %}سعر الوحدة{% else %}Unit Price{% endif %}</th>
                                    <th>{% if language == 'ar' %}الخصم{% else %}Discount{% endif %}</th>
                                    <th>{% if language == 'ar' %}المجموع{% else %}Total{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items %}
                                <tr>
                                    <td>
                                        <strong>{{ item.product.get_name(language) }}</strong><br>
                                        <small class="text-muted">{{ item.product.product_code }}</small>
                                    </td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ "%.2f"|format(item.unit_price) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</td>
                                    <td>
                                        {% if item.discount_amount > 0 %}
                                            {{ "%.2f"|format(item.discount_amount) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.2f"|format(item.total_price) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        {% if language == 'ar' %}
                            لا توجد عناصر في هذه المبيعة بعد
                        {% else %}
                            No items in this sale yet
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Sale Totals -->
                    <div class="row mt-4">
                        <div class="col-md-6 offset-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>{% if language == 'ar' %}المجموع الفرعي:{% else %}Subtotal:{% endif %}</strong></td>
                                    <td class="text-right">{{ "%.2f"|format(sale.subtotal) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% if language == 'ar' %}الخصم:{% else %}Discount:{% endif %}</strong></td>
                                    <td class="text-right">{{ "%.2f"|format(sale.discount_amount) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</td>
                                </tr>
                                <tr>
                                    <td><strong>{% if language == 'ar' %}الضريبة:{% else %}Tax:{% endif %}</strong></td>
                                    <td class="text-right">{{ "%.2f"|format(sale.tax_amount) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</td>
                                </tr>
                                <tr class="table-active">
                                    <td><strong>{% if language == 'ar' %}المجموع الكلي:{% else %}Total Amount:{% endif %}</strong></td>
                                    <td class="text-right"><strong>{{ "%.2f"|format(sale.total_amount) }} {% if language == 'ar' %}ريال{% else %}QAR{% endif %}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Notes -->
                    {% if sale.notes %}
                    <div class="mt-4">
                        <h6>{% if language == 'ar' %}ملاحظات:{% else %}Notes:{% endif %}</h6>
                        <p class="text-muted">{{ sale.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}إجراءات سريعة{% else %}Quick Actions{% endif %}
                    </h3>
                </div>
                
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('sales.invoice', sale_id=sale.id) }}" class="btn btn-info">
                            <i class="fas fa-file-invoice"></i>
                            {% if language == 'ar' %}عرض الفاتورة{% else %}View Invoice{% endif %}
                        </a>
                        
                        <a href="{{ url_for('sales.print_invoice', sale_id=sale.id) }}" class="btn btn-secondary" target="_blank">
                            <i class="fas fa-print"></i>
                            {% if language == 'ar' %}طباعة الفاتورة{% else %}Print Invoice{% endif %}
                        </a>
                        
                        {% if sale.status == 'completed' %}
                        <a href="{{ url_for('sales.refund', sale_id=sale.id) }}" class="btn btn-warning">
                            <i class="fas fa-undo"></i>
                            {% if language == 'ar' %}استرجاع المبيعة{% else %}Refund Sale{% endif %}
                        </a>
                        {% endif %}
                        
                        <a href="{{ url_for('sales.pos') }}" class="btn btn-success">
                            <i class="fas fa-cash-register"></i>
                            {% if language == 'ar' %}نقطة البيع{% else %}POS System{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sale Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}إحصائيات المبيعة{% else %}Sale Statistics{% endif %}
                    </h3>
                </div>
                
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-shopping-cart"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% if language == 'ar' %}العناصر{% else %}Items{% endif %}</span>
                                    <span class="info-box-number">{{ sale.get_items_count() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% if language == 'ar' %}المبلغ{% else %}Amount{% endif %}</span>
                                    <span class="info-box-number">{{ "%.0f"|format(sale.total_amount) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
