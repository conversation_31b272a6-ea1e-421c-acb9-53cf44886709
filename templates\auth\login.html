<!DOCTYPE html>
<html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'تسجيل الدخول - نظام نقاط البيع القطري' if language == 'ar' else 'Login - Qatar POS System' }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% if language == 'ar' %}
    <!-- RTL Support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    {% endif %}
    
    <style>
        body {
            background: linear-gradient(135deg, #8B1538 0%, #6d1028 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .language-switcher {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }
        
        .language-switcher a {
            color: white;
            text-decoration: none;
            margin: 0 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            transition: background-color 0.3s;
        }
        
        .language-switcher a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .language-switcher a.active {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <a href="{{ url_for('auth.switch_language', language='ar') }}" 
           class="{{ 'active' if language == 'ar' else '' }}">العربية</a>
        <a href="{{ url_for('auth.switch_language', language='en') }}" 
           class="{{ 'active' if language == 'en' else '' }}">English</a>
    </div>
    
    <div class="login-card">
        <div class="login-header">
            <div class="logo">
                <i class="bi bi-shop"></i>
            </div>
            <h2 class="mb-0">
                {{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}
            </h2>
            <p class="mb-0 mt-2 opacity-75">
                {{ 'تسجيل الدخول إلى حسابك' if language == 'ar' else 'Sign in to your account' }}
            </p>
        </div>
        
        <div class="login-body">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="language" value="{{ language }}">
                
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="bi bi-person"></i>
                        {{ 'اسم المستخدم أو البريد الإلكتروني' if language == 'ar' else 'Username or Email' }}
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required
                           placeholder="{{ 'أدخل اسم المستخدم' if language == 'ar' else 'Enter username' }}">
                    <div class="invalid-feedback">
                        {{ 'يرجى إدخال اسم المستخدم' if language == 'ar' else 'Please enter username' }}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="bi bi-lock"></i>
                        {{ 'كلمة المرور' if language == 'ar' else 'Password' }}
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" name="password" required
                               placeholder="{{ 'أدخل كلمة المرور' if language == 'ar' else 'Enter password' }}">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                            <i class="bi bi-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback">
                        {{ 'يرجى إدخال كلمة المرور' if language == 'ar' else 'Please enter password' }}
                    </div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        {{ 'تذكرني' if language == 'ar' else 'Remember me' }}
                    </label>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-box-arrow-in-right"></i>
                        {{ 'تسجيل الدخول' if language == 'ar' else 'Sign In' }}
                    </button>
                </div>
            </form>
            
            <hr class="my-4">
            
            <div class="text-center">
                <small class="text-muted">
                    {{ 'نسيت كلمة المرور؟ اتصل بالمدير' if language == 'ar' else 'Forgot password? Contact administrator' }}
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }
        
        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
