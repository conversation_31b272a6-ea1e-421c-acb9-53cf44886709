"""
تطبيق سطح مكتب مبسط لنظام نقاط البيع القطري
Simple Desktop Application for Qatar POS System
"""

import sys
import os
import threading
import time
import webbrowser
import socket

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
except ImportError:
    print("❌ tkinter غير متوفر")
    sys.exit(1)

try:
    from app import create_app
    from extensions import db
except ImportError as e:
    print(f"❌ خطأ في استيراد Flask: {e}")
    sys.exit(1)

class SimplePOSDesktop:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام نقاط البيع القطري - Qatar POS System")
        self.root.geometry("700x500")
        
        # متغيرات التطبيق
        self.flask_app = None
        self.flask_thread = None
        self.server_running = False
        self.port = 2626
        self.host = "127.0.0.1"
        
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text="نظام نقاط البيع القطري\nQatar POS System", 
                               font=('Arial', 16, 'bold'),
                               justify=tk.CENTER)
        title_label.pack(pady=(0, 30))
        
        # معلومات الحالة
        status_frame = ttk.LabelFrame(main_frame, text="حالة النظام - System Status", padding="15")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        # حالة الخادم
        self.status_var = tk.StringVar(value="متوقف - Stopped")
        status_label = ttk.Label(status_frame, text="حالة الخادم:")
        status_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.status_display = ttk.Label(status_frame, textvariable=self.status_var, 
                                       foreground="red", font=('Arial', 10, 'bold'))
        self.status_display.grid(row=0, column=1, sticky=tk.W)
        
        # رابط النظام
        url_label = ttk.Label(status_frame, text="رابط النظام:")
        url_label.grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        
        self.url_var = tk.StringVar(value=f"http://{self.host}:{self.port}")
        url_display = ttk.Label(status_frame, textvariable=self.url_var, 
                               foreground="blue", cursor="hand2")
        url_display.grid(row=1, column=1, sticky=tk.W)
        url_display.bind("<Button-1>", self.open_browser)
        
        # أزرار التحكم
        control_frame = ttk.LabelFrame(main_frame, text="التحكم - Control", padding="15")
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        self.start_btn = ttk.Button(button_frame, text="بدء الخادم\nStart Server", 
                                   command=self.start_server, width=15)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="إيقاف الخادم\nStop Server", 
                                  command=self.stop_server, width=15, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.browser_btn = ttk.Button(button_frame, text="فتح المتصفح\nOpen Browser", 
                                     command=self.open_browser, width=15)
        self.browser_btn.grid(row=0, column=2)
        
        # معلومات النظام
        info_frame = ttk.LabelFrame(main_frame, text="معلومات النظام - System Info", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = f"""
إصدار Python: {sys.version.split()[0]}
نظام التشغيل: {os.name}
المجلد الحالي: {os.getcwd()}
المنفذ الافتراضي: {self.port}
العنوان: {self.host}

تعليمات الاستخدام:
1. انقر "بدء الخادم" لتشغيل النظام
2. انقر "فتح المتصفح" للوصول للنظام
3. استخدم admin/admin123 لتسجيل الدخول
4. انقر "إيقاف الخادم" عند الانتهاء
        """
        
        info_label = ttk.Label(info_frame, text=info_text.strip(), 
                              justify=tk.LEFT, font=('Courier', 9))
        info_label.pack(anchor=tk.W)
        
        # شريط الحالة
        self.status_bar = ttk.Label(self.root, text="جاهز - Ready", 
                                   relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def find_available_port(self):
        """البحث عن منفذ متاح"""
        for port in range(2626, 2636):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind((self.host, port))
                    return port
            except OSError:
                continue
        return None
        
    def run_flask_server(self):
        """تشغيل خادم Flask"""
        try:
            self.flask_app = create_app()
            
            # إنشاء قاعدة البيانات
            with self.flask_app.app_context():
                db.create_all()
            
            # تشغيل الخادم
            self.flask_app.run(
                host=self.host,
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True
            )
            
        except Exception as e:
            self.server_running = False
            self.root.after(0, lambda: self.update_status(f"خطأ: {str(e)}"))
    
    def start_server(self):
        """بدء الخادم"""
        if self.server_running:
            return
            
        # البحث عن منفذ متاح
        available_port = self.find_available_port()
        if available_port:
            self.port = available_port
            self.url_var.set(f"http://{self.host}:{self.port}")
        else:
            messagebox.showerror("خطأ", "لا يوجد منفذ متاح")
            return
        
        self.server_running = True
        self.update_status("جاري البدء...")
        
        # تشغيل Flask في thread منفصل
        self.flask_thread = threading.Thread(target=self.run_flask_server, daemon=True)
        self.flask_thread.start()
        
        # التحقق من حالة الخادم بعد ثانيتين
        self.root.after(2000, self.check_server_status)
        
    def stop_server(self):
        """إيقاف الخادم"""
        self.server_running = False
        self.update_status("تم الإيقاف")
        
    def check_server_status(self):
        """فحص حالة الخادم"""
        try:
            import requests
            response = requests.get(f"http://{self.host}:{self.port}/health", timeout=5)
            if response.status_code == 200:
                self.update_status("يعمل - Running")
            else:
                self.server_running = False
                self.update_status("خطأ في الاتصال")
        except:
            self.server_running = False
            self.update_status("فشل في البدء")
    
    def update_status(self, message=None):
        """تحديث حالة الواجهة"""
        if self.server_running:
            self.status_var.set("يعمل - Running")
            self.status_display.configure(foreground="green")
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.status_bar.configure(text="الخادم يعمل - Server Running")
        else:
            self.status_var.set("متوقف - Stopped")
            self.status_display.configure(foreground="red")
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.status_bar.configure(text=message or "متوقف - Stopped")
    
    def open_browser(self, event=None):
        """فتح المتصفح"""
        url = f"http://{self.host}:{self.port}"
        try:
            webbrowser.open(url)
            self.status_bar.configure(text=f"تم فتح المتصفح: {url}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المتصفح: {str(e)}")
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        if self.server_running:
            if messagebox.askokcancel("إغلاق", "هل تريد إيقاف الخادم وإغلاق التطبيق؟"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # بدء الخادم تلقائياً
        self.root.after(1000, self.start_server)
        
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تطبيق نقاط البيع القطري...")
    
    try:
        app = SimplePOSDesktop()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
