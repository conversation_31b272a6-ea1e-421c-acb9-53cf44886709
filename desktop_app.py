"""
Qatar POS System - Desktop Application
تطبيق نقاط البيع القطري - إصدار سطح المكتب
"""

try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
except ImportError:
    print("❌ tkinter غير متوفر. يرجى تثبيت Python مع tkinter")
    input("اضغط Enter للخروج...")
    sys.exit(1)

import threading
import webbrowser
import subprocess
import sys
import os
import time
import socket
from datetime import datetime
import json

# Flask app imports
try:
    from app import create_app
    from extensions import db
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    input("اضغط Enter للخروج...")
    sys.exit(1)

class QatarPOSDesktop:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام نقاط البيع القطري - Qatar POS System")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap("static/images/logo.ico")
        except:
            pass
        
        # Configure RTL support for Arabic
        self.root.option_add('*Font', 'Arial 10')
        
        # Initialize variables
        self.flask_app = None
        self.flask_thread = None
        self.server_running = False
        self.port = 2626
        self.host = "127.0.0.1"
        
        # Create GUI
        self.create_widgets()
        
        # Start Flask app automatically
        self.start_server()
    
    def create_widgets(self):
        """إنشاء واجهة المستخدم"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="نظام نقاط البيع القطري", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Server status frame
        status_frame = ttk.LabelFrame(main_frame, text="حالة الخادم - Server Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # Server status
        ttk.Label(status_frame, text="الحالة:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.status_var = tk.StringVar(value="جاري البدء...")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                     foreground="orange")
        self.status_label.grid(row=0, column=1, sticky=tk.W)
        
        # Server URL
        ttk.Label(status_frame, text="الرابط:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.url_var = tk.StringVar(value=f"http://{self.host}:{self.port}")
        url_label = ttk.Label(status_frame, textvariable=self.url_var, 
                             foreground="blue", cursor="hand2")
        url_label.grid(row=1, column=1, sticky=tk.W)
        url_label.bind("<Button-1>", self.open_browser)
        
        # Control buttons frame
        control_frame = ttk.Frame(status_frame)
        control_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        # Control buttons
        self.start_btn = ttk.Button(control_frame, text="بدء الخادم", 
                                   command=self.start_server, state="disabled")
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="إيقاف الخادم", 
                                  command=self.stop_server)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.browser_btn = ttk.Button(control_frame, text="فتح المتصفح", 
                                     command=self.open_browser)
        self.browser_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.restart_btn = ttk.Button(control_frame, text="إعادة تشغيل", 
                                     command=self.restart_server)
        self.restart_btn.pack(side=tk.LEFT)
        
        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="سجل النظام - System Log", padding="10")
        log_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Log control buttons
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=1, column=0, pady=(10, 0))
        
        ttk.Button(log_control_frame, text="مسح السجل", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(log_control_frame, text="حفظ السجل", 
                  command=self.save_log).pack(side=tk.LEFT)
        
        # Info frame
        info_frame = ttk.LabelFrame(main_frame, text="معلومات النظام - System Info", padding="10")
        info_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        info_text = f"""
إصدار النظام: 1.0.0
المنفذ: {self.port}
العنوان: {self.host}
نظام التشغيل: {os.name}
Python: {sys.version.split()[0]}
        """
        
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack(anchor=tk.W)
        
        # Add log message
        self.add_log("تم تهيئة واجهة سطح المكتب")
    
    def add_log(self, message):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # Keep only last 1000 lines
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", f"{len(lines)-1000}.0")
    
    def clear_log(self):
        """مسح السجل"""
        self.log_text.delete("1.0", tk.END)
        self.add_log("تم مسح السجل")
    
    def save_log(self):
        """حفظ السجل"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="حفظ سجل النظام"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get("1.0", tk.END))
                self.add_log(f"تم حفظ السجل في: {filename}")
                messagebox.showinfo("نجح", "تم حفظ السجل بنجاح")
        except Exception as e:
            self.add_log(f"خطأ في حفظ السجل: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حفظ السجل: {str(e)}")
    
    def check_port_available(self, port):
        """فحص توفر المنفذ"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind((self.host, port))
                return True
        except:
            return False
    
    def find_available_port(self):
        """البحث عن منفذ متاح"""
        for port in range(2626, 2636):
            if self.check_port_available(port):
                return port
        return None
    
    def run_flask_app(self):
        """تشغيل تطبيق Flask"""
        try:
            self.flask_app = create_app()
            
            # Create database tables
            with self.flask_app.app_context():
                db.create_all()
                self.add_log("تم إنشاء قاعدة البيانات")
            
            # Run the app
            self.flask_app.run(
                host=self.host,
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True
            )
            
        except Exception as e:
            self.add_log(f"خطأ في تشغيل الخادم: {str(e)}")
            self.server_running = False
            self.root.after(0, self.update_status)
    
    def start_server(self):
        """بدء الخادم"""
        if self.server_running:
            self.add_log("الخادم يعمل بالفعل")
            return
        
        # Check if port is available
        if not self.check_port_available(self.port):
            new_port = self.find_available_port()
            if new_port:
                self.port = new_port
                self.url_var.set(f"http://{self.host}:{self.port}")
                self.add_log(f"تم تغيير المنفذ إلى: {self.port}")
            else:
                messagebox.showerror("خطأ", "لا يوجد منفذ متاح")
                return
        
        self.add_log("جاري بدء الخادم...")
        self.server_running = True
        
        # Start Flask in a separate thread
        self.flask_thread = threading.Thread(target=self.run_flask_app, daemon=True)
        self.flask_thread.start()
        
        # Wait a moment and check if server started
        self.root.after(2000, self.check_server_status)
        
        self.update_status()
    
    def stop_server(self):
        """إيقاف الخادم"""
        if not self.server_running:
            self.add_log("الخادم متوقف بالفعل")
            return
        
        self.add_log("جاري إيقاف الخادم...")
        self.server_running = False
        
        # Note: Flask development server doesn't have a clean shutdown method
        # In production, you would use a proper WSGI server
        self.add_log("تم إيقاف الخادم")
        self.update_status()
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.add_log("جاري إعادة تشغيل الخادم...")
        self.stop_server()
        time.sleep(1)
        self.start_server()
    
    def check_server_status(self):
        """فحص حالة الخادم"""
        try:
            import requests
            response = requests.get(f"http://{self.host}:{self.port}/health", timeout=5)
            if response.status_code == 200:
                self.add_log("الخادم يعمل بنجاح")
                self.server_running = True
            else:
                self.add_log("الخادم لا يستجيب")
                self.server_running = False
        except:
            self.add_log("فشل في الاتصال بالخادم")
            self.server_running = False
        
        self.update_status()
    
    def update_status(self):
        """تحديث حالة الخادم في الواجهة"""
        if self.server_running:
            self.status_var.set("يعمل - Running")
            self.status_label.configure(foreground="green")
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
        else:
            self.status_var.set("متوقف - Stopped")
            self.status_label.configure(foreground="red")
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
    
    def open_browser(self, event=None):
        """فتح المتصفح"""
        url = f"http://{self.host}:{self.port}"
        try:
            webbrowser.open(url)
            self.add_log(f"تم فتح المتصفح: {url}")
        except Exception as e:
            self.add_log(f"فشل في فتح المتصفح: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح المتصفح: {str(e)}")
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        if self.server_running:
            if messagebox.askokcancel("إغلاق", "هل تريد إيقاف الخادم وإغلاق التطبيق؟"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = QatarPOSDesktop()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
