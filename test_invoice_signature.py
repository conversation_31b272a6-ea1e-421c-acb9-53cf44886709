#!/usr/bin/env python3
"""
Test script to verify manager signature in invoices for Qatar POS System
"""

from app import create_app
from models.setting import Setting
from models.sale import Sale
from models.user import User
from extensions import db
import requests

def test_signature_in_invoice():
    """Test manager signature functionality in invoices"""
    app = create_app()
    
    with app.app_context():
        print("✍️ اختبار توقيع المسؤول في الفواتير")
        print("=" * 50)
        
        # Test 1: Check signature settings
        print("1️⃣ فحص إعدادات التوقيع...")
        
        signature_settings = [
            ('manager_signature', 'ملف التوقيع'),
            ('manager_name', 'اسم المسؤول'),
            ('manager_title', 'منصب المسؤول'),
            ('signature_width', 'عرض التوقيع'),
            ('signature_height', 'ارتفاع التوقيع')
        ]
        
        signature_complete = True
        for key, description in signature_settings:
            value = Setting.get_setting(key)
            status = "✅" if value else "❌"
            print(f"   {status} {description}: {value or 'غير محدد'}")
            if not value and key in ['manager_name', 'manager_title']:
                signature_complete = False
        
        # Test 2: Check signature file existence
        print("\n2️⃣ فحص ملف التوقيع...")
        
        signature_file = Setting.get_setting('manager_signature')
        if signature_file:
            import os
            signature_path = f"static/uploads/signatures/{signature_file}"
            if os.path.exists(signature_path):
                file_size = os.path.getsize(signature_path)
                print(f"   ✅ ملف التوقيع موجود: {signature_path}")
                print(f"   📊 حجم الملف: {file_size} بايت")
            else:
                print(f"   ❌ ملف التوقيع غير موجود: {signature_path}")
                signature_complete = False
        else:
            print("   ⚠️ لم يتم تحديد ملف توقيع")
        
        # Test 3: Check recent sales for testing
        print("\n3️⃣ فحص المبيعات المتاحة للاختبار...")
        
        recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(3).all()
        if recent_sales:
            print(f"   📊 عدد المبيعات المتاحة: {len(recent_sales)}")
            for sale in recent_sales:
                print(f"   🧾 فاتورة رقم: {sale.sale_number} - {sale.total_amount} ر.ق")
        else:
            print("   ⚠️ لا توجد مبيعات للاختبار")
            return False
        
        # Test 4: Test invoice routes
        print("\n4️⃣ اختبار صفحات الفواتير...")
        
        test_sale = recent_sales[0]
        base_url = "http://localhost:2626"
        
        invoice_routes = [
            (f'/sales/{test_sale.id}/invoice', 'فاتورة عادية'),
            (f'/sales/{test_sale.id}/print', 'فاتورة للطباعة')
        ]
        
        for route, description in invoice_routes:
            try:
                response = requests.get(f"{base_url}{route}", timeout=5)
                
                if response.status_code == 200:
                    print(f"   ✅ {description}: متاحة (200)")
                    
                    # Check if signature elements are in the HTML
                    content = response.text
                    signature_elements = [
                        'manager_signature',
                        'توقيع المسؤول',
                        'Authorized Signature',
                        'manager_name',
                        'manager_title'
                    ]
                    
                    found_elements = []
                    for element in signature_elements:
                        if element in content:
                            found_elements.append(element)
                    
                    print(f"      📋 عناصر التوقيع الموجودة: {len(found_elements)}/{len(signature_elements)}")
                    
                elif response.status_code == 302:
                    print(f"   🔄 {description}: إعادة توجيه (302) - يتطلب تسجيل دخول")
                elif response.status_code == 403:
                    print(f"   🔒 {description}: ممنوع (403) - يتطلب صلاحيات")
                else:
                    print(f"   ⚠️ {description}: حالة غير متوقعة ({response.status_code})")
                    
            except requests.exceptions.RequestException as e:
                print(f"   💥 {description}: خطأ في الاتصال - {e}")
        
        # Test 5: Check stamp settings too
        print("\n5️⃣ فحص إعدادات الختم...")
        
        stamp_file = Setting.get_setting('company_stamp')
        if stamp_file:
            import os
            stamp_path = f"static/uploads/stamps/{stamp_file}"
            if os.path.exists(stamp_path):
                print(f"   ✅ ختم الشركة موجود: {stamp_path}")
            else:
                print(f"   ❌ ختم الشركة غير موجود: {stamp_path}")
        else:
            print("   ⚠️ لم يتم تحديد ختم للشركة")
        
        # Test 6: Overall assessment
        print("\n6️⃣ التقييم العام...")
        
        if signature_complete and signature_file:
            print("   🎉 ممتاز! جميع إعدادات التوقيع مكتملة")
            print("   ✅ التوقيع سيظهر في الفواتير")
        elif signature_file:
            print("   👍 جيد! ملف التوقيع موجود ولكن بعض الإعدادات ناقصة")
            print("   ⚠️ يُنصح بإكمال اسم ومنصب المسؤول")
        else:
            print("   ⚠️ إعدادات التوقيع غير مكتملة")
            print("   📝 يجب رفع ملف التوقيع من إعدادات الشركة")
        
        return signature_complete

def create_test_sale():
    """Create a test sale for invoice testing"""
    app = create_app()
    
    with app.app_context():
        # Check if we have any sales
        sales_count = Sale.query.count()
        
        if sales_count == 0:
            print("\n🔧 إنشاء مبيعة تجريبية للاختبار...")
            
            try:
                from models.product import Product
                from models.customer import Customer
                from models.sale_item import SaleItem
                from datetime import datetime
                
                # Get or create a test customer
                customer = Customer.query.first()
                if not customer:
                    customer = Customer(
                        name_ar='عميل تجريبي',
                        name_en='Test Customer',
                        phone='12345678',
                        email='<EMAIL>'
                    )
                    db.session.add(customer)
                    db.session.flush()
                
                # Get or create a test product
                product = Product.query.first()
                if not product:
                    from models.category import Category
                    category = Category.query.first()
                    if not category:
                        category = Category(
                            name_ar='فئة تجريبية',
                            name_en='Test Category'
                        )
                        db.session.add(category)
                        db.session.flush()
                    
                    product = Product(
                        sku='TEST001',
                        barcode='1234567890',
                        name_ar='منتج تجريبي',
                        name_en='Test Product',
                        category_id=category.id,
                        selling_price=10.00,
                        cost_price=5.00,
                        current_stock=100,
                        minimum_stock=10,
                        track_inventory=True,
                        is_active=True
                    )
                    db.session.add(product)
                    db.session.flush()
                
                # Get a test user (seller)
                seller = User.query.first()
                if not seller:
                    print("   ❌ لا يوجد مستخدمين في النظام")
                    return False
                
                # Create test sale
                sale = Sale(
                    sale_number=f'INV-{datetime.now().strftime("%Y%m%d")}-001',
                    customer_id=customer.id,
                    seller_id=seller.id,
                    sale_date=datetime.now(),
                    subtotal=10.00,
                    tax_amount=0.00,
                    discount_amount=0.00,
                    total_amount=10.00,
                    amount_paid=10.00,
                    amount_due=0.00,
                    payment_method='cash',
                    status='completed'
                )
                db.session.add(sale)
                db.session.flush()
                
                # Create sale item
                sale_item = SaleItem(
                    sale_id=sale.id,
                    product_id=product.id,
                    quantity=1,
                    unit_price=10.00,
                    discount_amount=0.00,
                    total_price=10.00
                )
                db.session.add(sale_item)
                
                db.session.commit()
                
                print(f"   ✅ تم إنشاء مبيعة تجريبية: {sale.sale_number}")
                return True
                
            except Exception as e:
                db.session.rollback()
                print(f"   ❌ خطأ في إنشاء المبيعة التجريبية: {e}")
                return False
        
        return True

def display_signature_guide():
    """Display signature setup guide"""
    print("\n📋 دليل إعداد توقيع المسؤول في الفواتير:")
    print("=" * 50)
    
    print("🔗 للوصول إلى إعدادات التوقيع:")
    print("   • http://localhost:2626/settings/company")
    print("   • ابحث عن قسم 'توقيع المسؤول'")
    
    print("\n📝 الخطوات المطلوبة:")
    print("   1️⃣ ارفع صورة التوقيع (PNG, JPG, GIF)")
    print("   2️⃣ أدخل اسم المسؤول")
    print("   3️⃣ أدخل منصب المسؤول")
    print("   4️⃣ حدد أبعاد التوقيع")
    print("   5️⃣ احفظ الإعدادات")
    
    print("\n🎯 مواصفات ملف التوقيع:")
    print("   • الصيغ المدعومة: PNG, JPG, GIF")
    print("   • الحد الأقصى للحجم: 2MB")
    print("   • يُفضل خلفية شفافة (PNG)")
    print("   • الأبعاد المقترحة: 200x80 بكسل")
    
    print("\n📄 أين سيظهر التوقيع:")
    print("   ✅ في الفواتير العادية")
    print("   ✅ في الفواتير المطبوعة")
    print("   ✅ بجانب ختم الشركة")
    print("   ✅ مع اسم ومنصب المسؤول")
    
    print("\n💡 نصائح:")
    print("   • استخدم توقيع واضح ومقروء")
    print("   • تأكد من أن الخلفية شفافة")
    print("   • اختبر التوقيع في الفاتورة بعد الرفع")
    print("   • يمكن تغيير التوقيع في أي وقت")

if __name__ == '__main__':
    print("🔬 اختبار شامل لتوقيع المسؤول في الفواتير")
    print("=" * 60)
    
    try:
        # Create test sale if needed
        create_test_sale()
        
        # Test signature functionality
        success = test_signature_in_invoice()
        
        # Display guide
        display_signature_guide()
        
        if success:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ توقيع المسؤول جاهز للاستخدام في الفواتير")
        else:
            print("\n⚠️ بعض الإعدادات تحتاج إلى استكمال")
            print("📝 راجع دليل الإعداد أعلاه")
            
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار: {e}")
