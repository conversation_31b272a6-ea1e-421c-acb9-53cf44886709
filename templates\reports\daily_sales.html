{% extends "base.html" %}

{% block title %}
{{ 'تقرير المبيعات اليومية - نظام نقاط البيع القطري' if language == 'ar' else 'Daily Sales Report - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-calendar-day"></i>
                {{ 'تقرير المبيعات اليومية' if language == 'ar' else 'Daily Sales Report' }}
            </h1>
            <div>
                <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                    <i class="bi bi-file-pdf"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="bi bi-file-excel"></i>
                    Excel
                </button>
                <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للتقارير' if language == 'ar' else 'Back to Reports' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'تصفية التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                        <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                        <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                                {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(total_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'إجمالي المعاملات' if language == 'ar' else 'Total Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,}'.format(total_transactions) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'متوسط المبيعات اليومية' if language == 'ar' else 'Average Daily Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(avg_daily_sales) if language == 'ar' else 'QAR {:,.2f}'.format(avg_daily_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'عدد الأيام' if language == 'ar' else 'Number of Days' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ daily_sales|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {{ 'رسم بياني للمبيعات اليومية' if language == 'ar' else 'Daily Sales Chart' }}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="dailySalesChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'تفاصيل المبيعات اليومية' if language == 'ar' else 'Daily Sales Details' }}
                </h6>
            </div>
            <div class="card-body">
                {% if daily_sales %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'التاريخ' if language == 'ar' else 'Date' }}</th>
                                <th>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</th>
                                <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</th>
                                <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Avg Transaction' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day in daily_sales %}
                            <tr>
                                <td>{{ day.sale_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(day.total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(day.total_sales) }}</td>
                                <td>{{ '{:,}'.format(day.transaction_count) }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(day.avg_transaction) if language == 'ar' else 'QAR {:,.2f}'.format(day.avg_transaction) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'لا توجد بيانات للفترة المحددة' if language == 'ar' else 'No data found for the selected period' }}</h5>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Daily Sales Chart
const ctx = document.getElementById('dailySalesChart').getContext('2d');
const dailySalesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [
            {% for day in daily_sales %}
            '{{ day.sale_date.strftime('%m-%d') }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ 'المبيعات اليومية' if language == 'ar' else 'Daily Sales' }}',
            data: [
                {% for day in daily_sales %}
                {{ day.total_sales }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{{ 'المبيعات اليومية' if language == 'ar' else 'Daily Sales' }}'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    window.open(`{{ url_for('reports.daily_sales') }}?${params.toString()}`);
}
</script>
{% endblock %}
