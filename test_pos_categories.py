#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الفئات في نقطة البيع
Test POS Categories Fix
"""

import requests
import json
from app import create_app
from extensions import db
from models.user import User
from models.category import Category
from models.product import Product
from decimal import Decimal

def test_pos_categories_fix():
    """اختبار إصلاح مشكلة عرض المنتجات عند اختيار جميع الفئات"""
    print("🇶🇦 نظام نقاط البيع القطري - اختبار إصلاح الفئات في نقطة البيع")
    print("=" * 80)
    
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("\n🔐 تسجيل الدخول...")
            
            # Login as admin
            login_response = client.post('/auth/login', data={
                'username': 'admin',
                'password': 'admin123'
            }, follow_redirects=True)
            
            if login_response.status_code != 200:
                print("❌ فشل تسجيل الدخول")
                return False
            
            print("✅ تم تسجيل الدخول بنجاح")
            
            # Test 1: Check categories API
            print("\n📋 اختبار API الفئات...")
            categories_response = client.get('/api/categories')
            print(f"  📊 حالة الاستجابة: {categories_response.status_code}")
            
            if categories_response.status_code == 200:
                categories_data = categories_response.get_json()
                categories_count = len(categories_data.get('categories', []))
                print(f"  ✅ تم العثور على {categories_count} فئة")
                
                # Show categories
                for category in categories_data.get('categories', [])[:5]:
                    print(f"    - {category['name']} (ID: {category['id']})")
            else:
                print("  ❌ فشل في تحميل الفئات")
                return False
            
            # Test 2: Check products API without search (All Categories)
            print("\n🛍️ اختبار API المنتجات - جميع الفئات...")
            products_response = client.get('/products/api/search?limit=10&q=')
            print(f"  📊 حالة الاستجابة: {products_response.status_code}")
            
            if products_response.status_code == 200:
                products_data = products_response.get_json()
                products_count = len(products_data)
                print(f"  ✅ تم العثور على {products_count} منتج (جميع الفئات)")
                
                # Show first few products
                for product in products_data[:3]:
                    print(f"    - {product['name']} ({product['sku']}) - {product['final_price']} ر.ق")
            else:
                print("  ❌ فشل في تحميل المنتجات")
                return False
            
            # Test 3: Check products API with category filter
            if categories_count > 0:
                first_category_id = categories_data['categories'][0]['id']
                print(f"\n🏷️ اختبار API المنتجات - فئة محددة (ID: {first_category_id})...")
                
                category_products_response = client.get(f'/products/api/search?limit=10&q=&category={first_category_id}')
                print(f"  📊 حالة الاستجابة: {category_products_response.status_code}")
                
                if category_products_response.status_code == 200:
                    category_products_data = category_products_response.get_json()
                    category_products_count = len(category_products_data)
                    print(f"  ✅ تم العثور على {category_products_count} منتج في الفئة")
                    
                    # Show products in category
                    for product in category_products_data[:3]:
                        print(f"    - {product['name']} ({product['sku']}) - {product['final_price']} ر.ق")
                else:
                    print("  ❌ فشل في تحميل منتجات الفئة")
                    return False
            
            # Test 4: Check POS page loads
            print("\n🖥️ اختبار صفحة نقطة البيع...")
            pos_response = client.get('/sales/pos')
            print(f"  📊 حالة الاستجابة: {pos_response.status_code}")
            
            if pos_response.status_code == 200:
                print("  ✅ صفحة نقطة البيع تحمل بنجاح")
                
                # Check if page contains category filter
                pos_content = pos_response.get_data(as_text=True)
                if 'category_filter' in pos_content:
                    print("  ✅ عنصر فلتر الفئات موجود في الصفحة")
                else:
                    print("  ⚠️ عنصر فلتر الفئات غير موجود في الصفحة")
                
                if 'جميع الفئات' in pos_content or 'All Categories' in pos_content:
                    print("  ✅ خيار 'جميع الفئات' موجود")
                else:
                    print("  ⚠️ خيار 'جميع الفئات' غير موجود")
                    
            else:
                print("  ❌ فشل في تحميل صفحة نقطة البيع")
                return False
            
            print("\n" + "=" * 80)
            print("✅ انتهى اختبار إصلاح الفئات في نقطة البيع!")
            print("🎯 المشكلة تم حلها: الآن عند اختيار 'جميع الفئات' ستظهر جميع المنتجات")
            print("🛍️ وعند اختيار فئة محددة ستظهر منتجات تلك الفئة فقط")
            
            return True

def create_test_data():
    """إنشاء بيانات اختبار إذا لم تكن موجودة"""
    app = create_app()
    
    with app.app_context():
        # Check if we have categories and products
        categories_count = Category.query.filter_by(is_active=True).count()
        products_count = Product.query.filter_by(is_active=True).count()
        
        print(f"📊 الفئات الموجودة: {categories_count}")
        print(f"📦 المنتجات الموجودة: {products_count}")
        
        if categories_count == 0:
            print("⚠️ لا توجد فئات - إنشاء فئات اختبار...")
            
            # Create test categories
            categories = [
                Category(name_ar='مشروبات', name_en='Beverages', description_ar='مشروبات متنوعة', description_en='Various beverages'),
                Category(name_ar='وجبات خفيفة', name_en='Snacks', description_ar='وجبات خفيفة', description_en='Light snacks'),
                Category(name_ar='منتجات ألبان', name_en='Dairy Products', description_ar='منتجات الألبان', description_en='Dairy products')
            ]
            
            for category in categories:
                db.session.add(category)
            
            db.session.commit()
            print(f"✅ تم إنشاء {len(categories)} فئة")
        
        if products_count == 0:
            print("⚠️ لا توجد منتجات - إنشاء منتجات اختبار...")
            
            # Get categories
            beverage_cat = Category.query.filter_by(name_en='Beverages').first()
            snacks_cat = Category.query.filter_by(name_en='Snacks').first()
            dairy_cat = Category.query.filter_by(name_en='Dairy Products').first()
            
            # Create test products
            products = [
                Product(name_ar='كوكا كولا', name_en='Coca Cola', sku='COKE001', 
                       category_id=beverage_cat.id if beverage_cat else None,
                       cost_price=Decimal('1.50'), selling_price=Decimal('2.00'), current_stock=100),
                Product(name_ar='بيبسي', name_en='Pepsi', sku='PEPSI001',
                       category_id=beverage_cat.id if beverage_cat else None,
                       cost_price=Decimal('1.50'), selling_price=Decimal('2.00'), current_stock=80),
                Product(name_ar='شيبس', name_en='Chips', sku='CHIPS001',
                       category_id=snacks_cat.id if snacks_cat else None,
                       cost_price=Decimal('2.00'), selling_price=Decimal('3.00'), current_stock=50),
                Product(name_ar='حليب', name_en='Milk', sku='MILK001',
                       category_id=dairy_cat.id if dairy_cat else None,
                       cost_price=Decimal('3.00'), selling_price=Decimal('4.00'), current_stock=30)
            ]
            
            for product in products:
                db.session.add(product)
            
            db.session.commit()
            print(f"✅ تم إنشاء {len(products)} منتج")

if __name__ == '__main__':
    print("🔧 إنشاء بيانات اختبار...")
    create_test_data()
    
    print("\n🧪 بدء اختبار إصلاح الفئات...")
    success = test_pos_categories_fix()
    
    if success:
        print("\n🎉 تم إصلاح مشكلة الفئات في نقطة البيع بنجاح!")
    else:
        print("\n❌ فشل في إصلاح مشكلة الفئات")
