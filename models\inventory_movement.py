"""
Advanced Inventory Movement Model for Qatar POS System
Tracks all inventory transactions with detailed logging
"""

from extensions import db
from datetime import datetime
from sqlalchemy import event
from models.product import Product
from models.user import User

class InventoryMovement(db.Model):
    """Model for tracking inventory movements"""
    __tablename__ = 'inventory_movements'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Product and quantity information
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    old_quantity = db.Column(db.Integer, nullable=False, default=0)
    new_quantity = db.Column(db.Integer, nullable=False, default=0)
    quantity_change = db.Column(db.Integer, nullable=False)
    
    # Transaction details
    transaction_type = db.Column(db.Enum(
        'sale', 'purchase', 'adjustment', 'return', 'damage', 'transfer', 'initial',
        name='movement_types'
    ), nullable=False)
    
    transaction_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    reference_number = db.Column(db.String(100))  # Sale ID, Purchase ID, etc.
    
    # User and audit information
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    notes = db.Column(db.Text)
    reason = db.Column(db.String(100))  # For adjustments: damage, theft, count, etc.
    
    # Cost information (for valuation)
    unit_cost = db.Column(db.Numeric(10, 2))
    total_cost = db.Column(db.Numeric(10, 2))
    
    # Location information (for multi-location support)
    location = db.Column(db.String(100), default='main')
    
    # Relationships
    product = db.relationship('Product', backref='movements', lazy=True)
    user = db.relationship('User', backref='inventory_movements', lazy=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<InventoryMovement {self.id}: {self.transaction_type} {self.quantity_change}>'
    
    @property
    def is_inbound(self):
        """Check if this is an inbound movement (increases stock)"""
        return self.quantity_change > 0
    
    @property
    def is_outbound(self):
        """Check if this is an outbound movement (decreases stock)"""
        return self.quantity_change < 0
    
    @property
    def movement_direction(self):
        """Get movement direction as string"""
        if self.quantity_change > 0:
            return 'in'
        elif self.quantity_change < 0:
            return 'out'
        else:
            return 'neutral'
    
    def get_transaction_type_display(self, language='en'):
        """Get human-readable transaction type"""
        types = {
            'sale': {'ar': 'بيع', 'en': 'Sale'},
            'purchase': {'ar': 'شراء', 'en': 'Purchase'},
            'adjustment': {'ar': 'تعديل', 'en': 'Adjustment'},
            'return': {'ar': 'إرجاع', 'en': 'Return'},
            'damage': {'ar': 'تلف', 'en': 'Damage'},
            'transfer': {'ar': 'نقل', 'en': 'Transfer'},
            'initial': {'ar': 'مخزون أولي', 'en': 'Initial Stock'}
        }
        return types.get(self.transaction_type, {}).get(language, self.transaction_type)
    
    def get_reason_display(self, language='en'):
        """Get human-readable reason"""
        reasons = {
            'physical_count': {'ar': 'جرد فعلي', 'en': 'Physical Count'},
            'damage': {'ar': 'تلف', 'en': 'Damage'},
            'theft': {'ar': 'سرقة', 'en': 'Theft'},
            'expired': {'ar': 'منتهي الصلاحية', 'en': 'Expired'},
            'lost': {'ar': 'مفقود', 'en': 'Lost'},
            'found': {'ar': 'موجود', 'en': 'Found'},
            'correction': {'ar': 'تصحيح', 'en': 'Correction'},
            'other': {'ar': 'أخرى', 'en': 'Other'}
        }
        return reasons.get(self.reason, {}).get(language, self.reason) if self.reason else ''
    
    @classmethod
    def create_movement(cls, product_id, new_quantity, transaction_type, user_id=None, 
                       reference_number=None, notes=None, reason=None, unit_cost=None):
        """Create a new inventory movement"""
        try:
            # Get current product stock
            product = Product.query.get(product_id)
            if not product:
                raise ValueError("Product not found")
            
            old_quantity = product.current_stock
            quantity_change = new_quantity - old_quantity
            
            # Calculate cost information
            if unit_cost is None:
                unit_cost = product.cost_price or 0
            
            total_cost = abs(quantity_change) * unit_cost
            
            # Create movement record
            movement = cls(
                product_id=product_id,
                old_quantity=old_quantity,
                new_quantity=new_quantity,
                quantity_change=quantity_change,
                transaction_type=transaction_type,
                user_id=user_id,
                reference_number=reference_number,
                notes=notes,
                reason=reason,
                unit_cost=unit_cost,
                total_cost=total_cost
            )
            
            db.session.add(movement)
            
            # Update product stock
            product.current_stock = new_quantity
            
            # Update product timestamps
            product.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            return movement
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    @classmethod
    def create_sale_movement(cls, product_id, quantity_sold, sale_id, user_id=None):
        """Create movement for sale transaction"""
        product = Product.query.get(product_id)
        if not product:
            raise ValueError("Product not found")
        
        new_quantity = product.current_stock - quantity_sold
        if new_quantity < 0:
            raise ValueError("Insufficient stock")
        
        return cls.create_movement(
            product_id=product_id,
            new_quantity=new_quantity,
            transaction_type='sale',
            user_id=user_id,
            reference_number=f"SALE-{sale_id}",
            notes=f"Sale of {quantity_sold} units"
        )
    
    @classmethod
    def create_purchase_movement(cls, product_id, quantity_purchased, purchase_id, 
                                user_id=None, unit_cost=None):
        """Create movement for purchase transaction"""
        product = Product.query.get(product_id)
        if not product:
            raise ValueError("Product not found")
        
        new_quantity = product.current_stock + quantity_purchased
        
        return cls.create_movement(
            product_id=product_id,
            new_quantity=new_quantity,
            transaction_type='purchase',
            user_id=user_id,
            reference_number=f"PURCHASE-{purchase_id}",
            notes=f"Purchase of {quantity_purchased} units",
            unit_cost=unit_cost
        )
    
    @classmethod
    def create_adjustment_movement(cls, product_id, new_quantity, reason, user_id=None, notes=None):
        """Create movement for stock adjustment"""
        return cls.create_movement(
            product_id=product_id,
            new_quantity=new_quantity,
            transaction_type='adjustment',
            user_id=user_id,
            reference_number=f"ADJ-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            notes=notes,
            reason=reason
        )
    
    @classmethod
    def create_return_movement(cls, product_id, quantity_returned, return_id, user_id=None):
        """Create movement for return transaction"""
        product = Product.query.get(product_id)
        if not product:
            raise ValueError("Product not found")
        
        new_quantity = product.current_stock + quantity_returned
        
        return cls.create_movement(
            product_id=product_id,
            new_quantity=new_quantity,
            transaction_type='return',
            user_id=user_id,
            reference_number=f"RETURN-{return_id}",
            notes=f"Return of {quantity_returned} units"
        )
    
    @classmethod
    def get_movements_summary(cls, date_from=None, date_to=None, product_id=None):
        """Get summary of movements for reporting"""
        query = cls.query
        
        if date_from:
            query = query.filter(cls.transaction_date >= date_from)
        if date_to:
            query = query.filter(cls.transaction_date <= date_to)
        if product_id:
            query = query.filter(cls.product_id == product_id)
        
        movements = query.all()
        
        summary = {
            'total_movements': len(movements),
            'total_in': sum(1 for m in movements if m.is_inbound),
            'total_out': sum(1 for m in movements if m.is_outbound),
            'total_adjustments': sum(1 for m in movements if m.transaction_type == 'adjustment'),
            'total_quantity_in': sum(m.quantity_change for m in movements if m.is_inbound),
            'total_quantity_out': abs(sum(m.quantity_change for m in movements if m.is_outbound)),
            'total_value_in': sum(m.total_cost for m in movements if m.is_inbound and m.total_cost),
            'total_value_out': sum(m.total_cost for m in movements if m.is_outbound and m.total_cost),
            'by_type': {}
        }
        
        # Group by transaction type
        for movement in movements:
            if movement.transaction_type not in summary['by_type']:
                summary['by_type'][movement.transaction_type] = {
                    'count': 0,
                    'quantity': 0,
                    'value': 0
                }
            
            summary['by_type'][movement.transaction_type]['count'] += 1
            summary['by_type'][movement.transaction_type]['quantity'] += abs(movement.quantity_change)
            if movement.total_cost:
                summary['by_type'][movement.transaction_type]['value'] += movement.total_cost
        
        return summary
    
    @classmethod
    def get_product_movement_history(cls, product_id, limit=50):
        """Get movement history for a specific product"""
        return cls.query.filter_by(product_id=product_id)\
                       .order_by(cls.transaction_date.desc())\
                       .limit(limit).all()
    
    @classmethod
    def get_recent_movements(cls, limit=10):
        """Get recent movements across all products"""
        return cls.query.order_by(cls.transaction_date.desc())\
                       .limit(limit).all()
    
    def to_dict(self):
        """Convert movement to dictionary for API responses"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.get_name() if self.product else None,
            'product_sku': self.product.sku if self.product else None,
            'old_quantity': self.old_quantity,
            'new_quantity': self.new_quantity,
            'quantity_change': self.quantity_change,
            'transaction_type': self.transaction_type,
            'transaction_date': self.transaction_date.isoformat(),
            'reference_number': self.reference_number,
            'user_id': self.user_id,
            'user_name': self.user.get_full_name() if self.user else None,
            'notes': self.notes,
            'reason': self.reason,
            'unit_cost': float(self.unit_cost) if self.unit_cost else None,
            'total_cost': float(self.total_cost) if self.total_cost else None,
            'location': self.location,
            'movement_direction': self.movement_direction,
            'created_at': self.created_at.isoformat()
        }

# Event listeners for automatic movement tracking
@event.listens_for(Product.current_stock, 'set')
def track_stock_changes(target, value, oldvalue, initiator):
    """Automatically track stock changes (for manual updates)"""
    # This will be triggered when current_stock is directly modified
    # We can use this for additional validation or logging
    pass
