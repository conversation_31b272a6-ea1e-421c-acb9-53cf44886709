{% extends "base.html" %}

{% block title %}
{{ 'التقارير والتحليلات - نظام نقاط البيع القطري' if language == 'ar' else 'Reports & Analytics - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-graph-up"></i>
            {{ 'التقارير والتحليلات' if language == 'ar' else 'Reports & Analytics' }}
        </h1>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'مبيعات اليوم' if language == 'ar' else "Today's Sales" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(today_stats.sales) if language == 'ar' else 'QAR {:,.2f}'.format(today_stats.sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'معاملات اليوم' if language == 'ar' else "Today's Transactions" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ today_stats.transactions }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'مبيعات الشهر' if language == 'ar' else "This Month's Sales" }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.0f} ر.ق'.format(month_stats.sales) if language == 'ar' else 'QAR {:,.0f}'.format(month_stats.sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'إجمالي العملاء' if language == 'ar' else 'Total Customers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_customers }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row">
    <!-- Sales Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-cart"></i>
                    {{ 'تقارير المبيعات' if language == 'ar' else 'Sales Reports' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.sales_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-graph-up"></i>
                        {{ 'تقرير المبيعات' if language == 'ar' else 'Sales Report' }}
                    </a>
                    <a href="{{ url_for('reports.daily_sales') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-day"></i>
                        {{ 'المبيعات اليومية' if language == 'ar' else 'Daily Sales' }}
                    </a>
                    <a href="{{ url_for('reports.monthly_sales') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-month"></i>
                        {{ 'المبيعات الشهرية' if language == 'ar' else 'Monthly Sales' }}
                    </a>
                    <a href="{{ url_for('reports.sales_by_payment') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-credit-card"></i>
                        {{ 'المبيعات حسب طريقة الدفع' if language == 'ar' else 'Sales by Payment Method' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-box"></i>
                    {{ 'تقارير المنتجات' if language == 'ar' else 'Product Reports' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.products_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-graph-up"></i>
                        {{ 'أداء المنتجات' if language == 'ar' else 'Product Performance' }}
                    </a>
                    <a href="{{ url_for('reports.top_selling') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-trophy"></i>
                        {{ 'الأكثر مبيعاً' if language == 'ar' else 'Top Selling' }}
                    </a>
                    <a href="{{ url_for('reports.slow_moving') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock"></i>
                        {{ 'بطيئة الحركة' if language == 'ar' else 'Slow Moving' }}
                    </a>
                    <a href="{{ url_for('reports.profit_analysis') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-currency-dollar"></i>
                        {{ 'تحليل الأرباح' if language == 'ar' else 'Profit Analysis' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-boxes"></i>
                    {{ 'تقارير المخزون' if language == 'ar' else 'Inventory Reports' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.inventory_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-clipboard-data"></i>
                        {{ 'حالة المخزون' if language == 'ar' else 'Inventory Status' }}
                    </a>
                    <a href="{{ url_for('reports.stock_valuation') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-calculator"></i>
                        {{ 'تقييم المخزون' if language == 'ar' else 'Stock Valuation' }}
                    </a>
                    <a href="{{ url_for('reports.stock_movement') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-arrow-left-right"></i>
                        {{ 'حركة المخزون' if language == 'ar' else 'Stock Movement' }}
                    </a>
                    <a href="{{ url_for('reports.reorder_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-exclamation-triangle"></i>
                        {{ 'تقرير إعادة الطلب' if language == 'ar' else 'Reorder Report' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-people"></i>
                    {{ 'تقارير العملاء' if language == 'ar' else 'Customer Reports' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.customers_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-check"></i>
                        {{ 'تحليل العملاء' if language == 'ar' else 'Customer Analysis' }}
                    </a>
                    <a href="{{ url_for('reports.customer_purchases') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-cart-check"></i>
                        {{ 'مشتريات العملاء' if language == 'ar' else 'Customer Purchases' }}
                    </a>
                    <a href="{{ url_for('reports.customer_loyalty') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-heart"></i>
                        {{ 'ولاء العملاء' if language == 'ar' else 'Customer Loyalty' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Reports -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up-arrow"></i>
                    {{ 'التقارير المالية' if language == 'ar' else 'Financial Reports' }}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.profit_loss') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-graph-up"></i>
                        {{ 'الأرباح والخسائر' if language == 'ar' else 'Profit & Loss' }}
                    </a>
                    <a href="{{ url_for('reports.cash_flow') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-cash-stack"></i>
                        {{ 'التدفق النقدي' if language == 'ar' else 'Cash Flow' }}
                    </a>
                    <a href="{{ url_for('reports.tax_report') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-receipt"></i>
                        {{ 'التقرير الضريبي' if language == 'ar' else 'Tax Report' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Report Generator -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning"></i>
                    {{ 'مولد التقارير السريع' if language == 'ar' else 'Quick Report Generator' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('reports.quick_report') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">{{ 'نوع التقرير' if language == 'ar' else 'Report Type' }}</label>
                        <select class="form-select" name="report_type" required>
                            <option value="">{{ 'اختر نوع التقرير' if language == 'ar' else 'Select Report Type' }}</option>
                            <option value="sales">{{ 'المبيعات' if language == 'ar' else 'Sales' }}</option>
                            <option value="products">{{ 'المنتجات' if language == 'ar' else 'Products' }}</option>
                            <option value="inventory">{{ 'المخزون' if language == 'ar' else 'Inventory' }}</option>
                            <option value="customers">{{ 'العملاء' if language == 'ar' else 'Customers' }}</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                        <input type="date" class="form-control" name="date_from" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                        <input type="date" class="form-control" name="date_to" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">{{ 'التنسيق' if language == 'ar' else 'Format' }}</label>
                        <select class="form-select" name="format">
                            <option value="html">{{ 'عرض' if language == 'ar' else 'View' }}</option>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-play-circle"></i>
                                {{ 'إنشاء التقرير' if language == 'ar' else 'Generate Report' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.querySelector('input[name="date_from"]').value = firstDayOfMonth.toISOString().split('T')[0];
    document.querySelector('input[name="date_to"]').value = today.toISOString().split('T')[0];
});
</script>
{% endblock %}
