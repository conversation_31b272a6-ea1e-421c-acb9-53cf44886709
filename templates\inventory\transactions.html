{% extends "base.html" %}

{% block title %}
{{ 'حركات المخزون - نظام نقاط البيع القطري' if language == 'ar' else 'Inventory Transactions - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-arrow-left-right"></i>
                {{ 'حركات المخزون' if language == 'ar' else 'Inventory Transactions' }}
            </h1>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة للمخزون' if language == 'ar' else 'Back to Inventory' }}
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{{ 'المنتج' if language == 'ar' else 'Product' }}</label>
                <select class="form-select" name="product_id">
                    <option value="">{{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" {{ 'selected' if product.id == product_id }}>
                        {{ product.get_name(language) }} ({{ product.sku }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'نوع الحركة' if language == 'ar' else 'Transaction Type' }}</label>
                <select class="form-select" name="transaction_type">
                    <option value="">{{ 'جميع الأنواع' if language == 'ar' else 'All Types' }}</option>
                    <option value="sale" {{ 'selected' if transaction_type == 'sale' }}>
                        {{ 'بيع' if language == 'ar' else 'Sale' }}
                    </option>
                    <option value="purchase" {{ 'selected' if transaction_type == 'purchase' }}>
                        {{ 'شراء' if language == 'ar' else 'Purchase' }}
                    </option>
                    <option value="adjustment" {{ 'selected' if transaction_type == 'adjustment' }}>
                        {{ 'تعديل' if language == 'ar' else 'Adjustment' }}
                    </option>
                    <option value="return" {{ 'selected' if transaction_type == 'return' }}>
                        {{ 'إرجاع' if language == 'ar' else 'Return' }}
                    </option>
                    <option value="damage" {{ 'selected' if transaction_type == 'damage' }}>
                        {{ 'تلف' if language == 'ar' else 'Damage' }}
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        {{ 'بحث' if language == 'ar' else 'Search' }}
                    </button>
                    <a href="{{ url_for('inventory.transactions') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </a>
                    <button type="button" class="btn btn-outline-success" onclick="exportTransactions()">
                        <i class="bi bi-download"></i>
                        {{ 'تصدير' if language == 'ar' else 'Export' }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Transaction Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي الحركات' if language == 'ar' else 'Total Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ transactions.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-arrow-left-right fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'حركات الإضافة' if language == 'ar' else 'Stock In' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ transactions.items | selectattr('quantity_change', 'gt', 0) | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-plus-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {{ 'حركات الخصم' if language == 'ar' else 'Stock Out' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ transactions.items | selectattr('quantity_change', 'lt', 0) | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-dash-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'صافي التغيير' if language == 'ar' else 'Net Change' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set net_change = transactions.items | sum(attribute='quantity_change') %}
                            {% if net_change > 0 %}+{% endif %}{{ net_change }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            {{ 'قائمة حركات المخزون' if language == 'ar' else 'Inventory Transactions List' }}
            <span class="badge bg-primary">{{ transactions.total }}</span>
        </h6>
    </div>
    <div class="card-body p-0">
        {% if transactions.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{{ 'التاريخ والوقت' if language == 'ar' else 'Date & Time' }}</th>
                        <th>{{ 'المنتج' if language == 'ar' else 'Product' }}</th>
                        <th>{{ 'نوع الحركة' if language == 'ar' else 'Type' }}</th>
                        <th>{{ 'التغيير' if language == 'ar' else 'Change' }}</th>
                        <th>{{ 'المخزون السابق' if language == 'ar' else 'Previous Stock' }}</th>
                        <th>{{ 'المخزون الجديد' if language == 'ar' else 'New Stock' }}</th>
                        <th>{{ 'المستخدم' if language == 'ar' else 'User' }}</th>
                        <th>{{ 'المرجع' if language == 'ar' else 'Reference' }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions.items %}
                    <tr>
                        <td>
                            <div>{{ transaction.transaction_date.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ transaction.transaction_date.strftime('%H:%M:%S') }}</small>
                        </td>
                        <td>
                            <div>
                                <strong>{{ transaction.product.get_name(language) }}</strong>
                                <br>
                                <small class="text-muted">{{ transaction.product.sku }}</small>
                            </div>
                        </td>
                        <td>
                            {% if transaction.transaction_type == 'sale' %}
                            <span class="badge bg-primary">{{ 'بيع' if language == 'ar' else 'Sale' }}</span>
                            {% elif transaction.transaction_type == 'purchase' %}
                            <span class="badge bg-success">{{ 'شراء' if language == 'ar' else 'Purchase' }}</span>
                            {% elif transaction.transaction_type == 'adjustment' %}
                            <span class="badge bg-warning">{{ 'تعديل' if language == 'ar' else 'Adjustment' }}</span>
                            {% elif transaction.transaction_type == 'return' %}
                            <span class="badge bg-info">{{ 'إرجاع' if language == 'ar' else 'Return' }}</span>
                            {% elif transaction.transaction_type == 'damage' %}
                            <span class="badge bg-danger">{{ 'تلف' if language == 'ar' else 'Damage' }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ transaction.transaction_type.title() }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if transaction.quantity_change > 0 else 'danger' }}">
                                {% if transaction.quantity_change > 0 %}+{% endif %}{{ transaction.quantity_change }}
                            </span>
                        </td>
                        <td>{{ transaction.old_quantity }}</td>
                        <td>
                            <strong>{{ transaction.new_quantity }}</strong>
                        </td>
                        <td>
                            {% if transaction.user %}
                            {{ transaction.user.get_full_name(language) }}
                            {% else %}
                            <span class="text-muted">{{ 'النظام' if language == 'ar' else 'System' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if transaction.reference_type and transaction.reference_id %}
                            <a href="#" class="text-decoration-none">
                                {{ transaction.reference_type }}#{{ transaction.reference_id }}
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if transactions.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Transactions pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if transactions.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.transactions', page=transactions.prev_num, product_id=product_id, transaction_type=transaction_type, date_from=date_from, date_to=date_to) }}">
                            {{ 'السابق' if language == 'ar' else 'Previous' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in transactions.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != transactions.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory.transactions', page=page_num, product_id=product_id, transaction_type=transaction_type, date_from=date_from, date_to=date_to) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if transactions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.transactions', page=transactions.next_num, product_id=product_id, transaction_type=transaction_type, date_from=date_from, date_to=date_to) }}">
                            {{ 'التالي' if language == 'ar' else 'Next' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-arrow-left-right display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد حركات مخزون' if language == 'ar' else 'No inventory transactions found' }}</h5>
            <p class="text-muted">
                {% if product_id or transaction_type or date_from or date_to %}
                {{ 'جرب تغيير معايير البحث' if language == 'ar' else 'Try changing your search criteria' }}
                {% else %}
                {{ 'ستظهر حركات المخزون هنا عند حدوثها' if language == 'ar' else 'Inventory transactions will appear here when they occur' }}
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.querySelectorAll('select[name="product_id"], select[name="transaction_type"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

function exportTransactions() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    
    // Create download link
    const url = `${window.location.pathname}?${params.toString()}`;
    window.open(url, '_blank');
}

// Date range validation
document.querySelector('input[name="date_from"]').addEventListener('change', function() {
    const dateFrom = this.value;
    const dateTo = document.querySelector('input[name="date_to"]').value;
    
    if (dateFrom && dateTo && dateFrom > dateTo) {
        document.querySelector('input[name="date_to"]').value = dateFrom;
    }
});

document.querySelector('input[name="date_to"]').addEventListener('change', function() {
    const dateTo = this.value;
    const dateFrom = document.querySelector('input[name="date_from"]').value;
    
    if (dateFrom && dateTo && dateTo < dateFrom) {
        document.querySelector('input[name="date_from"]').value = dateTo;
    }
});
</script>
{% endblock %}
