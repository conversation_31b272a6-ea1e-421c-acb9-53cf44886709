#!/usr/bin/env python3
"""
Qatar POS System - Setup Script
This script helps set up the development environment
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def print_header():
    """Print setup header"""
    print("🇶🇦 نظام نقاط البيع القطري | Qatar POS System")
    print("=" * 60)
    print("🚀 Setting up development environment...")
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")

def install_requirements():
    """Install Python requirements"""
    print("\n📦 Installing Python packages...")
    
    # Basic requirements for development
    basic_requirements = [
        'Flask>=2.3.0',
        'Flask-SQLAlchemy>=3.0.0',
        'Flask-Login>=0.6.0',
        'Flask-Migrate>=4.0.0',
        'Flask-WTF>=1.1.0',
        'Werkzeug>=2.3.0',
        'WTForms>=3.0.0',
        'python-dotenv>=1.0.0',
        'Babel>=2.12.0',
        'Pillow>=10.0.0',
        'qrcode>=7.4.0',
        'reportlab>=4.0.0',
        'openpyxl>=3.1.0',
        'redis>=4.5.0'
    ]
    
    for package in basic_requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        'static/css',
        'static/js',
        'static/images',
        'static/uploads',
        'uploads/logos',
        'uploads/products',
        'logs',
        'backups',
        'migrations',
        'instance'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}")

def create_env_file():
    """Create .env file if it doesn't exist"""
    print("\n⚙️ Setting up environment file...")
    
    if not os.path.exists('.env'):
        env_content = """# Qatar POS System - Development Environment
SECRET_KEY=dev-secret-key-change-in-production
FLASK_ENV=development
FLASK_DEBUG=1

# Database
DATABASE_URL=sqlite:///qatarpos.db

# Qatar Settings
DEFAULT_LANGUAGE=ar
DEFAULT_CURRENCY=QAR
DEFAULT_TIMEZONE=Asia/Qatar

# Upload Settings
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Development Settings
TESTING=False
"""
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ Created .env file")
    else:
        print("✅ .env file already exists")

def setup_database():
    """Set up SQLite database for development"""
    print("\n🗄️ Setting up database...")
    
    try:
        # Create database file
        db_path = 'qatarpos.db'
        if not os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            conn.close()
            print(f"✅ Created database file: {db_path}")
        else:
            print(f"✅ Database file already exists: {db_path}")
            
    except Exception as e:
        print(f"❌ Database setup error: {e}")

def create_test_files():
    """Create basic test files"""
    print("\n🧪 Creating test files...")
    
    # Create a simple test HTML template
    template_dir = Path('templates')
    template_dir.mkdir(exist_ok=True)
    
    test_template = template_dir / 'test.html'
    if not test_template.exists():
        test_content = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري - اختبار</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-size: 1.2em; }
        .info { color: #17a2b8; }
        h1 { color: #333; text-align: center; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <p class="success">✅ النظام جاهز للعمل!</p>
        
        <div class="features">
            <div class="feature">
                <h3>🛒 نقطة البيع</h3>
                <p>واجهة سهلة لإجراء المبيعات</p>
            </div>
            <div class="feature">
                <h3>📦 إدارة المخزون</h3>
                <p>تتبع المنتجات والكميات</p>
            </div>
            <div class="feature">
                <h3>👥 إدارة العملاء</h3>
                <p>قاعدة بيانات العملاء</p>
            </div>
            <div class="feature">
                <h3>📊 التقارير</h3>
                <p>تقارير مفصلة للمبيعات</p>
            </div>
        </div>
        
        <div class="info">
            <p><strong>للبدء:</strong></p>
            <ol>
                <li>تشغيل الخادم: <code>python run.py</code></li>
                <li>فتح المتصفح: <code>http://localhost:5000</code></li>
                <li>تسجيل الدخول: admin / admin123</li>
            </ol>
        </div>
    </div>
</body>
</html>"""
        test_template.write_text(test_content, encoding='utf-8')
        print("✅ Created test template")

def print_instructions():
    """Print final instructions"""
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run the development server:")
    print("   python run.py")
    print("\n2. Open your browser and go to:")
    print("   http://localhost:5000")
    print("\n3. Test page available at:")
    print("   http://localhost:5000/test")
    print("\n4. Default login credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("\n🇶🇦 مرحباً بك في نظام نقاط البيع القطري!")
    print("=" * 60)

def main():
    """Main setup function"""
    print_header()
    
    try:
        check_python_version()
        install_requirements()
        create_directories()
        create_env_file()
        setup_database()
        create_test_files()
        print_instructions()
        
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
