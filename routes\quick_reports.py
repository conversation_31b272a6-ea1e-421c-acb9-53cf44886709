"""
مولد التقارير السريع - Qatar POS System
Quick Reports Generator for instant business insights
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from extensions import db
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer
from models.category import Category
from models.supplier import Supplier
from sqlalchemy import func, desc, asc, and_, or_
from datetime import datetime, timedelta
from decimal import Decimal
import json

quick_reports_bp = Blueprint('quick_reports', __name__)

@quick_reports_bp.route('/')
@login_required
def index():
    """صفحة مولد التقارير السريع الرئيسية"""
    return render_template('quick_reports/index.html')

@quick_reports_bp.route('/generate', methods=['POST'])
@login_required
def generate_report():
    """توليد تقرير سريع حسب المعايير المحددة"""
    try:
        data = request.get_json()
        report_type = data.get('report_type')
        date_range = data.get('date_range', 'today')
        filters = data.get('filters', {})
        
        # تحديد نطاق التاريخ
        start_date, end_date = get_date_range(date_range)
        
        # توليد التقرير حسب النوع
        if report_type == 'sales_summary':
            result = generate_sales_summary(start_date, end_date, filters)
        elif report_type == 'top_products':
            result = generate_top_products(start_date, end_date, filters)
        elif report_type == 'customer_activity':
            result = generate_customer_activity(start_date, end_date, filters)
        elif report_type == 'inventory_status':
            result = generate_inventory_status(filters)
        elif report_type == 'payment_methods':
            result = generate_payment_methods(start_date, end_date, filters)
        elif report_type == 'hourly_sales':
            result = generate_hourly_sales(start_date, end_date, filters)
        elif report_type == 'category_performance':
            result = generate_category_performance(start_date, end_date, filters)
        else:
            return jsonify({'error': 'نوع التقرير غير مدعوم'}), 400
        
        return jsonify({
            'success': True,
            'data': result,
            'generated_at': datetime.now().isoformat(),
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في توليد التقرير: {str(e)}'}), 500

def get_date_range(range_type):
    """تحديد نطاق التاريخ حسب النوع"""
    now = datetime.now()
    
    if range_type == 'today':
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif range_type == 'yesterday':
        yesterday = now - timedelta(days=1)
        start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif range_type == 'this_week':
        start_date = now - timedelta(days=now.weekday())
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif range_type == 'this_month':
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif range_type == 'last_7_days':
        start_date = now - timedelta(days=7)
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif range_type == 'last_30_days':
        start_date = now - timedelta(days=30)
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    else:
        # افتراضي: اليوم
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    return start_date, end_date

def generate_sales_summary(start_date, end_date, filters):
    """توليد ملخص المبيعات السريع"""
    # إجمالي المبيعات
    total_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    
    # عدد المعاملات
    transaction_count = Sale.query.filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).count()
    
    # متوسط قيمة المعاملة
    avg_transaction = float(total_sales) / transaction_count if transaction_count > 0 else 0
    
    # عدد العملاء الفريدين
    unique_customers = db.session.query(func.count(func.distinct(Sale.customer_id))).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date,
        Sale.customer_id.isnot(None)
    ).scalar() or 0
    
    # إجمالي الكمية المباعة
    total_quantity = db.session.query(func.sum(SaleItem.quantity)).select_from(SaleItem).join(
        Sale, SaleItem.sale_id == Sale.id
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).scalar() or 0
    
    return {
        'total_sales': float(total_sales),
        'transaction_count': transaction_count,
        'avg_transaction': avg_transaction,
        'unique_customers': unique_customers,
        'total_quantity': float(total_quantity)
    }

def generate_top_products(start_date, end_date, filters):
    """توليد تقرير أفضل المنتجات مبيعاً"""
    limit = filters.get('limit', 10)
    
    top_products = db.session.query(
        Product.id,
        Product.name_ar,
        Product.name_en,
        func.sum(SaleItem.quantity).label('total_sold'),
        func.sum(SaleItem.total_price).label('total_revenue')
    ).select_from(Product).join(
        SaleItem, Product.id == SaleItem.product_id
    ).join(
        Sale, SaleItem.sale_id == Sale.id
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(
        Product.id, Product.name_ar, Product.name_en
    ).order_by(
        func.sum(SaleItem.quantity).desc()
    ).limit(limit).all()
    
    return [
        {
            'product_id': product.id,
            'name_ar': product.name_ar,
            'name_en': product.name_en,
            'quantity_sold': float(product.total_sold),
            'revenue': float(product.total_revenue)
        }
        for product in top_products
    ]

def generate_customer_activity(start_date, end_date, filters):
    """توليد تقرير نشاط العملاء"""
    limit = filters.get('limit', 10)

    customer_activity = db.session.query(
        Customer.id,
        Customer.customer_code,
        Customer.first_name_ar,
        Customer.last_name_ar,
        Customer.first_name_en,
        Customer.last_name_en,
        Customer.company_name_ar,
        Customer.company_name_en,
        Customer.customer_type,
        func.count(Sale.id).label('purchase_count'),
        func.sum(Sale.total_amount).label('total_spent'),
        func.max(Sale.sale_date).label('last_purchase')
    ).select_from(Customer).join(
        Sale, Customer.id == Sale.customer_id
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(
        Customer.id, Customer.customer_code, Customer.first_name_ar, Customer.last_name_ar,
        Customer.first_name_en, Customer.last_name_en, Customer.company_name_ar,
        Customer.company_name_en, Customer.customer_type
    ).order_by(
        func.sum(Sale.total_amount).desc()
    ).limit(limit).all()

    return [
        {
            'customer_id': customer.id,
            'customer_code': customer.customer_code,
            'name': get_customer_display_name(customer),
            'purchase_count': customer.purchase_count,
            'total_spent': float(customer.total_spent),
            'last_purchase': customer.last_purchase.isoformat() if customer.last_purchase else None
        }
        for customer in customer_activity
    ]

def get_customer_display_name(customer):
    """الحصول على اسم العميل للعرض"""
    if customer.customer_type == 'company':
        return customer.company_name_ar or customer.company_name_en or customer.customer_code
    else:
        # للأفراد
        name_ar = f"{customer.first_name_ar or ''} {customer.last_name_ar or ''}".strip()
        name_en = f"{customer.first_name_en or ''} {customer.last_name_en or ''}".strip()
        return name_ar or name_en or customer.customer_code

def generate_inventory_status(filters):
    """توليد تقرير حالة المخزون"""
    # المنتجات منخفضة المخزون
    low_stock = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.minimum_stock.isnot(None),
        Product.is_active == True
    ).limit(20).all()
    
    # المنتجات نفدت من المخزون
    out_of_stock = Product.query.filter(
        Product.current_stock <= 0,
        Product.is_active == True
    ).limit(20).all()
    
    # إجمالي قيمة المخزون
    total_inventory_value = db.session.query(
        func.sum(Product.current_stock * Product.cost_price)
    ).filter(
        Product.cost_price.isnot(None),
        Product.is_active == True
    ).scalar() or 0
    
    return {
        'low_stock_products': [
            {
                'id': product.id,
                'name_ar': product.name_ar,
                'name_en': product.name_en,
                'current_stock': float(product.current_stock or 0),
                'minimum_stock': float(product.minimum_stock or 0)
            }
            for product in low_stock
        ],
        'out_of_stock_products': [
            {
                'id': product.id,
                'name_ar': product.name_ar,
                'name_en': product.name_en,
                'current_stock': float(product.current_stock or 0)
            }
            for product in out_of_stock
        ],
        'total_inventory_value': float(total_inventory_value)
    }

def generate_payment_methods(start_date, end_date, filters):
    """توليد تقرير طرق الدفع"""
    payment_methods = db.session.query(
        Sale.payment_method,
        func.count(Sale.id).label('transaction_count'),
        func.sum(Sale.total_amount).label('total_amount')
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(Sale.payment_method).order_by(
        func.sum(Sale.total_amount).desc()
    ).all()
    
    # حساب إجمالي المبلغ لحساب النسب المئوية
    total_sum = sum(float(m.total_amount) for m in payment_methods) if payment_methods else 0

    return [
        {
            'payment_method': method.payment_method,
            'transaction_count': method.transaction_count,
            'total_amount': float(method.total_amount),
            'percentage': (float(method.total_amount) / total_sum) * 100 if total_sum > 0 else 0
        }
        for method in payment_methods
    ]

def generate_hourly_sales(start_date, end_date, filters):
    """توليد تقرير المبيعات بالساعة"""
    hourly_sales = db.session.query(
        func.extract('hour', Sale.sale_date).label('hour'),
        func.count(Sale.id).label('transaction_count'),
        func.sum(Sale.total_amount).label('total_amount')
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(
        func.extract('hour', Sale.sale_date)
    ).order_by('hour').all()
    
    return [
        {
            'hour': int(hour.hour),
            'transaction_count': hour.transaction_count,
            'total_amount': float(hour.total_amount)
        }
        for hour in hourly_sales
    ]

def generate_category_performance(start_date, end_date, filters):
    """توليد تقرير أداء الفئات"""
    category_performance = db.session.query(
        Category.id,
        Category.name_ar,
        Category.name_en,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.total_price).label('total_revenue')
    ).select_from(Category).join(
        Product, Category.id == Product.category_id
    ).join(
        SaleItem, Product.id == SaleItem.product_id
    ).join(
        Sale, SaleItem.sale_id == Sale.id
    ).filter(
        Sale.status == 'completed',
        Sale.sale_date >= start_date,
        Sale.sale_date <= end_date
    ).group_by(
        Category.id, Category.name_ar, Category.name_en
    ).order_by(
        func.sum(SaleItem.total_price).desc()
    ).all()
    
    return [
        {
            'category_id': category.id,
            'name_ar': category.name_ar,
            'name_en': category.name_en,
            'total_quantity': float(category.total_quantity),
            'total_revenue': float(category.total_revenue)
        }
        for category in category_performance
    ]

@quick_reports_bp.route('/export/<report_type>')
@login_required
def export_report(report_type):
    """تصدير التقرير السريع"""
    # هذه الوظيفة يمكن تطويرها لاحقاً لتصدير التقارير بصيغ مختلفة
    flash('ميزة التصدير قيد التطوير', 'info')
    return redirect(url_for('quick_reports.index'))
