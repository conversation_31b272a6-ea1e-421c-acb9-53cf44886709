"""
Sales models for Qatar POS System
Handles sales transactions, items, and payments with Qatar Tax Authority compliance
"""

from datetime import datetime
from decimal import Decimal
from extensions import db

class Sale(db.Model):
    """Main sales transaction model"""
    
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Sale identification
    sale_number = db.Column(db.String(20), unique=True, nullable=False, index=True)
    
    # Customer and seller
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    seller_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Sale amounts
    subtotal = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    discount_amount = db.Column(db.Numeric(10, 2), default=0)
    tax_amount = db.Column(db.Numeric(10, 2), default=0)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    
    # Payment information
    payment_method = db.Column(db.Enum('cash', 'card', 'bank_transfer', 'credit', 
                                      name='payment_methods'), 
                              nullable=False, default='cash')
    payment_status = db.Column(db.Enum('paid', 'partial', 'pending', 'refunded',
                                      name='payment_statuses'),
                              nullable=False, default='paid')
    amount_paid = db.Column(db.Numeric(10, 2), default=0)
    amount_due = db.Column(db.Numeric(10, 2), default=0)
    
    # Sale status
    status = db.Column(db.Enum('completed', 'pending', 'cancelled', 'refunded',
                              name='sale_statuses'),
                      nullable=False, default='completed')
    
    # Qatar Tax Authority fields
    tax_invoice_number = db.Column(db.String(50))
    qr_code_data = db.Column(db.Text)
    
    # Notes and references
    notes = db.Column(db.Text)
    reference_number = db.Column(db.String(50))
    
    # Timestamps
    sale_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    items = db.relationship('SaleItem', backref='sale', lazy='dynamic', 
                           cascade='all, delete-orphan')
    payments = db.relationship('Payment', backref='sale', lazy='dynamic',
                              cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """Calculate sale totals from items"""
        self.subtotal = sum(item.total_price for item in self.items)
        
        # Calculate tax if applicable
        taxable_amount = sum(item.total_price for item in self.items 
                           if item.product.is_taxable)
        self.tax_amount = taxable_amount * Decimal('0.0')  # Qatar VAT rate
        
        # Calculate total
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        
        # Update amount due
        self.amount_due = self.total_amount - self.amount_paid
        
        return self.total_amount
    
    def add_item(self, product, quantity, unit_price=None):
        """Add item to sale"""
        if unit_price is None:
            unit_price = product.get_final_price()
        
        # Check if item already exists
        existing_item = self.items.filter_by(product_id=product.id).first()
        if existing_item:
            existing_item.quantity += quantity
            existing_item.calculate_total()
        else:
            item = SaleItem(
                sale_id=self.id,
                product_id=product.id,
                quantity=quantity,
                unit_price=unit_price
            )
            item.calculate_total()
            db.session.add(item)
        
        self.calculate_totals()
        return True
    
    def remove_item(self, product_id):
        """Remove item from sale"""
        item = self.items.filter_by(product_id=product_id).first()
        if item:
            db.session.delete(item)
            self.calculate_totals()
            return True
        return False
    
    def apply_discount(self, discount_amount):
        """Apply discount to sale"""
        self.discount_amount = discount_amount
        self.calculate_totals()
    
    def process_payment(self, amount, method='cash'):
        """Process payment for sale"""
        payment = Payment(
            sale_id=self.id,
            amount=amount,
            payment_method=method,
            payment_date=datetime.utcnow()
        )
        db.session.add(payment)
        
        self.amount_paid += amount
        self.amount_due = self.total_amount - self.amount_paid
        
        if self.amount_due <= 0:
            self.payment_status = 'paid'
        elif self.amount_paid > 0:
            self.payment_status = 'partial'
        
        return payment
    
    def generate_qr_code(self):
        """Generate QR code data for Qatar Tax Authority"""
        # QR code format for Qatar Tax Authority
        qr_data = {
            'seller_name': 'Business Name',  # From config
            'tax_number': 'TAX123456789',    # From config
            'invoice_date': self.sale_date.isoformat(),
            'total_amount': str(self.total_amount),
            'tax_amount': str(self.tax_amount)
        }
        
        import json
        self.qr_code_data = json.dumps(qr_data)
        return self.qr_code_data
    
    def to_dict(self, language='ar'):
        """Convert sale to dictionary"""
        return {
            'id': self.id,
            'sale_number': self.sale_number,
            'customer_name': self.customer.get_display_name(language) if self.customer else 'Walk-in Customer',
            'seller_name': self.seller.get_full_name(language),
            'subtotal': float(self.subtotal),
            'discount_amount': float(self.discount_amount),
            'tax_amount': float(self.tax_amount),
            'total_amount': float(self.total_amount),
            'payment_method': self.payment_method,
            'payment_status': self.payment_status,
            'amount_paid': float(self.amount_paid),
            'amount_due': float(self.amount_due),
            'status': self.status,
            'items_count': self.items.count(),
            'sale_date': self.sale_date.isoformat(),
            'created_at': self.created_at.isoformat()
        }
    
    @staticmethod
    def generate_sale_number():
        """Generate unique sale number"""
        from datetime import datetime
        today = datetime.now()
        prefix = f"INV{today.strftime('%Y%m%d')}"
        
        # Get last sale number for today
        last_sale = Sale.query.filter(
            Sale.sale_number.like(f"{prefix}%")
        ).order_by(Sale.sale_number.desc()).first()
        
        if last_sale:
            last_number = int(last_sale.sale_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:04d}"
    
    def get_items_count(self):
        """Get total number of items in this sale"""
        return self.items.count()

    def __repr__(self):
        return f'<Sale {self.sale_number}>'


class SaleItem(db.Model):
    """Individual items in a sale"""
    
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    total_price = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Discount on item level
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    discount_amount = db.Column(db.Numeric(10, 2), default=0)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def calculate_total(self):
        """Calculate total price for this item"""
        subtotal = self.quantity * self.unit_price
        self.total_price = subtotal - self.discount_amount
        return self.total_price
    
    def apply_discount(self, percentage=None, amount=None):
        """Apply discount to item"""
        if percentage:
            self.discount_percentage = percentage
            subtotal = self.quantity * self.unit_price
            self.discount_amount = subtotal * (percentage / 100)
        elif amount:
            self.discount_amount = amount
        
        self.calculate_total()
    
    def to_dict(self, language='ar'):
        """Convert sale item to dictionary"""
        return {
            'id': self.id,
            'product_name': self.product.get_name(language),
            'product_sku': self.product.sku,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price),
            'total_price': float(self.total_price),
            'discount_amount': float(self.discount_amount)
        }
    
    def __repr__(self):
        return f'<SaleItem {self.product.sku} x {self.quantity}>'


class Payment(db.Model):
    """Payment records for sales"""
    
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    payment_method = db.Column(db.Enum('cash', 'card', 'bank_transfer', 'credit',
                                      name='payment_methods'), 
                              nullable=False)
    
    # Payment details
    reference_number = db.Column(db.String(100))
    card_last_four = db.Column(db.String(4))
    bank_name = db.Column(db.String(100))
    
    payment_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    notes = db.Column(db.Text)
    
    def to_dict(self):
        """Convert payment to dictionary"""
        return {
            'id': self.id,
            'amount': float(self.amount),
            'payment_method': self.payment_method,
            'reference_number': self.reference_number,
            'payment_date': self.payment_date.isoformat(),
            'notes': self.notes
        }
    
    def __repr__(self):
        return f'<Payment {self.amount} {self.payment_method}>'
