"""
Excel Export Utility for Qatar POS System
مكتبة تصدير Excel لنظام نقاط البيع القطري
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from datetime import datetime
import io
from flask import make_response
import os

class ExcelExporter:
    """مصدر Excel مع دعم اللغة العربية والتنسيق المتقدم"""
    
    def __init__(self, language='ar'):
        self.language = language
        self.workbook = Workbook()
        self.worksheet = self.workbook.active
        
        # ألوان النظام
        self.colors = {
            'header': 'FF2E86AB',      # أزرق داكن
            'subheader': 'FFA23B72',   # بنفسجي
            'accent': 'FFF18F01',      # برتقالي
            'light': 'FFF5F5F5',       # رمادي فاتح
            'white': 'FFFFFFFF'        # أبيض
        }
        
        # خطوط النظام
        self.fonts = {
            'header': Font(name='Arial', size=14, bold=True, color='FFFFFF'),
            'subheader': Font(name='Arial', size=12, bold=True, color='FFFFFF'),
            'data': Font(name='Arial', size=10),
            'total': Font(name='Arial', size=11, bold=True)
        }
    
    def create_sales_report(self, sales_data, summary_data, date_range):
        """إنشاء تقرير المبيعات"""
        self.worksheet.title = "تقرير المبيعات" if self.language == 'ar' else "Sales Report"
        
        # إضافة العنوان الرئيسي
        self._add_header("تقرير المبيعات - نظام نقاط البيع القطري" if self.language == 'ar' else "Sales Report - Qatar POS System")
        
        # إضافة معلومات التاريخ
        self._add_info_section([
            ("فترة التقرير:" if self.language == 'ar' else "Report Period:", f"{date_range['start']} - {date_range['end']}"),
            ("تاريخ الإنشاء:" if self.language == 'ar' else "Generated On:", datetime.now().strftime('%Y-%m-%d %H:%M')),
            ("العملة:" if self.language == 'ar' else "Currency:", "ريال قطري (QAR)")
        ])
        
        # إضافة الملخص
        self._add_summary_section(summary_data)
        
        # إضافة بيانات المبيعات التفصيلية
        if sales_data:
            self._add_sales_data_table(sales_data)
        
        # تنسيق الورقة
        self._format_worksheet()
        
        return self.workbook
    
    def create_products_report(self, products_data, summary_data):
        """إنشاء تقرير المنتجات"""
        self.worksheet.title = "تقرير المنتجات" if self.language == 'ar' else "Products Report"
        
        # إضافة العنوان الرئيسي
        self._add_header("تقرير المنتجات والمخزون" if self.language == 'ar' else "Products & Inventory Report")
        
        # إضافة معلومات عامة
        self._add_info_section([
            ("تاريخ الإنشاء:" if self.language == 'ar' else "Generated On:", datetime.now().strftime('%Y-%m-%d %H:%M')),
            ("إجمالي المنتجات:" if self.language == 'ar' else "Total Products:", str(len(products_data))),
            ("العملة:" if self.language == 'ar' else "Currency:", "ريال قطري (QAR)")
        ])
        
        # إضافة الملخص
        self._add_summary_section(summary_data)
        
        # إضافة بيانات المنتجات
        if products_data:
            self._add_products_data_table(products_data)
        
        # تنسيق الورقة
        self._format_worksheet()
        
        return self.workbook
    
    def create_customers_report(self, customers_data, summary_data):
        """إنشاء تقرير العملاء"""
        self.worksheet.title = "تقرير العملاء" if self.language == 'ar' else "Customers Report"
        
        # إضافة العنوان الرئيسي
        self._add_header("تقرير العملاء ونشاط الشراء" if self.language == 'ar' else "Customers & Purchase Activity Report")
        
        # إضافة معلومات عامة
        self._add_info_section([
            ("تاريخ الإنشاء:" if self.language == 'ar' else "Generated On:", datetime.now().strftime('%Y-%m-%d %H:%M')),
            ("إجمالي العملاء:" if self.language == 'ar' else "Total Customers:", str(len(customers_data))),
            ("العملة:" if self.language == 'ar' else "Currency:", "ريال قطري (QAR)")
        ])
        
        # إضافة الملخص
        self._add_summary_section(summary_data)
        
        # إضافة بيانات العملاء
        if customers_data:
            self._add_customers_data_table(customers_data)
        
        # تنسيق الورقة
        self._format_worksheet()
        
        return self.workbook
    
    def create_inventory_report(self, inventory_data, summary_data):
        """إنشاء تقرير المخزون"""
        self.worksheet.title = "تقرير المخزون" if self.language == 'ar' else "Inventory Report"
        
        # إضافة العنوان الرئيسي
        self._add_header("تقرير حالة المخزون" if self.language == 'ar' else "Inventory Status Report")
        
        # إضافة معلومات عامة
        self._add_info_section([
            ("تاريخ الإنشاء:" if self.language == 'ar' else "Generated On:", datetime.now().strftime('%Y-%m-%d %H:%M')),
            ("إجمالي المنتجات:" if self.language == 'ar' else "Total Products:", str(len(inventory_data))),
            ("العملة:" if self.language == 'ar' else "Currency:", "ريال قطري (QAR)")
        ])
        
        # إضافة الملخص
        self._add_summary_section(summary_data)
        
        # إضافة بيانات المخزون
        if inventory_data:
            self._add_inventory_data_table(inventory_data)
        
        # تنسيق الورقة
        self._format_worksheet()
        
        return self.workbook
    
    def _add_header(self, title):
        """إضافة العنوان الرئيسي"""
        self.current_row = 1
        cell = self.worksheet.cell(row=self.current_row, column=1, value=title)
        cell.font = self.fonts['header']
        cell.fill = PatternFill(start_color=self.colors['header'], end_color=self.colors['header'], fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')

        # تطبيق نفس التنسيق على الخلايا المجاورة بدلاً من الدمج
        for col in range(2, 9):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['header']
            cell.fill = PatternFill(start_color=self.colors['header'], end_color=self.colors['header'], fill_type='solid')

        self.current_row += 2
    
    def _add_info_section(self, info_items):
        """إضافة قسم المعلومات العامة"""
        for label, value in info_items:
            self.worksheet.cell(row=self.current_row, column=1, value=label).font = self.fonts['total']
            self.worksheet.cell(row=self.current_row, column=2, value=value).font = self.fonts['data']
            self.current_row += 1
        self.current_row += 1
    
    def _add_summary_section(self, summary_data):
        """إضافة قسم الملخص"""
        # عنوان الملخص
        cell = self.worksheet.cell(row=self.current_row, column=1, value="الملخص" if self.language == 'ar' else "Summary")
        cell.font = self.fonts['subheader']
        cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        # تطبيق نفس التنسيق على الخلايا المجاورة
        for col in range(2, 5):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        self.current_row += 1

        # بيانات الملخص
        for key, value in summary_data.items():
            self.worksheet.cell(row=self.current_row, column=1, value=key).font = self.fonts['data']
            self.worksheet.cell(row=self.current_row, column=2, value=value).font = self.fonts['total']
            self.current_row += 1
        self.current_row += 1
    
    def _add_sales_data_table(self, sales_data):
        """إضافة جدول بيانات المبيعات"""
        # عنوان الجدول
        cell = self.worksheet.cell(row=self.current_row, column=1, value="تفاصيل المبيعات" if self.language == 'ar' else "Sales Details")
        cell.font = self.fonts['subheader']
        cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        # تطبيق نفس التنسيق على الخلايا المجاورة
        for col in range(2, 9):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        self.current_row += 1
        
        # رؤوس الأعمدة
        headers = [
            "رقم الفاتورة" if self.language == 'ar' else "Invoice No",
            "التاريخ" if self.language == 'ar' else "Date",
            "العميل" if self.language == 'ar' else "Customer",
            "المبلغ الإجمالي" if self.language == 'ar' else "Total Amount",
            "طريقة الدفع" if self.language == 'ar' else "Payment Method",
            "الحالة" if self.language == 'ar' else "Status",
            "المستخدم" if self.language == 'ar' else "User",
            "ملاحظات" if self.language == 'ar' else "Notes"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=self.current_row, column=col, value=header)
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['accent'], end_color=self.colors['accent'], fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
        
        self.current_row += 1
        
        # بيانات المبيعات
        for sale in sales_data:
            row_data = [
                sale.get('invoice_number', ''),
                sale.get('sale_date', ''),
                sale.get('customer_name', 'عميل نقدي' if self.language == 'ar' else 'Cash Customer'),
                f"{sale.get('total_amount', 0):.2f}",
                sale.get('payment_method', ''),
                sale.get('status', ''),
                sale.get('user_name', ''),
                sale.get('notes', '')
            ]
            
            for col, data in enumerate(row_data, 1):
                self.worksheet.cell(row=self.current_row, column=col, value=data).font = self.fonts['data']
            
            self.current_row += 1
    
    def _add_products_data_table(self, products_data):
        """إضافة جدول بيانات المنتجات"""
        # عنوان الجدول
        cell = self.worksheet.cell(row=self.current_row, column=1, value="تفاصيل المنتجات" if self.language == 'ar' else "Products Details")
        cell.font = self.fonts['subheader']
        cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        # تطبيق نفس التنسيق على الخلايا المجاورة
        for col in range(2, 10):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        self.current_row += 1
        
        # رؤوس الأعمدة
        headers = [
            "الكود" if self.language == 'ar' else "Code",
            "اسم المنتج" if self.language == 'ar' else "Product Name",
            "الفئة" if self.language == 'ar' else "Category",
            "المخزون الحالي" if self.language == 'ar' else "Current Stock",
            "سعر التكلفة" if self.language == 'ar' else "Cost Price",
            "سعر البيع" if self.language == 'ar' else "Selling Price",
            "هامش الربح" if self.language == 'ar' else "Profit Margin",
            "قيمة المخزون" if self.language == 'ar' else "Stock Value",
            "الحالة" if self.language == 'ar' else "Status"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=self.current_row, column=col, value=header)
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['accent'], end_color=self.colors['accent'], fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
        
        self.current_row += 1
        
        # بيانات المنتجات
        for product in products_data:
            profit_margin = 0
            if product.get('cost_price', 0) > 0:
                profit_margin = ((product.get('selling_price', 0) - product.get('cost_price', 0)) / product.get('cost_price', 0)) * 100
            
            stock_value = product.get('current_stock', 0) * product.get('cost_price', 0)
            
            row_data = [
                product.get('product_code', ''),
                product.get('name', ''),
                product.get('category_name', ''),
                str(product.get('current_stock', 0)),
                f"{product.get('cost_price', 0):.2f}",
                f"{product.get('selling_price', 0):.2f}",
                f"{profit_margin:.1f}%",
                f"{stock_value:.2f}",
                "نشط" if product.get('is_active', True) else "غير نشط" if self.language == 'ar' else "Active" if product.get('is_active', True) else "Inactive"
            ]
            
            for col, data in enumerate(row_data, 1):
                self.worksheet.cell(row=self.current_row, column=col, value=data).font = self.fonts['data']
            
            self.current_row += 1
    
    def _add_customers_data_table(self, customers_data):
        """إضافة جدول بيانات العملاء"""
        # عنوان الجدول
        cell = self.worksheet.cell(row=self.current_row, column=1, value="تفاصيل العملاء" if self.language == 'ar' else "Customers Details")
        cell.font = self.fonts['subheader']
        cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        # تطبيق نفس التنسيق على الخلايا المجاورة
        for col in range(2, 9):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        self.current_row += 1
        
        # رؤوس الأعمدة
        headers = [
            "كود العميل" if self.language == 'ar' else "Customer Code",
            "اسم العميل" if self.language == 'ar' else "Customer Name",
            "الهاتف" if self.language == 'ar' else "Phone",
            "البريد الإلكتروني" if self.language == 'ar' else "Email",
            "إجمالي المشتريات" if self.language == 'ar' else "Total Purchases",
            "عدد المشتريات" if self.language == 'ar' else "Purchase Count",
            "آخر شراء" if self.language == 'ar' else "Last Purchase",
            "الحالة" if self.language == 'ar' else "Status"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=self.current_row, column=col, value=header)
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['accent'], end_color=self.colors['accent'], fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
        
        self.current_row += 1
        
        # بيانات العملاء
        for customer in customers_data:
            row_data = [
                customer.get('customer_code', ''),
                customer.get('name', ''),
                customer.get('phone', ''),
                customer.get('email', ''),
                f"{customer.get('total_purchases', 0):.2f}",
                str(customer.get('purchase_count', 0)),
                customer.get('last_purchase', ''),
                "نشط" if customer.get('is_active', True) else "غير نشط" if self.language == 'ar' else "Active" if customer.get('is_active', True) else "Inactive"
            ]
            
            for col, data in enumerate(row_data, 1):
                self.worksheet.cell(row=self.current_row, column=col, value=data).font = self.fonts['data']
            
            self.current_row += 1
    
    def _add_inventory_data_table(self, inventory_data):
        """إضافة جدول بيانات المخزون"""
        # عنوان الجدول
        cell = self.worksheet.cell(row=self.current_row, column=1, value="تفاصيل المخزون" if self.language == 'ar' else "Inventory Details")
        cell.font = self.fonts['subheader']
        cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        # تطبيق نفس التنسيق على الخلايا المجاورة
        for col in range(2, 9):
            cell = self.worksheet.cell(row=self.current_row, column=col, value="")
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['subheader'], end_color=self.colors['subheader'], fill_type='solid')

        self.current_row += 1
        
        # رؤوس الأعمدة
        headers = [
            "كود المنتج" if self.language == 'ar' else "Product Code",
            "اسم المنتج" if self.language == 'ar' else "Product Name",
            "المخزون الحالي" if self.language == 'ar' else "Current Stock",
            "الحد الأدنى" if self.language == 'ar' else "Minimum Stock",
            "الحد الأقصى" if self.language == 'ar' else "Maximum Stock",
            "سعر التكلفة" if self.language == 'ar' else "Cost Price",
            "قيمة المخزون" if self.language == 'ar' else "Stock Value",
            "حالة المخزون" if self.language == 'ar' else "Stock Status"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=self.current_row, column=col, value=header)
            cell.font = self.fonts['subheader']
            cell.fill = PatternFill(start_color=self.colors['accent'], end_color=self.colors['accent'], fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
        
        self.current_row += 1
        
        # بيانات المخزون
        for item in inventory_data:
            current_stock = item.get('current_stock', 0)
            minimum_stock = item.get('minimum_stock', 0)
            
            # تحديد حالة المخزون
            if current_stock <= 0:
                status = "نفد المخزون" if self.language == 'ar' else "Out of Stock"
            elif current_stock <= minimum_stock:
                status = "مخزون منخفض" if self.language == 'ar' else "Low Stock"
            else:
                status = "متوفر" if self.language == 'ar' else "Available"
            
            stock_value = current_stock * item.get('cost_price', 0)
            
            row_data = [
                item.get('product_code', ''),
                item.get('name', ''),
                str(current_stock),
                str(minimum_stock),
                str(item.get('maximum_stock', 0)),
                f"{item.get('cost_price', 0):.2f}",
                f"{stock_value:.2f}",
                status
            ]
            
            for col, data in enumerate(row_data, 1):
                cell = self.worksheet.cell(row=self.current_row, column=col, value=data)
                cell.font = self.fonts['data']
                
                # تلوين الخلايا حسب حالة المخزون
                if col == 8:  # عمود حالة المخزون
                    if "نفد" in status or "Out of Stock" in status:
                        cell.fill = PatternFill(start_color='FFFF0000', end_color='FFFF0000', fill_type='solid')
                        cell.font = Font(name='Arial', size=10, color='FFFFFF', bold=True)
                    elif "منخفض" in status or "Low Stock" in status:
                        cell.fill = PatternFill(start_color='FFFFA500', end_color='FFFFA500', fill_type='solid')
                        cell.font = Font(name='Arial', size=10, color='FFFFFF', bold=True)
                    else:
                        cell.fill = PatternFill(start_color='FF008000', end_color='FF008000', fill_type='solid')
                        cell.font = Font(name='Arial', size=10, color='FFFFFF', bold=True)
            
            self.current_row += 1
    
    def _format_worksheet(self):
        """تنسيق الورقة النهائي"""
        # تعديل عرض الأعمدة
        for col_num in range(1, self.worksheet.max_column + 1):
            max_length = 0
            column_letter = get_column_letter(col_num)

            for row_num in range(1, self.worksheet.max_row + 1):
                try:
                    cell = self.worksheet.cell(row=row_num, column=col_num)
                    if cell.value is not None:
                        cell_value = str(cell.value)
                        if len(cell_value) > max_length:
                            max_length = len(cell_value)
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)
            self.worksheet.column_dimensions[column_letter].width = adjusted_width

        # إضافة حدود بسيطة للجداول
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # تطبيق الحدود على الخلايا التي تحتوي على بيانات فقط
        for row_num in range(1, self.worksheet.max_row + 1):
            for col_num in range(1, self.worksheet.max_column + 1):
                try:
                    cell = self.worksheet.cell(row=row_num, column=col_num)
                    if cell.value is not None:
                        cell.border = thin_border
                except:
                    pass
    
    def save_to_response(self, filename):
        """حفظ الملف وإرجاع استجابة Flask"""
        # حفظ الملف في الذاكرة
        output = io.BytesIO()
        self.workbook.save(output)
        output.seek(0)
        
        # إنشاء استجابة Flask
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response

def create_excel_response(data, report_type, language='ar', date_range=None):
    """دالة مساعدة لإنشاء استجابة Excel"""
    exporter = ExcelExporter(language)
    
    # تحديد نوع التقرير وإنشاؤه
    if report_type == 'sales':
        workbook = exporter.create_sales_report(
            data.get('sales', []),
            data.get('summary', {}),
            date_range or {'start': '', 'end': ''}
        )
        filename = f"sales_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    elif report_type == 'products':
        workbook = exporter.create_products_report(
            data.get('products', []),
            data.get('summary', {})
        )
        filename = f"products_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    elif report_type == 'customers':
        workbook = exporter.create_customers_report(
            data.get('customers', []),
            data.get('summary', {})
        )
        filename = f"customers_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    elif report_type == 'inventory':
        workbook = exporter.create_inventory_report(
            data.get('inventory', []),
            data.get('summary', {})
        )
        filename = f"inventory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    else:
        raise ValueError(f"Unsupported report type: {report_type}")
    
    return exporter.save_to_response(filename)
