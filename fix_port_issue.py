#!/usr/bin/env python3
"""
Qatar POS System - Fix Port Issue
Automatically find and use available port
"""

import socket
import subprocess
import sys
import http.server
import socketserver
import webbrowser
import threading
import time

def find_available_port(start_port=5000, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result != 0:  # Port is available
                return port
        except:
            continue
    return None

def kill_process_on_port(port):
    """Try to kill process using the specified port"""
    try:
        # Get process ID using the port
        result = subprocess.run(
            f'netstat -ano | findstr :{port}',
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            pids = set()
            
            for line in lines:
                parts = line.split()
                if len(parts) >= 5 and 'LISTENING' in line:
                    pid = parts[-1]
                    if pid.isdigit():
                        pids.add(pid)
            
            for pid in pids:
                print(f"🔧 Trying to kill process {pid} using port {port}...")
                subprocess.run(f'taskkill /PID {pid} /F', shell=True, capture_output=True)
                time.sleep(1)
            
            return len(pids) > 0
    except:
        return False

def open_browser(port):
    """Open browser after server starts"""
    time.sleep(2)
    try:
        webbrowser.open(f'http://localhost:{port}')
        print(f"🌐 Browser opened: http://localhost:{port}")
    except:
        print(f"🌐 Please open browser manually: http://localhost:{port}")

class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, port=5000, **kwargs):
        self.port = port
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = f'''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }}
        .container {{
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 60px;
            border-radius: 30px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            max-width: 700px;
            width: 90%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }}
        h1 {{
            font-size: 4em;
            margin-bottom: 15px;
            text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.4);
        }}
        .success {{
            background: rgba(40, 167, 69, 0.4);
            border: 3px solid rgba(40, 167, 69, 0.7);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
            font-size: 1.5em;
            font-weight: bold;
            animation: pulse 2s infinite;
        }}
        @keyframes pulse {{
            0% {{ transform: scale(1); }}
            50% {{ transform: scale(1.05); }}
            100% {{ transform: scale(1); }}
        }}
        .port-info {{
            background: rgba(255, 193, 7, 0.3);
            border: 2px solid rgba(255, 193, 7, 0.6);
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            font-weight: bold;
        }}
        .status {{
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(40, 167, 69, 0.9);
            padding: 15px 25px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1em;
        }}
    </style>
</head>
<body>
    <div class="status">🟢 PORT {self.port}</div>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        
        <div class="success">
            🎉 تم حل مشكلة المنفذ تلقائياً!<br>
            🎉 Port Issue Fixed Automatically!
        </div>
        
        <div class="port-info">
            🔧 المنفذ المستخدم: {self.port}<br>
            🔧 Port Used: {self.port}<br>
            📍 URL: http://localhost:{self.port}
        </div>
        
        <div style="margin-top: 50px; font-size: 1.3em;">
            <p><strong>✅ النظام يعمل بنجاح!</strong></p>
            <p><strong>✅ System Working Successfully!</strong></p>
            <p><strong>🔧 تم العثور على منفذ متاح تلقائياً</strong></p>
            <p><strong>🔧 Available port found automatically</strong></p>
        </div>
    </div>
</body>
</html>'''
        
        self.wfile.write(html.encode('utf-8'))

def main():
    print("🇶🇦 Qatar POS System - Port Issue Fixer")
    print("نظام نقاط البيع القطري - حل مشكلة المنفذ")
    print("=" * 60)
    
    # First, try to free port 5000
    print("🔧 Trying to free port 5000...")
    if kill_process_on_port(5000):
        print("✅ Processes on port 5000 terminated")
        time.sleep(2)
    
    # Find available port
    print("🔍 Finding available port...")
    available_port = find_available_port(5000, 20)
    
    if not available_port:
        print("❌ No available ports found")
        return
    
    print(f"✅ Found available port: {available_port}")
    print(f"📍 Server URL: http://localhost:{available_port}")
    print("🌐 Opening browser automatically...")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Create handler with port info
    def handler_factory(*args, **kwargs):
        return QatarPOSHandler(*args, port=available_port, **kwargs)
    
    # Start browser in background
    browser_thread = threading.Thread(target=lambda: open_browser(available_port))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", available_port), handler_factory) as httpd:
            print(f"✅ Server started successfully on port {available_port}!")
            print(f"✅ تم تشغيل الخادم بنجاح على المنفذ {available_port}!")
            print(f"🎉 SUCCESS! Open http://localhost:{available_port}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Thank you!")
        print("👋 تم إيقاف الخادم. شكراً!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Try running as administrator")

if __name__ == '__main__':
    main()
