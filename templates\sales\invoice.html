{% extends "base.html" %}

{% block title %}
{{ 'فاتورة رقم' if language == 'ar' else 'Invoice #' }} {{ sale.sale_number }}
{% endblock %}

{% block extra_css %}
<style>
.invoice-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.company-info {
    text-align: {{ 'right' if language == 'ar' else 'left' }};
}

.invoice-details {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.items-table th {
    background-color: #007bff;
    color: white;
}

.total-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.qr-code {
    text-align: center;
    margin-top: 20px;
}

@media print {
    .no-print { display: none !important; }
    .card { border: none !important; box-shadow: none !important; }
    .container-fluid { padding: 0 !important; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="card">
        <div class="card-body">
            <!-- Print Button -->
            <div class="no-print mb-3">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="bi bi-printer"></i>
                    {{ 'طباعة الفاتورة' if language == 'ar' else 'Print Invoice' }}
                </button>
                <a href="{{ url_for('sales.view', sale_id=sale.id) }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة' if language == 'ar' else 'Back' }}
                </a>
            </div>

            <!-- Invoice Header -->
            <div class="invoice-header">
                <div class="row">
                    <div class="col-md-6">
                        <div class="company-info">
                            {% if settings.get('company_logo') %}
                            <div class="company-logo mb-3">
                                <img src="{{ url_for('static', filename='uploads/logos/' + settings.get('company_logo')) }}"
                                     alt="Company Logo"
                                     style="max-width: {{ settings.get('logo_width', '150') }}px; max-height: {{ settings.get('logo_height', '80') }}px;">
                            </div>
                            {% endif %}
                            <h2 class="text-primary">
                                {{ settings.get('company_name_ar') if language == 'ar' else settings.get('company_name_en') }}
                            </h2>
                            <p class="mb-1">
                                {{ settings.get('company_address_ar') if language == 'ar' else settings.get('company_address_en') }}
                            </p>
                            <p class="mb-1">
                                <strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong>
                                {{ settings.get('company_phone') }}
                            </p>
                            <p class="mb-1">
                                <strong>{{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}</strong>
                                {{ settings.get('company_email') }}
                            </p>

                            {% if settings.get('company_cr_number') %}
                            <p class="mb-0">
                                <strong>{{ 'رقم السجل التجاري:' if language == 'ar' else 'CR Number:' }}</strong>
                                {{ settings.get('company_cr_number') }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6 text-{{ 'left' if language == 'ar' else 'right' }}">
                        <h1 class="text-primary">
                            {{ 'فاتورة' if language == 'ar' else 'INVOICE' }}
                        </h1>
                        <h3>{{ sale.sale_number }}</h3>
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="row">
                <div class="col-md-6">
                    <div class="invoice-details">
                        <h5 class="mb-3">{{ 'بيانات العميل' if language == 'ar' else 'Customer Information' }}</h5>
                        {% if sale.customer %}
                            <p class="mb-1">
                                <strong>{{ 'الاسم:' if language == 'ar' else 'Name:' }}</strong>
                                {{ sale.customer.get_display_name(language) }}
                            </p>
                            {% if sale.customer.phone %}
                            <p class="mb-1">
                                <strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong>
                                {{ sale.customer.phone }}
                            </p>
                            {% endif %}
                            {% if sale.customer.email %}
                            <p class="mb-1">
                                <strong>{{ 'البريد الإلكتروني:' if language == 'ar' else 'Email:' }}</strong>
                                {{ sale.customer.email }}
                            </p>
                            {% endif %}
                            {% if sale.customer.address %}
                            <p class="mb-0">
                                <strong>{{ 'العنوان:' if language == 'ar' else 'Address:' }}</strong>
                                {{ sale.customer.address }}
                            </p>
                            {% endif %}
                        {% else %}
                            <p class="mb-0">{{ 'عميل عادي' if language == 'ar' else 'Walk-in Customer' }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="invoice-details">
                        <h5 class="mb-3">{{ 'تفاصيل الفاتورة' if language == 'ar' else 'Invoice Details' }}</h5>
                        <p class="mb-1">
                            <strong>{{ 'رقم الفاتورة:' if language == 'ar' else 'Invoice Number:' }}</strong>
                            {{ sale.sale_number }}
                        </p>
                        <p class="mb-1">
                            <strong>{{ 'تاريخ الفاتورة:' if language == 'ar' else 'Invoice Date:' }}</strong>
                            {{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}
                        </p>
                        <p class="mb-1">
                            <strong>{{ 'البائع:' if language == 'ar' else 'Seller:' }}</strong>
                            {{ sale.seller.get_full_name(language) }}
                        </p>
                        <p class="mb-1">
                            <strong>{{ 'طريقة الدفع:' if language == 'ar' else 'Payment Method:' }}</strong>
                            {% if sale.payment_method == 'cash' %}
                                {{ 'نقدي' if language == 'ar' else 'Cash' }}
                            {% elif sale.payment_method == 'card' %}
                                {{ 'بطاقة' if language == 'ar' else 'Card' }}
                            {% elif sale.payment_method == 'bank_transfer' %}
                                {{ 'تحويل بنكي' if language == 'ar' else 'Bank Transfer' }}
                            {% elif sale.payment_method == 'cod' %}
                                <span class="text-warning">{{ 'دفع عند الاستلام' if language == 'ar' else 'Cash on Delivery' }}</span>
                                {% if sale.cod_status %}
                                <br><small class="text-muted">{{ 'الحالة:' if language == 'ar' else 'Status:' }} {{ sale.get_cod_status_display(language) }}</small>
                                {% endif %}
                            {% else %}
                                {{ sale.payment_method }}
                            {% endif %}
                        </p>
                        <p class="mb-0">
                            <strong>{{ 'حالة الدفع:' if language == 'ar' else 'Payment Status:' }}</strong>
                            {% if sale.payment_status == 'paid' %}
                                <span class="badge bg-success">{{ 'مدفوع' if language == 'ar' else 'Paid' }}</span>
                            {% elif sale.payment_status == 'partial' %}
                                <span class="badge bg-warning">{{ 'مدفوع جزئياً' if language == 'ar' else 'Partially Paid' }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ 'غير مدفوع' if language == 'ar' else 'Unpaid' }}</span>
                            {% endif %}
                        </p>

                        <!-- COD Delivery Information -->
                        {% if sale.payment_method == 'cod' and sale.delivery_address %}
                        <div class="mt-3 p-3 bg-light border-start border-warning border-4">
                            <h6 class="text-warning mb-2">
                                <i class="bi bi-truck"></i>
                                {{ 'معلومات التوصيل' if language == 'ar' else 'Delivery Information' }}
                            </h6>
                            <p class="mb-1">
                                <strong>{{ 'العنوان:' if language == 'ar' else 'Address:' }}</strong><br>
                                {{ sale.delivery_address }}
                            </p>
                            {% if sale.delivery_phone %}
                            <p class="mb-1">
                                <strong>{{ 'الهاتف:' if language == 'ar' else 'Phone:' }}</strong>
                                {{ sale.delivery_phone }}
                            </p>
                            {% endif %}
                            {% if sale.delivery_notes %}
                            <p class="mb-0">
                                <strong>{{ 'ملاحظات:' if language == 'ar' else 'Notes:' }}</strong>
                                {{ sale.delivery_notes }}
                            </p>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Items Table -->
            <div class="table-responsive mb-4">
                <table class="table table-bordered items-table">
                    <thead>
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 15%;">{{ 'رمز المنتج' if language == 'ar' else 'SKU' }}</th>
                            <th style="width: 35%;">{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                            <th style="width: 10%;">{{ 'الكمية' if language == 'ar' else 'Qty' }}</th>
                            <th style="width: 15%;">{{ 'سعر الوحدة' if language == 'ar' else 'Unit Price' }}</th>
                            <th style="width: 10%;">{{ 'الخصم' if language == 'ar' else 'Discount' }}</th>
                            <th style="width: 15%;">{{ 'المجموع' if language == 'ar' else 'Total' }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in sale.items %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ item.product.sku }}</td>
                            <td>{{ item.product.get_name(language) }}</td>
                            <td class="text-center">{{ item.quantity }}</td>
                            <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                {{ '%.2f'|format(item.unit_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                            </td>
                            <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                {{ '%.2f'|format(item.discount_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                            </td>
                            <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                <strong>{{ '%.2f'|format(item.total_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Totals Section -->
            <div class="row">
                <div class="col-md-6">
                    {% if sale.notes %}
                    <div class="mb-3">
                        <h6>{{ 'ملاحظات:' if language == 'ar' else 'Notes:' }}</h6>
                        <p>{{ sale.notes }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <div class="total-section">
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong>{{ '%.2f'|format(sale.subtotal) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            {% if sale.discount_amount > 0 %}
                            <tr>
                                <td><strong>{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong class="text-danger">-{{ '%.2f'|format(sale.discount_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            {% endif %}
                            {% if sale.tax_amount > 0 %}
                            <tr>
                                <td><strong>{{ 'ضريبة القيمة المضافة:' if language == 'ar' else 'VAT:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong>{{ '%.2f'|format(sale.tax_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            {% endif %}
                            <tr class="table-primary">
                                <td><strong>{{ 'المجموع الكلي:' if language == 'ar' else 'Total Amount:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong class="fs-5">{{ '%.2f'|format(sale.total_amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{{ 'المبلغ المدفوع:' if language == 'ar' else 'Amount Paid:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong class="text-success">{{ '%.2f'|format(sale.amount_paid) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            {% if sale.amount_due > 0 %}
                            <tr>
                                <td><strong>{{ 'المبلغ المستحق:' if language == 'ar' else 'Amount Due:' }}</strong></td>
                                <td class="text-{{ 'left' if language == 'ar' else 'right' }}">
                                    <strong class="text-danger">{{ '%.2f'|format(sale.amount_due) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>

            <!-- Stamp and Signature Section -->
            <div class="row mt-4 pt-3" style="border-top: 1px solid #dee2e6;">
                <div class="col-md-6">
                    <!-- Company Stamp -->
                    <div class="text-center">
                        {% if settings.get('company_stamp') %}
                            <div class="mb-2">
                                <img src="{{ url_for('static', filename='uploads/stamps/' + settings.get('company_stamp')) }}"
                                     alt="Company Stamp"
                                     style="max-width: {{ settings.get('stamp_width', '120') }}px; max-height: {{ settings.get('stamp_height', '120') }}px;"
                                     class="img-fluid">
                            </div>
                        {% else %}
                            <div class="border rounded-circle d-inline-flex align-items-center justify-content-center"
                                 style="width: 120px; height: 120px; border-color: #007bff !important;">
                                <div class="text-center">
                                    <small class="text-primary fw-bold">
                                        {{ settings.get('company_name_ar') if language == 'ar' else settings.get('company_name_en') }}<br>
                                        <span style="font-size: 0.7em;">{{ 'ختم الشركة' if language == 'ar' else 'COMPANY SEAL' }}</span>
                                    </small>
                                </div>
                            </div>
                        {% endif %}
                        <div class="mt-2">
                            <small class="fw-bold">{{ 'ختم الشركة' if language == 'ar' else 'Company Stamp' }}</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <!-- Manager Signature -->
                    <div class="text-center">
                        {% if settings.get('manager_signature') %}
                            <div class="mb-2">
                                <img src="{{ url_for('static', filename='uploads/signatures/' + settings.get('manager_signature')) }}"
                                     alt="Manager Signature"
                                     style="max-width: {{ settings.get('signature_width', '200') }}px; max-height: {{ settings.get('signature_height', '80') }}px;"
                                     class="img-fluid">
                            </div>
                            <div class="mt-2">
                                <div class="fw-bold">{{ settings.get('manager_name', 'المدير العام' if language == 'ar' else 'General Manager') }}</div>
                                <small class="text-muted">{{ settings.get('manager_title', 'المدير العام' if language == 'ar' else 'General Manager') }}</small>
                            </div>
                        {% else %}
                            <div class="border-bottom mb-3" style="height: 60px; width: 200px; margin: 0 auto;"></div>
                            <div>
                                <div class="fw-bold">{{ settings.get('manager_name', 'المدير العام' if language == 'ar' else 'General Manager') }}</div>
                                <small class="text-muted">{{ settings.get('manager_title', 'المدير العام' if language == 'ar' else 'General Manager') }}</small>
                            </div>
                        {% endif %}
                        <div class="mt-2 pt-2" style="border-top: 1px solid #dee2e6;">
                            <small class="fw-bold">{{ 'توقيع المسؤول' if language == 'ar' else 'Authorized Signature' }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-4 pt-3" style="border-top: 1px solid #dee2e6;">
                <p class="mb-1">
                    <strong>{{ 'شكراً لتعاملكم معنا' if language == 'ar' else 'Thank you for your business!' }}</strong>
                </p>
                {% if settings.get('receipt_footer_ar') and language == 'ar' %}
                    <small class="text-muted">{{ settings.get('receipt_footer_ar') }}</small>
                {% elif settings.get('receipt_footer_en') and language == 'en' %}
                    <small class="text-muted">{{ settings.get('receipt_footer_en') }}</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}