<!DOCTYPE html>
<html lang="{{ 'ar' if language == 'ar' else 'en' }}" dir="{{ 'rtl' if language == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% if language == 'ar' %}
    <!-- RTL Support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="bi bi-shop"></i>
                {{ 'نظام نقاط البيع' if language == 'ar' else 'POS System' }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">
                            <i class="bi bi-speedometer2"></i>
                            {{ 'لوحة التحكم' if language == 'ar' else 'Dashboard' }}
                        </a>
                    </li>
                    
                    {% if current_user.has_permission('sales') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-cart"></i>
                            {{ 'المبيعات' if language == 'ar' else 'Sales' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('sales.pos') }}">
                                <i class="bi bi-calculator"></i>
                                {{ 'نقطة البيع' if language == 'ar' else 'POS' }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('sales.index') }}">
                                <i class="bi bi-list-ul"></i>
                                {{ 'قائمة المبيعات' if language == 'ar' else 'Sales List' }}
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('products_read') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-box"></i>
                            {{ 'المنتجات' if language == 'ar' else 'Products' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('products.index') }}">
                                <i class="bi bi-list-ul"></i>
                                {{ 'قائمة المنتجات' if language == 'ar' else 'Products List' }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('products.categories') }}">
                                <i class="bi bi-tags"></i>
                                {{ 'الفئات' if language == 'ar' else 'Categories' }}
                            </a></li>
                            {% if current_user.has_permission('products_write') %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('products.create') }}">
                                <i class="bi bi-plus-circle"></i>
                                {{ 'إضافة منتج' if language == 'ar' else 'Add Product' }}
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('inventory_read') %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-boxes"></i>
                            {{ 'المخزون' if language == 'ar' else 'Inventory' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('inventory.index') }}">
                                <i class="bi bi-speedometer2"></i>
                                {{ 'نظرة عامة' if language == 'ar' else 'Overview' }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory.transactions') }}">
                                <i class="bi bi-arrow-left-right"></i>
                                {{ 'حركات المخزون' if language == 'ar' else 'Transactions' }}
                            </a></li>
                            {% if current_user.has_permission('inventory') %}
                            <li><a class="dropdown-item" href="{{ url_for('inventory.adjustments') }}">
                                <i class="bi bi-gear"></i>
                                {{ 'تعديلات المخزون' if language == 'ar' else 'Adjustments' }}
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('customers_read') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('customers.index') }}">
                            <i class="bi bi-people"></i>
                            {{ 'العملاء' if language == 'ar' else 'Customers' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('suppliers') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('suppliers.index') }}">
                            <i class="bi bi-truck"></i>
                            {{ 'الموردين' if language == 'ar' else 'Suppliers' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.has_permission('reports') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports.index') }}">
                            <i class="bi bi-graph-up"></i>
                            {{ 'التقارير' if language == 'ar' else 'Reports' }}
                        </a>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-translate"></i>
                            {{ 'العربية' if language == 'ar' else 'English' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.switch_language', language='ar') }}">العربية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.switch_language', language='en') }}">English</a></li>
                        </ul>
                    </li>
                    
                    {% if current_user.is_authenticated %}
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ current_user.get_full_name(language) }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="bi bi-person"></i>
                                {{ 'الملف الشخصي' if language == 'ar' else 'Profile' }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="bi bi-key"></i>
                                {{ 'تغيير كلمة المرور' if language == 'ar' else 'Change Password' }}
                            </a></li>
                            {% if current_user.role == 'manager' %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('users.index') }}">
                                <i class="bi bi-people"></i>
                                {{ 'إدارة المستخدمين' if language == 'ar' else 'User Management' }}
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i>
                                {{ 'تسجيل الخروج' if language == 'ar' else 'Logout' }}
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="bi bi-box-arrow-in-right"></i>
                            {{ 'تسجيل الدخول' if language == 'ar' else 'Login' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; 2024 {{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}. 
                {{ 'جميع الحقوق محفوظة' if language == 'ar' else 'All rights reserved' }}.
            </p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
