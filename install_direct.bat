@echo off
echo ========================================
echo    Qatar POS System - تثبيت سطح المكتب
echo ========================================
echo.

if not exist "QatarPOS.exe" (
    echo ❌ ملف QatarPOS.exe غير موجود
    pause
    exit /b 1
)

echo جاري إنشاء مجلد التطبيق...
if not exist "C:\QatarPOS" mkdir "C:\QatarPOS"

echo جاري نسخ الملف التنفيذي...
copy "QatarPOS.exe" "C:\QatarPOS\"

echo جاري إنشاء اختصار على سطح المكتب...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Qatar POS.lnk'); $Shortcut.TargetPath = 'C:\QatarPOS\QatarPOS.exe'; $Shortcut.WorkingDirectory = 'C:\QatarPOS'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()"

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مسار التطبيق: C:\QatarPOS\QatarPOS.exe
echo 🖥️ تم إنشاء اختصار على سطح المكتب
echo.
pause
