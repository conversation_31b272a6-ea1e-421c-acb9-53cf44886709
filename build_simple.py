"""
Simple Build Script for Qatar POS Desktop Application
سكريپت بناء مبسط لتطبيق نقاط البيع القطري
"""

import os
import sys
import subprocess
import shutil

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    required_files = ['desktop_app.py', 'app.py', 'config.py', 'extensions.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return False
        print(f"✅ {file}")
    
    return True

def install_basic_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("\n📦 تثبيت المتطلبات الأساسية...")
    
    basic_packages = [
        'flask',
        'flask-sqlalchemy', 
        'flask-login',
        'pyinstaller'
    ]
    
    for package in basic_packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {package}, سيتم المتابعة...")
    
    return True

def create_simple_spec():
    """إنشاء ملف مواصفات مبسط"""
    print("\n📝 إنشاء ملف المواصفات...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['desktop_app.py'],
    pathex=['{os.getcwd()}'],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('models', 'models'),
        ('routes', 'routes'),
        ('utils', 'utils'),
        ('config.py', '.'),
        ('extensions.py', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'sqlalchemy',
        'tkinter',
        'threading',
        'webbrowser',
        'socket',
        'datetime',
        'json',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='QatarPOS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('qatar_pos_simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف المواصفات: qatar_pos_simple.spec")
    return True

def build_exe():
    """بناء الملف التنفيذي"""
    print("\n🔨 بناء الملف التنفيذي...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            'qatar_pos_simple.spec'
        ])
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء الملف التنفيذي: {e}")
        return False

def create_installer():
    """إنشاء ملفات التثبيت"""
    print("\n📦 إنشاء ملفات التثبيت...")
    
    # مثبت بسيط
    installer_content = '''@echo off
echo ========================================
echo    Qatar POS System - تثبيت سطح المكتب
echo ========================================
echo.

echo جاري نسخ الملفات...
if not exist "C:\\QatarPOS" mkdir "C:\\QatarPOS"
copy "QatarPOS.exe" "C:\\QatarPOS\\"

echo جاري إنشاء اختصار...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\Qatar POS.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\QatarPOS\\QatarPOS.exe" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مسار التطبيق: C:\\QatarPOS\\QatarPOS.exe
echo 🖥️ تم إنشاء اختصار على سطح المكتب
echo.
pause
'''
    
    with open('install_simple.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء مثبت التطبيق: install_simple.bat")

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_items = ['build', 'qatar_pos_simple.spec']
    for item in temp_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"🗑️ تم حذف: {item}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء تطبيق Qatar POS لسطح المكتب - إصدار مبسط")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return False
    
    # تثبيت المتطلبات الأساسية
    install_basic_requirements()
    
    # إنشاء ملف المواصفات
    if not create_simple_spec():
        input("اضغط Enter للخروج...")
        return False
    
    # بناء الملف التنفيذي
    if not build_exe():
        input("اضغط Enter للخروج...")
        return False
    
    # إنشاء ملفات التثبيت
    create_installer()
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 تم بناء التطبيق بنجاح!")
    
    if os.path.exists('dist/QatarPOS.exe'):
        print("📁 الملف التنفيذي: dist/QatarPOS.exe")
        print("💾 مثبت التطبيق: install_simple.bat")
        print("\n✨ يمكنك الآن توزيع التطبيق!")
    else:
        print("⚠️ لم يتم العثور على الملف التنفيذي في dist/")
        print("تحقق من وجود أخطاء في عملية البناء")
    
    input("\nاضغط Enter للخروج...")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        input("اضغط Enter للخروج...")
