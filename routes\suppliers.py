"""
Supplier management routes for Qatar POS System
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models.supplier import Supplier
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language, validate_email

suppliers_bp = Blueprint('suppliers', __name__)

@suppliers_bp.route('/')
@login_required
@permission_required('suppliers')
def index():
    """List all suppliers"""
    language = get_user_language()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Supplier.query
    
    if search:
        query = query.filter(
            (Supplier.company_name_ar.contains(search)) |
            (Supplier.company_name_en.contains(search)) |
            (Supplier.contact_person_ar.contains(search)) |
            (Supplier.contact_person_en.contains(search)) |
            (Supplier.phone.contains(search)) |
            (Supplier.supplier_code.contains(search))
        )
    
    suppliers = query.order_by(Supplier.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('suppliers/index.html',
                         suppliers=suppliers,
                         search=search,
                         language=language)

@suppliers_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('suppliers')
def create():
    """Create new supplier"""
    language = get_user_language()
    
    if request.method == 'POST':
        company_name_ar = request.form.get('company_name_ar', '').strip()
        company_name_en = request.form.get('company_name_en', '').strip()
        contact_person_ar = request.form.get('contact_person_ar', '').strip()
        contact_person_en = request.form.get('contact_person_en', '').strip()
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip()
        website = request.form.get('website', '').strip()
        address_ar = request.form.get('address_ar', '').strip()
        address_en = request.form.get('address_en', '').strip()
        city_ar = request.form.get('city_ar', '').strip()
        city_en = request.form.get('city_en', '').strip()
        postal_code = request.form.get('postal_code', '').strip()
        commercial_registration = request.form.get('commercial_registration', '').strip()
        tax_number = request.form.get('tax_number', '').strip()
        payment_terms = request.form.get('payment_terms', '').strip()
        
        # Validation
        errors = []
        
        if not company_name_ar or not company_name_en:
            errors.append('اسم الشركة مطلوب بالعربية والإنجليزية' if language == 'ar' 
                         else 'Company name is required in both Arabic and English')
        
        if not phone:
            errors.append('رقم الهاتف مطلوب' if language == 'ar' else 'Phone number is required')
        
        if email and not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('suppliers/create.html', language=language)
        
        try:
            supplier = Supplier(
                supplier_code=Supplier.generate_supplier_code(),
                company_name_ar=company_name_ar,
                company_name_en=company_name_en,
                contact_person_ar=contact_person_ar,
                contact_person_en=contact_person_en,
                phone=phone,
                email=email,
                website=website,
                address_ar=address_ar,
                address_en=address_en,
                city_ar=city_ar,
                city_en=city_en,
                postal_code=postal_code,
                commercial_registration=commercial_registration,
                tax_number=tax_number,
                payment_terms=payment_terms
            )
            
            db.session.add(supplier)
            db.session.commit()
            
            flash('تم إنشاء المورد بنجاح' if language == 'ar' 
                  else 'Supplier created successfully', 'success')
            return redirect(url_for('suppliers.view', supplier_id=supplier.id))
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المورد' if language == 'ar' 
                  else 'Error creating supplier', 'error')
    
    return render_template('suppliers/create.html', language=language)

@suppliers_bp.route('/<int:supplier_id>')
@login_required
@permission_required('suppliers')
def view(supplier_id):
    """View supplier details"""
    language = get_user_language()
    supplier = Supplier.query.get_or_404(supplier_id)
    
    # Get recent purchase orders
    recent_orders = supplier.purchase_orders.order_by(
        supplier.purchase_orders.c.created_at.desc()
    ).limit(10).all()
    
    return render_template('suppliers/view.html',
                         supplier=supplier,
                         recent_orders=recent_orders,
                         language=language)
