#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد سريع للنظام - بدون تعقيدات
Quick System Setup - No Complications
"""

import os
from app import create_app
from extensions import db

def main():
    """تشغيل الإعداد السريع"""
    print("🇶🇦 نظام نقاط البيع القطري - الإعداد السريع")
    print("=" * 50)
    
    # Remove existing database
    db_path = 'instance/database.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    app = create_app()
    
    with app.app_context():
        print("📋 إنشاء جداول قاعدة البيانات...")
        
        # Import only basic models
        from models.user import User
        from models.setting import Setting, SystemInfo
        
        # Create tables
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # Create admin user
        print("👤 إنشاء المستخدم الإداري...")
        
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name_ar='مدير',
            first_name_en='Admin',
            last_name_ar='النظام',
            last_name_en='System',
            phone='+974 5555 5555',
            role='manager',
            is_active=True
        )
        admin_user.set_password('admin123')
        
        try:
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الإداري (admin/admin123)")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            db.session.rollback()
        
        # Create basic settings
        print("⚙️ إنشاء الإعدادات الأساسية...")
        
        basic_settings = [
            Setting(key='company_name_ar', value='شركة نقاط البيع القطرية', category='company'),
            Setting(key='company_name_en', value='Qatar POS Company', category='company'),
            Setting(key='default_language', value='ar', category='system'),
            Setting(key='default_currency', value='QAR', category='system'),
            Setting(key='tax_enabled', value='true', value_type='boolean', category='tax'),
            Setting(key='vat_rate', value='5.0', value_type='float', category='tax'),
        ]
        
        try:
            for setting in basic_settings:
                db.session.add(setting)
            
            # Create system info
            system_info = SystemInfo()
            db.session.add(system_info)
            
            db.session.commit()
            print("✅ تم إنشاء الإعدادات الأساسية")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الإعدادات: {e}")
            db.session.rollback()
        
        print("\n" + "=" * 50)
        print("🎉 تم الإعداد السريع بنجاح!")
        print("\n📋 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("\n🌐 يمكنك الآن الوصول للنظام على: http://localhost:2626")
        print("⚙️ يمكنك الوصول للإعدادات على: http://localhost:2626/settings/")

if __name__ == '__main__':
    main()
