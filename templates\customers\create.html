{% extends "base.html" %}

{% block title %}
{{ 'إضافة عميل جديد - نظام نقاط البيع القطري' if language == 'ar' else 'Add New Customer - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة عميل جديد' if language == 'ar' else 'Add New Customer' }}
            </h1>
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {{ 'العودة للقائمة' if language == 'ar' else 'Back to List' }}
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {{ 'معلومات العميل' if language == 'ar' else 'Customer Information' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- Customer Type Selection -->
                    <div class="mb-4">
                        <label class="form-label">{{ 'نوع العميل' if language == 'ar' else 'Customer Type' }}</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="customer_type" id="individual" value="individual" checked>
                            <label class="btn btn-outline-primary" for="individual">
                                <i class="bi bi-person"></i>
                                {{ 'فرد' if language == 'ar' else 'Individual' }}
                            </label>
                            
                            <input type="radio" class="btn-check" name="customer_type" id="company" value="company">
                            <label class="btn btn-outline-primary" for="company">
                                <i class="bi bi-building"></i>
                                {{ 'شركة' if language == 'ar' else 'Company' }}
                            </label>
                        </div>
                    </div>
                    
                    <!-- Individual Fields -->
                    <div id="individual_fields">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name_ar" class="form-label">
                                    {{ 'الاسم الأول بالعربية' if language == 'ar' else 'First Name (Arabic)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_ar" name="first_name_ar" required>
                                <div class="invalid-feedback">
                                    {{ 'الاسم الأول بالعربية مطلوب' if language == 'ar' else 'Arabic first name is required' }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="first_name_en" class="form-label">
                                    {{ 'الاسم الأول بالإنجليزية' if language == 'ar' else 'First Name (English)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_en" name="first_name_en" required>
                                <div class="invalid-feedback">
                                    {{ 'الاسم الأول بالإنجليزية مطلوب' if language == 'ar' else 'English first name is required' }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="last_name_ar" class="form-label">
                                    {{ 'اسم العائلة بالعربية' if language == 'ar' else 'Last Name (Arabic)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_ar" name="last_name_ar" required>
                                <div class="invalid-feedback">
                                    {{ 'اسم العائلة بالعربية مطلوب' if language == 'ar' else 'Arabic last name is required' }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name_en" class="form-label">
                                    {{ 'اسم العائلة بالإنجليزية' if language == 'ar' else 'Last Name (English)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_en" name="last_name_en" required>
                                <div class="invalid-feedback">
                                    {{ 'اسم العائلة بالإنجليزية مطلوب' if language == 'ar' else 'English last name is required' }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Company Fields -->
                    <div id="company_fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name_ar" class="form-label">
                                    {{ 'اسم الشركة بالعربية' if language == 'ar' else 'Company Name (Arabic)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_ar" name="company_name_ar">
                                <div class="invalid-feedback">
                                    {{ 'اسم الشركة بالعربية مطلوب' if language == 'ar' else 'Arabic company name is required' }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_name_en" class="form-label">
                                    {{ 'اسم الشركة بالإنجليزية' if language == 'ar' else 'Company Name (English)' }}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="company_name_en" name="company_name_en">
                                <div class="invalid-feedback">
                                    {{ 'اسم الشركة بالإنجليزية مطلوب' if language == 'ar' else 'English company name is required' }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_person_ar" class="form-label">
                                    {{ 'جهة الاتصال بالعربية' if language == 'ar' else 'Contact Person (Arabic)' }}
                                </label>
                                <input type="text" class="form-control" id="contact_person_ar" name="contact_person_ar">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_person_en" class="form-label">
                                    {{ 'جهة الاتصال بالإنجليزية' if language == 'ar' else 'Contact Person (English)' }}
                                </label>
                                <input type="text" class="form-control" id="contact_person_en" name="contact_person_en">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="commercial_registration" class="form-label">
                                    {{ 'السجل التجاري' if language == 'ar' else 'Commercial Registration' }}
                                </label>
                                <input type="text" class="form-control" id="commercial_registration" name="commercial_registration">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">
                                    {{ 'الرقم الضريبي' if language == 'ar' else 'Tax Number' }}
                                </label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <hr>
                    <h6 class="mb-3">{{ 'معلومات الاتصال' if language == 'ar' else 'Contact Information' }}</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                {{ 'رقم الهاتف' if language == 'ar' else 'Phone Number' }}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" required
                                   placeholder="{{ '+974-XXXX-XXXX' if language == 'ar' else '+974-XXXX-XXXX' }}">
                            <div class="invalid-feedback">
                                {{ 'رقم الهاتف مطلوب' if language == 'ar' else 'Phone number is required' }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                {{ 'البريد الإلكتروني' if language == 'ar' else 'Email Address' }}
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="{{ '<EMAIL>' if language == 'ar' else '<EMAIL>' }}">
                            <div class="invalid-feedback">
                                {{ 'البريد الإلكتروني غير صحيح' if language == 'ar' else 'Invalid email format' }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Address Information -->
                    <hr>
                    <h6 class="mb-3">{{ 'معلومات العنوان' if language == 'ar' else 'Address Information' }}</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="address_ar" class="form-label">
                                {{ 'العنوان بالعربية' if language == 'ar' else 'Address (Arabic)' }}
                            </label>
                            <textarea class="form-control" id="address_ar" name="address_ar" rows="3"></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address_en" class="form-label">
                                {{ 'العنوان بالإنجليزية' if language == 'ar' else 'Address (English)' }}
                            </label>
                            <textarea class="form-control" id="address_en" name="address_en" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="city_ar" class="form-label">
                                {{ 'المدينة بالعربية' if language == 'ar' else 'City (Arabic)' }}
                            </label>
                            <input type="text" class="form-control" id="city_ar" name="city_ar">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="city_en" class="form-label">
                                {{ 'المدينة بالإنجليزية' if language == 'ar' else 'City (English)' }}
                            </label>
                            <input type="text" class="form-control" id="city_en" name="city_en">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="postal_code" class="form-label">
                                {{ 'الرمز البريدي' if language == 'ar' else 'Postal Code' }}
                            </label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code">
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <hr>
                    <h6 class="mb-3">{{ 'معلومات إضافية' if language == 'ar' else 'Additional Information' }}</h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="qatar_id" class="form-label">
                                {{ 'رقم الهوية القطرية' if language == 'ar' else 'Qatar ID Number' }}
                            </label>
                            <input type="text" class="form-control" id="qatar_id" name="qatar_id"
                                   placeholder="{{ '12345678901' if language == 'ar' else '12345678901' }}">
                            <div class="invalid-feedback">
                                {{ 'رقم الهوية القطرية يجب أن يكون 11 رقم' if language == 'ar' else 'Qatar ID must be 11 digits' }}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            {{ 'حفظ العميل' if language == 'ar' else 'Save Customer' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Customer type toggle
document.querySelectorAll('input[name="customer_type"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const individualFields = document.getElementById('individual_fields');
        const companyFields = document.getElementById('company_fields');
        
        if (this.value === 'individual') {
            individualFields.style.display = 'block';
            companyFields.style.display = 'none';
            
            // Set required attributes
            document.querySelectorAll('#individual_fields input[required]').forEach(input => input.required = true);
            document.querySelectorAll('#company_fields input').forEach(input => input.required = false);
        } else {
            individualFields.style.display = 'none';
            companyFields.style.display = 'block';
            
            // Set required attributes
            document.querySelectorAll('#individual_fields input').forEach(input => input.required = false);
            document.querySelectorAll('#company_fields input[data-required]').forEach(input => input.required = true);
        }
    });
});

// Qatar ID validation
document.getElementById('qatar_id').addEventListener('input', function() {
    const value = this.value.replace(/\D/g, '');
    this.value = value;
    
    if (value.length > 0 && value.length !== 11) {
        this.setCustomValidity('{{ "رقم الهوية القطرية يجب أن يكون 11 رقم" if language == "ar" else "Qatar ID must be 11 digits" }}');
    } else {
        this.setCustomValidity('');
    }
});

// Phone number formatting
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    
    // Add Qatar country code if not present
    if (value.length > 0 && !value.startsWith('974')) {
        if (value.startsWith('0')) {
            value = '974' + value.substring(1);
        } else if (value.length === 8) {
            value = '974' + value;
        }
    }
    
    // Format the number
    if (value.length >= 3) {
        value = '+' + value.substring(0, 3) + '-' + value.substring(3);
    }
    
    this.value = value;
});
</script>
{% endblock %}
