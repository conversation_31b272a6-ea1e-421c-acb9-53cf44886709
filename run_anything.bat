@echo off
chcp 65001 >nul
title نظام نقاط البيع القطري - تشغيل تلقائي

echo.
echo ========================================
echo 🇶🇦 نظام نقاط البيع القطري
echo    Qatar POS System - Auto Run
echo ========================================
echo.

echo 🔍 Checking system...

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

echo.
echo 🚀 Trying different servers...
echo.

REM Try emergency server first (most likely to work)
echo 📍 Trying emergency server...
python emergency_server.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying working server...
python working_server.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying run now...
python run_now.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying test server...
python test_server.py
if not errorlevel 1 goto success

echo.
echo 📍 Trying simple run...
python simple_run.py
if not errorlevel 1 goto success

echo.
echo ❌ All servers failed to start
echo.
echo 💡 Solutions:
echo 1. Install Flask: pip install flask
echo 2. Check port 5000 is free
echo 3. Run as administrator
echo 4. Check firewall settings
echo.
echo 🔧 Running quick test...
python quick_test.py
pause
exit /b 1

:success
echo.
echo 🎉 Server started successfully!
echo 🌐 Open: http://localhost:5000
pause
