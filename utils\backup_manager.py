"""
Advanced Backup Manager for Qatar POS System
Handles database and file backups with encryption and compression
"""

import os
import shutil
import zipfile
import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
import logging
from cryptography.fernet import Fernet
import gzip

class BackupManager:
    def __init__(self, app=None):
        self.app = app
        self.backup_dir = 'backups'
        self.temp_dir = 'temp_backup'
        self.encryption_key = None
        self.logger = logging.getLogger(__name__)
        
        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Initialize encryption
        self._init_encryption()
    
    def _init_encryption(self):
        """Initialize encryption key"""
        key_file = os.path.join(self.backup_dir, '.backup_key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                self.encryption_key = f.read()
        else:
            # Generate new key
            self.encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self.encryption_key)
            
            # Hide the key file
            if os.name == 'nt':  # Windows
                os.system(f'attrib +h "{key_file}"')
    
    def create_backup(self, backup_type='full', compress=True, encrypt=True):
        """Create a backup of the specified type"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{backup_type}_{timestamp}"
            
            self.logger.info(f"Starting {backup_type} backup: {backup_name}")
            
            if backup_type == 'full':
                return self._create_full_backup(backup_name, compress, encrypt)
            elif backup_type == 'database':
                return self._create_database_backup(backup_name, compress, encrypt)
            elif backup_type == 'files':
                return self._create_files_backup(backup_name, compress, encrypt)
            else:
                raise ValueError(f"Unknown backup type: {backup_type}")
                
        except Exception as e:
            self.logger.error(f"Backup failed: {str(e)}")
            return False, str(e)
    
    def _create_full_backup(self, backup_name, compress=True, encrypt=True):
        """Create a full system backup"""
        temp_path = os.path.join(self.temp_dir, backup_name)
        os.makedirs(temp_path, exist_ok=True)
        
        try:
            # Backup database
            db_success, db_path = self._backup_database(temp_path)
            if not db_success:
                return False, "Database backup failed"
            
            # Backup files
            files_success, files_info = self._backup_files(temp_path)
            if not files_success:
                return False, "Files backup failed"
            
            # Create metadata
            metadata = {
                'type': 'full',
                'created_at': datetime.now().isoformat(),
                'database': db_path,
                'files': files_info,
                'compressed': compress,
                'encrypted': encrypt
            }
            
            with open(os.path.join(temp_path, 'metadata.json'), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Package the backup
            final_path = self._package_backup(backup_name, temp_path, compress, encrypt)
            
            # Cleanup temp directory
            shutil.rmtree(temp_path)
            
            self.logger.info(f"Full backup completed: {final_path}")
            return True, final_path
            
        except Exception as e:
            # Cleanup on error
            if os.path.exists(temp_path):
                shutil.rmtree(temp_path)
            raise e
    
    def _create_database_backup(self, backup_name, compress=True, encrypt=True):
        """Create database-only backup"""
        temp_path = os.path.join(self.temp_dir, backup_name)
        os.makedirs(temp_path, exist_ok=True)
        
        try:
            # Backup database
            db_success, db_path = self._backup_database(temp_path)
            if not db_success:
                return False, "Database backup failed"
            
            # Create metadata
            metadata = {
                'type': 'database',
                'created_at': datetime.now().isoformat(),
                'database': db_path,
                'compressed': compress,
                'encrypted': encrypt
            }
            
            with open(os.path.join(temp_path, 'metadata.json'), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Package the backup
            final_path = self._package_backup(backup_name, temp_path, compress, encrypt)
            
            # Cleanup temp directory
            shutil.rmtree(temp_path)
            
            self.logger.info(f"Database backup completed: {final_path}")
            return True, final_path
            
        except Exception as e:
            # Cleanup on error
            if os.path.exists(temp_path):
                shutil.rmtree(temp_path)
            raise e
    
    def _create_files_backup(self, backup_name, compress=True, encrypt=True):
        """Create files-only backup"""
        temp_path = os.path.join(self.temp_dir, backup_name)
        os.makedirs(temp_path, exist_ok=True)
        
        try:
            # Backup files
            files_success, files_info = self._backup_files(temp_path)
            if not files_success:
                return False, "Files backup failed"
            
            # Create metadata
            metadata = {
                'type': 'files',
                'created_at': datetime.now().isoformat(),
                'files': files_info,
                'compressed': compress,
                'encrypted': encrypt
            }
            
            with open(os.path.join(temp_path, 'metadata.json'), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Package the backup
            final_path = self._package_backup(backup_name, temp_path, compress, encrypt)
            
            # Cleanup temp directory
            shutil.rmtree(temp_path)
            
            self.logger.info(f"Files backup completed: {final_path}")
            return True, final_path
            
        except Exception as e:
            # Cleanup on error
            if os.path.exists(temp_path):
                shutil.rmtree(temp_path)
            raise e
    
    def _backup_database(self, temp_path):
        """Backup the SQLite database"""
        try:
            db_path = 'qatar_pos.db'  # Adjust path as needed
            
            if not os.path.exists(db_path):
                return False, "Database file not found"
            
            # Create SQL dump
            backup_sql_path = os.path.join(temp_path, 'database.sql')
            
            # Connect to database and create dump
            conn = sqlite3.connect(db_path)
            with open(backup_sql_path, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write('%s\n' % line)
            conn.close()
            
            # Also copy the database file
            backup_db_path = os.path.join(temp_path, 'database.db')
            shutil.copy2(db_path, backup_db_path)
            
            return True, {'sql': backup_sql_path, 'db': backup_db_path}
            
        except Exception as e:
            self.logger.error(f"Database backup failed: {str(e)}")
            return False, str(e)
    
    def _backup_files(self, temp_path):
        """Backup important files and directories"""
        try:
            files_to_backup = [
                'static',
                'templates', 
                'uploads',
                'config.py',
                'requirements.txt'
            ]
            
            files_backup_path = os.path.join(temp_path, 'files')
            os.makedirs(files_backup_path, exist_ok=True)
            
            backed_up_files = []
            
            for item in files_to_backup:
                if os.path.exists(item):
                    dest_path = os.path.join(files_backup_path, item)
                    
                    if os.path.isdir(item):
                        shutil.copytree(item, dest_path, ignore=shutil.ignore_patterns('*.pyc', '__pycache__'))
                    else:
                        shutil.copy2(item, dest_path)
                    
                    backed_up_files.append(item)
            
            return True, backed_up_files
            
        except Exception as e:
            self.logger.error(f"Files backup failed: {str(e)}")
            return False, str(e)
    
    def _package_backup(self, backup_name, temp_path, compress=True, encrypt=True):
        """Package the backup into final format"""
        if compress:
            # Create ZIP file
            zip_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, temp_path)
                        zipf.write(file_path, arc_path)
            
            final_path = zip_path
        else:
            # Just move the directory
            final_path = os.path.join(self.backup_dir, backup_name)
            shutil.move(temp_path, final_path)
        
        if encrypt:
            # Encrypt the backup
            encrypted_path = f"{final_path}.encrypted"
            self._encrypt_file(final_path, encrypted_path)
            
            # Remove unencrypted version
            if os.path.isfile(final_path):
                os.remove(final_path)
            else:
                shutil.rmtree(final_path)
            
            final_path = encrypted_path
        
        return final_path
    
    def _encrypt_file(self, source_path, dest_path):
        """Encrypt a file using Fernet encryption"""
        fernet = Fernet(self.encryption_key)
        
        with open(source_path, 'rb') as source_file:
            file_data = source_file.read()
        
        encrypted_data = fernet.encrypt(file_data)
        
        with open(dest_path, 'wb') as dest_file:
            dest_file.write(encrypted_data)
    
    def _decrypt_file(self, source_path, dest_path):
        """Decrypt a file using Fernet encryption"""
        fernet = Fernet(self.encryption_key)
        
        with open(source_path, 'rb') as source_file:
            encrypted_data = source_file.read()
        
        decrypted_data = fernet.decrypt(encrypted_data)
        
        with open(dest_path, 'wb') as dest_file:
            dest_file.write(decrypted_data)
    
    def restore_backup(self, backup_path):
        """Restore from a backup file"""
        try:
            self.logger.info(f"Starting restore from: {backup_path}")
            
            # Check if backup is encrypted
            if backup_path.endswith('.encrypted'):
                # Decrypt first
                decrypted_path = backup_path.replace('.encrypted', '')
                self._decrypt_file(backup_path, decrypted_path)
                backup_path = decrypted_path
            
            # Extract if compressed
            if backup_path.endswith('.zip'):
                extract_path = os.path.join(self.temp_dir, 'restore_temp')
                os.makedirs(extract_path, exist_ok=True)
                
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(extract_path)
            else:
                extract_path = backup_path
            
            # Read metadata
            metadata_path = os.path.join(extract_path, 'metadata.json')
            if not os.path.exists(metadata_path):
                return False, "Invalid backup: metadata not found"
            
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            # Restore based on type
            if metadata['type'] in ['full', 'database']:
                self._restore_database(extract_path, metadata)
            
            if metadata['type'] in ['full', 'files']:
                self._restore_files(extract_path, metadata)
            
            # Cleanup
            if extract_path != backup_path:
                shutil.rmtree(extract_path)
            
            self.logger.info("Restore completed successfully")
            return True, "Restore completed successfully"
            
        except Exception as e:
            self.logger.error(f"Restore failed: {str(e)}")
            return False, str(e)
    
    def _restore_database(self, extract_path, metadata):
        """Restore database from backup"""
        # This is a simplified version - in production, you'd want more careful handling
        db_backup_path = os.path.join(extract_path, 'database.db')
        if os.path.exists(db_backup_path):
            # Backup current database
            current_db = 'qatar_pos.db'
            if os.path.exists(current_db):
                backup_current = f"{current_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(current_db, backup_current)
            
            # Restore database
            shutil.copy2(db_backup_path, current_db)
    
    def _restore_files(self, extract_path, metadata):
        """Restore files from backup"""
        files_path = os.path.join(extract_path, 'files')
        if os.path.exists(files_path):
            for item in os.listdir(files_path):
                source = os.path.join(files_path, item)
                dest = item
                
                # Backup current if exists
                if os.path.exists(dest):
                    backup_dest = f"{dest}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    if os.path.isdir(dest):
                        shutil.copytree(dest, backup_dest)
                    else:
                        shutil.copy2(dest, backup_dest)
                
                # Restore
                if os.path.isdir(source):
                    if os.path.exists(dest):
                        shutil.rmtree(dest)
                    shutil.copytree(source, dest)
                else:
                    shutil.copy2(source, dest)
    
    def get_backup_history(self):
        """Get list of available backups"""
        backups = []
        
        for file in os.listdir(self.backup_dir):
            if file.startswith('backup_') and not file.startswith('.'):
                file_path = os.path.join(self.backup_dir, file)
                
                # Get file info
                stat = os.stat(file_path)
                
                # Parse backup info from filename
                parts = file.replace('.zip', '').replace('.encrypted', '').split('_')
                backup_type = parts[1] if len(parts) > 1 else 'unknown'
                
                backup_info = {
                    'id': file,
                    'name': file,
                    'type': backup_type,
                    'size': stat.st_size,
                    'created_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'status': 'completed',
                    'encrypted': file.endswith('.encrypted'),
                    'compressed': '.zip' in file
                }
                
                backups.append(backup_info)
        
        # Sort by creation date (newest first)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return backups
    
    def delete_backup(self, backup_id):
        """Delete a backup file"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_id)
            
            if not os.path.exists(backup_path):
                return False, "Backup not found"
            
            if os.path.isfile(backup_path):
                os.remove(backup_path)
            else:
                shutil.rmtree(backup_path)
            
            self.logger.info(f"Backup deleted: {backup_id}")
            return True, "Backup deleted successfully"
            
        except Exception as e:
            self.logger.error(f"Failed to delete backup {backup_id}: {str(e)}")
            return False, str(e)
    
    def cleanup_old_backups(self, retention_days=30):
        """Clean up old backups based on retention policy"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            deleted_count = 0
            
            for file in os.listdir(self.backup_dir):
                if file.startswith('backup_') and not file.startswith('.'):
                    file_path = os.path.join(self.backup_dir, file)
                    file_date = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_date < cutoff_date:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        else:
                            shutil.rmtree(file_path)
                        deleted_count += 1
                        self.logger.info(f"Deleted old backup: {file}")
            
            return True, f"Deleted {deleted_count} old backups"
            
        except Exception as e:
            self.logger.error(f"Cleanup failed: {str(e)}")
            return False, str(e)

# Global backup manager instance
backup_manager = BackupManager()
