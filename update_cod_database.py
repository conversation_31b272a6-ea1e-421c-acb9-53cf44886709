#!/usr/bin/env python3
"""
Update database for COD (Cash on Delivery) functionality
"""

from app import create_app, db
from models.sale import Sale

def update_database():
    """Add COD columns to sales table"""
    app = create_app()
    with app.app_context():
        print("🔄 Updating database for COD functionality...")
        
        # Add new columns to sales table
        columns_to_add = [
            {
                'name': 'cod_status',
                'definition': 'VARCHAR(20) DEFAULT "not_applicable"',
                'description': 'COD status tracking'
            },
            {
                'name': 'delivery_address',
                'definition': 'TEXT',
                'description': 'Delivery address for COD orders'
            },
            {
                'name': 'delivery_phone',
                'definition': 'VARCHAR(20)',
                'description': 'Delivery phone number'
            },
            {
                'name': 'delivery_notes',
                'definition': 'TEXT',
                'description': 'Delivery notes and instructions'
            }
        ]
        
        for column in columns_to_add:
            try:
                sql = f"ALTER TABLE sales ADD COLUMN {column['name']} {column['definition']}"
                with db.engine.connect() as conn:
                    conn.execute(db.text(sql))
                    conn.commit()
                print(f"   ✅ Added {column['name']} column - {column['description']}")
            except Exception as e:
                if 'duplicate column name' in str(e).lower() or 'already exists' in str(e).lower():
                    print(f"   ⚠️  Column {column['name']} already exists")
                else:
                    print(f"   ❌ Error adding {column['name']}: {e}")

        # Verify columns exist
        print("\n🔍 Verifying database structure...")
        try:
            with db.engine.connect() as conn:
                result = conn.execute(db.text("PRAGMA table_info(sales)"))
                columns = [row[1] for row in result.fetchall()]
            
            required_columns = ['cod_status', 'delivery_address', 'delivery_phone', 'delivery_notes']
            for col in required_columns:
                if col in columns:
                    print(f"   ✅ {col}: Present")
                else:
                    print(f"   ❌ {col}: Missing")
            
            print(f"\n📊 Total columns in sales table: {len(columns)}")
            
        except Exception as e:
            print(f"   ❌ Error verifying structure: {e}")
        
        print("\n✅ Database update completed!")

if __name__ == '__main__':
    update_database()
