"""
Qatar POS System - Main Application Entry Point
A comprehensive Point of Sale system designed for Qatar market
with Arabic/English support and Qatar Tax Authority compliance
"""

from flask import Flask
from config import Config
from extensions import db, login_manager, migrate, babel
import os

def create_app(config_class=Config):
    """Application factory pattern"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    babel.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'

    # Import models to ensure they are registered with SQLAlchemy
    import models

    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from models.user import User
        return User.query.get(int(user_id))
    
    # Register blueprints
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.products import products_bp
    from routes.sales import sales_bp
    from routes.inventory import inventory_bp
    from routes.reports import reports_bp
    from routes.customers import customers_bp
    from routes.suppliers import suppliers_bp
    from routes.users import users_bp
    from routes.settings import settings_bp
    from routes.barcode import barcode_bp
    from routes.delivery import delivery_bp
    from routes.backup_api import backup_api_bp
    from routes.data_management import data_management_bp
    from routes.security_api import security_api_bp
    from routes.quick_reports import quick_reports_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(data_management_bp)
    app.register_blueprint(barcode_bp, url_prefix='/barcode')
    app.register_blueprint(delivery_bp, url_prefix='/delivery')
    app.register_blueprint(backup_api_bp)
    app.register_blueprint(security_api_bp)
    app.register_blueprint(quick_reports_bp, url_prefix='/quick_reports')

    # Register cart blueprint
    from routes.cart import cart_bp
    app.register_blueprint(cart_bp, url_prefix='/cart')

    # Create upload directories
    upload_dirs = ['static/uploads/products', 'static/uploads/invoices']
    for directory in upload_dirs:
        os.makedirs(directory, exist_ok=True)

    # Add main route that redirects to dashboard
    @app.route('/')
    def index():
        """Main route - redirect to dashboard"""
        from flask import redirect, url_for
        from flask_login import current_user

        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        else:
            return redirect(url_for('auth.login'))

    # Add health check route
    @app.route('/health')
    def health():
        """Health check endpoint"""
        return {'status': 'ok', 'message': 'Qatar POS System is running', 'version': '1.0.0'}

    # Add API routes for POS system
    @app.route('/api/categories')
    def api_categories():
        """API endpoint for categories (for POS interface)"""
        from models.category import Category
        from utils.helpers import get_user_language

        language = get_user_language()
        categories = Category.query.filter_by(is_active=True).all()

        return {
            'categories': [
                {
                    'id': category.id,
                    'name': category.get_name(language),
                    'description': category.get_description(language)
                }
                for category in categories
            ]
        }

    # Register custom template filters
    @app.template_filter('currency')
    def currency_filter(amount):
        """Format currency for Qatar"""
        if amount is None:
            amount = 0
        return f"{amount:,.2f} ر.ق"

    @app.template_filter('abs')
    def abs_filter(value):
        """Get absolute value"""
        try:
            return abs(float(value))
        except (ValueError, TypeError):
            return 0

    @app.template_filter('percentage')
    def percentage_filter(value):
        """Format percentage"""
        try:
            return f"{float(value):.1f}%"
        except (ValueError, TypeError):
            return "0.0%"

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=2626)
