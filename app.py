"""
Qatar POS System - Main Application Entry Point
A comprehensive Point of Sale system designed for Qatar market
with Arabic/English support and Qatar Tax Authority compliance
"""

from flask import Flask
from config import Config
from extensions import db, login_manager, migrate, babel
import os

def create_app(config_class=Config):
    """Application factory pattern"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    babel.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'

    # Import models to ensure they are registered with SQLAlchemy
    import models

    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from models.user import User
        return User.query.get(int(user_id))
    
    # Register blueprints
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.products import products_bp
    from routes.sales import sales_bp
    from routes.inventory import inventory_bp
    from routes.reports import reports_bp
    from routes.customers import customers_bp
    from routes.suppliers import suppliers_bp
    from routes.users import users_bp
    from routes.settings import settings_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/')
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(suppliers_bp, url_prefix='/suppliers')
    app.register_blueprint(users_bp, url_prefix='/users')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    
    # Create upload directories
    upload_dirs = ['static/uploads/products', 'static/uploads/invoices']
    for directory in upload_dirs:
        os.makedirs(directory, exist_ok=True)

    # Add health check route
    @app.route('/health')
    def health():
        """Health check endpoint"""
        return {'status': 'ok', 'message': 'Qatar POS System is running', 'version': '1.0.0'}

    # Add API routes for POS system
    @app.route('/api/categories')
    def api_categories():
        """API endpoint for categories (for POS interface)"""
        from models.category import Category
        from utils.helpers import get_user_language

        language = get_user_language()
        categories = Category.query.filter_by(is_active=True).all()

        return {
            'categories': [
                {
                    'id': category.id,
                    'name': category.get_name(language),
                    'description': category.get_description(language)
                }
                for category in categories
            ]
        }

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=2626)
