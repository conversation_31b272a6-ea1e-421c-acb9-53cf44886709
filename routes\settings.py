"""
Settings routes for Qatar POS System
Handles system configuration and preferences
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models.setting import Setting, SystemInfo, DEFAULT_SETTINGS
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language
import json

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """Settings overview"""
    language = get_user_language()
    
    # Get settings by category
    categories = {
        'company': Setting.get_category_settings('company'),
        'system': Setting.get_category_settings('system'),
        'pos': Setting.get_category_settings('pos'),
        'inventory': Setting.get_category_settings('inventory'),
        'tax': Setting.get_category_settings('tax')
    }
    
    # Get system info
    system_info = SystemInfo.get_info()
    system_info.update_stats()
    
    # Flatten settings for template compatibility
    settings = {}
    for category, category_settings in categories.items():
        for setting in category_settings:
            settings[setting.key] = setting.get_value()

    return render_template('settings/index.html',
                         categories=categories,
                         settings=settings,
                         system_info=system_info,
                         language=language)

@settings_bp.route('/company', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def company():
    """Company settings"""
    language = get_user_language()
    
    if request.method == 'POST':
        try:
            # Update company settings
            company_settings = [
                'company_name_ar', 'company_name_en',
                'company_address_ar', 'company_address_en',
                'company_phone', 'company_email',
                'company_tax_number', 'company_cr_number'
            ]
            
            for setting_key in company_settings:
                value = request.form.get(setting_key, '').strip()
                if value:
                    Setting.set_setting(setting_key, value, category='company')
            
            flash('تم حفظ إعدادات الشركة بنجاح' if language == 'ar' 
                  else 'Company settings saved successfully', 'success')
            
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الإعدادات' if language == 'ar' 
                  else 'Error saving settings', 'error')
    
    # Get current settings
    settings = {setting.key: setting.get_value() 
               for setting in Setting.get_category_settings('company')}
    
    return render_template('settings/company.html',
                         settings=settings,
                         language=language)

@settings_bp.route('/system', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def system():
    """System settings"""
    language = get_user_language()
    
    if request.method == 'POST':
        try:
            # Update system settings
            Setting.set_setting('default_language', request.form.get('default_language', 'ar'))
            Setting.set_setting('currency_code', request.form.get('currency_code', 'QAR'))
            Setting.set_setting('currency_symbol', request.form.get('currency_symbol', 'ر.ق'))
            Setting.set_setting('timezone', request.form.get('timezone', 'Asia/Qatar'))
            Setting.set_setting('date_format', request.form.get('date_format', 'DD/MM/YYYY'))
            Setting.set_setting('working_days', request.form.get('working_days', '6'), 'integer')
            Setting.set_setting('weekend_day', request.form.get('weekend_day', 'friday'))
            
            flash('تم حفظ إعدادات النظام بنجاح' if language == 'ar' 
                  else 'System settings saved successfully', 'success')
            
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الإعدادات' if language == 'ar' 
                  else 'Error saving settings', 'error')
    
    # Get current settings
    settings = {setting.key: setting.get_value() 
               for setting in Setting.get_category_settings('system')}
    
    return render_template('settings/system.html',
                         settings=settings,
                         language=language)

@settings_bp.route('/pos', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def pos():
    """POS settings"""
    language = get_user_language()
    
    if request.method == 'POST':
        try:
            # Update POS settings
            Setting.set_setting('auto_print_invoice', 
                              'true' if request.form.get('auto_print_invoice') else 'false', 
                              'boolean')
            Setting.set_setting('receipt_footer_ar', request.form.get('receipt_footer_ar', ''))
            Setting.set_setting('receipt_footer_en', request.form.get('receipt_footer_en', ''))
            
            flash('تم حفظ إعدادات نقطة البيع بنجاح' if language == 'ar' 
                  else 'POS settings saved successfully', 'success')
            
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الإعدادات' if language == 'ar' 
                  else 'Error saving settings', 'error')
    
    # Get current settings
    settings = {setting.key: setting.get_value() 
               for setting in Setting.get_category_settings('pos')}
    
    return render_template('settings/pos.html',
                         settings=settings,
                         language=language)

@settings_bp.route('/inventory', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def inventory():
    """Inventory settings"""
    language = get_user_language()
    
    if request.method == 'POST':
        try:
            # Update inventory settings
            Setting.set_setting('low_stock_threshold', 
                              request.form.get('low_stock_threshold', '10'), 
                              'integer')
            
            flash('تم حفظ إعدادات المخزون بنجاح' if language == 'ar' 
                  else 'Inventory settings saved successfully', 'success')
            
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الإعدادات' if language == 'ar' 
                  else 'Error saving settings', 'error')
    
    # Get current settings
    settings = {setting.key: setting.get_value() 
               for setting in Setting.get_category_settings('inventory')}
    
    return render_template('settings/inventory.html',
                         settings=settings,
                         language=language)

@settings_bp.route('/tax', methods=['GET', 'POST'])
@login_required
@permission_required('admin')
def tax():
    """Tax settings"""
    language = get_user_language()
    
    if request.method == 'POST':
        try:
            # Update tax settings
            Setting.set_setting('tax_enabled', 
                              'true' if request.form.get('tax_enabled') else 'false', 
                              'boolean')
            Setting.set_setting('vat_rate', 
                              request.form.get('vat_rate', '0'), 
                              'float')
            
            flash('تم حفظ إعدادات الضرائب بنجاح' if language == 'ar' 
                  else 'Tax settings saved successfully', 'success')
            
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الإعدادات' if language == 'ar' 
                  else 'Error saving settings', 'error')
    
    # Get current settings
    settings = {setting.key: setting.get_value() 
               for setting in Setting.get_category_settings('tax')}
    
    return render_template('settings/tax.html',
                         settings=settings,
                         language=language)

@settings_bp.route('/backup')
@login_required
@permission_required('admin')
def backup():
    """Database backup"""
    language = get_user_language()
    
    return render_template('settings/backup.html',
                         language=language)

@settings_bp.route('/api/backup', methods=['POST'])
@login_required
@permission_required('admin')
def api_backup():
    """Create database backup"""
    try:
        import os
        import subprocess
        from datetime import datetime
        
        # Create backup filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.sql'
        backup_path = os.path.join('backups', backup_filename)
        
        # Create backups directory if it doesn't exist
        os.makedirs('backups', exist_ok=True)
        
        # For SQLite, we can simply copy the database file
        if 'sqlite' in db.engine.url.drivername:
            import shutil
            db_path = db.engine.url.database
            shutil.copy2(db_path, backup_path.replace('.sql', '.db'))
            
            # Update system info
            system_info = SystemInfo.get_info()
            system_info.last_backup = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'filename': backup_filename.replace('.sql', '.db')
            })
        
        return jsonify({
            'success': False,
            'error': 'نوع قاعدة البيانات غير مدعوم للنسخ الاحتياطي'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        })

@settings_bp.route('/api/settings/<category>')
@login_required
@permission_required('admin')
def api_get_settings(category):
    """Get settings by category via API"""
    settings = Setting.get_category_settings(category)
    return jsonify({
        setting.key: {
            'value': setting.get_value(),
            'type': setting.value_type,
            'description': setting.get_description(get_user_language())
        }
        for setting in settings
    })

@settings_bp.route('/api/settings/<category>', methods=['POST'])
@login_required
@permission_required('admin')
def api_update_settings(category):
    """Update settings by category via API"""
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        for key, value in data.items():
            # Get existing setting to preserve type and category
            existing = Setting.query.filter_by(key=key, category=category).first()
            if existing:
                existing.set_value(value)
                existing.updated_at = datetime.utcnow()
            else:
                # Create new setting if it doesn't exist
                new_setting = Setting(
                    key=key,
                    value=value,
                    category=category,
                    value_type='string'
                )
                db.session.add(new_setting)

        db.session.commit()

        # Return appropriate response based on request type
        if request.is_json:
            return jsonify({
                'success': True,
                'message': 'تم حفظ الإعدادات بنجاح'
            })
        else:
            flash('تم حفظ الإعدادات بنجاح' if get_user_language() == 'ar'
                  else 'Settings saved successfully', 'success')
            return redirect(url_for('settings.index'))

    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({
                'success': False,
                'error': f'خطأ في حفظ الإعدادات: {str(e)}'
            })
        else:
            flash('حدث خطأ أثناء حفظ الإعدادات' if get_user_language() == 'ar'
                  else 'Error saving settings', 'error')
            return redirect(url_for('settings.index'))

@settings_bp.route('/initialize')
@login_required
@permission_required('admin')
def initialize():
    """Initialize default settings"""
    language = get_user_language()
    
    try:
        # Create default settings
        for setting_data in DEFAULT_SETTINGS:
            existing = Setting.query.filter_by(key=setting_data['key']).first()
            if not existing:
                setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    value_type=setting_data.get('value_type', 'string'),
                    category=setting_data['category'],
                    description_ar=setting_data.get('description_ar'),
                    description_en=setting_data.get('description_en'),
                    is_public=setting_data.get('is_public', False)
                )
                db.session.add(setting)
        
        # Create system info if not exists
        system_info = SystemInfo.get_info()
        
        db.session.commit()
        
        flash('تم تهيئة الإعدادات الافتراضية بنجاح' if language == 'ar' 
              else 'Default settings initialized successfully', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تهيئة الإعدادات' if language == 'ar' 
              else 'Error initializing settings', 'error')
    
    return redirect(url_for('settings.index'))
