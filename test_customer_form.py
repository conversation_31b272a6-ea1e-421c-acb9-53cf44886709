#!/usr/bin/env python3
"""
اختبار نموذج إنشاء العميل
Test customer creation form
"""

from app import create_app
from models.user import User

def test_customer_form():
    """اختبار نموذج إنشاء العميل"""
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("🧪 اختبار نموذج إنشاء العميل")
            print("=" * 40)
            
            # محاولة تسجيل الدخول
            user = User.query.filter_by(username='admin').first()
            if not user:
                print("  ❌ لا يوجد مستخدم admin للاختبار")
                return
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = client.post('/auth/login', data=login_data, follow_redirects=True)
            
            if response.status_code != 200:
                print(f"  ❌ فشل تسجيل الدخول: {response.status_code}")
                return
            
            print("  ✅ تم تسجيل الدخول بنجاح")
            
            # اختبار صفحة إنشاء العميل
            response = client.get('/customers/create')
            if response.status_code == 200:
                print("  ✅ صفحة إنشاء العميل تعمل")
            else:
                print(f"  ❌ صفحة إنشاء العميل: {response.status_code}")
                return
            
            # اختبار إنشاء عميل فرد
            individual_data = {
                'customer_type': 'individual',
                'first_name_ar': 'أحمد',
                'first_name_en': 'Ahmed',
                'last_name_ar': 'محمد',
                'last_name_en': 'Mohammed',
                'phone': '+974-1234-5678',
                'email': '<EMAIL>',
                'address_ar': 'الدوحة، قطر',
                'address_en': 'Doha, Qatar',
                'city_ar': 'الدوحة',
                'city_en': 'Doha'
            }
            
            response = client.post('/customers/create', data=individual_data, follow_redirects=True)
            
            if response.status_code == 200:
                if 'تم إنشاء العميل بنجاح' in response.get_data(as_text=True) or 'Customer created successfully' in response.get_data(as_text=True):
                    print("  ✅ تم إنشاء عميل فرد بنجاح")
                elif 'حدث خطأ أثناء إنشاء العميل' in response.get_data(as_text=True) or 'Error creating customer' in response.get_data(as_text=True):
                    print("  ❌ خطأ في إنشاء عميل فرد")
                    # البحث عن رسائل الخطأ
                    content = response.get_data(as_text=True)
                    if 'alert-danger' in content:
                        print("      يوجد رسائل خطأ في الصفحة")
                else:
                    print("  ⚠️ تم إنشاء عميل فرد (غير مؤكد)")
            else:
                print(f"  ❌ خطأ في إنشاء عميل فرد: {response.status_code}")
            
            # اختبار إنشاء عميل شركة
            company_data = {
                'customer_type': 'company',
                'company_name_ar': 'شركة الاختبار',
                'company_name_en': 'Test Company',
                'phone': '+974-8765-4321',
                'email': '<EMAIL>',
                'address_ar': 'الدوحة، قطر',
                'address_en': 'Doha, Qatar',
                'city_ar': 'الدوحة',
                'city_en': 'Doha',
                'commercial_registration': '12345',
                'tax_number': '67890'
            }
            
            response = client.post('/customers/create', data=company_data, follow_redirects=True)
            
            if response.status_code == 200:
                if 'تم إنشاء العميل بنجاح' in response.get_data(as_text=True) or 'Customer created successfully' in response.get_data(as_text=True):
                    print("  ✅ تم إنشاء عميل شركة بنجاح")
                elif 'حدث خطأ أثناء إنشاء العميل' in response.get_data(as_text=True) or 'Error creating customer' in response.get_data(as_text=True):
                    print("  ❌ خطأ في إنشاء عميل شركة")
                else:
                    print("  ⚠️ تم إنشاء عميل شركة (غير مؤكد)")
            else:
                print(f"  ❌ خطأ في إنشاء عميل شركة: {response.status_code}")
            
            # اختبار بيانات ناقصة
            incomplete_data = {
                'customer_type': 'individual',
                'first_name_ar': 'أحمد',
                # missing first_name_en
                'last_name_ar': 'محمد',
                'last_name_en': 'Mohammed',
                'phone': '+974-1234-5678'
                # missing email
            }
            
            response = client.post('/customers/create', data=incomplete_data, follow_redirects=False)
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                if 'الاسم الأول مطلوب' in content or 'First name is required' in content:
                    print("  ✅ التحقق من البيانات الناقصة يعمل")
                else:
                    print("  ⚠️ التحقق من البيانات الناقصة قد لا يعمل")
            else:
                print(f"  ❌ خطأ في اختبار البيانات الناقصة: {response.status_code}")

def test_customer_routes():
    """اختبار routes العملاء"""
    app = create_app()
    
    with app.test_client() as client:
        with app.app_context():
            print("\n🌐 اختبار routes العملاء")
            print("=" * 40)
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = client.post('/auth/login', data=login_data, follow_redirects=True)
            
            if response.status_code != 200:
                print(f"  ❌ فشل تسجيل الدخول")
                return
            
            # اختبار الصفحات
            test_routes = [
                ('/customers/', 'قائمة العملاء'),
                ('/customers/create', 'إنشاء عميل'),
            ]
            
            for route, description in test_routes:
                response = client.get(route)
                if response.status_code == 200:
                    print(f"  ✅ {description}: {response.status_code}")
                else:
                    print(f"  ❌ {description}: {response.status_code}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار نموذج العميل")
    print("=" * 60)
    
    test_customer_form()
    test_customer_routes()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار!")
    print("🎯 هذا سيساعد في تحديد مصدر مشكلة إنشاء العميل")
