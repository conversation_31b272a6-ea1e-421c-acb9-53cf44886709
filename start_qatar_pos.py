#!/usr/bin/env python3
"""
Qatar POS System - Enhanced Startup Script
Complete startup script with initialization and health checks
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_banner():
    """Print startup banner"""
    print("=" * 60)
    print("🇶🇦 Qatar POS System - Enhanced Startup")
    print("=" * 60)
    print(f"📅 Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python version: {sys.version.split()[0]}")
    print(f"📁 Working directory: {os.getcwd()}")
    print("=" * 60)

def check_requirements():
    """Check if all required packages are installed"""
    print("🔍 Checking requirements...")
    
    required_packages = [
        'flask', 'flask-sqlalchemy', 'flask-login', 
        'flask-migrate', 'flask-babel', 'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All requirements satisfied")
    return True

def check_database():
    """Check database connectivity"""
    print("\n🗄️ Checking database...")
    
    try:
        from app import create_app
        from extensions import db
        
        app = create_app()
        with app.app_context():
            # Test database connection
            db.create_all()
            
            # Check if admin user exists
            from models.user import User
            admin_user = User.query.filter_by(username='admin').first()
            
            if not admin_user:
                print("👤 Creating default admin user...")
                admin_user = User(
                    username='admin',
                    first_name_ar='المدير',
                    first_name_en='Admin',
                    last_name_ar='العام',
                    last_name_en='User',
                    email='<EMAIL>',
                    role='admin',
                    is_active=True,
                    preferred_language='ar'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ Admin user created successfully")
            else:
                print("✅ Admin user already exists")
        
        print("✅ Database is ready")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def check_directories():
    """Check and create required directories"""
    print("\n📁 Checking directories...")
    
    required_dirs = [
        'static/css',
        'static/js', 
        'static/uploads/products',
        'static/uploads/invoices',
        'templates/customers',
        'templates/suppliers',
        'templates/sales',
        'templates/users',
        'templates/auth'
    ]
    
    created_dirs = []
    
    for directory in required_dirs:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            created_dirs.append(directory)
            print(f"   📂 Created: {directory}")
        else:
            print(f"   ✅ Exists: {directory}")
    
    if created_dirs:
        print(f"✅ Created {len(created_dirs)} directories")
    else:
        print("✅ All directories exist")
    
    return True

def start_server():
    """Start the Flask development server"""
    print("\n🚀 Starting Qatar POS System...")
    print("=" * 60)
    print("📍 Server will be available at:")
    print("   🌐 http://127.0.0.1:2626")
    print("   🌐 http://localhost:2626")
    print("\n🔑 Default login credentials:")
    print("   👤 Username: admin")
    print("   🔒 Password: admin123")
    print("\n💡 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        from app import create_app
        app = create_app()
        
        # Initialize database
        with app.app_context():
            from extensions import db
            db.create_all()
        
        # Start server
        app.run(
            debug=True,
            host='0.0.0.0',
            port=2626,
            use_reloader=True,
            use_debugger=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("👋 Thank you for using Qatar POS System!")
        
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("💡 Please check the error above and try again")

def main():
    """Main startup function"""
    print_banner()
    
    # Run all checks
    if not check_requirements():
        print("\n❌ Requirements check failed")
        sys.exit(1)
    
    if not check_directories():
        print("\n❌ Directory check failed")
        sys.exit(1)
    
    if not check_database():
        print("\n❌ Database check failed")
        sys.exit(1)
    
    print("\n🎉 All checks passed! Starting server...")
    time.sleep(2)
    
    # Start the server
    start_server()

if __name__ == '__main__':
    main()
