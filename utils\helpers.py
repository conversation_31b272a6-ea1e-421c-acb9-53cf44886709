"""
Helper functions for Qatar POS System
Common utilities used across the application
"""

from flask import session, request
from flask_login import current_user
from datetime import datetime, timedelta
import re
import os
from werkzeug.utils import secure_filename

def get_user_language():
    """Get current user's preferred language"""
    if current_user.is_authenticated:
        # Could get from user preferences if we add that field
        return session.get('language', 'ar')
    return session.get('language', 'ar')

def format_currency(amount, language='ar'):
    """Format currency amount for display"""
    if language == 'ar':
        return f"{amount:,.2f} ر.ق"
    else:
        return f"QAR {amount:,.2f}"

def format_date(date, language='ar', format_type='short'):
    """Format date for display"""
    if not date:
        return ''
    
    if format_type == 'short':
        if language == 'ar':
            return date.strftime('%d/%m/%Y')
        else:
            return date.strftime('%m/%d/%Y')
    elif format_type == 'long':
        if language == 'ar':
            return date.strftime('%d %B %Y')
        else:
            return date.strftime('%B %d, %Y')
    elif format_type == 'datetime':
        if language == 'ar':
            return date.strftime('%d/%m/%Y %H:%M')
        else:
            return date.strftime('%m/%d/%Y %I:%M %p')
    
    return str(date)

def format_phone(phone):
    """Format phone number for Qatar"""
    if not phone:
        return ''
    
    # Remove all non-digits
    digits = re.sub(r'\D', '', phone)
    
    # Qatar phone number formatting
    if len(digits) == 8:
        # Local number, add Qatar country code
        return f"+974 {digits[:4]} {digits[4:]}"
    elif len(digits) == 11 and digits.startswith('974'):
        # Already has country code
        return f"+{digits[:3]} {digits[3:7]} {digits[7:]}"
    elif len(digits) == 12 and digits.startswith('974'):
        # International format
        return f"+{digits[:3]} {digits[3:7]} {digits[7:]}"
    
    return phone

def validate_qatar_id(qatar_id):
    """Validate Qatar ID number format"""
    if not qatar_id:
        return False
    
    # Qatar ID should be 11 digits
    if not re.match(r'^\d{11}$', qatar_id):
        return False
    
    return True

def validate_email(email):
    """Validate email format"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def allowed_file(filename, allowed_extensions=None):
    """Check if file extension is allowed"""
    if allowed_extensions is None:
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file, upload_folder, filename=None):
    """Save uploaded file securely"""
    if file and allowed_file(file.filename):
        if filename is None:
            filename = secure_filename(file.filename)
        
        # Add timestamp to avoid conflicts
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{name}_{timestamp}{ext}"
        
        filepath = os.path.join(upload_folder, filename)
        file.save(filepath)
        return filename
    
    return None

def get_working_days_between(start_date, end_date):
    """Get number of working days between two dates (Sunday-Thursday in Qatar)"""
    if start_date > end_date:
        return 0
    
    working_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        # In Qatar: Sunday=6, Monday=0, Tuesday=1, Wednesday=2, Thursday=3, Friday=4, Saturday=5
        # Working days are Sunday(6) to Thursday(3)
        weekday = current_date.weekday()
        if weekday in [0, 1, 2, 3, 6]:  # Monday to Thursday + Sunday
            working_days += 1
        current_date += timedelta(days=1)
    
    return working_days

def is_working_day(date=None):
    """Check if given date is a working day in Qatar"""
    if date is None:
        date = datetime.now()
    
    weekday = date.weekday()
    return weekday in [0, 1, 2, 3, 6]  # Monday to Thursday + Sunday

def get_next_working_day(date=None):
    """Get next working day"""
    if date is None:
        date = datetime.now()
    
    next_day = date + timedelta(days=1)
    while not is_working_day(next_day):
        next_day += timedelta(days=1)
    
    return next_day

def generate_barcode():
    """Generate a simple barcode number"""
    import random
    import string
    
    # Generate 12-digit barcode
    return ''.join(random.choices(string.digits, k=12))

def calculate_discount(original_price, discount_percentage):
    """Calculate discount amount and final price"""
    discount_amount = original_price * (discount_percentage / 100)
    final_price = original_price - discount_amount
    return discount_amount, final_price

def paginate_query(query, page, per_page=20):
    """Paginate SQLAlchemy query"""
    return query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

def get_client_ip():
    """Get client IP address"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def log_user_activity(action, details=None):
    """Log user activity (could be expanded to save to database)"""
    if current_user.is_authenticated:
        timestamp = datetime.now().isoformat()
        ip_address = get_client_ip()
        
        log_entry = {
            'timestamp': timestamp,
            'user_id': current_user.id,
            'username': current_user.username,
            'action': action,
            'details': details,
            'ip_address': ip_address
        }
        
        # For now, just print to console
        # In production, save to database or log file
        print(f"USER_ACTIVITY: {log_entry}")
        
        return log_entry
    
    return None
