{% extends "base.html" %}

{% block title %}
    {% if language == 'ar' %}
        إضافة مستخدم جديد
    {% else %}
        Create New User
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if language == 'ar' %}
                            إضافة مستخدم جديد
                        {% else %}
                            Create New User
                        {% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            {% if language == 'ar' %}عودة{% else %}Back{% endif %}
                        </a>
                    </div>
                </div>
                
                <form method="POST" class="card-body">
                    <!-- Basic Information -->
                    <h5 class="mb-3">
                        {% if language == 'ar' %}المعلومات الأساسية{% else %}Basic Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name_ar">
                                    {% if language == 'ar' %}الاسم الأول (عربي){% else %}First Name (Arabic){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_ar" name="first_name_ar" required dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="first_name_en">
                                    {% if language == 'ar' %}الاسم الأول (إنجليزي){% else %}First Name (English){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name_en" name="first_name_en" required dir="ltr">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name_ar">
                                    {% if language == 'ar' %}اسم العائلة (عربي){% else %}Last Name (Arabic){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_ar" name="last_name_ar" required dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="last_name_en">
                                    {% if language == 'ar' %}اسم العائلة (إنجليزي){% else %}Last Name (English){% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name_en" name="last_name_en" required dir="ltr">
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات الحساب{% else %}Account Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="username">
                                    {% if language == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}يجب أن يكون فريداً ولا يحتوي على مسافات{% else %}Must be unique and contain no spaces{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">
                                    {% if language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password">
                                    {% if language == 'ar' %}كلمة المرور{% else %}Password{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                <small class="form-text text-muted">
                                    {% if language == 'ar' %}يجب أن تكون 6 أحرف على الأقل{% else %}Must be at least 6 characters{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="confirm_password">
                                    {% if language == 'ar' %}تأكيد كلمة المرور{% else %}Confirm Password{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}معلومات الاتصال{% else %}Contact Information{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">
                                    {% if language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="preferred_language">
                                    {% if language == 'ar' %}اللغة المفضلة{% else %}Preferred Language{% endif %}
                                </label>
                                <select class="form-control" id="preferred_language" name="preferred_language">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Role and Permissions -->
                    <h5 class="mb-3 mt-4">
                        {% if language == 'ar' %}الدور والصلاحيات{% else %}Role and Permissions{% endif %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="role">
                                    {% if language == 'ar' %}الدور{% else %}Role{% endif %}
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="role" name="role" required>
                                    <option value="">{% if language == 'ar' %}اختر الدور{% else %}Select Role{% endif %}</option>
                                    <option value="admin">{% if language == 'ar' %}مدير النظام{% else %}System Administrator{% endif %}</option>
                                    <option value="manager">{% if language == 'ar' %}مدير{% else %}Manager{% endif %}</option>
                                    <option value="seller">{% if language == 'ar' %}بائع{% else %}Seller{% endif %}</option>
                                    <option value="accountant">{% if language == 'ar' %}محاسب{% else %}Accountant{% endif %}</option>
                                    <option value="inventory_manager">{% if language == 'ar' %}مدير مخزون{% else %}Inventory Manager{% endif %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_active">
                                    {% if language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" checked>
                                    <label class="custom-control-label" for="is_active">
                                        {% if language == 'ar' %}نشط{% else %}Active{% endif %}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label>
                                    {% if language == 'ar' %}الصلاحيات{% else %}Permissions{% endif %}
                                </label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_products" name="permissions" value="products">
                                            <label class="custom-control-label" for="perm_products">
                                                {% if language == 'ar' %}المنتجات{% else %}Products{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_customers" name="permissions" value="customers">
                                            <label class="custom-control-label" for="perm_customers">
                                                {% if language == 'ar' %}العملاء{% else %}Customers{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_suppliers" name="permissions" value="suppliers">
                                            <label class="custom-control-label" for="perm_suppliers">
                                                {% if language == 'ar' %}الموردين{% else %}Suppliers{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_sales" name="permissions" value="sales">
                                            <label class="custom-control-label" for="perm_sales">
                                                {% if language == 'ar' %}المبيعات{% else %}Sales{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_inventory" name="permissions" value="inventory">
                                            <label class="custom-control-label" for="perm_inventory">
                                                {% if language == 'ar' %}المخزون{% else %}Inventory{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_reports" name="permissions" value="reports">
                                            <label class="custom-control-label" for="perm_reports">
                                                {% if language == 'ar' %}التقارير{% else %}Reports{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_users" name="permissions" value="users">
                                            <label class="custom-control-label" for="perm_users">
                                                {% if language == 'ar' %}المستخدمين{% else %}Users{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="perm_settings" name="permissions" value="settings">
                                            <label class="custom-control-label" for="perm_settings">
                                                {% if language == 'ar' %}الإعدادات{% else %}Settings{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if language == 'ar' %}إنشاء المستخدم{% else %}Create User{% endif %}
                            </button>
                            <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                {% if language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('{% if language == "ar" %}كلمات المرور غير متطابقة{% else %}Passwords do not match{% endif %}');
    } else {
        this.setCustomValidity('');
    }
});

// Username validation (no spaces)
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    if (username.includes(' ')) {
        this.setCustomValidity('{% if language == "ar" %}اسم المستخدم لا يجب أن يحتوي على مسافات{% else %}Username should not contain spaces{% endif %}');
    } else {
        this.setCustomValidity('');
    }
});

// Role-based permission suggestions
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    
    // Clear all checkboxes first
    checkboxes.forEach(cb => cb.checked = false);
    
    // Set permissions based on role
    if (role === 'admin') {
        checkboxes.forEach(cb => cb.checked = true);
    } else if (role === 'manager') {
        ['products', 'customers', 'suppliers', 'sales', 'inventory', 'reports'].forEach(perm => {
            const checkbox = document.getElementById('perm_' + perm);
            if (checkbox) checkbox.checked = true;
        });
    } else if (role === 'cashier') {
        ['products', 'customers', 'sales'].forEach(perm => {
            const checkbox = document.getElementById('perm_' + perm);
            if (checkbox) checkbox.checked = true;
        });
    } else if (role === 'employee') {
        ['products', 'customers'].forEach(perm => {
            const checkbox = document.getElementById('perm_' + perm);
            if (checkbox) checkbox.checked = true;
        });
    }
});
</script>
{% endblock %}
