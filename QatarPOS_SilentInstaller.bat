@echo off
chcp 65001 >nul
title Qatar POS System - Silent Installer

:: مثبت صامت للشركات - لا يحتاج تدخل المستخدم
:: Silent installer for enterprises - no user interaction required

:: تشغيل كمدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:: متغيرات التثبيت
set INSTALL_DIR=C:\QatarPOS
set APP_NAME=Qatar POS System
set VERSION=1.0.0
set PUBLISHER=Qatar POS System

:: بدء التثبيت الصامت
echo [%time%] Starting silent installation of %APP_NAME%...

:: التحقق من وجود الملف
if not exist "dist\QatarPOS.exe" (
    echo [%time%] ERROR: QatarPOS.exe not found in dist folder
    exit /b 1
)

:: إنشاء مجلد التثبيت
if exist "%INSTALL_DIR%" rmdir /S /Q "%INSTALL_DIR%" >nul 2>&1
mkdir "%INSTALL_DIR%" >nul 2>&1

:: نسخ الملفات
copy "dist\QatarPOS.exe" "%INSTALL_DIR%\" >nul 2>&1
if %errorlevel% neq 0 (
    echo [%time%] ERROR: Failed to copy executable
    exit /b 1
)

:: إنشاء أداة إلغاء التثبيت
(
echo @echo off
echo chcp 65001 ^>nul
echo title %APP_NAME% - Uninstaller
echo.
echo Uninstalling %APP_NAME%...
echo.
echo Deleting files...
echo if exist "C:\QatarPOS" rmdir /S /Q "C:\QatarPOS"
echo.
echo Deleting shortcuts...
echo if exist "%%USERPROFILE%%\Desktop\Qatar POS.lnk" del "%%USERPROFILE%%\Desktop\Qatar POS.lnk"
echo if exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS" rmdir /S /Q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS"
echo.
echo Removing from registry...
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /f ^>nul 2^>^&1
echo.
echo Uninstallation completed.
echo pause
) > "%INSTALL_DIR%\Uninstall.bat"

:: إنشاء الاختصارات
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = '%APP_NAME%'; $Shortcut.Save()" >nul 2>&1

:: إنشاء مجلد قائمة البداية
set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Qatar POS
mkdir "%START_MENU_DIR%" >nul 2>&1

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = '%APP_NAME%'; $Shortcut.Save()" >nul 2>&1

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Uninstall Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Uninstall %APP_NAME%'; $Shortcut.Save()" >nul 2>&1

:: تسجيل في النظام
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayName" /t REG_SZ /d "%APP_NAME%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "Publisher" /t REG_SZ /d "%PUBLISHER%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayVersion" /t REG_SZ /d "%VERSION%" /f >nul 2>&1

:: إنشاء ملف سجل التثبيت
(
echo %APP_NAME% - Silent Installation Log
echo =====================================
echo.
echo Installation Date: %date% %time%
echo Installation Path: %INSTALL_DIR%
echo Version: %VERSION%
echo Status: SUCCESS
echo.
echo Files installed:
echo - QatarPOS.exe
echo - Uninstall.bat
echo.
echo Shortcuts created:
echo - Desktop: Qatar POS.lnk
echo - Start Menu: Qatar POS.lnk
echo - Start Menu: Uninstall Qatar POS.lnk
echo.
echo Registry entries:
echo - HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS
) > "%INSTALL_DIR%\Installation_Log.txt"

echo [%time%] Installation completed successfully
echo [%time%] Application installed to: %INSTALL_DIR%
echo [%time%] Desktop shortcut created
echo [%time%] Start Menu shortcuts created
echo [%time%] Registry entries added

exit /b 0
