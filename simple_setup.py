#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد بسيط للنظام
Simple System Setup
"""

import os
from app import create_app
from extensions import db
from models.setting import Setting, SystemInfo

def create_simple_admin():
    """إنشاء مستخدم إداري بسيط"""
    print("👤 إنشاء المستخدم الإداري...")
    
    # Use raw SQL to create admin user with legacy role system
    from sqlalchemy import text
    
    # Check if admin exists
    result = db.session.execute(text("SELECT COUNT(*) FROM users WHERE username = 'admin'"))
    count = result.scalar()
    
    if count > 0:
        print("⚠️ المستخدم الإداري موجود بالفعل")
        return
    
    # Create admin user using raw SQL
    sql = """
    INSERT INTO users (
        username, email, password_hash, 
        first_name_ar, first_name_en, 
        last_name_ar, last_name_en, 
        phone, is_active, role_legacy,
        created_at, updated_at
    ) VALUES (
        'admin', '<EMAIL>', 
        'scrypt:32768:8:1$VQqQJQqQJQqQ$1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        'مدير', 'Admin',
        'النظام', 'System',
        '+974 5555 5555', 1, 'manager',
        datetime('now'), datetime('now')
    )
    """
    
    try:
        db.session.execute(text(sql))
        db.session.commit()
        print("✅ تم إنشاء المستخدم الإداري (admin/admin123)")
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")

def create_basic_settings():
    """إنشاء الإعدادات الأساسية"""
    print("⚙️ إنشاء الإعدادات الأساسية...")
    
    basic_settings = [
        {
            'key': 'company_name_ar',
            'value': 'شركة نقاط البيع القطرية',
            'category': 'company',
            'description_ar': 'اسم الشركة بالعربية'
        },
        {
            'key': 'company_name_en',
            'value': 'Qatar POS Company',
            'category': 'company',
            'description_en': 'Company name in English'
        },
        {
            'key': 'default_language',
            'value': 'ar',
            'category': 'system',
            'description_ar': 'اللغة الافتراضية للنظام'
        },
        {
            'key': 'default_currency',
            'value': 'QAR',
            'category': 'system',
            'description_ar': 'العملة الافتراضية'
        },
        {
            'key': 'tax_enabled',
            'value': 'true',
            'value_type': 'boolean',
            'category': 'tax',
            'description_ar': 'تفعيل الضرائب'
        },
        {
            'key': 'vat_rate',
            'value': '5.0',
            'value_type': 'float',
            'category': 'tax',
            'description_ar': 'معدل ضريبة القيمة المضافة'
        }
    ]
    
    created_count = 0
    for setting_data in basic_settings:
        if not Setting.query.filter_by(key=setting_data['key']).first():
            setting = Setting(
                key=setting_data['key'],
                value=setting_data['value'],
                value_type=setting_data.get('value_type', 'string'),
                category=setting_data['category'],
                description_ar=setting_data.get('description_ar'),
                description_en=setting_data.get('description_en')
            )
            db.session.add(setting)
            created_count += 1
    
    # Create system info
    if not SystemInfo.query.first():
        system_info = SystemInfo()
        db.session.add(system_info)
        created_count += 1
    
    db.session.commit()
    print(f"✅ تم إنشاء {created_count} إعداد")

def main():
    """تشغيل الإعداد البسيط"""
    print("🇶🇦 نظام نقاط البيع القطري - الإعداد البسيط")
    print("=" * 50)
    
    # Remove existing database
    db_path = 'instance/database.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    app = create_app()
    
    with app.app_context():
        print("📋 إنشاء جداول قاعدة البيانات...")
        
        # Import models to register them
        from models.user import User
        from models.setting import Setting, SystemInfo
        
        # Create tables
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # Setup basic data
        create_simple_admin()
        create_basic_settings()
        
        print("\n" + "=" * 50)
        print("🎉 تم الإعداد البسيط بنجاح!")
        print("\n📋 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("\n🌐 يمكنك الآن الوصول للنظام على: http://localhost:2626")
        print("⚙️ يمكنك الوصول للإعدادات على: http://localhost:2626/settings/")

if __name__ == '__main__':
    main()
