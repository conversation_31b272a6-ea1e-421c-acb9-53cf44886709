#!/usr/bin/env python3
"""
اختبار إصلاح BuildError
Test BuildError fix for inventory.quick_adjust endpoint
"""

from app import create_app
from flask import url_for

def test_inventory_routes():
    """اختبار routes المخزون"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار routes المخزون")
        print("=" * 40)
        
        # اختبار وجود الـ routes
        routes_to_test = [
            ('inventory.index', {}),
            ('inventory.quick_adjust', {'product_id': 1}),
            ('inventory.api_quick_adjust', {'product_id': 1}),
            ('inventory.api_product_stock', {'product_id': 1}),
            ('inventory.transactions', {}),
            ('inventory.adjustments', {}),
        ]
        
        for route_name, kwargs in routes_to_test:
            try:
                url = url_for(route_name, **kwargs)
                print(f"  ✅ {route_name}: {url}")
            except Exception as e:
                print(f"  ❌ {route_name}: خطأ - {e}")
        
        print("\n🎯 اختبار URL generation للقوالب:")
        
        # اختبار URLs المستخدمة في القوالب
        template_urls = [
            ("products.view", {'product_id': 1}),
            ("inventory.quick_adjust", {'product_id': 1}),
            ("sales.index", {}),
            ("products.index", {}),
        ]
        
        for route_name, kwargs in template_urls:
            try:
                url = url_for(route_name, **kwargs)
                print(f"  ✅ {route_name}: {url}")
            except Exception as e:
                print(f"  ❌ {route_name}: خطأ - {e}")

def test_app_routes():
    """اختبار جميع routes التطبيق"""
    app = create_app()
    
    with app.app_context():
        print("\n📋 جميع routes المسجلة:")
        print("=" * 40)
        
        # عرض جميع الـ routes
        for rule in app.url_map.iter_rules():
            if rule.endpoint.startswith('inventory'):
                print(f"  📦 {rule.endpoint}: {rule.rule} {list(rule.methods)}")
        
        print("\n🔍 البحث عن quick_adjust:")
        found_quick_adjust = False
        for rule in app.url_map.iter_rules():
            if 'quick' in rule.endpoint.lower():
                print(f"  ✅ {rule.endpoint}: {rule.rule}")
                found_quick_adjust = True
        
        if not found_quick_adjust:
            print("  ❌ لم يتم العثور على quick_adjust routes")

def test_with_client():
    """اختبار مع test client"""
    app = create_app()
    
    with app.test_client() as client:
        print("\n🌐 اختبار HTTP requests:")
        print("=" * 40)
        
        # اختبار الصفحات الأساسية
        test_pages = [
            ('/auth/login', 'صفحة تسجيل الدخول'),
            ('/health', 'صفحة الصحة'),
        ]
        
        for url, description in test_pages:
            try:
                response = client.get(url)
                if response.status_code == 200:
                    print(f"  ✅ {description}: {response.status_code}")
                else:
                    print(f"  ⚠️ {description}: {response.status_code}")
            except Exception as e:
                print(f"  ❌ {description}: خطأ - {e}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار إصلاح BuildError")
    print("=" * 60)
    
    test_inventory_routes()
    test_app_routes()
    test_with_client()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار!")
    print("🎯 إذا ظهرت جميع الـ routes بعلامة ✅، فقد تم إصلاح BuildError!")
