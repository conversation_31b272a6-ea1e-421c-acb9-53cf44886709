{% extends "base.html" %}

{% block title %}
{{ 'حركات المخزون المتقدمة - نظام نقاط البيع القطري' if language == 'ar' else 'Advanced Inventory Movements - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
.movement-card {
    transition: all 0.3s ease;
    border-left: 4px solid #8B1538;
}

.movement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.movement-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.movement-in {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.movement-out {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.movement-adjust {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.movement-return {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.movement-damage {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.movement-transfer {
    background-color: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

.quantity-change {
    font-weight: bold;
    font-size: 1.1rem;
}

.quantity-positive {
    color: #28a745;
}

.quantity-negative {
    color: #dc3545;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-row {
    background: linear-gradient(135deg, #8B1538, #6d1028);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #8B1538;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 13px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background: #dee2e6;
}

.timeline-item:last-child::after {
    display: none;
}

.movement-details {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.export-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .export-buttons {
        flex-direction: column;
    }
    
    .filter-section {
        padding: 15px;
    }
    
    .stats-row {
        padding: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-arrow-left-right"></i>
                    {{ 'حركات المخزون المتقدمة' if language == 'ar' else 'Advanced Inventory Movements' }}
                </h1>
                <div class="export-buttons">
                    <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="bi bi-file-excel"></i>
                        {{ 'تصدير Excel' if language == 'ar' else 'Export Excel' }}
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                        <i class="bi bi-file-pdf"></i>
                        {{ 'تصدير PDF' if language == 'ar' else 'Export PDF' }}
                    </button>
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-left"></i>
                        {{ 'العودة' if language == 'ar' else 'Back' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row stats-row">
        <div class="col-md-3 stat-item">
            <span class="stat-number">{{ movements_stats.total_movements }}</span>
            <span class="stat-label">{{ 'إجمالي الحركات' if language == 'ar' else 'Total Movements' }}</span>
        </div>
        <div class="col-md-3 stat-item">
            <span class="stat-number text-success">{{ movements_stats.total_in }}</span>
            <span class="stat-label">{{ 'حركات الإدخال' if language == 'ar' else 'Inbound Movements' }}</span>
        </div>
        <div class="col-md-3 stat-item">
            <span class="stat-number text-danger">{{ movements_stats.total_out }}</span>
            <span class="stat-label">{{ 'حركات الإخراج' if language == 'ar' else 'Outbound Movements' }}</span>
        </div>
        <div class="col-md-3 stat-item">
            <span class="stat-number text-warning">{{ movements_stats.total_adjustments }}</span>
            <span class="stat-label">{{ 'التعديلات' if language == 'ar' else 'Adjustments' }}</span>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ 'نوع الحركة' if language == 'ar' else 'Movement Type' }}</label>
                    <select class="form-select" name="movement_type" onchange="submitFilter()">
                        <option value="">{{ 'جميع الأنواع' if language == 'ar' else 'All Types' }}</option>
                        <option value="sale" {{ 'selected' if request.args.get('movement_type') == 'sale' }}>{{ 'بيع' if language == 'ar' else 'Sale' }}</option>
                        <option value="purchase" {{ 'selected' if request.args.get('movement_type') == 'purchase' }}>{{ 'شراء' if language == 'ar' else 'Purchase' }}</option>
                        <option value="adjustment" {{ 'selected' if request.args.get('movement_type') == 'adjustment' }}>{{ 'تعديل' if language == 'ar' else 'Adjustment' }}</option>
                        <option value="return" {{ 'selected' if request.args.get('movement_type') == 'return' }}>{{ 'إرجاع' if language == 'ar' else 'Return' }}</option>
                        <option value="damage" {{ 'selected' if request.args.get('movement_type') == 'damage' }}>{{ 'تلف' if language == 'ar' else 'Damage' }}</option>
                        <option value="transfer" {{ 'selected' if request.args.get('movement_type') == 'transfer' }}>{{ 'نقل' if language == 'ar' else 'Transfer' }}</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">{{ 'المنتج' if language == 'ar' else 'Product' }}</label>
                    <select class="form-select" name="product_id" onchange="submitFilter()">
                        <option value="">{{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" {{ 'selected' if request.args.get('product_id')|int == product.id }}>
                            {{ product.get_name(language) }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">{{ 'من تاريخ' if language == 'ar' else 'From Date' }}</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="{{ request.args.get('date_from', '') }}" onchange="submitFilter()">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">{{ 'إلى تاريخ' if language == 'ar' else 'To Date' }}</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="{{ request.args.get('date_to', '') }}" onchange="submitFilter()">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">{{ 'المستخدم' if language == 'ar' else 'User' }}</label>
                    <select class="form-select" name="user_id" onchange="submitFilter()">
                        <option value="">{{ 'جميع المستخدمين' if language == 'ar' else 'All Users' }}</option>
                        {% for user in users %}
                        <option value="{{ user.id }}" {{ 'selected' if request.args.get('user_id')|int == user.id }}>
                            {{ user.get_full_name(language) }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="bi bi-x-circle"></i>
                        {{ 'مسح الفلاتر' if language == 'ar' else 'Clear Filters' }}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Movements Timeline -->
    <div class="row">
        <div class="col-12">
            <div class="card movement-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i>
                        {{ 'سجل حركات المخزون' if language == 'ar' else 'Inventory Movement Log' }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if movements.items %}
                        <div class="timeline">
                            {% for movement in movements.items %}
                            <div class="timeline-item">
                                <div class="movement-details">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <div class="d-flex align-items-center">
                                                <span class="movement-type-badge movement-{{ movement.transaction_type }}">
                                                    {% if movement.transaction_type == 'sale' %}
                                                        <i class="bi bi-cart-dash"></i> {{ 'بيع' if language == 'ar' else 'Sale' }}
                                                    {% elif movement.transaction_type == 'purchase' %}
                                                        <i class="bi bi-cart-plus"></i> {{ 'شراء' if language == 'ar' else 'Purchase' }}
                                                    {% elif movement.transaction_type == 'adjustment' %}
                                                        <i class="bi bi-gear"></i> {{ 'تعديل' if language == 'ar' else 'Adjustment' }}
                                                    {% elif movement.transaction_type == 'return' %}
                                                        <i class="bi bi-arrow-return-left"></i> {{ 'إرجاع' if language == 'ar' else 'Return' }}
                                                    {% elif movement.transaction_type == 'damage' %}
                                                        <i class="bi bi-exclamation-triangle"></i> {{ 'تلف' if language == 'ar' else 'Damage' }}
                                                    {% elif movement.transaction_type == 'transfer' %}
                                                        <i class="bi bi-arrow-left-right"></i> {{ 'نقل' if language == 'ar' else 'Transfer' }}
                                                    {% endif %}
                                                </span>
                                            </div>
                                            <small class="text-muted">
                                                {{ movement.transaction_date.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>{{ movement.product.get_name(language) }}</strong>
                                            <br>
                                            <small class="text-muted">{{ movement.product.sku }}</small>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="quantity-change {{ 'quantity-positive' if movement.quantity_change > 0 else 'quantity-negative' }}">
                                                {% if movement.quantity_change > 0 %}+{% endif %}{{ movement.quantity_change }}
                                            </span>
                                            <br>
                                            <small class="text-muted">{{ 'التغيير' if language == 'ar' else 'Change' }}</small>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <strong>{{ movement.old_quantity }} → {{ movement.new_quantity }}</strong>
                                            <br>
                                            <small class="text-muted">{{ 'المخزون' if language == 'ar' else 'Stock' }}</small>
                                        </div>
                                        <div class="col-md-2">
                                            {% if movement.user %}
                                                <small class="text-muted">
                                                    <i class="bi bi-person"></i>
                                                    {{ movement.user.get_full_name(language) }}
                                                </small>
                                            {% endif %}
                                            {% if movement.reference_number %}
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-hash"></i>
                                                    {{ movement.reference_number }}
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% if movement.notes %}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">
                                                <i class="bi bi-chat-text"></i>
                                                {{ movement.notes }}
                                            </small>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Pagination -->
                        {% if movements.pages > 1 %}
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if movements.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('inventory.movements', page=movements.prev_num, **request.args) }}">
                                        {{ 'السابق' if language == 'ar' else 'Previous' }}
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in movements.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != movements.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('inventory.movements', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if movements.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('inventory.movements', page=movements.next_num, **request.args) }}">
                                        {{ 'التالي' if language == 'ar' else 'Next' }}
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">{{ 'لا توجد حركات مخزون' if language == 'ar' else 'No inventory movements found' }}</h5>
                            <p class="text-muted">{{ 'جرب تغيير الفلاتر أو إضافة منتجات جديدة' if language == 'ar' else 'Try changing filters or adding new products' }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function submitFilter() {
    document.getElementById('filterForm').submit();
}

function clearFilters() {
    // Clear all form inputs
    const form = document.getElementById('filterForm');
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.type === 'date' || input.type === 'text') {
            input.value = '';
        } else if (input.tagName === 'SELECT') {
            input.selectedIndex = 0;
        }
    });
    
    // Submit the form to apply cleared filters
    form.submit();
}

function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = `{{ url_for('inventory.movements') }}?${params.toString()}`;
}

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.location.href = `{{ url_for('inventory.movements') }}?${params.toString()}`;
}

// Auto-refresh every 30 seconds if no filters are applied
{% if not request.args %}
setInterval(() => {
    if (!document.hidden) {
        location.reload();
    }
}, 30000);
{% endif %}
</script>
{% endblock %}
