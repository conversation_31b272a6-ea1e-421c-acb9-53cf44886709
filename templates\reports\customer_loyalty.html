{% extends "base.html" %}

{% block title %}{{ 'تقرير ولاء العملاء' if language == 'ar' else 'Customer Loyalty Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-heart-fill me-2"></i>
                    {{ 'تقرير ولاء العملاء' if language == 'ar' else 'Customer Loyalty Report' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        {{ 'إعدادات التقرير' if language == 'ar' else 'Report Settings' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">{{ 'فترة التحليل (بالأشهر)' if language == 'ar' else 'Analysis Period (Months)' }}</label>
                            <select name="period" class="form-select">
                                <option value="3" {% if period_months == 3 %}selected{% endif %}>3 {{ 'أشهر' if language == 'ar' else 'Months' }}</option>
                                <option value="6" {% if period_months == 6 %}selected{% endif %}>6 {{ 'أشهر' if language == 'ar' else 'Months' }}</option>
                                <option value="12" {% if period_months == 12 %}selected{% endif %}>12 {{ 'شهر' if language == 'ar' else 'Months' }}</option>
                                <option value="24" {% if period_months == 24 %}selected{% endif %}>24 {{ 'شهر' if language == 'ar' else 'Months' }}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ 'الحد الأدنى للمشتريات' if language == 'ar' else 'Minimum Purchases' }}</label>
                            <input type="number" name="min_purchases" class="form-control" value="{{ min_purchases }}" min="1">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    {{ 'تحليل' if language == 'ar' else 'Analyze' }}
                                </button>
                                <a href="{{ url_for('reports.customer_loyalty') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'العملاء المخلصون' if language == 'ar' else 'Loyal Customers' }}</h6>
                                    <h3 class="mb-0">{{ total_loyal_customers }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-people fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي الإيرادات' if language == 'ar' else 'Total Revenue' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(total_revenue) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'متوسط قيمة العميل' if language == 'ar' else 'Avg Customer Value' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(avg_customer_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'العملاء المتميزون' if language == 'ar' else 'Champions' }}</h6>
                                    <h3 class="mb-0">{{ category_counts.champions }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-trophy fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loyalty Categories -->
            <div class="row">
                <!-- Champions -->
                <div class="col-md-6 mb-4">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-trophy-fill me-2"></i>
                                {{ 'العملاء المتميزون' if language == 'ar' else 'Champions' }}
                                <span class="badge bg-dark ms-2">{{ category_counts.champions }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if loyalty_categories.champions %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                                            <th>{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th>{{ 'المعاملات' if language == 'ar' else 'Purchases' }}</th>
                                            <th>{{ 'النقاط' if language == 'ar' else 'Score' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer_data in loyalty_categories.champions[:5] %}
                                        <tr>
                                            <td><strong>{{ customer_data.customer.name }}</strong></td>
                                            <td>{{ "%.0f"|format(customer_data.total_spent) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td><span class="badge bg-info">{{ customer_data.purchase_count }}</span></td>
                                            <td><span class="badge bg-warning">{{ "%.1f"|format(customer_data.loyalty_score) }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center">{{ 'لا يوجد عملاء متميزون' if language == 'ar' else 'No champions found' }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Loyal Customers -->
                <div class="col-md-6 mb-4">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-heart-fill me-2"></i>
                                {{ 'العملاء المخلصون' if language == 'ar' else 'Loyal Customers' }}
                                <span class="badge bg-dark ms-2">{{ category_counts.loyal }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if loyalty_categories.loyal %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                                            <th>{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th>{{ 'المعاملات' if language == 'ar' else 'Purchases' }}</th>
                                            <th>{{ 'النقاط' if language == 'ar' else 'Score' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer_data in loyalty_categories.loyal[:5] %}
                                        <tr>
                                            <td><strong>{{ customer_data.customer.name }}</strong></td>
                                            <td>{{ "%.0f"|format(customer_data.total_spent) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td><span class="badge bg-info">{{ customer_data.purchase_count }}</span></td>
                                            <td><span class="badge bg-success">{{ "%.1f"|format(customer_data.loyalty_score) }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center">{{ 'لا يوجد عملاء مخلصون' if language == 'ar' else 'No loyal customers found' }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Potential Customers -->
                <div class="col-md-6 mb-4">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-star-fill me-2"></i>
                                {{ 'العملاء المحتملون' if language == 'ar' else 'Potential Customers' }}
                                <span class="badge bg-dark ms-2">{{ category_counts.potential }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if loyalty_categories.potential %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                                            <th>{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th>{{ 'المعاملات' if language == 'ar' else 'Purchases' }}</th>
                                            <th>{{ 'النقاط' if language == 'ar' else 'Score' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer_data in loyalty_categories.potential[:5] %}
                                        <tr>
                                            <td><strong>{{ customer_data.customer.name }}</strong></td>
                                            <td>{{ "%.0f"|format(customer_data.total_spent) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td><span class="badge bg-info">{{ customer_data.purchase_count }}</span></td>
                                            <td><span class="badge bg-info">{{ "%.1f"|format(customer_data.loyalty_score) }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center">{{ 'لا يوجد عملاء محتملون' if language == 'ar' else 'No potential customers found' }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- At Risk Customers -->
                <div class="col-md-6 mb-4">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                {{ 'العملاء المعرضون للخطر' if language == 'ar' else 'At Risk Customers' }}
                                <span class="badge bg-dark ms-2">{{ category_counts.at_risk }}</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if loyalty_categories.at_risk %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'العميل' if language == 'ar' else 'Customer' }}</th>
                                            <th>{{ 'آخر شراء' if language == 'ar' else 'Last Purchase' }}</th>
                                            <th>{{ 'الأيام' if language == 'ar' else 'Days Ago' }}</th>
                                            <th>{{ 'النقاط' if language == 'ar' else 'Score' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer_data in loyalty_categories.at_risk[:5] %}
                                        <tr>
                                            <td><strong>{{ customer_data.customer.name }}</strong></td>
                                            <td>
                                                {% if customer_data.last_purchase %}
                                                {{ customer_data.last_purchase.strftime('%Y-%m-%d') }}
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge {% if customer_data.days_since_last > 90 %}bg-danger{% elif customer_data.days_since_last > 60 %}bg-warning{% else %}bg-secondary{% endif %}">
                                                    {{ customer_data.days_since_last }}
                                                </span>
                                            </td>
                                            <td><span class="badge bg-danger">{{ "%.1f"|format(customer_data.loyalty_score) }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted text-center">{{ 'لا يوجد عملاء معرضون للخطر' if language == 'ar' else 'No at-risk customers found' }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Create workbook with multiple sheets for each category
    const wb = XLSX.utils.book_new();
    
    // Add summary sheet
    const summaryData = [
        ['Category', 'Count'],
        ['Champions', {{ category_counts.champions }}],
        ['Loyal', {{ category_counts.loyal }}],
        ['Potential', {{ category_counts.potential }}],
        ['New', {{ category_counts.new }}],
        ['At Risk', {{ category_counts.at_risk }}]
    ];
    const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
    
    XLSX.writeFile(wb, 'customer_loyalty_report.xlsx');
}

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
