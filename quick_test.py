#!/usr/bin/env python3
"""
Qatar POS System - Quick Test
Test what works on your system
"""

import sys
import os

def test_python():
    print("🐍 Testing Python...")
    print(f"   Version: {sys.version}")
    print(f"   Executable: {sys.executable}")
    return True

def test_flask():
    print("\n📦 Testing Flask...")
    try:
        import flask
        print(f"   ✅ Flask {flask.__version__} available")
        return True
    except ImportError:
        print("   ❌ Flask not available")
        print("   💡 Install with: pip install flask")
        return False

def test_http_server():
    print("\n🌐 Testing HTTP server...")
    try:
        import http.server
        import socketserver
        print("   ✅ HTTP server available")
        return True
    except ImportError:
        print("   ❌ HTTP server not available")
        return False

def test_port():
    print("\n🔌 Testing port 5000...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("   ⚠️ Port 5000 is in use")
            return False
        else:
            print("   ✅ Port 5000 is available")
            return True
    except Exception as e:
        print(f"   ❌ Port test failed: {e}")
        return False

def recommend_action():
    print("\n" + "="*50)
    print("📋 RECOMMENDATIONS:")
    
    flask_available = False
    try:
        import flask
        flask_available = True
    except ImportError:
        pass
    
    if flask_available:
        print("✅ Flask is available - try these:")
        print("   python working_server.py")
        print("   python run_now.py")
        print("   python test_server.py")
    else:
        print("⚠️ Flask not available - try these:")
        print("   python emergency_server.py")
        print("   python run_now.py")
        print("   pip install flask")
    
    print("\n🌐 Then open: http://localhost:5000")

def main():
    print("🇶🇦 Qatar POS System - Quick Test")
    print("نظام نقاط البيع القطري - اختبار سريع")
    print("="*50)
    
    # Run tests
    test_python()
    flask_ok = test_flask()
    http_ok = test_http_server()
    port_ok = test_port()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS:")
    print(f"🐍 Python: ✅ OK")
    print(f"📦 Flask: {'✅ OK' if flask_ok else '❌ NOT AVAILABLE'}")
    print(f"🌐 HTTP Server: {'✅ OK' if http_ok else '❌ NOT AVAILABLE'}")
    print(f"🔌 Port 5000: {'✅ AVAILABLE' if port_ok else '⚠️ IN USE'}")
    
    recommend_action()

if __name__ == '__main__':
    main()
