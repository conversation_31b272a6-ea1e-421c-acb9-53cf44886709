{% extends "base.html" %}

{% block title %}
{{ 'تقرير إعادة الطلب' if language == 'ar' else 'Reorder Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-arrow-repeat"></i>
                        {{ 'تقرير إعادة الطلب' if language == 'ar' else 'Reorder Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'القسم' if language == 'ar' else 'Category' }}</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">{{ 'جميع الأقسام' if language == 'ar' else 'All Categories' }}</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {{ 'selected' if selected_category == category.id }}>
                                            {{ category.name_ar if language == 'ar' else category.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'مستوى الأولوية' if language == 'ar' else 'Priority Level' }}</label>
                                    <select name="priority" class="form-select">
                                        <option value="">{{ 'جميع المستويات' if language == 'ar' else 'All Priorities' }}</option>
                                        <option value="critical" {{ 'selected' if priority == 'critical' }}>
                                            {{ 'حرج (نفد المخزون)' if language == 'ar' else 'Critical (Out of Stock)' }}
                                        </option>
                                        <option value="high" {{ 'selected' if priority == 'high' }}>
                                            {{ 'عالي (أقل من الحد الأدنى)' if language == 'ar' else 'High (Below Minimum)' }}
                                        </option>
                                        <option value="medium" {{ 'selected' if priority == 'medium' }}>
                                            {{ 'متوسط (يساوي الحد الأدنى)' if language == 'ar' else 'Medium (At Minimum)' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel"></i>
                                            {{ 'تطبيق الفلتر' if language == 'ar' else 'Apply Filter' }}
                                        </button>
                                        <a href="{{ url_for('reports.reorder_report') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</h6>
                                            <h3 class="mb-0">{{ critical_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-exclamation-triangle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'أقل من الحد الأدنى' if language == 'ar' else 'Below Minimum' }}</h6>
                                            <h3 class="mb-0">{{ high_priority_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-arrow-down fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'في الحد الأدنى' if language == 'ar' else 'At Minimum' }}</h6>
                                            <h3 class="mb-0">{{ medium_priority_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-dash-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                                            <h3 class="mb-0">{{ products|length }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-box-seam fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reorder Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الأولوية' if language == 'ar' else 'Priority' }}</th>
                                    <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    {% if not selected_category %}
                                    <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                    {% endif %}
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'الحد الأدنى' if language == 'ar' else 'Minimum Stock' }}</th>
                                    <th>{{ 'الكمية المطلوبة' if language == 'ar' else 'Required Quantity' }}</th>
                                    <th>{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</th>
                                    <th>{{ 'التكلفة المقدرة' if language == 'ar' else 'Estimated Cost' }}</th>
                                    <th>{{ 'آخر حركة' if language == 'ar' else 'Last Movement' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        {% if product.current_stock <= 0 %}
                                        <span class="badge bg-danger">{{ 'حرج' if language == 'ar' else 'Critical' }}</span>
                                        {% elif product.current_stock < product.minimum_stock %}
                                        <span class="badge bg-warning">{{ 'عالي' if language == 'ar' else 'High' }}</span>
                                        {% else %}
                                        <span class="badge bg-info">{{ 'متوسط' if language == 'ar' else 'Medium' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ product.barcode or 'N/A' }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                        {% if product.sku %}
                                        <br><small class="text-muted">{{ product.sku }}</small>
                                        {% endif %}
                                    </td>
                                    {% if not selected_category %}
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-secondary">
                                            {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ product.current_stock }}
                                        </span>
                                    </td>
                                    <td>{{ product.minimum_stock or 0 }}</td>
                                    <td>
                                        {% set required_qty = (product.minimum_stock or 0) * 2 - product.current_stock %}
                                        {% if required_qty > 0 %}
                                        <strong class="text-primary">{{ required_qty }}</strong>
                                        {% else %}
                                        <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>
                                        {% if required_qty > 0 %}
                                        <strong>{{ "%.2f"|format((product.cost_price or 0) * required_qty) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                        {% else %}
                                        <span class="text-muted">0.00 {{ 'ر.ق' if language == 'ar' else 'QAR' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ 'غير متوفر' if language == 'ar' else 'N/A' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if products %}
                    <!-- Summary Footer -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5>{{ 'ملخص إعادة الطلب' if language == 'ar' else 'Reorder Summary' }}</h5>
                                <p class="mb-0">
                                    {{ 'إجمالي التكلفة المقدرة لإعادة الطلب:' if language == 'ar' else 'Total Estimated Reorder Cost:' }}
                                    <strong>
                                        {% set total_cost = 0 %}
                                        {% for product in products %}
                                            {% set required_qty = (product.minimum_stock or 0) * 2 - product.current_stock %}
                                            {% if required_qty > 0 %}
                                                {% set total_cost = total_cost + ((product.cost_price or 0) * required_qty) %}
                                            {% endif %}
                                        {% endfor %}
                                        {{ "%.2f"|format(total_cost) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}
                                    </strong>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
