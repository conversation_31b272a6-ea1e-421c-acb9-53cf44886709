# 🇶🇦 نظام نقاط البيع القطري - دليل مبسط

## ⚡ تشغيل فوري - خطوة واحدة فقط!

### الطريقة الأسهل:
```
python start_here.py
```
**هذا الملف سيقوم بكل شيء تلقائياً!**

---

## 🚀 تشغيل النظام في 3 خطوات (الطريقة اليدوية)

### الخطوة 1: تحقق من Python
افتح Command Prompt واكتب:
```
python --version
```

إذا لم يعمل، حمل Python من: https://python.org/downloads/

### الخطوة 2: تثبيت Flask
```
pip install flask
```

### الخطوة 3: تشغيل النظام
```
python test_server.py
```

## 🌐 فتح النظام

افتح المتصفح واذهب إلى:
**http://localhost:5000**

---

## 📱 إذا لم يعمل الرابط

جرب هذه الروابط:
- http://127.0.0.1:5000
- http://0.0.0.0:5000

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**الحل**: تثبيت Python وإضافته للـ PATH

### مشكلة: "No module named 'flask'"
**الحل**: 
```
pip install flask
```

### مشكلة: "Port 5000 is already in use"
**الحل**: أغلق البرامج الأخرى أو استخدم منفذ آخر

---

## 📋 ملفات التشغيل المختلفة

| الملف | الوصف | التعقيد |
|-------|--------|---------|
| `start_here.py` | **ابدأ من هنا - تلقائي** | ⭐ |
| `test_server.py` | خادم اختبار بسيط | ⭐ |
| `simple_run.py` | نظام بسيط مع تسجيل دخول | ⭐⭐ |
| `run.py` | النظام الكامل | ⭐⭐⭐ |
| `start.bat` | تشغيل تلقائي (Windows) | ⭐ |
| `install.bat` | تثبيت المتطلبات (Windows) | ⭐ |

## 🛠️ ملفات المساعدة

| الملف | الوصف |
|-------|--------|
| `check_system.py` | فحص النظام والمتطلبات |
| `fix_system.py` | إصلاح المشاكل تلقائياً |
| `run_all_tests.py` | اختبار جميع الملفات |

---

## 🎯 الهدف من كل ملف

### `test_server.py` - للاختبار السريع
- لا يحتاج قاعدة بيانات
- لا يحتاج مكتبات إضافية
- فقط للتأكد أن النظام يعمل

### `simple_run.py` - نظام بسيط
- يحتاج Flask فقط
- يحتوي على تسجيل دخول
- مناسب للتجربة

### `run.py` - النظام الكامل
- يحتاج جميع المكتبات
- قاعدة بيانات كاملة
- جميع المميزات

---

## 🆘 طلب المساعدة

إذا واجهت مشاكل:

1. **تأكد من تثبيت Python**
2. **جرب الملف البسيط أولاً**: `python test_server.py`
3. **تحقق من رسائل الخطأ في Command Prompt**
4. **جرب روابط مختلفة**

---

## ✅ علامات النجاح

عندما يعمل النظام ستشاهد:

```
* Running on http://127.0.0.1:5000
* Debug mode: on
```

وفي المتصفح ستشاهد:
- شعار قطر 🇶🇦
- "نظام نقاط البيع القطري"
- "النظام يعمل بنجاح!"

---

**مبروك! النظام يعمل الآن! 🎉**
