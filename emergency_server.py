#!/usr/bin/env python3
"""
Qatar POS System - Emergency Server
This will work with just Python - no additional libraries needed
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import sys
import os

# HTML content for the main page
MAIN_PAGE = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري - Qatar POS System</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            padding: 50px;
            border-radius: 25px;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            max-width: 800px;
            width: 90%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            font-size: 3.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        h2 {
            font-size: 2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .success {
            background: rgba(40, 167, 69, 0.3);
            border: 2px solid rgba(40, 167, 69, 0.6);
            padding: 20px;
            border-radius: 15px;
            margin: 30px 0;
            font-size: 1.3em;
            font-weight: bold;
        }
        .info {
            background: rgba(23, 162, 184, 0.3);
            border: 2px solid rgba(23, 162, 184, 0.6);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        .feature h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        .links {
            margin-top: 40px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(40, 167, 69, 0.9);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .server-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="status">🟢 ONLINE</div>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        
        <div class="success">
            ✅ النظام يعمل بنجاح!<br>
            ✅ System Running Successfully!
        </div>
        
        <div class="info">
            <strong>🌐 معلومات الخادم / Server Information:</strong><br>
            📍 URL: http://localhost:5000<br>
            🔧 Engine: Python HTTP Server<br>
            🐍 Python Version: ''' + sys.version.split()[0] + '''<br>
            ⏰ Status: Active & Ready<br>
            💡 No additional libraries required!
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🌐 متعدد اللغات</h3>
                <p>Arabic & English Support</p>
            </div>
            <div class="feature">
                <h3>💰 الريال القطري</h3>
                <p>QAR Currency Ready</p>
            </div>
            <div class="feature">
                <h3>🏢 للشركات القطرية</h3>
                <p>Qatar Business Ready</p>
            </div>
            <div class="feature">
                <h3>🆔 الهوية القطرية</h3>
                <p>Qatar ID Support</p>
            </div>
            <div class="feature">
                <h3>📅 أسبوع العمل</h3>
                <p>6-Day Work Week</p>
            </div>
            <div class="feature">
                <h3>🕌 إغلاق الجمعة</h3>
                <p>Friday Closure</p>
            </div>
        </div>
        
        <div class="server-info">
            <strong>🔧 Technical Details:</strong><br>
            • Server Type: Python Built-in HTTP Server<br>
            • Port: 5000<br>
            • Protocol: HTTP/1.1<br>
            • Encoding: UTF-8<br>
            • Status: Fully Operational
        </div>
        
        <div class="links">
            <a href="/test">🧪 Test Page</a>
            <a href="/health">💚 Health Check</a>
            <a href="/about">ℹ️ About System</a>
        </div>
        
        <div style="margin-top: 40px; opacity: 0.8;">
            <p><strong>🎉 مبروك! النظام يعمل الآن!</strong></p>
            <p><strong>🎉 Congratulations! The system is now working!</strong></p>
        </div>
    </div>
</body>
</html>'''

TEST_PAGE = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>صفحة الاختبار - Test Page</title>
    <style>
        body { font-family: Arial; padding: 40px; background: #f0f8ff; text-align: center; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #c3e6cb; }
        h1 { color: #333; }
        a { color: #007bff; text-decoration: none; font-weight: bold; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 صفحة الاختبار / Test Page</h1>
        <div class="success">
            <h2>✅ الاختبار نجح بشكل مثالي!</h2>
            <h2>✅ Test Passed Perfectly!</h2>
        </div>
        <p><strong>🇶🇦 Qatar POS System Test Successful</strong></p>
        <p>Python HTTP Server is working correctly!</p>
        <p>خادم Python HTTP يعمل بشكل صحيح!</p>
        <p><a href="/">← العودة للرئيسية / Back to Home</a></p>
    </div>
</body>
</html>'''

HEALTH_PAGE = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Health Check</title>
    <style>
        body { font-family: Arial; padding: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .healthy { color: #28a745; font-weight: bold; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💚 Health Check / فحص الصحة</h1>
        <div class="info">
            <p class="healthy">✅ Status: HEALTHY</p>
            <p class="healthy">✅ الحالة: سليم</p>
        </div>
        <div class="info">
            <strong>System Information:</strong><br>
            • Server: Python HTTP Server<br>
            • Port: 5000<br>
            • Python: ''' + sys.version + '''<br>
            • Working: YES
        </div>
        <p><a href="/">← Back to Home</a></p>
    </div>
</body>
</html>'''

ABOUT_PAGE = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>حول النظام - About System</title>
    <style>
        body { font-family: Arial; padding: 40px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; }
        h1 { color: #333; text-align: center; }
        .section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .feature-item { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇶🇦 حول نظام نقاط البيع القطري</h1>
        <h2 style="text-align: center;">About Qatar POS System</h2>
        
        <div class="section">
            <h3>📋 وصف النظام / System Description</h3>
            <p>نظام نقاط البيع القطري هو حل شامل مصمم خصيصاً للشركات في دولة قطر، يدعم اللغتين العربية والإنجليزية ويتوافق مع القوانين والمتطلبات التجارية القطرية.</p>
            <p>Qatar POS System is a comprehensive solution designed specifically for businesses in Qatar, supporting both Arabic and English languages and complying with Qatari commercial laws and requirements.</p>
        </div>
        
        <div class="section">
            <h3>✨ المميزات الرئيسية / Key Features</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🛒 نقطة البيع</h4>
                    <p>واجهة سهلة الاستخدام لإجراء المبيعات</p>
                </div>
                <div class="feature-item">
                    <h4>📦 إدارة المخزون</h4>
                    <p>تتبع المنتجات والكميات في الوقت الفعلي</p>
                </div>
                <div class="feature-item">
                    <h4>👥 إدارة العملاء</h4>
                    <p>قاعدة بيانات شاملة للعملاء</p>
                </div>
                <div class="feature-item">
                    <h4>📊 التقارير</h4>
                    <p>تقارير مفصلة للمبيعات والأرباح</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🇶🇦 متطلبات قطر / Qatar Requirements</h3>
            <ul>
                <li>✅ دعم الريال القطري (QAR)</li>
                <li>✅ أسبوع عمل 6 أيام (إغلاق الجمعة)</li>
                <li>✅ دعم رقم الهوية القطرية</li>
                <li>✅ السجل التجاري القطري</li>
                <li>✅ المنطقة الزمنية القطرية</li>
                <li>✅ التوافق مع القوانين التجارية</li>
            </ul>
        </div>
        
        <p style="text-align: center; margin-top: 40px;">
            <a href="/" style="color: #007bff; text-decoration: none; font-weight: bold;">← العودة للرئيسية / Back to Home</a>
        </p>
    </div>
</body>
</html>'''

class QatarPOSHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(MAIN_PAGE.encode('utf-8'))
        
        elif self.path == '/test':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(TEST_PAGE.encode('utf-8'))
        
        elif self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(HEALTH_PAGE.encode('utf-8'))
        
        elif self.path == '/about':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(ABOUT_PAGE.encode('utf-8'))
        
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(b'''
            <div style="font-family: Arial; padding: 40px; text-align: center;">
                <h1>404 - Page Not Found</h1>
                <p><a href="/">Back to Home</a></p>
            </div>
            ''')

def open_browser():
    """Open browser after a short delay"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass

def main():
    print("🇶🇦 Qatar POS System - Emergency Server")
    print("نظام نقاط البيع القطري - خادم الطوارئ")
    print("=" * 60)
    print("🔧 Using Python built-in HTTP server")
    print("🔧 استخدام خادم HTTP المدمج في Python")
    print("💡 No additional libraries required!")
    print("💡 لا يحتاج مكتبات إضافية!")
    print("=" * 60)
    print("📍 Server URL: http://localhost:5000")
    print("🧪 Test page: http://localhost:5000/test")
    print("💚 Health check: http://localhost:5000/health")
    print("ℹ️ About page: http://localhost:5000/about")
    print("=" * 60)
    print("🌐 Opening browser automatically...")
    print("🌐 فتح المتصفح تلقائياً...")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        with socketserver.TCPServer(("", 5000), QatarPOSHandler) as httpd:
            print("✅ Server started successfully!")
            print("✅ تم تشغيل الخادم بنجاح!")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped. Thank you for using Qatar POS System!")
        print("👋 تم إيقاف الخادم. شكراً لاستخدام نظام نقاط البيع القطري!")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("💡 Try running on a different port or check if port 5000 is available")

if __name__ == '__main__':
    main()
