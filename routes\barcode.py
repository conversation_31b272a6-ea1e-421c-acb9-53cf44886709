"""
Barcode routes for Qatar POS System
Handles barcode generation, printing, and management
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime
from models.product import Product
from models.category import Category
from extensions import db
from utils.decorators import permission_required
from utils.helpers import get_user_language
from utils.barcode_generator import barcode_generator, generate_product_barcode, create_labels_sheet
import os

barcode_bp = Blueprint('barcode', __name__)

@barcode_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """Barcode management dashboard"""
    language = get_user_language()
    
    # Get statistics
    total_products = Product.query.filter_by(is_active=True).count()
    products_with_barcode = Product.query.filter(
        Product.is_active == True,
        Product.barcode.isnot(None),
        Product.barcode != ''
    ).count()
    products_without_barcode = total_products - products_with_barcode
    
    # Get recent products without barcodes
    recent_products = Product.query.filter(
        Product.is_active == True,
        db.or_(Product.barcode.is_(None), Product.barcode == '')
    ).order_by(Product.created_at.desc()).limit(10).all()
    
    return render_template('barcode/index.html',
                         total_products=total_products,
                         products_with_barcode=products_with_barcode,
                         products_without_barcode=products_without_barcode,
                         recent_products=recent_products,
                         language=language)

@barcode_bp.route('/generate')
@login_required
@permission_required('admin')
def generate():
    """Generate barcodes for products"""
    language = get_user_language()
    
    # Get products without barcodes
    products = Product.query.filter(
        Product.is_active == True,
        db.or_(Product.barcode.is_(None), Product.barcode == '')
    ).order_by(Product.name_ar).all()
    
    # Get categories for filtering
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_ar).all()
    
    return render_template('barcode/generate.html',
                         products=products,
                         categories=categories,
                         language=language)

@barcode_bp.route('/api/generate', methods=['POST'])
@login_required
@permission_required('admin')
def api_generate():
    """API endpoint to generate barcodes"""
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        barcode_type = data.get('barcode_type', 'EAN13')
        
        if not product_ids:
            return jsonify({
                'success': False,
                'message': 'لم يتم تحديد منتجات' if get_user_language() == 'ar' else 'No products selected'
            })
        
        generated_count = 0
        errors = []
        
        for product_id in product_ids:
            product = Product.query.get(product_id)
            if product:
                try:
                    # Generate barcode
                    barcode = product.generate_barcode(barcode_type)
                    generated_count += 1
                except Exception as e:
                    errors.append(f"Product {product.get_name()}: {str(e)}")
        
        db.session.commit()
        
        language = get_user_language()
        message = f"تم توليد {generated_count} باركود" if language == 'ar' else f"Generated {generated_count} barcodes"
        
        return jsonify({
            'success': True,
            'message': message,
            'generated_count': generated_count,
            'errors': errors
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f"خطأ في توليد الباركود: {str(e)}" if get_user_language() == 'ar' else f"Error generating barcodes: {str(e)}"
        }), 500

@barcode_bp.route('/image/<barcode>')
def barcode_image(barcode):
    """Serve barcode image"""
    try:
        from flask import send_file
        import os

        # Check if barcode image exists
        image_path = os.path.join('static', 'barcodes', f'{barcode}.png')

        if os.path.exists(image_path):
            return send_file(image_path, mimetype='image/png')
        else:
            # Generate barcode on-the-fly if it doesn't exist
            from utils.barcode_generator import generate_barcode_image

            # Generate barcode image
            success, image_path = generate_barcode_image(barcode, 'EAN13')

            if success and os.path.exists(image_path):
                return send_file(image_path, mimetype='image/png')
            else:
                # Return a placeholder or error image
                from flask import abort
                abort(404)

    except Exception as e:
        from flask import abort
        abort(404)

@barcode_bp.route('/print')
@login_required
@permission_required('admin')
def print_labels():
    """Print barcode labels"""
    language = get_user_language()
    
    # Get products with barcodes
    products = Product.query.filter(
        Product.is_active == True,
        Product.barcode.isnot(None),
        Product.barcode != ''
    ).order_by(Product.name_ar).all()
    
    # Get categories for filtering
    categories = Category.query.filter_by(is_active=True).order_by(Category.name_ar).all()
    
    return render_template('barcode/print.html',
                         products=products,
                         categories=categories,
                         language=language)

@barcode_bp.route('/api/print', methods=['POST'])
@login_required
@permission_required('admin')
def api_print():
    """API endpoint to generate printable labels"""
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        labels_per_row = data.get('labels_per_row', 3)
        language = get_user_language()
        
        if not product_ids:
            return jsonify({
                'success': False,
                'message': 'لم يتم تحديد منتجات' if language == 'ar' else 'No products selected'
            })
        
        # Get products
        products = Product.query.filter(
            Product.id.in_(product_ids),
            Product.is_active == True,
            Product.barcode.isnot(None),
            Product.barcode != ''
        ).all()
        
        if not products:
            return jsonify({
                'success': False,
                'message': 'لا توجد منتجات صالحة للطباعة' if language == 'ar' else 'No valid products for printing'
            })
        
        # Generate individual barcode images first
        for product in products:
            filename, message = generate_product_barcode(product, language)
            if not filename:
                return jsonify({
                    'success': False,
                    'message': f"خطأ في توليد باركود {product.get_name(language)}: {message}"
                })
        
        # Create labels sheet
        sheet_filename, message = create_labels_sheet(products, language)
        
        if sheet_filename:
            return jsonify({
                'success': True,
                'message': message,
                'sheet_url': url_for('static', filename=f'barcodes/{sheet_filename}'),
                'filename': sheet_filename
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"خطأ في إنشاء الملصقات: {str(e)}" if get_user_language() == 'ar' else f"Error creating labels: {str(e)}"
        }), 500

@barcode_bp.route('/api/product/<int:product_id>/barcode')
@login_required
@permission_required('admin')
def api_product_barcode(product_id):
    """Generate barcode image for specific product"""
    try:
        product = Product.query.get_or_404(product_id)
        language = get_user_language()
        
        if not product.barcode:
            return jsonify({
                'success': False,
                'message': 'المنتج لا يحتوي على باركود' if language == 'ar' else 'Product has no barcode'
            })
        
        filename, message = generate_product_barcode(product, language)
        
        if filename:
            return jsonify({
                'success': True,
                'message': message,
                'barcode_url': url_for('static', filename=f'barcodes/{filename}'),
                'filename': filename
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"خطأ في توليد الباركود: {str(e)}" if get_user_language() == 'ar' else f"Error generating barcode: {str(e)}"
        }), 500

@barcode_bp.route('/download/<filename>')
@login_required
@permission_required('admin')
def download_file(filename):
    """Download barcode file"""
    try:
        file_path = os.path.join('static', 'barcodes', filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            flash('الملف غير موجود' if get_user_language() == 'ar' else 'File not found', 'error')
            return redirect(url_for('barcode.index'))
    except Exception as e:
        flash('خطأ في تحميل الملف' if get_user_language() == 'ar' else 'Error downloading file', 'error')
        return redirect(url_for('barcode.index'))

@barcode_bp.route('/api/products/filter')
@login_required
@permission_required('admin')
def api_filter_products():
    """Filter products for barcode operations"""
    try:
        category_id = request.args.get('category_id', type=int)
        has_barcode = request.args.get('has_barcode', 'all')
        search = request.args.get('search', '').strip()
        
        query = Product.query.filter_by(is_active=True)
        
        # Apply category filter
        if category_id:
            query = query.filter_by(category_id=category_id)
        
        # Apply barcode filter
        if has_barcode == 'yes':
            query = query.filter(
                Product.barcode.isnot(None),
                Product.barcode != ''
            )
        elif has_barcode == 'no':
            query = query.filter(
                db.or_(Product.barcode.is_(None), Product.barcode == '')
            )
        
        # Apply search filter
        if search:
            search_filter = db.or_(
                Product.name_ar.contains(search),
                Product.name_en.contains(search),
                Product.sku.contains(search)
            )
            query = query.filter(search_filter)
        
        products = query.order_by(Product.name_ar).all()
        language = get_user_language()
        
        return jsonify({
            'success': True,
            'products': [product.to_dict(language) for product in products]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
