#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة AppenderQuery TypeError
Test AppenderQuery TypeError fix
"""

from app import create_app
from extensions import db
from models.product import Product
from models.sale import Sale, SaleItem
from models.category import Category
from models.user import User
from models.customer import Customer

def test_appender_query_fixes():
    """اختبار إصلاحات AppenderQuery"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار إصلاحات AppenderQuery TypeError")
        print("=" * 60)
        
        # اختبار Product.get_sales_count()
        print("\n📦 اختبار Product.get_sales_count():")
        products = Product.query.limit(3).all()
        for product in products:
            try:
                count = product.get_sales_count()
                print(f"  ✅ {product.name_ar}: {count} مبيعة")
            except Exception as e:
                print(f"  ❌ {product.name_ar}: خطأ - {e}")
        
        # اختبار Product.get_last_sale()
        print("\n📦 اختبار Product.get_last_sale():")
        for product in products:
            try:
                last_sale = product.get_last_sale()
                if last_sale:
                    print(f"  ✅ {product.name_ar}: آخر بيع في {last_sale.sale.sale_date}")
                else:
                    print(f"  ✅ {product.name_ar}: لا توجد مبيعات")
            except Exception as e:
                print(f"  ❌ {product.name_ar}: خطأ - {e}")
        
        # اختبار Sale.get_items_count()
        print("\n🛒 اختبار Sale.get_items_count():")
        sales = Sale.query.limit(3).all()
        for sale in sales:
            try:
                count = sale.get_items_count()
                print(f"  ✅ {sale.sale_number}: {count} عنصر")
            except Exception as e:
                print(f"  ❌ {sale.sale_number}: خطأ - {e}")
        
        # اختبار العلاقات الديناميكية
        print("\n🔗 اختبار العلاقات الديناميكية:")
        
        # اختبار product.sale_items (AppenderQuery)
        if products:
            product = products[0]
            try:
                # هذا يجب أن يعمل (count على AppenderQuery)
                count = product.sale_items.count()
                print(f"  ✅ product.sale_items.count(): {count}")
                
                # هذا لا يجب أن يعمل (len على AppenderQuery)
                try:
                    length = len(product.sale_items)
                    print(f"  ❌ len(product.sale_items): {length} - هذا لا يجب أن يعمل!")
                except TypeError as e:
                    print(f"  ✅ len(product.sale_items): خطأ متوقع - {e}")
                
            except Exception as e:
                print(f"  ❌ product.sale_items: خطأ - {e}")
        
        # اختبار sale.items (AppenderQuery)
        if sales:
            sale = sales[0]
            try:
                # هذا يجب أن يعمل (count على AppenderQuery)
                count = sale.items.count()
                print(f"  ✅ sale.items.count(): {count}")
                
                # هذا لا يجب أن يعمل (len على AppenderQuery)
                try:
                    length = len(sale.items)
                    print(f"  ❌ len(sale.items): {length} - هذا لا يجب أن يعمل!")
                except TypeError as e:
                    print(f"  ✅ len(sale.items): خطأ متوقع - {e}")
                
            except Exception as e:
                print(f"  ❌ sale.items: خطأ - {e}")

def test_template_compatibility():
    """اختبار توافق القوالب"""
    app = create_app()
    
    with app.test_client() as client:
        print("\n🎨 اختبار توافق القوالب:")
        
        # تسجيل دخول تجريبي
        with app.app_context():
            user = User.query.filter_by(username='admin').first()
            if not user:
                print("  ❌ لا يوجد مستخدم admin للاختبار")
                return
        
        # محاولة تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = client.post('/auth/login', data=login_data, follow_redirects=True)
        
        if response.status_code == 200:
            print("  ✅ تم تسجيل الدخول بنجاح")
            
            # اختبار صفحة المنتجات
            try:
                response = client.get('/products/')
                if response.status_code == 200:
                    print("  ✅ صفحة المنتجات تعمل بدون أخطاء")
                else:
                    print(f"  ❌ صفحة المنتجات: كود الحالة {response.status_code}")
            except Exception as e:
                print(f"  ❌ صفحة المنتجات: خطأ - {e}")
            
            # اختبار صفحة عرض منتج
            with app.app_context():
                product = Product.query.first()
                if product:
                    try:
                        response = client.get(f'/products/{product.id}')
                        if response.status_code == 200:
                            print("  ✅ صفحة عرض المنتج تعمل بدون أخطاء")
                        else:
                            print(f"  ❌ صفحة عرض المنتج: كود الحالة {response.status_code}")
                    except Exception as e:
                        print(f"  ❌ صفحة عرض المنتج: خطأ - {e}")
            
            # اختبار صفحة المبيعات
            try:
                response = client.get('/sales/')
                if response.status_code == 200:
                    print("  ✅ صفحة المبيعات تعمل بدون أخطاء")
                else:
                    print(f"  ❌ صفحة المبيعات: كود الحالة {response.status_code}")
            except Exception as e:
                print(f"  ❌ صفحة المبيعات: خطأ - {e}")
            
        else:
            print(f"  ❌ فشل تسجيل الدخول: كود الحالة {response.status_code}")

if __name__ == '__main__':
    print("🇶🇦 نظام نقاط البيع القطري - اختبار إصلاح AppenderQuery")
    print("=" * 60)
    
    test_appender_query_fixes()
    test_template_compatibility()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار!")
    print("🚀 إذا لم تظهر أخطاء، فقد تم إصلاح المشكلة بنجاح!")
