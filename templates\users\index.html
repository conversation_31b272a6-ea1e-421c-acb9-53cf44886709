{% extends "base.html" %}

{% block title %}
{{ 'إدارة المستخدمين - نظام نقاط البيع القطري' if language == 'ar' else 'User Management - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-people"></i>
                {{ 'إدارة المستخدمين' if language == 'ar' else 'User Management' }}
            </h1>
            {% if current_user.has_permission('users_write') %}
            <div>
                <a href="{{ url_for('users.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'إضافة مستخدم جديد' if language == 'ar' else 'Add New User' }}
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المستخدمين' if language == 'ar' else 'Total Users' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ users.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'مستخدمين نشطين' if language == 'ar' else 'Active Users' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ users.items | selectattr('is_active', 'equalto', true) | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'مديرين' if language == 'ar' else 'Managers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ users.items | selectattr('role', 'equalto', 'manager') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-gear fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'بائعين' if language == 'ar' else 'Sellers' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ users.items | selectattr('role', 'equalto', 'seller') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-badge fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">{{ 'البحث' if language == 'ar' else 'Search' }}</label>
                <input type="text" class="form-control" name="search" value="{{ search }}" 
                       placeholder="{{ 'اسم المستخدم، البريد، أو رقم الموظف' if language == 'ar' else 'Username, email, or employee ID' }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">{{ 'الدور' if language == 'ar' else 'Role' }}</label>
                <select class="form-select" name="role">
                    <option value="">{{ 'جميع الأدوار' if language == 'ar' else 'All Roles' }}</option>
                    <option value="manager" {{ 'selected' if role_filter == 'manager' }}>
                        {{ 'مدير' if language == 'ar' else 'Manager' }}
                    </option>
                    <option value="seller" {{ 'selected' if role_filter == 'seller' }}>
                        {{ 'بائع' if language == 'ar' else 'Seller' }}
                    </option>
                    <option value="accountant" {{ 'selected' if role_filter == 'accountant' }}>
                        {{ 'محاسب' if language == 'ar' else 'Accountant' }}
                    </option>
                    <option value="inventory_manager" {{ 'selected' if role_filter == 'inventory_manager' }}>
                        {{ 'مدير مخزون' if language == 'ar' else 'Inventory Manager' }}
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">{{ 'الحالة' if language == 'ar' else 'Status' }}</label>
                <select class="form-select" name="status">
                    <option value="">{{ 'جميع الحالات' if language == 'ar' else 'All Status' }}</option>
                    <option value="active" {{ 'selected' if status_filter == 'active' }}>
                        {{ 'نشط' if language == 'ar' else 'Active' }}
                    </option>
                    <option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>
                        {{ 'غير نشط' if language == 'ar' else 'Inactive' }}
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        {{ 'بحث' if language == 'ar' else 'Search' }}
                    </button>
                    <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            {{ 'قائمة المستخدمين' if language == 'ar' else 'Users List' }}
            <span class="badge bg-primary">{{ users.total }}</span>
        </h6>
    </div>
    <div class="card-body p-0">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{{ 'المستخدم' if language == 'ar' else 'User' }}</th>
                        <th>{{ 'الدور' if language == 'ar' else 'Role' }}</th>
                        <th>{{ 'معلومات الاتصال' if language == 'ar' else 'Contact Info' }}</th>
                        <th>{{ 'آخر تسجيل دخول' if language == 'ar' else 'Last Login' }}</th>
                        <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                        <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary text-white me-3">
                                    {{ user.first_name_en[0] if user.first_name_en else user.username[0] }}
                                </div>
                                <div>
                                    <strong>{{ user.get_full_name(language) }}</strong>
                                    <br>
                                    <small class="text-muted">@{{ user.username }}</small>
                                    {% if user.employee_id %}
                                    <br>
                                    <small class="text-muted">{{ 'رقم الموظف:' if language == 'ar' else 'ID:' }} {{ user.employee_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if user.role == 'manager' %}
                            <span class="badge bg-danger">{{ 'مدير' if language == 'ar' else 'Manager' }}</span>
                            {% elif user.role == 'seller' %}
                            <span class="badge bg-primary">{{ 'بائع' if language == 'ar' else 'Seller' }}</span>
                            {% elif user.role == 'accountant' %}
                            <span class="badge bg-success">{{ 'محاسب' if language == 'ar' else 'Accountant' }}</span>
                            {% elif user.role == 'inventory_manager' %}
                            <span class="badge bg-warning">{{ 'مدير مخزون' if language == 'ar' else 'Inventory Manager' }}</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ user.role.title() }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <i class="bi bi-envelope"></i> {{ user.email }}
                                {% if user.phone %}
                                <br>
                                <i class="bi bi-telephone"></i> {{ user.phone }}
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if user.last_login %}
                            <div>{{ user.last_login.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ user.last_login.strftime('%H:%M') }}</small>
                            {% else %}
                            <span class="text-muted">{{ 'لم يسجل دخول' if language == 'ar' else 'Never logged in' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">{{ 'نشط' if language == 'ar' else 'Active' }}</span>
                            {% else %}
                            <span class="badge bg-danger">{{ 'غير نشط' if language == 'ar' else 'Inactive' }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('users.view', user_id=user.id) }}" 
                                   class="btn btn-outline-primary" title="{{ 'عرض' if language == 'ar' else 'View' }}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.has_permission('users_write') and user.id != current_user.id %}
                                <a href="{{ url_for('users.edit', user_id=user.id) }}" 
                                   class="btn btn-outline-warning" title="{{ 'تعديل' if language == 'ar' else 'Edit' }}">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                        onclick="toggleUserStatus({{ user.id }}, {{ 'true' if user.is_active else 'false' }})"
                                        title="{{ 'تعطيل' if user.is_active and language == 'ar' else 'تفعيل' if not user.is_active and language == 'ar' else 'Deactivate' if user.is_active else 'Activate' }}">
                                    <i class="bi bi-{{ 'x-circle' if user.is_active else 'check-circle' }}"></i>
                                </button>
                                {% endif %}
                                {% if current_user.has_permission('users_write') and user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="resetPassword({{ user.id }}, '{{ user.username }}')"
                                        title="{{ 'إعادة تعيين كلمة المرور' if language == 'ar' else 'Reset Password' }}">
                                    <i class="bi bi-key"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <div class="card-footer">
            <nav aria-label="Users pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.index', page=users.prev_num, search=search, role=role_filter, status=status_filter) }}">
                            {{ 'السابق' if language == 'ar' else 'Previous' }}
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != users.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.index', page=page_num, search=search, role=role_filter, status=status_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.index', page=users.next_num, search=search, role=role_filter, status=status_filter) }}">
                            {{ 'التالي' if language == 'ar' else 'Next' }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">{{ 'لا توجد مستخدمين' if language == 'ar' else 'No users found' }}</h5>
            <p class="text-muted">
                {% if search or role_filter or status_filter %}
                {{ 'جرب تغيير معايير البحث' if language == 'ar' else 'Try changing your search criteria' }}
                {% else %}
                {{ 'ابدأ بإضافة مستخدم جديد' if language == 'ar' else 'Start by adding a new user' }}
                {% endif %}
            </p>
            {% if current_user.has_permission('users_write') and not (search or role_filter or status_filter) %}
            <a href="{{ url_for('users.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {{ 'إضافة مستخدم جديد' if language == 'ar' else 'Add New User' }}
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleStatusTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="toggleStatusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-primary" id="confirmToggleStatus">
                    {{ 'تأكيد' if language == 'ar' else 'Confirm' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'إعادة تعيين كلمة المرور' if language == 'ar' else 'Reset Password' }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="resetPasswordMessage"></p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    {{ 'سيتم إرسال كلمة مرور مؤقتة للمستخدم عبر البريد الإلكتروني' if language == 'ar' else 'A temporary password will be sent to the user via email' }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ 'إلغاء' if language == 'ar' else 'Cancel' }}
                </button>
                <button type="button" class="btn btn-warning" id="confirmResetPassword">
                    {{ 'إعادة تعيين' if language == 'ar' else 'Reset Password' }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentUserId = null;
let currentAction = null;

// Auto-submit search form on filter change
document.querySelectorAll('select[name="role"], select[name="status"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

// Search input with debounce
let searchTimeout;
document.querySelector('input[name="search"]').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});

function toggleUserStatus(userId, isActive) {
    currentUserId = userId;
    currentAction = 'toggle_status';
    
    const title = isActive ? 
        '{{ "تعطيل المستخدم" if language == "ar" else "Deactivate User" }}' : 
        '{{ "تفعيل المستخدم" if language == "ar" else "Activate User" }}';
    
    const message = isActive ? 
        '{{ "هل أنت متأكد من تعطيل هذا المستخدم؟" if language == "ar" else "Are you sure you want to deactivate this user?" }}' : 
        '{{ "هل أنت متأكد من تفعيل هذا المستخدم؟" if language == "ar" else "Are you sure you want to activate this user?" }}';
    
    document.getElementById('toggleStatusTitle').textContent = title;
    document.getElementById('toggleStatusMessage').textContent = message;
    
    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}

function resetPassword(userId, username) {
    currentUserId = userId;
    currentAction = 'reset_password';
    
    const message = '{{ "هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم" if language == "ar" else "Are you sure you want to reset password for user" }}' + 
                   ` "${username}"?`;
    
    document.getElementById('resetPasswordMessage').textContent = message;
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

document.getElementById('confirmToggleStatus').addEventListener('click', function() {
    if (currentUserId && currentAction === 'toggle_status') {
        fetch(`/users/${currentUserId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ');
        });
        
        bootstrap.Modal.getInstance(document.getElementById('toggleStatusModal')).hide();
    }
});

document.getElementById('confirmResetPassword').addEventListener('click', function() {
    if (currentUserId && currentAction === 'reset_password') {
        fetch(`/users/${currentUserId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ "تم إرسال كلمة المرور الجديدة بنجاح" if language == "ar" else "New password sent successfully" }}');
            } else {
                alert(data.error || 'حدث خطأ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ');
        });
        
        bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
    }
});
</script>
{% endblock %}
