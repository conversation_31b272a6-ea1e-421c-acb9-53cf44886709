{% extends "base.html" %}

{% block title %}
{{ 'المساعدة والدعم - نظام نقاط البيع القطري' if language == 'ar' else 'Help & Support - Qatar POS System' }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-question-circle"></i>
            {{ 'المساعدة والدعم' if language == 'ar' else 'Help & Support' }}
        </h1>
    </div>
</div>

<!-- Quick Help Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="text-primary mb-3">
                    <i class="bi bi-book display-4"></i>
                </div>
                <h5 class="card-title">{{ 'دليل المستخدم' if language == 'ar' else 'User Guide' }}</h5>
                <p class="card-text">{{ 'دليل شامل لاستخدام النظام' if language == 'ar' else 'Comprehensive guide to using the system' }}</p>
                <a href="{{ url_for('help.user_guide') }}" class="btn btn-primary">
                    {{ 'عرض الدليل' if language == 'ar' else 'View Guide' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="text-success mb-3">
                    <i class="bi bi-play-circle display-4"></i>
                </div>
                <h5 class="card-title">{{ 'فيديوهات تعليمية' if language == 'ar' else 'Video Tutorials' }}</h5>
                <p class="card-text">{{ 'شروحات مرئية خطوة بخطوة' if language == 'ar' else 'Step-by-step visual explanations' }}</p>
                <a href="{{ url_for('help.tutorials') }}" class="btn btn-success">
                    {{ 'مشاهدة الفيديوهات' if language == 'ar' else 'Watch Videos' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="text-info mb-3">
                    <i class="bi bi-question-circle display-4"></i>
                </div>
                <h5 class="card-title">{{ 'الأسئلة الشائعة' if language == 'ar' else 'FAQ' }}</h5>
                <p class="card-text">{{ 'إجابات للأسئلة الأكثر شيوعاً' if language == 'ar' else 'Answers to frequently asked questions' }}</p>
                <a href="{{ url_for('help.faq') }}" class="btn btn-info">
                    {{ 'عرض الأسئلة' if language == 'ar' else 'View FAQ' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="text-warning mb-3">
                    <i class="bi bi-headset display-4"></i>
                </div>
                <h5 class="card-title">{{ 'الدعم الفني' if language == 'ar' else 'Technical Support' }}</h5>
                <p class="card-text">{{ 'تواصل مع فريق الدعم الفني' if language == 'ar' else 'Contact our technical support team' }}</p>
                <a href="{{ url_for('help.contact') }}" class="btn btn-warning">
                    {{ 'تواصل معنا' if language == 'ar' else 'Contact Us' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search Help -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-search"></i>
                    {{ 'البحث في المساعدة' if language == 'ar' else 'Search Help' }}
                </h5>
                <form method="GET" action="{{ url_for('help.search') }}">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" 
                               placeholder="{{ 'ابحث عن موضوع أو سؤال...' if language == 'ar' else 'Search for a topic or question...' }}"
                               value="{{ request.args.get('q', '') }}">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="bi bi-search"></i>
                            {{ 'بحث' if language == 'ar' else 'Search' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Help Categories -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-cash-register"></i>
                    {{ 'نقطة البيع' if language == 'ar' else 'Point of Sale' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='pos-getting-started') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-play-circle"></i>
                    {{ 'البدء مع نقطة البيع' if language == 'ar' else 'Getting Started with POS' }}
                </a>
                <a href="{{ url_for('help.topic', topic='pos-making-sale') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-cart-plus"></i>
                    {{ 'إجراء عملية بيع' if language == 'ar' else 'Making a Sale' }}
                </a>
                <a href="{{ url_for('help.topic', topic='pos-payment-methods') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-credit-card"></i>
                    {{ 'طرق الدفع' if language == 'ar' else 'Payment Methods' }}
                </a>
                <a href="{{ url_for('help.topic', topic='pos-discounts') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-percent"></i>
                    {{ 'تطبيق الخصومات' if language == 'ar' else 'Applying Discounts' }}
                </a>
                <a href="{{ url_for('help.topic', topic='pos-returns') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-arrow-return-left"></i>
                    {{ 'المرتجعات والاستردادات' if language == 'ar' else 'Returns & Refunds' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-box"></i>
                    {{ 'إدارة المنتجات' if language == 'ar' else 'Product Management' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='products-adding') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-plus-circle"></i>
                    {{ 'إضافة منتجات جديدة' if language == 'ar' else 'Adding New Products' }}
                </a>
                <a href="{{ url_for('help.topic', topic='products-categories') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-tags"></i>
                    {{ 'إدارة الفئات' if language == 'ar' else 'Managing Categories' }}
                </a>
                <a href="{{ url_for('help.topic', topic='products-pricing') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-currency-dollar"></i>
                    {{ 'تحديد الأسعار' if language == 'ar' else 'Setting Prices' }}
                </a>
                <a href="{{ url_for('help.topic', topic='products-barcodes') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-upc-scan"></i>
                    {{ 'الباركود والرموز' if language == 'ar' else 'Barcodes & SKUs' }}
                </a>
                <a href="{{ url_for('help.topic', topic='products-import') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-upload"></i>
                    {{ 'استيراد المنتجات' if language == 'ar' else 'Importing Products' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-boxes"></i>
                    {{ 'إدارة المخزون' if language == 'ar' else 'Inventory Management' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='inventory-tracking') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-graph-up"></i>
                    {{ 'تتبع المخزون' if language == 'ar' else 'Stock Tracking' }}
                </a>
                <a href="{{ url_for('help.topic', topic='inventory-adjustments') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-gear"></i>
                    {{ 'تعديلات المخزون' if language == 'ar' else 'Stock Adjustments' }}
                </a>
                <a href="{{ url_for('help.topic', topic='inventory-alerts') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-bell"></i>
                    {{ 'تنبيهات المخزون' if language == 'ar' else 'Stock Alerts' }}
                </a>
                <a href="{{ url_for('help.topic', topic='inventory-reports') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-file-text"></i>
                    {{ 'تقارير المخزون' if language == 'ar' else 'Inventory Reports' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-people"></i>
                    {{ 'إدارة العملاء' if language == 'ar' else 'Customer Management' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='customers-adding') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-person-plus"></i>
                    {{ 'إضافة عملاء جدد' if language == 'ar' else 'Adding New Customers' }}
                </a>
                <a href="{{ url_for('help.topic', topic='customers-types') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-person-badge"></i>
                    {{ 'أنواع العملاء' if language == 'ar' else 'Customer Types' }}
                </a>
                <a href="{{ url_for('help.topic', topic='customers-loyalty') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-heart"></i>
                    {{ 'برامج الولاء' if language == 'ar' else 'Loyalty Programs' }}
                </a>
                <a href="{{ url_for('help.topic', topic='customers-history') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-clock-history"></i>
                    {{ 'تاريخ المشتريات' if language == 'ar' else 'Purchase History' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {{ 'التقارير والتحليلات' if language == 'ar' else 'Reports & Analytics' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='reports-sales') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-cart"></i>
                    {{ 'تقارير المبيعات' if language == 'ar' else 'Sales Reports' }}
                </a>
                <a href="{{ url_for('help.topic', topic='reports-financial') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-currency-dollar"></i>
                    {{ 'التقارير المالية' if language == 'ar' else 'Financial Reports' }}
                </a>
                <a href="{{ url_for('help.topic', topic='reports-custom') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-gear"></i>
                    {{ 'التقارير المخصصة' if language == 'ar' else 'Custom Reports' }}
                </a>
                <a href="{{ url_for('help.topic', topic='reports-export') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-download"></i>
                    {{ 'تصدير التقارير' if language == 'ar' else 'Exporting Reports' }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    {{ 'الإعدادات والتخصيص' if language == 'ar' else 'Settings & Customization' }}
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="{{ url_for('help.topic', topic='settings-general') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-sliders"></i>
                    {{ 'الإعدادات العامة' if language == 'ar' else 'General Settings' }}
                </a>
                <a href="{{ url_for('help.topic', topic='settings-users') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-people"></i>
                    {{ 'إدارة المستخدمين' if language == 'ar' else 'User Management' }}
                </a>
                <a href="{{ url_for('help.topic', topic='settings-permissions') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-shield-check"></i>
                    {{ 'الصلاحيات والأمان' if language == 'ar' else 'Permissions & Security' }}
                </a>
                <a href="{{ url_for('help.topic', topic='settings-backup') }}" class="list-group-item list-group-item-action">
                    <i class="bi bi-cloud-download"></i>
                    {{ 'النسخ الاحتياطي' if language == 'ar' else 'Backup & Restore' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Contact Support -->
<div class="row">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="bi bi-headset"></i>
                    {{ 'هل تحتاج مساعدة إضافية؟' if language == 'ar' else 'Need Additional Help?' }}
                </h5>
                <p class="card-text">
                    {{ 'فريق الدعم الفني متاح لمساعدتك في أي وقت' if language == 'ar' else 'Our technical support team is available to help you anytime' }}
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-3 mb-2">
                        <a href="tel:+97444123456" class="btn btn-outline-primary w-100">
                            <i class="bi bi-telephone"></i>
                            {{ 'اتصل بنا' if language == 'ar' else 'Call Us' }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-success w-100">
                            <i class="bi bi-envelope"></i>
                            {{ 'راسلنا' if language == 'ar' else 'Email Us' }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('help.chat') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-chat-dots"></i>
                            {{ 'دردشة مباشرة' if language == 'ar' else 'Live Chat' }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('help.ticket') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-ticket"></i>
                            {{ 'تذكرة دعم' if language == 'ar' else 'Support Ticket' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}

.display-4 {
    font-size: 3rem;
}
</style>
{% endblock %}
