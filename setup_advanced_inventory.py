#!/usr/bin/env python3
"""
Setup script for Advanced Inventory Management System
Qatar POS System
"""

import sys
import os
sys.path.append('.')

from app import create_app
from extensions import db
from models.user import User
from models.product import Product
from models.inventory_movement import InventoryMovement
from models.category import Category
from datetime import datetime, timedelta
import random

def create_inventory_manager_user():
    """Create a sample inventory manager user"""
    
    print("👤 Creating inventory manager user...")
    
    app = create_app()
    
    with app.app_context():
        # Check if inventory manager already exists
        existing_manager = User.query.filter_by(username='inventory_manager').first()
        
        if existing_manager:
            print("✅ Inventory manager user already exists")
            return existing_manager
        
        # Create inventory manager user
        inventory_manager = User(
            username='inventory_manager',
            email='<EMAIL>',
            first_name_ar='مدير',
            first_name_en='Inventory',
            last_name_ar='المخزون',
            last_name_en='Manager',
            phone='+974-5555-0003',
            role='inventory_manager',
            is_active=True
        )
        
        inventory_manager.set_password('inventory123')
        
        try:
            db.session.add(inventory_manager)
            db.session.commit()
            print("✅ Created inventory manager user: inventory_manager / inventory123")
            return inventory_manager
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error creating inventory manager: {str(e)}")
            return None

def create_sample_inventory_movements():
    """Create sample inventory movements for testing"""
    
    print("📦 Creating sample inventory movements...")
    
    app = create_app()
    
    with app.app_context():
        # Get some products
        products = Product.query.filter_by(is_active=True).limit(10).all()
        
        if not products:
            print("❌ No products found. Please create products first.")
            return False
        
        # Get users
        users = User.query.filter_by(is_active=True).all()
        
        if not users:
            print("❌ No users found.")
            return False
        
        movements_created = 0
        
        # Create various types of movements
        movement_types = ['sale', 'purchase', 'adjustment', 'return', 'damage']
        
        for i in range(50):  # Create 50 sample movements
            try:
                product = random.choice(products)
                user = random.choice(users)
                movement_type = random.choice(movement_types)
                
                # Generate realistic quantity changes based on movement type
                if movement_type == 'sale':
                    quantity_change = -random.randint(1, 5)
                elif movement_type == 'purchase':
                    quantity_change = random.randint(10, 50)
                elif movement_type == 'adjustment':
                    quantity_change = random.randint(-10, 10)
                elif movement_type == 'return':
                    quantity_change = random.randint(1, 3)
                elif movement_type == 'damage':
                    quantity_change = -random.randint(1, 5)
                
                # Calculate new quantity
                old_quantity = product.current_stock
                new_quantity = max(0, old_quantity + quantity_change)
                
                # Create movement
                movement = InventoryMovement(
                    product_id=product.id,
                    old_quantity=old_quantity,
                    new_quantity=new_quantity,
                    quantity_change=quantity_change,
                    transaction_type=movement_type,
                    transaction_date=datetime.now() - timedelta(days=random.randint(0, 30)),
                    reference_number=f"{movement_type.upper()}-{datetime.now().strftime('%Y%m%d')}-{i+1:03d}",
                    user_id=user.id,
                    notes=f"Sample {movement_type} movement for testing",
                    reason=random.choice(['physical_count', 'damage', 'correction', 'other']) if movement_type == 'adjustment' else None,
                    unit_cost=product.cost_price,
                    total_cost=abs(quantity_change) * product.cost_price
                )
                
                db.session.add(movement)
                
                # Update product stock
                product.current_stock = new_quantity
                
                movements_created += 1
                
            except Exception as e:
                print(f"⚠️ Error creating movement {i+1}: {str(e)}")
                continue
        
        try:
            db.session.commit()
            print(f"✅ Created {movements_created} sample inventory movements")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error saving movements: {str(e)}")
            return False

def update_user_permissions():
    """Update existing users with inventory permissions"""
    
    print("🔐 Updating user permissions...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Update admin users to have all permissions
            admin_users = User.query.filter_by(role='manager').all()
            for user in admin_users:
                user.role = 'admin'  # Ensure they have admin role
            
            # Update accountant users to have inventory permissions
            accountant_users = User.query.filter_by(role='accountant').all()
            for user in accountant_users:
                # Accountants already have inventory permissions in the updated model
                pass
            
            db.session.commit()
            print("✅ Updated user permissions successfully")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error updating permissions: {str(e)}")
            return False

def create_inventory_database_tables():
    """Create inventory movement tables"""
    
    print("🗄️ Creating inventory database tables...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {str(e)}")
            return False

def test_inventory_system():
    """Test the inventory system functionality"""
    
    print("🧪 Testing inventory system...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Test 1: Check if InventoryMovement model works
            movement_count = InventoryMovement.query.count()
            print(f"📊 Total movements in database: {movement_count}")
            
            # Test 2: Check movement summary
            summary = InventoryMovement.get_movements_summary()
            print(f"📈 Movement summary:")
            print(f"   - Total movements: {summary['total_movements']}")
            print(f"   - Inbound movements: {summary['total_in']}")
            print(f"   - Outbound movements: {summary['total_out']}")
            print(f"   - Adjustments: {summary['total_adjustments']}")
            
            # Test 3: Check recent movements
            recent = InventoryMovement.get_recent_movements(5)
            print(f"🕒 Recent movements: {len(recent)} found")
            
            # Test 4: Test product movement history
            products = Product.query.limit(3).all()
            for product in products:
                history = InventoryMovement.get_product_movement_history(product.id, 5)
                print(f"📦 {product.get_name()}: {len(history)} movements")
            
            print("✅ All inventory system tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Inventory system test failed: {str(e)}")
            return False

def generate_inventory_report():
    """Generate a sample inventory report"""
    
    print("📋 Generating inventory report...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Get inventory statistics
            total_products = Product.query.filter_by(is_active=True).count()
            
            low_stock_products = Product.query.filter(
                Product.current_stock <= Product.minimum_stock,
                Product.is_active == True,
                Product.track_inventory == True
            ).all()
            
            out_of_stock_products = Product.query.filter(
                Product.current_stock <= 0,
                Product.is_active == True,
                Product.track_inventory == True
            ).all()
            
            # Calculate total inventory value
            all_products = Product.query.filter_by(is_active=True, track_inventory=True).all()
            total_value = sum(p.current_stock * p.cost_price for p in all_products)
            
            # Get movement statistics
            movements_summary = InventoryMovement.get_movements_summary()
            
            print("\n" + "="*50)
            print("📊 INVENTORY REPORT")
            print("="*50)
            print(f"📦 Total Products: {total_products}")
            print(f"⚠️  Low Stock Products: {len(low_stock_products)}")
            print(f"❌ Out of Stock Products: {len(out_of_stock_products)}")
            print(f"💰 Total Inventory Value: {total_value:,.2f} QAR")
            print("\n📈 MOVEMENT STATISTICS:")
            print(f"   Total Movements: {movements_summary['total_movements']}")
            print(f"   Inbound: {movements_summary['total_in']}")
            print(f"   Outbound: {movements_summary['total_out']}")
            print(f"   Adjustments: {movements_summary['total_adjustments']}")
            
            if low_stock_products:
                print("\n⚠️  LOW STOCK ALERT:")
                for product in low_stock_products[:5]:  # Show first 5
                    print(f"   - {product.get_name()}: {product.current_stock} (min: {product.minimum_stock})")
            
            if out_of_stock_products:
                print("\n❌ OUT OF STOCK ALERT:")
                for product in out_of_stock_products[:5]:  # Show first 5
                    print(f"   - {product.get_name()}: {product.current_stock}")
            
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"❌ Error generating report: {str(e)}")
            return False

if __name__ == "__main__":
    print("🚀 Qatar POS Advanced Inventory Management Setup")
    print("=" * 60)
    
    # Step 1: Create database tables
    tables_created = create_inventory_database_tables()
    
    # Step 2: Update user permissions
    permissions_updated = update_user_permissions()
    
    # Step 3: Create inventory manager user
    inventory_manager = create_inventory_manager_user()
    
    # Step 4: Create sample movements
    movements_created = create_sample_inventory_movements()
    
    # Step 5: Test the system
    system_tested = test_inventory_system()
    
    # Step 6: Generate report
    report_generated = generate_inventory_report()
    
    print("\n" + "=" * 60)
    if all([tables_created, permissions_updated, inventory_manager, movements_created, system_tested]):
        print("🎉 Advanced Inventory Management Setup Completed Successfully!")
        print("\n💡 You can now access:")
        print("   🔗 Inventory Dashboard: http://localhost:2626/inventory")
        print("   📊 Inventory Movements: http://localhost:2626/inventory/movements")
        print("   📋 Stock Reports: http://localhost:2626/inventory/reports/stock-levels")
        print("\n👤 New User Created:")
        print("   Username: inventory_manager")
        print("   Password: inventory123")
        print("   Role: Inventory Manager")
        print("\n🔐 Updated Permissions:")
        print("   ✅ Admin/Manager: Full access")
        print("   ✅ Inventory Manager: Full inventory access")
        print("   ✅ Accountant: Inventory read/write access")
        print("   ✅ Seller: Inventory read access")
        
    else:
        print("❌ Setup completed with some issues!")
        print("Please check the error messages above.")
    
    print("=" * 60)
