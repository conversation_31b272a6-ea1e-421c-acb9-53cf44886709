@echo off
chcp 65001 >nul
title Qatar POS - Quick Install

:: مثبت سريع بنقرة واحدة
:: Quick one-click installer

:: تشغيل كمدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

cls
echo ========================================
echo    Qatar POS System - Quick Install
echo    نظام نقاط البيع القطري - تثبيت سريع
echo ========================================
echo.
echo 🚀 تثبيت سريع بنقرة واحدة
echo 🚀 One-click quick installation
echo.

:: التحقق من الملف
if not exist "dist\QatarPOS.exe" (
    echo ❌ ملف QatarPOS.exe غير موجود
    pause
    exit /b 1
)

echo ⏳ جاري التثبيت... يرجى الانتظار
echo ⏳ Installing... Please wait
echo.

:: التثبيت السريع
set INSTALL_DIR=C:\QatarPOS

:: إنشاء المجلد
if exist "%INSTALL_DIR%" rmdir /S /Q "%INSTALL_DIR%" >nul 2>&1
mkdir "%INSTALL_DIR%" >nul 2>&1

:: نسخ الملف
copy "dist\QatarPOS.exe" "%INSTALL_DIR%\" >nul 2>&1

:: إنشاء أداة إلغاء التثبيت السريعة
(
echo @echo off
echo echo جاري إلغاء التثبيت...
echo if exist "C:\QatarPOS" rmdir /S /Q "C:\QatarPOS"
echo if exist "%%USERPROFILE%%\Desktop\Qatar POS.lnk" del "%%USERPROFILE%%\Desktop\Qatar POS.lnk"
echo if exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS" rmdir /S /Q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS"
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /f ^>nul 2^>^&1
echo echo تم إلغاء التثبيت بنجاح!
echo pause
) > "%INSTALL_DIR%\QuickUninstall.bat"

:: إنشاء الاختصارات
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()" >nul 2>&1

:: قائمة البداية
set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Qatar POS
mkdir "%START_MENU_DIR%" >nul 2>&1
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()" >nul 2>&1

:: تسجيل في النظام
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayName" /t REG_SZ /d "Qatar POS System" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\QuickUninstall.bat" /f >nul 2>&1

echo ✅ تم التثبيت بنجاح!
echo ✅ Installation completed!
echo.
echo 📍 المسار: %INSTALL_DIR%\QatarPOS.exe
echo 🖥️ اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة البداية: تم إنشاؤها
echo.
echo 🚀 تشغيل التطبيق...
echo 🚀 Starting application...

:: تشغيل التطبيق تلقائياً
start "" "%INSTALL_DIR%\QatarPOS.exe"

echo.
echo 🎉 مرحباً بك في نظام نقاط البيع القطري!
echo 🎉 Welcome to Qatar POS System!
echo.
timeout /t 3 >nul
