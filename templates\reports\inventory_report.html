{% extends "base.html" %}

{% block title %}
{{ 'تقرير المخزون' if language == 'ar' else 'Inventory Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-boxes"></i>
                        {{ 'تقرير المخزون' if language == 'ar' else 'Inventory Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                                            <h3 class="mb-0">{{ products|length }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-box-seam fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة المخزون' if language == 'ar' else 'Inventory Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.2f"|format(total_inventory_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-currency-dollar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</h6>
                                            <h3 class="mb-0">{{ low_stock_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-exclamation-triangle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</h6>
                                            <h3 class="mb-0">{{ out_of_stock_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-x-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'الحد الأدنى' if language == 'ar' else 'Min Stock' }}</th>
                                    <th>{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</th>
                                    <th>{{ 'قيمة المخزون' if language == 'ar' else 'Stock Value' }}</th>
                                    <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        <code>{{ product.barcode or 'N/A' }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                    </td>
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-secondary">
                                            {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ product.current_stock }}
                                        </span>
                                    </td>
                                    <td>{{ product.minimum_stock or 0 }}</td>
                                    <td>{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>
                                        <strong>{{ "%.2f"|format((product.cost_price or 0) * product.current_stock) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        {% if product.current_stock <= 0 %}
                                        <span class="badge bg-danger">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</span>
                                        {% elif product.current_stock <= product.minimum_stock %}
                                        <span class="badge bg-warning">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</span>
                                        {% else %}
                                        <span class="badge bg-success">{{ 'متوفر' if language == 'ar' else 'In Stock' }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
