{% extends "base.html" %}

{% block title %}
{{ 'تقرير المخزون' if language == 'ar' else 'Inventory Report' }} - {{ config.COMPANY_NAME }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-boxes"></i>
                        {{ 'تقرير المخزون' if language == 'ar' else 'Inventory Report' }}
                    </h4>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            {{ 'طباعة' if language == 'ar' else 'Print' }}
                        </button>
                        <a href="{{ url_for('reports.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            {{ 'العودة' if language == 'ar' else 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'القسم' if language == 'ar' else 'Category' }}</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">{{ 'جميع الأقسام' if language == 'ar' else 'All Categories' }}</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {{ 'selected' if selected_category == category.id }}>
                                            {{ category.name_ar if language == 'ar' else category.name_en }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{{ 'حالة المخزون' if language == 'ar' else 'Stock Status' }}</label>
                                    <select name="stock_status" class="form-select">
                                        <option value="">{{ 'جميع الحالات' if language == 'ar' else 'All Status' }}</option>
                                        <option value="in_stock" {{ 'selected' if selected_stock_status == 'in_stock' }}>
                                            {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                                        </option>
                                        <option value="low_stock" {{ 'selected' if selected_stock_status == 'low_stock' }}>
                                            {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                                        </option>
                                        <option value="out_of_stock" {{ 'selected' if selected_stock_status == 'out_of_stock' }}>
                                            {{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-funnel"></i>
                                            {{ 'تطبيق الفلتر' if language == 'ar' else 'Apply Filter' }}
                                        </button>
                                        <a href="{{ url_for('reports.inventory_report') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            {{ 'إعادة تعيين' if language == 'ar' else 'Reset' }}
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</h6>
                                            <h3 class="mb-0">{{ products|length }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-box-seam fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة التكلفة' if language == 'ar' else 'Cost Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_inventory_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-cash-stack fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'قيمة البيع' if language == 'ar' else 'Selling Value' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_selling_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-currency-dollar fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'الربح المحتمل' if language == 'ar' else 'Potential Profit' }}</h6>
                                            <h3 class="mb-0">{{ "%.0f"|format(total_potential_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-graph-up-arrow fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</h6>
                                            <h3 class="mb-0">{{ low_stock_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-exclamation-triangle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</h6>
                                            <h3 class="mb-0">{{ out_of_stock_count }}</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-x-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Statistics -->
                    {% if not selected_category and category_stats %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="bi bi-pie-chart"></i>
                                {{ 'إحصائيات الأقسام' if language == 'ar' else 'Category Statistics' }}
                            </h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                            <th>{{ 'عدد المنتجات' if language == 'ar' else 'Products Count' }}</th>
                                            <th>{{ 'إجمالي الكمية' if language == 'ar' else 'Total Quantity' }}</th>
                                            <th>{{ 'قيمة التكلفة' if language == 'ar' else 'Cost Value' }}</th>
                                            <th>{{ 'قيمة البيع' if language == 'ar' else 'Selling Value' }}</th>
                                            <th>{{ 'الربح المحتمل' if language == 'ar' else 'Potential Profit' }}</th>
                                            <th>{{ 'متوفر' if language == 'ar' else 'In Stock' }}</th>
                                            <th>{{ 'منخفض' if language == 'ar' else 'Low Stock' }}</th>
                                            <th>{{ 'نفد' if language == 'ar' else 'Out of Stock' }}</th>
                                            <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for category_id, stats in category_stats.items() %}
                                        <tr>
                                            <td>
                                                <strong>{{ stats.category.name_ar if language == 'ar' else stats.category.name_en }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ stats.products_count }}</span>
                                            </td>
                                            <td>{{ stats.total_quantity }}</td>
                                            <td>{{ "%.0f"|format(stats.total_cost_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td>{{ "%.0f"|format(stats.total_selling_value) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td>
                                                <span class="{% if stats.potential_profit > 0 %}text-success{% elif stats.potential_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                                    <strong>{{ "%.0f"|format(stats.potential_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ stats.in_stock_count }}</span>
                                            </td>
                                            <td>
                                                {% if stats.low_stock_count > 0 %}
                                                <span class="badge bg-warning">{{ stats.low_stock_count }}</span>
                                                {% else %}
                                                <span class="text-muted">0</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if stats.out_of_stock_count > 0 %}
                                                <span class="badge bg-danger">{{ stats.out_of_stock_count }}</span>
                                                {% else %}
                                                <span class="text-muted">0</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('reports.inventory_report', category_id=stats.category.id) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                    {{ 'عرض' if language == 'ar' else 'View' }}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Products Table -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="bi bi-boxes"></i>
                                {% if selected_category %}
                                    {% for category in categories if category.id == selected_category %}
                                    {{ 'منتجات قسم' if language == 'ar' else 'Products in' }} {{ category.name_ar if language == 'ar' else category.name_en }}
                                    {% endfor %}
                                {% else %}
                                {{ 'جميع المنتجات' if language == 'ar' else 'All Products' }}
                                {% endif %}
                                <span class="badge bg-secondary ms-2">{{ products|length }}</span>
                            </h5>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'الباركود' if language == 'ar' else 'Barcode' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    {% if not selected_category %}
                                    <th>{{ 'القسم' if language == 'ar' else 'Category' }}</th>
                                    {% endif %}
                                    <th>{{ 'المخزون الحالي' if language == 'ar' else 'Current Stock' }}</th>
                                    <th>{{ 'الحد الأدنى' if language == 'ar' else 'Min Stock' }}</th>
                                    <th>{{ 'سعر التكلفة' if language == 'ar' else 'Cost Price' }}</th>
                                    <th>{{ 'سعر البيع' if language == 'ar' else 'Selling Price' }}</th>
                                    <th>{{ 'قيمة التكلفة' if language == 'ar' else 'Cost Value' }}</th>
                                    <th>{{ 'قيمة البيع' if language == 'ar' else 'Selling Value' }}</th>
                                    <th>{{ 'الربح المحتمل' if language == 'ar' else 'Potential Profit' }}</th>
                                    <th>{{ 'الحالة' if language == 'ar' else 'Status' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        <code>{{ product.barcode or 'N/A' }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ product.name_ar if language == 'ar' else product.name_en }}</strong>
                                        {% if product.sku %}
                                        <br><small class="text-muted">{{ product.sku }}</small>
                                        {% endif %}
                                    </td>
                                    {% if not selected_category %}
                                    <td>
                                        {% if product.category %}
                                        <span class="badge bg-secondary">
                                            {{ product.category.name_ar if language == 'ar' else product.category.name_en }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">{{ 'غير محدد' if language == 'ar' else 'Uncategorized' }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock <= product.minimum_stock %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ product.current_stock }}
                                        </span>
                                    </td>
                                    <td>{{ product.minimum_stock or 0 }}</td>
                                    <td>{{ "%.2f"|format(product.cost_price or 0) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>{{ "%.2f"|format(product.selling_price) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>
                                        <strong>{{ "%.2f"|format((product.cost_price or 0) * product.current_stock) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(product.selling_price * product.current_stock) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                    </td>
                                    <td>
                                        {% set potential_profit = (product.selling_price - (product.cost_price or 0)) * product.current_stock %}
                                        <span class="{% if potential_profit > 0 %}text-success{% elif potential_profit < 0 %}text-danger{% else %}text-muted{% endif %}">
                                            <strong>{{ "%.2f"|format(potential_profit) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</strong>
                                        </span>
                                    </td>
                                    <td>
                                        {% if product.current_stock <= 0 %}
                                        <span class="badge bg-danger">{{ 'نفد المخزون' if language == 'ar' else 'Out of Stock' }}</span>
                                        {% elif product.current_stock <= product.minimum_stock %}
                                        <span class="badge bg-warning">{{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}</span>
                                        {% else %}
                                        <span class="badge bg-success">{{ 'متوفر' if language == 'ar' else 'In Stock' }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
