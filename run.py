#!/usr/bin/env python3
"""
Qatar POS System - Development Server
Run this file to start the development server
"""

import os
import sys
from flask import Flask
from flask_migrate import upgrade

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_simple_app():
    """Create a simple Flask app for testing"""
    app = Flask(__name__)
    
    # Basic configuration
    app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///qatarpos.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize extensions
    from extensions import db, login_manager, migrate
    
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from models.user import User
        return User.query.get(int(user_id))
    
    # Register blueprints
    try:
        from routes.auth import auth_bp
        app.register_blueprint(auth_bp, url_prefix='/auth')
    except ImportError as e:
        print(f"Warning: Could not import auth routes: {e}")
    
    try:
        from routes.dashboard import dashboard_bp
        app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    except ImportError as e:
        print(f"Warning: Could not import dashboard routes: {e}")
    
    # Basic routes
    @app.route('/')
    def index():
        """Home page - redirect to dashboard or login"""
        from flask_login import current_user
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))
    
    @app.route('/health')
    def health():
        """Health check endpoint"""
        return {'status': 'ok', 'message': 'Qatar POS System is running'}
    
    @app.route('/test')
    def test():
        """Simple test page"""
        return '''
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>نظام نقاط البيع القطري - اختبار</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .success { color: #28a745; }
                .info { color: #17a2b8; }
                h1 { color: #333; }
                .links { margin-top: 20px; }
                .links a { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
                .links a:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🇶🇦 نظام نقاط البيع القطري</h1>
                <p class="success">✅ النظام يعمل بنجاح!</p>
                <p class="info">📍 الخادم: http://localhost:5000</p>
                
                <div class="links">
                    <a href="/auth/login">تسجيل الدخول</a>
                    <a href="/dashboard/">لوحة التحكم</a>
                    <a href="/health">فحص الحالة</a>
                </div>
                
                <hr>
                <h2>معلومات النظام</h2>
                <ul>
                    <li>🏢 مصمم خصيصاً للسوق القطري</li>
                    <li>🌐 دعم اللغتين العربية والإنجليزية</li>
                    <li>💰 دعم الريال القطري (QAR)</li>
                    <li>📅 أسبوع عمل 6 أيام (إغلاق الجمعة)</li>
                    <li>🆔 دعم رقم الهوية القطرية</li>
                </ul>
            </div>
        </body>
        </html>
        '''
    
    # Create database tables
    with app.app_context():
        try:
            db.create_all()
            print("✅ Database tables created successfully")
            
            # Create default admin user if not exists
            from models.user import User
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    first_name_ar='المدير',
                    first_name_en='Admin',
                    last_name_ar='العام',
                    last_name_en='User',
                    role='manager',
                    is_active=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ Default admin user created (username: admin, password: admin123)")
            
        except Exception as e:
            print(f"❌ Database error: {e}")
    
    return app

if __name__ == '__main__':
    print("🚀 Starting Qatar POS System...")
    print("🇶🇦 نظام نقاط البيع القطري")
    print("=" * 50)
    
    app = create_simple_app()
    
    print("📍 Server will be available at: http://localhost:5000")
    print("🔑 Default login: admin / admin123")
    print("🌐 Test page: http://localhost:5000/test")
    print("=" * 50)
    
    # Run the development server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
