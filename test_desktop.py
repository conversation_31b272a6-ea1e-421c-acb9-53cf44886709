"""
اختبار بسيط لتطبيق سطح المكتب
Simple Desktop Application Test
"""

import sys
import os

def test_imports():
    """اختبار استيراد الوحدات المطلوبة"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False
    
    try:
        from flask import Flask
        print("✅ Flask متوفر")
    except ImportError:
        print("❌ Flask غير متوفر")
        print("تشغيل: pip install flask")
        return False
    
    try:
        import threading
        import webbrowser
        import socket
        import datetime
        import json
        print("✅ الوحدات الأساسية متوفرة")
    except ImportError as e:
        print(f"❌ خطأ في الوحدات الأساسية: {e}")
        return False
    
    return True

def test_files():
    """اختبار وجود الملفات المطلوبة"""
    print("\n📁 اختبار وجود الملفات...")
    
    required_files = [
        'app.py',
        'config.py',
        'extensions.py',
        'desktop_app.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def test_flask_app():
    """اختبار تطبيق Flask"""
    print("\n🌐 اختبار تطبيق Flask...")
    
    try:
        from app import create_app
        app = create_app()
        print("✅ تم إنشاء تطبيق Flask بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء تطبيق Flask: {e}")
        return False

def test_port_availability():
    """اختبار توفر المنفذ"""
    print("\n🔌 اختبار توفر المنفذ...")

    import socket
    port = 2626
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            print(f"✅ المنفذ {port} متوفر")
            return True
    except OSError:
        print(f"⚠️ المنفذ {port} مستخدم، سيتم البحث عن منفذ آخر")
        
        # البحث عن منفذ متوفر
        for test_port in range(2627, 2636):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', test_port))
                    print(f"✅ المنفذ {test_port} متوفر")
                    return True
            except OSError:
                continue
        
        print("❌ لا يوجد منفذ متوفر")
        return False

def create_simple_gui():
    """إنشاء واجهة مستخدم بسيطة للاختبار"""
    print("\n🖥️ إنشاء واجهة الاختبار...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.title("Qatar POS - اختبار سطح المكتب")
        root.geometry("600x400")
        
        # إطار رئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="نظام نقاط البيع القطري", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # معلومات النظام
        info_text = f"""
معلومات النظام:
- Python: {sys.version.split()[0]}
- نظام التشغيل: {os.name}
- المجلد الحالي: {os.getcwd()}
        """
        
        info_label = ttk.Label(main_frame, text=info_text.strip(), 
                              justify=tk.LEFT, font=('Courier', 10))
        info_label.pack(pady=(0, 20))
        
        # أزرار الاختبار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        def test_browser():
            try:
                webbrowser.open("http://127.0.0.1:2626")
                result_label.config(text="✅ تم فتح المتصفح", foreground="green")
            except Exception as e:
                result_label.config(text=f"❌ خطأ: {e}", foreground="red")
        
        def run_tests():
            result_label.config(text="🔍 جاري تشغيل الاختبارات...", foreground="blue")
            root.update()
            
            if test_imports() and test_files() and test_flask_app() and test_port_availability():
                result_label.config(text="✅ جميع الاختبارات نجحت!", foreground="green")
            else:
                result_label.config(text="❌ بعض الاختبارات فشلت", foreground="red")
        
        ttk.Button(button_frame, text="تشغيل الاختبارات", 
                  command=run_tests).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="اختبار المتصفح", 
                  command=test_browser).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="إغلاق", 
                  command=root.quit).pack(side=tk.LEFT)
        
        # نتائج الاختبار
        result_label = ttk.Label(main_frame, text="جاهز للاختبار", 
                                font=('Arial', 12))
        result_label.pack(pady=20)
        
        print("✅ تم إنشاء واجهة الاختبار بنجاح")
        
        # تشغيل الاختبارات تلقائياً
        root.after(1000, run_tests)
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("    Qatar POS System - Desktop Test")
    print("    نظام نقاط البيع القطري - اختبار سطح المكتب")
    print("=" * 60)
    
    # تشغيل الاختبارات
    if not test_imports():
        input("\nاضغط Enter للخروج...")
        return
    
    if not test_files():
        input("\nاضغط Enter للخروج...")
        return
    
    if not test_flask_app():
        input("\nاضغط Enter للخروج...")
        return
    
    if not test_port_availability():
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("🖥️ جاري فتح واجهة الاختبار...")
    
    # إنشاء واجهة الاختبار
    create_simple_gui()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
