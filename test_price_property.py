#!/usr/bin/env python3
"""
Test script for Product.price property
"""

from app import create_app
from models.product import Product
from extensions import db

def test_price_property():
    """Test the price property functionality"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار خاصية price في نموذج Product")
        print("=" * 50)
        
        # Get first product
        product = Product.query.first()
        
        if not product:
            print("❌ لا توجد منتجات في قاعدة البيانات")
            return
        
        print(f"📦 اختبار المنتج: {product.name_ar}")
        print(f"   SKU: {product.sku}")
        
        # Test reading price property
        print(f"\n🔍 اختبار قراءة السعر:")
        print(f"   selling_price: {product.selling_price}")
        print(f"   price (property): {product.price}")
        
        # Verify they are the same
        if product.selling_price == product.price:
            print("   ✅ price property يقرأ selling_price بشكل صحيح")
        else:
            print("   ❌ خطأ في قراءة price property")
            return
        
        # Test setting price property
        print(f"\n✏️ اختبار تعديل السعر:")
        original_price = product.price
        test_price = 99.99
        
        product.price = test_price
        
        print(f"   السعر الأصلي: {original_price}")
        print(f"   السعر الجديد: {test_price}")
        print(f"   price بعد التعديل: {product.price}")
        print(f"   selling_price بعد التعديل: {product.selling_price}")
        
        # Verify the setter worked
        if product.price == test_price and product.selling_price == test_price:
            print("   ✅ price property يحدث selling_price بشكل صحيح")
        else:
            print("   ❌ خطأ في تحديث price property")
            return
        
        # Restore original price
        product.price = original_price
        print(f"   تم استعادة السعر الأصلي: {product.price}")
        
        # Test with None values
        print(f"\n🔍 اختبار القيم الفارغة:")
        product.selling_price = None
        try:
            price_value = product.price
            print(f"   price مع selling_price=None: {price_value}")
            print("   ✅ يتعامل مع القيم الفارغة بشكل صحيح")
        except Exception as e:
            print(f"   ❌ خطأ مع القيم الفارغة: {e}")
        
        # Restore original price
        product.selling_price = original_price
        
        print(f"\n🎉 جميع اختبارات price property نجحت!")
        print("✅ يمكن الآن استخدام product.price بدلاً من product.selling_price")

if __name__ == '__main__':
    test_price_property()
