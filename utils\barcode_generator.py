"""
Barcode Generator for Qatar POS System
Handles barcode generation, image creation, and printing
"""

import os
import io
import qrcode
from barcode import EAN13, Code128, Code39
from barcode.writer import ImageWriter
from PIL import Image, ImageDraw, ImageFont
from decimal import Decimal

class BarcodeGenerator:
    """Handle barcode generation and image creation"""
    
    def __init__(self, output_dir='static/barcodes'):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def generate_barcode_image(self, barcode_data, barcode_type='EAN13', 
                              product_name='', price='', language='ar'):
        """Generate barcode image with product information"""
        try:
            # Create barcode
            if barcode_type == 'EAN13':
                # Ensure barcode is 13 digits
                if len(barcode_data) == 12:
                    barcode_data = self._add_ean13_check_digit(barcode_data)
                elif len(barcode_data) != 13:
                    raise ValueError("EAN13 barcode must be 12 or 13 digits")
                
                barcode_class = EAN13
            elif barcode_type == 'CODE128':
                barcode_class = Code128
            elif barcode_type == 'CODE39':
                barcode_class = Code39
            else:
                barcode_class = Code128
            
            # Generate barcode
            barcode = barcode_class(barcode_data, writer=ImageWriter())
            
            # Save barcode to buffer
            buffer = io.BytesIO()
            barcode.write(buffer)
            buffer.seek(0)
            
            # Open barcode image
            barcode_img = Image.open(buffer)
            
            # Create label with product info
            label_img = self._create_product_label(
                barcode_img, product_name, price, barcode_data, language
            )
            
            # Save final image
            filename = f"{barcode_data}.png"
            filepath = os.path.join(self.output_dir, filename)
            label_img.save(filepath, 'PNG', dpi=(300, 300))
            
            return filename, "تم إنشاء الباركود بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء الباركود: {str(e)}"
    
    def _add_ean13_check_digit(self, barcode):
        """Add check digit to EAN13 barcode"""
        odd_sum = sum(int(barcode[i]) for i in range(0, 12, 2))
        even_sum = sum(int(barcode[i]) for i in range(1, 12, 2))
        total = odd_sum + (even_sum * 3)
        check_digit = (10 - (total % 10)) % 10
        return barcode + str(check_digit)
    
    def _create_product_label(self, barcode_img, product_name, price, barcode_data, language='ar'):
        """Create product label with barcode and information"""
        # Label dimensions (adjust as needed)
        label_width = 400
        label_height = 250
        
        # Create new image for label
        label = Image.new('RGB', (label_width, label_height), 'white')
        draw = ImageDraw.Draw(label)
        
        try:
            # Try to load a font (fallback to default if not available)
            if language == 'ar':
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_medium = ImageFont.truetype("arial.ttf", 14)
                font_small = ImageFont.truetype("arial.ttf", 12)
            else:
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_medium = ImageFont.truetype("arial.ttf", 14)
                font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            # Fallback to default font
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Resize barcode to fit label
        barcode_width = int(label_width * 0.9)
        barcode_height = int(barcode_img.height * (barcode_width / barcode_img.width))
        barcode_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
        
        # Position barcode in center
        barcode_x = (label_width - barcode_width) // 2
        barcode_y = 40
        
        # Paste barcode
        label.paste(barcode_resized, (barcode_x, barcode_y))
        
        # Add product name (truncate if too long)
        if product_name:
            max_chars = 30
            if len(product_name) > max_chars:
                product_name = product_name[:max_chars-3] + "..."
            
            # Calculate text position
            bbox = draw.textbbox((0, 0), product_name, font=font_medium)
            text_width = bbox[2] - bbox[0]
            text_x = (label_width - text_width) // 2
            text_y = 10
            
            draw.text((text_x, text_y), product_name, fill='black', font=font_medium)
        
        # Add price
        if price:
            price_text = f"{price} ر.ق" if language == 'ar' else f"QAR {price}"
            bbox = draw.textbbox((0, 0), price_text, font=font_large)
            text_width = bbox[2] - bbox[0]
            text_x = (label_width - text_width) // 2
            text_y = barcode_y + barcode_height + 10
            
            draw.text((text_x, text_y), price_text, fill='black', font=font_large)
        
        # Add barcode number
        bbox = draw.textbbox((0, 0), barcode_data, font=font_small)
        text_width = bbox[2] - bbox[0]
        text_x = (label_width - text_width) // 2
        text_y = label_height - 25
        
        draw.text((text_x, text_y), barcode_data, fill='black', font=font_small)
        
        return label
    
    def generate_qr_code(self, data, size=(200, 200)):
        """Generate QR code for invoices or products"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_img = qr_img.resize(size, Image.Resampling.LANCZOS)
            
            return qr_img, "تم إنشاء رمز QR بنجاح"
            
        except Exception as e:
            return None, f"خطأ في إنشاء رمز QR: {str(e)}"
    
    def create_multiple_labels(self, products, labels_per_row=3, language='ar'):
        """Create a sheet with multiple product labels"""
        if not products:
            return None, "لا توجد منتجات لطباعتها"
        
        # A4 size at 300 DPI
        sheet_width = 2480  # 8.27 inches * 300 DPI
        sheet_height = 3508  # 11.69 inches * 300 DPI
        
        # Label dimensions
        label_width = 400
        label_height = 250
        
        # Calculate spacing
        margin = 50
        spacing_x = (sheet_width - (labels_per_row * label_width) - (2 * margin)) // (labels_per_row - 1) if labels_per_row > 1 else 0
        spacing_y = 30
        
        # Create sheet
        sheet = Image.new('RGB', (sheet_width, sheet_height), 'white')
        
        current_x = margin
        current_y = margin
        labels_in_row = 0
        
        for product in products:
            if not product.barcode:
                continue
            
            # Generate barcode image
            filename, message = self.generate_barcode_image(
                product.barcode,
                product.barcode_type or 'EAN13',
                product.get_name(language),
                str(product.get_final_price()),
                language
            )
            
            if filename:
                # Load the generated label
                label_path = os.path.join(self.output_dir, filename)
                if os.path.exists(label_path):
                    label_img = Image.open(label_path)
                    
                    # Paste label on sheet
                    sheet.paste(label_img, (current_x, current_y))
                    
                    # Update position
                    labels_in_row += 1
                    if labels_in_row >= labels_per_row:
                        # Move to next row
                        current_x = margin
                        current_y += label_height + spacing_y
                        labels_in_row = 0
                        
                        # Check if we've reached the bottom of the sheet
                        if current_y + label_height > sheet_height:
                            break
                    else:
                        # Move to next column
                        current_x += label_width + spacing_x
        
        # Save sheet
        sheet_filename = f"labels_sheet_{len(products)}_products.png"
        sheet_path = os.path.join(self.output_dir, sheet_filename)
        sheet.save(sheet_path, 'PNG', dpi=(300, 300))
        
        return sheet_filename, f"تم إنشاء ورقة تحتوي على {len(products)} ملصق"
    
    def delete_barcode_image(self, filename):
        """Delete barcode image file"""
        try:
            filepath = os.path.join(self.output_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
                return True, "تم حذف صورة الباركود"
            return False, "الملف غير موجود"
        except Exception as e:
            return False, f"خطأ في حذف الملف: {str(e)}"

# Global instance
barcode_generator = BarcodeGenerator()

def generate_product_barcode(product, language='ar'):
    """Convenience function for generating product barcode"""
    return barcode_generator.generate_barcode_image(
        product.barcode,
        product.barcode_type or 'EAN13',
        product.get_name(language),
        str(product.get_final_price()),
        language
    )

def create_labels_sheet(products, language='ar'):
    """Convenience function for creating multiple labels"""
    return barcode_generator.create_multiple_labels(products, language=language)
