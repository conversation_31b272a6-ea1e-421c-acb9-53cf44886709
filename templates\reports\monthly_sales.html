{% extends "base.html" %}

{% block title %}
{{ 'تقرير المبيعات الشهرية - نظام نقاط البيع القطري' if language == 'ar' else 'Monthly Sales Report - Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="bi bi-calendar-month"></i>
                {{ 'تقرير المبيعات الشهرية' if language == 'ar' else 'Monthly Sales Report' }}
            </h1>
            <div>
                <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                    <i class="bi bi-file-pdf"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="bi bi-file-excel"></i>
                    Excel
                </button>
                <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {{ 'العودة للتقارير' if language == 'ar' else 'Back to Reports' }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {{ 'تصفية التقرير' if language == 'ar' else 'Report Filters' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">{{ 'السنة' if language == 'ar' else 'Year' }}</label>
                        <select class="form-select" name="year">
                            {% for y in range(2020, 2030) %}
                            <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                                {{ 'تطبيق' if language == 'ar' else 'Apply' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(total_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {{ 'إجمالي المعاملات' if language == 'ar' else 'Total Transactions' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,}'.format(total_transactions) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {{ 'متوسط المبيعات الشهرية' if language == 'ar' else 'Average Monthly Sales' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ '{:,.2f} ر.ق'.format(avg_monthly_sales) if language == 'ar' else 'QAR {:,.2f}'.format(avg_monthly_sales) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {{ 'السنة' if language == 'ar' else 'Year' }}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ year }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {{ 'رسم بياني للمبيعات الشهرية' if language == 'ar' else 'Monthly Sales Chart' }}
                </h6>
            </div>
            <div class="card-body">
                <canvas id="monthlySalesChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table"></i>
                    {{ 'تفاصيل المبيعات الشهرية' if language == 'ar' else 'Monthly Sales Details' }}
                </h6>
            </div>
            <div class="card-body">
                {% if monthly_sales %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ 'الشهر' if language == 'ar' else 'Month' }}</th>
                                <th>{{ 'إجمالي المبيعات' if language == 'ar' else 'Total Sales' }}</th>
                                <th>{{ 'عدد المعاملات' if language == 'ar' else 'Transactions' }}</th>
                                <th>{{ 'متوسط المعاملة' if language == 'ar' else 'Avg Transaction' }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month in monthly_sales %}
                            <tr>
                                <td>{{ month.month_name }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(month.total_sales) if language == 'ar' else 'QAR {:,.2f}'.format(month.total_sales) }}</td>
                                <td>{{ '{:,}'.format(month.transaction_count) }}</td>
                                <td>{{ '{:,.2f} ر.ق'.format(month.avg_transaction) if language == 'ar' else 'QAR {:,.2f}'.format(month.avg_transaction) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">{{ 'لا توجد بيانات للسنة المحددة' if language == 'ar' else 'No data found for the selected year' }}</h5>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Monthly Sales Chart
const ctx = document.getElementById('monthlySalesChart').getContext('2d');
const monthlySalesChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: [
            {% for month in monthly_sales %}
            '{{ month.month_name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ 'المبيعات الشهرية' if language == 'ar' else 'Monthly Sales' }}',
            data: [
                {% for month in monthly_sales %}
                {{ month.total_sales }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '{{ 'المبيعات الشهرية لعام' if language == 'ar' else 'Monthly Sales for' }} {{ year }}'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Export functions
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    window.open(`{{ url_for('reports.monthly_sales') }}?${params.toString()}`);
}
</script>
{% endblock %}
