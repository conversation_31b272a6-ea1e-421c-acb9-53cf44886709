"""
Product model for Qatar POS System
Supports bilingual product information, pricing, and inventory tracking
"""

from datetime import datetime
from decimal import Decimal
from extensions import db

class Product(db.Model):
    """Product model with bilingual support and inventory tracking"""
    
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Product identification
    sku = db.Column(db.String(50), unique=True, nullable=False, index=True)
    barcode = db.Column(db.String(50), unique=True, index=True)
    
    # Bilingual product information
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200), nullable=False)
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    
    # Category relationship
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False)
    
    # Pricing (in QAR)
    cost_price = db.Column(db.Numeric(10, 3), nullable=False, default=0)
    selling_price = db.Column(db.Numeric(10, 3), nullable=False, default=0)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    
    # Inventory
    current_stock = db.Column(db.Integer, nullable=False, default=0)
    minimum_stock = db.Column(db.Integer, nullable=False, default=5)
    maximum_stock = db.Column(db.Integer, nullable=False, default=1000)
    
    # Product details
    unit_of_measure = db.Column(db.String(20), default='piece')  # piece, kg, liter, etc.
    weight = db.Column(db.Numeric(8, 3))  # in kg
    dimensions = db.Column(db.String(50))  # LxWxH in cm
    
    # Images
    image_filename = db.Column(db.String(255))
    
    # Status and flags
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_taxable = db.Column(db.Boolean, default=False, nullable=False)
    track_inventory = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    sale_items = db.relationship('SaleItem', backref='product', lazy='dynamic')
    inventory_transactions = db.relationship('InventoryTransaction', 
                                           backref='product', lazy='dynamic')
    
    def get_name(self, language='ar'):
        """Get product name in specified language"""
        return self.name_ar if language == 'ar' else self.name_en
    
    def get_description(self, language='ar'):
        """Get product description in specified language"""
        return self.description_ar if language == 'ar' else self.description_en
    
    def get_final_price(self):
        """Get final selling price after discount"""
        if self.discount_percentage > 0:
            discount_amount = self.selling_price * (self.discount_percentage / 100)
            return self.selling_price - discount_amount
        return self.selling_price
    
    def get_profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price > 0:
            profit = self.get_final_price() - self.cost_price
            return (profit / self.cost_price) * 100
        return 0
    
    def is_low_stock(self):
        """Check if product is low on stock"""
        return self.current_stock <= self.minimum_stock
    
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        return self.current_stock <= 0
    
    def can_sell_quantity(self, quantity):
        """Check if we can sell the requested quantity"""
        if not self.track_inventory:
            return True
        return self.current_stock >= quantity
    
    def update_stock(self, quantity_change, transaction_type='manual'):
        """Update stock and create inventory transaction"""
        old_stock = self.current_stock
        self.current_stock += quantity_change

        # Create inventory transaction record will be handled in inventory.py
        return self.current_stock
    
    def to_dict(self, language='ar'):
        """Convert product to dictionary"""
        return {
            'id': self.id,
            'sku': self.sku,
            'barcode': self.barcode,
            'name': self.get_name(language),
            'description': self.get_description(language),
            'category_name': self.category.get_name(language) if self.category else None,
            'cost_price': float(self.cost_price),
            'selling_price': float(self.selling_price),
            'final_price': float(self.get_final_price()),
            'discount_percentage': float(self.discount_percentage),
            'current_stock': self.current_stock,
            'minimum_stock': self.minimum_stock,
            'is_low_stock': self.is_low_stock(),
            'is_out_of_stock': self.is_out_of_stock(),
            'unit_of_measure': self.unit_of_measure,
            'is_active': self.is_active,
            'image_filename': self.image_filename,
            'profit_margin': round(self.get_profit_margin(), 2)
        }
    
    def __repr__(self):
        return f'<Product {self.sku}: {self.name_en}>'
