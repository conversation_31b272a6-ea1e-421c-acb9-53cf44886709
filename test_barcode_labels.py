#!/usr/bin/env python3
"""
Test script for enhanced barcode label generation
Qatar POS System
"""

import os
import sys
sys.path.append('.')

from utils.barcode_generator import BarcodeGenerator
from PIL import Image, ImageDraw, ImageFont

def test_enhanced_labels():
    """Test the enhanced barcode label generation"""
    
    print("🧪 Testing Enhanced Barcode Label Generation...")
    
    # Initialize generator
    generator = BarcodeGenerator()
    
    # Test data
    test_products = [
        {
            'barcode': '123456789012',
            'name_ar': 'منتج تجريبي 1',
            'name_en': 'Test Product 1',
            'price': '25.50'
        },
        {
            'barcode': '987654321098',
            'name_ar': 'منتج تجريبي 2',
            'name_en': 'Test Product 2',
            'price': '15.75'
        },
        {
            'barcode': '456789123456',
            'name_ar': 'منتج تجريبي 3',
            'name_en': 'Test Product 3',
            'price': '99.99'
        }
    ]
    
    print(f"📦 Testing {len(test_products)} products...")
    
    # Test Arabic labels
    print("\n🔤 Testing Arabic labels...")
    for i, product in enumerate(test_products, 1):
        try:
            filename, message = generator.generate_barcode_image(
                product['barcode'],
                'EAN13',
                product['name_ar'],
                product['price'],
                'ar'
            )
            
            if filename:
                print(f"✅ Product {i} (AR): {filename}")
                
                # Check if file exists
                filepath = os.path.join('static/barcodes', filename)
                if os.path.exists(filepath):
                    # Get file size
                    size = os.path.getsize(filepath)
                    print(f"   📁 File size: {size} bytes")
                    
                    # Check image dimensions
                    with Image.open(filepath) as img:
                        print(f"   📐 Dimensions: {img.size[0]}x{img.size[1]} pixels")
                else:
                    print(f"   ❌ File not found: {filepath}")
            else:
                print(f"❌ Product {i} (AR): {message}")
                
        except Exception as e:
            print(f"❌ Product {i} (AR): Error - {str(e)}")
    
    # Test English labels
    print("\n🔤 Testing English labels...")
    for i, product in enumerate(test_products, 1):
        try:
            filename, message = generator.generate_barcode_image(
                product['barcode'] + '1',  # Different barcode to avoid overwrite
                'EAN13',
                product['name_en'],
                product['price'],
                'en'
            )
            
            if filename:
                print(f"✅ Product {i} (EN): {filename}")
            else:
                print(f"❌ Product {i} (EN): {message}")
                
        except Exception as e:
            print(f"❌ Product {i} (EN): Error - {str(e)}")
    
    # Test different barcode types
    print("\n🏷️ Testing different barcode types...")
    test_barcodes = [
        ('123456789', 'CODE128'),
        ('TESTCODE39', 'CODE39'),
        ('123456789012', 'EAN13'),
    ]
    
    for barcode_data, barcode_type in test_barcodes:
        try:
            filename, message = generator.generate_barcode_image(
                barcode_data,
                barcode_type,
                f'Test {barcode_type}',
                '10.00',
                'ar'
            )
            
            if filename:
                print(f"✅ {barcode_type}: {filename}")
            else:
                print(f"❌ {barcode_type}: {message}")
                
        except Exception as e:
            print(f"❌ {barcode_type}: Error - {str(e)}")
    
    print("\n📊 Test Summary:")
    print("=" * 50)
    
    # Count generated files
    barcode_dir = 'static/barcodes'
    if os.path.exists(barcode_dir):
        files = [f for f in os.listdir(barcode_dir) if f.endswith('.png')]
        print(f"📁 Total barcode files: {len(files)}")
        
        # Calculate total size
        total_size = sum(os.path.getsize(os.path.join(barcode_dir, f)) for f in files)
        print(f"💾 Total size: {total_size / 1024:.2f} KB")
        
        # List recent files
        print(f"\n📋 Recent files:")
        for f in files[-5:]:  # Show last 5 files
            filepath = os.path.join(barcode_dir, f)
            size = os.path.getsize(filepath)
            print(f"   • {f} ({size} bytes)")
    else:
        print("❌ Barcode directory not found")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    test_enhanced_labels()
