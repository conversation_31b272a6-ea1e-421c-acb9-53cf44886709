@echo off
chcp 65001 >nul
title نظام نقاط البيع القطري - Qatar POS System

echo.
echo ========================================
echo 🇶🇦 نظام نقاط البيع القطري
echo    Qatar POS System
echo ========================================
echo.

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

echo 📦 Installing/updating requirements...
python -m pip install --upgrade pip
python -m pip install Flask Flask-SQLAlchemy Flask-Login Flask-Migrate Flask-WTF python-dotenv

echo.
echo 🚀 Starting Qatar POS System...
echo.
echo 📍 Server will be available at: http://localhost:5000
echo 🔑 Default login: admin / admin123
echo 🌐 Test page: http://localhost:5000/test
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

python run.py

echo.
echo Server stopped.
pause
