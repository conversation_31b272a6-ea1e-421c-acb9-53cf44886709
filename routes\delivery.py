"""
Delivery management routes for Qatar POS System
Handles delivery orders, COD payments, and delivery tracking
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models.delivery import DeliveryOrder, DeliveryAttempt, DeliveryZone
from models.sale import Sale
from models.customer import Customer
from extensions import db
from utils.helpers import get_user_language
from utils.decorators import permission_required
from datetime import datetime, date, timedelta
import json

delivery_bp = Blueprint('delivery', __name__)

@delivery_bp.route('/')
@login_required
@permission_required('sales')
def index():
    """List all delivery orders"""
    language = get_user_language()
    
    # Get filter parameters
    status_filter = request.args.get('status', '')
    date_filter = request.args.get('date', '')
    search = request.args.get('search', '').strip()
    
    # Build query
    query = DeliveryOrder.query
    
    # Apply filters
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(db.func.date(DeliveryOrder.created_at) == filter_date)
        except ValueError:
            pass
    
    if search:
        search_filter = db.or_(
            DeliveryOrder.delivery_number.contains(search),
            DeliveryOrder.customer_name.contains(search),
            DeliveryOrder.customer_phone.contains(search),
            DeliveryOrder.tracking_number.contains(search)
        )
        query = query.filter(search_filter)
    
    # Order by creation date (newest first)
    deliveries = query.order_by(DeliveryOrder.created_at.desc()).all()
    
    # Get delivery statistics
    stats = {
        'total': DeliveryOrder.query.count(),
        'pending': DeliveryOrder.query.filter_by(status='pending').count(),
        'in_transit': DeliveryOrder.query.filter_by(status='in_transit').count(),
        'delivered': DeliveryOrder.query.filter_by(status='delivered').count(),
        'cod_orders': DeliveryOrder.query.filter_by(is_cod=True).count(),
        'cod_pending': DeliveryOrder.query.filter_by(is_cod=True, cod_collected=False).count()
    }
    
    return render_template('delivery/index.html',
                         deliveries=deliveries,
                         stats=stats,
                         language=language)

@delivery_bp.route('/create/<int:sale_id>')
@login_required
@permission_required('sales')
def create(sale_id):
    """Create delivery order for a sale"""
    language = get_user_language()
    sale = Sale.query.get_or_404(sale_id)
    
    # Check if delivery order already exists
    if sale.delivery_order:
        flash('يوجد طلب توصيل لهذه المبيعة بالفعل' if language == 'ar' 
              else 'Delivery order already exists for this sale', 'warning')
        return redirect(url_for('delivery.view', delivery_id=sale.delivery_order.id))
    
    # Get delivery zones
    zones = DeliveryZone.query.filter_by(is_active=True).all()
    
    return render_template('delivery/create.html',
                         sale=sale,
                         zones=zones,
                         language=language)

@delivery_bp.route('/api/create', methods=['POST'])
@login_required
@permission_required('sales')
def api_create():
    """API endpoint to create delivery order"""
    data = request.get_json()
    
    try:
        sale = Sale.query.get(data['sale_id'])
        if not sale:
            return jsonify({'error': 'Sale not found'}), 404
        
        # Check if delivery order already exists
        if sale.delivery_order:
            return jsonify({'error': 'Delivery order already exists'}), 400
        
        # Create delivery order
        delivery = DeliveryOrder(
            sale_id=sale.id,
            customer_name=data['customer_name'],
            customer_phone=data['customer_phone'],
            customer_email=data.get('customer_email', ''),
            delivery_address=data['delivery_address'],
            delivery_area=data.get('delivery_area', ''),
            delivery_city=data.get('delivery_city', 'الدوحة'),
            delivery_zone=data.get('delivery_zone', 'doha_center'),
            building_number=data.get('building_number', ''),
            floor_number=data.get('floor_number', ''),
            apartment_number=data.get('apartment_number', ''),
            landmark=data.get('landmark', ''),
            delivery_type=data.get('delivery_type', 'standard'),
            delivery_date=datetime.strptime(data['delivery_date'], '%Y-%m-%d').date() if data.get('delivery_date') else None,
            delivery_time_slot=data.get('delivery_time_slot', ''),
            is_cod=data.get('is_cod', False),
            cod_amount=sale.total_amount if data.get('is_cod') else 0,
            delivery_company=data.get('delivery_company', ''),
            special_instructions=data.get('special_instructions', ''),
            customer_notes=data.get('customer_notes', '')
        )
        
        db.session.add(delivery)
        
        # Update sale payment method if COD
        if delivery.is_cod:
            sale.payment_method = 'cod'
            sale.payment_status = 'pending'
        
        db.session.commit()
        
        language = get_user_language()
        return jsonify({
            'success': True,
            'delivery': delivery.to_dict(language),
            'message': 'تم إنشاء طلب التوصيل بنجاح' if language == 'ar' else 'Delivery order created successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@delivery_bp.route('/<int:delivery_id>')
@login_required
@permission_required('sales')
def view(delivery_id):
    """View delivery order details"""
    language = get_user_language()
    delivery = DeliveryOrder.query.get_or_404(delivery_id)
    
    # Get delivery attempts
    attempts = delivery.delivery_attempts.order_by(DeliveryAttempt.attempt_date.desc()).all()
    
    return render_template('delivery/view.html',
                         delivery=delivery,
                         attempts=attempts,
                         language=language)

@delivery_bp.route('/<int:delivery_id>/update-status', methods=['POST'])
@login_required
@permission_required('sales')
def update_status(delivery_id):
    """Update delivery status"""
    language = get_user_language()
    delivery = DeliveryOrder.query.get_or_404(delivery_id)
    
    new_status = request.form.get('status')
    notes = request.form.get('notes', '')
    
    if not new_status:
        flash('يرجى اختيار الحالة الجديدة' if language == 'ar' 
              else 'Please select new status', 'error')
        return redirect(url_for('delivery.view', delivery_id=delivery.id))
    
    try:
        delivery.update_status(new_status, notes)
        
        # If delivered and COD, update sale payment status
        if new_status == 'delivered' and delivery.is_cod:
            delivery.sale.payment_status = 'paid'
            delivery.sale.amount_paid = delivery.cod_amount
            delivery.sale.amount_due = 0
        
        db.session.commit()
        
        flash('تم تحديث حالة التوصيل بنجاح' if language == 'ar' 
              else 'Delivery status updated successfully', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الحالة' if language == 'ar' 
              else 'Error updating status', 'error')
    
    return redirect(url_for('delivery.view', delivery_id=delivery.id))

@delivery_bp.route('/zones')
@login_required
@permission_required('sales')
def zones():
    """Manage delivery zones"""
    language = get_user_language()
    zones = DeliveryZone.query.all()
    
    return render_template('delivery/zones.html',
                         zones=zones,
                         language=language)

@delivery_bp.route('/api/zones/<zone_code>/calculate-fee')
@login_required
def api_calculate_fee(zone_code):
    """Calculate delivery fee for zone and type"""
    delivery_type = request.args.get('type', 'standard')
    
    zone = DeliveryZone.query.filter_by(zone_code=zone_code).first()
    if not zone:
        return jsonify({'error': 'Zone not found'}), 404
    
    fee = zone.calculate_fee(delivery_type)
    
    return jsonify({
        'success': True,
        'zone': zone.get_name(get_user_language()),
        'delivery_type': delivery_type,
        'fee': fee,
        'currency': 'QAR'
    })

@delivery_bp.route('/reports')
@login_required
@permission_required('reports')
def reports():
    """Delivery reports"""
    language = get_user_language()
    
    # Get date range
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # Build query
    query = DeliveryOrder.query.filter(
        db.func.date(DeliveryOrder.created_at) >= start_date,
        db.func.date(DeliveryOrder.created_at) <= end_date
    )
    
    deliveries = query.all()
    
    # Calculate statistics
    stats = {
        'total_deliveries': len(deliveries),
        'total_revenue': sum(d.sale.total_amount for d in deliveries),
        'total_delivery_fees': sum(d.total_delivery_charges for d in deliveries),
        'cod_orders': len([d for d in deliveries if d.is_cod]),
        'cod_revenue': sum(d.cod_amount for d in deliveries if d.is_cod),
        'delivered_orders': len([d for d in deliveries if d.status == 'delivered']),
        'pending_orders': len([d for d in deliveries if d.status in ['pending', 'confirmed', 'in_transit']]),
        'failed_orders': len([d for d in deliveries if d.status in ['failed', 'cancelled']])
    }
    
    # Group by status
    status_breakdown = {}
    for delivery in deliveries:
        status = delivery.status
        if status not in status_breakdown:
            status_breakdown[status] = 0
        status_breakdown[status] += 1
    
    # Group by zone
    zone_breakdown = {}
    for delivery in deliveries:
        zone = delivery.delivery_zone or 'unknown'
        if zone not in zone_breakdown:
            zone_breakdown[zone] = {'count': 0, 'revenue': 0}
        zone_breakdown[zone]['count'] += 1
        zone_breakdown[zone]['revenue'] += float(delivery.sale.total_amount)
    
    return render_template('delivery/reports.html',
                         deliveries=deliveries,
                         stats=stats,
                         status_breakdown=status_breakdown,
                         zone_breakdown=zone_breakdown,
                         start_date=start_date,
                         end_date=end_date,
                         language=language)
