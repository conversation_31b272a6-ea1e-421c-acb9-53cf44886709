"""
إصلاح مشكلة image_url في نموذج Product
Fix Product image_url attribute issue
"""

import os
import sys
from flask import Flask
from extensions import db

def create_app():
    """إنشاء تطبيق Flask للإصلاح"""
    app = Flask(__name__)
    
    # إعداد قاعدة البيانات
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/qatar_pos.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'dev-secret-key'
    
    # تهيئة الملحقات
    db.init_app(app)
    
    return app

def test_product_image_url():
    """اختبار خاصية image_url في نموذج Product"""
    print("🔍 اختبار خاصية image_url في نموذج Product...")
    print("🔍 Testing Product image_url property...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # استيراد النموذج
            from models.product import Product
            
            # اختبار إنشاء منتج جديد
            print("\n📝 اختبار إنشاء منتج جديد...")
            print("📝 Testing new product creation...")
            
            # إنشاء منتج تجريبي
            test_product = Product(
                sku='TEST001',
                name_ar='منتج تجريبي',
                name_en='Test Product',
                selling_price=10.0,
                cost_price=5.0,
                category_id=1,
                image_filename='test_image.jpg'
            )
            
            # اختبار خاصية image_url
            print(f"✅ image_url مع اسم ملف: {test_product.image_url}")
            
            # اختبار بدون اسم ملف
            test_product.image_filename = None
            print(f"✅ image_url بدون اسم ملف: {test_product.image_url}")
            
            # اختبار دالة get_image_url
            test_product.image_filename = 'another_image.png'
            print(f"✅ get_image_url(): {test_product.get_image_url()}")
            
            # اختبار to_dict
            product_dict = test_product.to_dict()
            print(f"✅ to_dict() يحتوي على image_url: {'image_url' in product_dict}")
            print(f"✅ قيمة image_url في to_dict: {product_dict.get('image_url')}")
            
            print("\n🎉 جميع الاختبارات نجحت!")
            print("🎉 All tests passed!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"❌ Test error: {str(e)}")
            return False

def check_existing_products():
    """فحص المنتجات الموجودة"""
    print("\n🔍 فحص المنتجات الموجودة...")
    print("🔍 Checking existing products...")
    
    app = create_app()
    
    with app.app_context():
        try:
            from models.product import Product
            
            # جلب جميع المنتجات
            products = Product.query.limit(5).all()
            
            if not products:
                print("📝 لا توجد منتجات في قاعدة البيانات")
                print("📝 No products found in database")
                return True
            
            print(f"📊 تم العثور على {len(products)} منتجات")
            print(f"📊 Found {len(products)} products")
            
            for product in products:
                print(f"\n📦 المنتج: {product.get_name()}")
                print(f"📦 Product: {product.get_name()}")
                print(f"   - image_filename: {product.image_filename}")
                print(f"   - image_url: {product.image_url}")
                print(f"   - get_image_url(): {product.get_image_url()}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص المنتجات: {str(e)}")
            print(f"❌ Error checking products: {str(e)}")
            return False

def create_default_image():
    """إنشاء صورة افتراضية"""
    print("\n🖼️ إنشاء صورة افتراضية...")
    print("🖼️ Creating default image...")
    
    try:
        # إنشاء مجلد الصور إذا لم يكن موجوداً
        images_dir = "static/images"
        uploads_dir = "static/uploads"
        
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(uploads_dir, exist_ok=True)
        
        # إنشاء ملف صورة افتراضية بسيط
        default_image_path = os.path.join(images_dir, "no-image.png")
        
        if not os.path.exists(default_image_path):
            # إنشاء صورة افتراضية بسيطة (ملف نصي مؤقت)
            with open(default_image_path.replace('.png', '.txt'), 'w') as f:
                f.write("Default image placeholder")
            
            print(f"✅ تم إنشاء ملف افتراضي: {default_image_path}")
            print(f"✅ Created default file: {default_image_path}")
        else:
            print(f"✅ الملف الافتراضي موجود: {default_image_path}")
            print(f"✅ Default file exists: {default_image_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة الافتراضية: {str(e)}")
        print(f"❌ Error creating default image: {str(e)}")
        return False

def update_database_schema():
    """تحديث مخطط قاعدة البيانات إذا لزم الأمر"""
    print("\n🔄 فحص مخطط قاعدة البيانات...")
    print("🔄 Checking database schema...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # التحقق من وجود قاعدة البيانات
            if not os.path.exists('instance/qatar_pos.db'):
                print("📝 إنشاء قاعدة البيانات...")
                print("📝 Creating database...")
                db.create_all()
                print("✅ تم إنشاء قاعدة البيانات")
                print("✅ Database created")
            else:
                print("✅ قاعدة البيانات موجودة")
                print("✅ Database exists")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
            print(f"❌ Database error: {str(e)}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشكلة image_url في نموذج Product")
    print("🔧 Fixing Product image_url attribute issue")
    print("=" * 60)
    
    # فحص مخطط قاعدة البيانات
    if not update_database_schema():
        print("❌ فشل في فحص قاعدة البيانات")
        return False
    
    # إنشاء الصورة الافتراضية
    if not create_default_image():
        print("⚠️ تحذير: فشل في إنشاء الصورة الافتراضية")
    
    # اختبار خاصية image_url
    if not test_product_image_url():
        print("❌ فشل في اختبار image_url")
        return False
    
    # فحص المنتجات الموجودة
    if not check_existing_products():
        print("⚠️ تحذير: فشل في فحص المنتجات الموجودة")
    
    print("\n" + "=" * 60)
    print("🎉 تم إصلاح مشكلة image_url بنجاح!")
    print("🎉 Product image_url issue fixed successfully!")
    
    print("\n📋 الملخص:")
    print("📋 Summary:")
    print("✅ تم إضافة خاصية image_url إلى نموذج Product")
    print("✅ Added image_url property to Product model")
    print("✅ تم إضافة دالة get_image_url()")
    print("✅ Added get_image_url() method")
    print("✅ تم تحديث دالة to_dict() لتشمل image_url")
    print("✅ Updated to_dict() to include image_url")
    print("✅ تم إنشاء مجلدات الصور")
    print("✅ Created image directories")
    
    print("\n🎯 الآن يمكن استخدام:")
    print("🎯 Now you can use:")
    print("   - product.image_url")
    print("   - product.get_image_url()")
    print("   - product.to_dict()['image_url']")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ الإصلاح مكتمل!")
            print("✅ Fix completed!")
        else:
            print("\n❌ فشل الإصلاح!")
            print("❌ Fix failed!")
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
        print("⏹️ Operation cancelled")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {str(e)}")
        print(f"💥 Unexpected error: {str(e)}")
    
    input("\nاضغط Enter للخروج...")
