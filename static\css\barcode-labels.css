/* Enhanced Barcode Label Styles for Qatar POS System */

/* Label Container */
.label-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

/* Enhanced Label Preview */
.label-preview {
    border: 3px solid #333;
    padding: 8px;
    margin: 8px;
    display: inline-block;
    text-align: center;
    background: white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    border-radius: 6px;
    position: relative;
    overflow: hidden;
}

.label-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #8B1538, #6d1028);
}

/* Size Variants */
.label-size-30x20 {
    width: 30mm;
    height: 20mm;
    font-size: 6px;
}

.label-size-40x30 {
    width: 40mm;
    height: 30mm;
    font-size: 8px;
}

.label-size-50x30 {
    width: 50mm;
    height: 30mm;
    font-size: 9px;
}

.label-size-60x40 {
    width: 60mm;
    height: 40mm;
    font-size: 10px;
}

/* Barcode Image */
.barcode-image {
    max-width: 90%;
    height: auto;
    border: 1px solid #ddd;
    padding: 2px;
    background: white;
    border-radius: 2px;
    margin: 2px 0;
}

/* Product Name */
.product-name {
    font-size: 0.8em;
    margin: 3px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
}

/* Product Price */
.product-price {
    font-size: 1em;
    font-weight: bold;
    margin: 4px 0;
    color: #1976d2;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    padding: 3px 6px;
    border-radius: 4px;
    border: 2px solid #1976d2;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

/* Barcode Number */
.barcode-number {
    font-size: 0.7em;
    margin: 2px 0;
    font-family: 'Courier New', monospace;
    color: #666;
    background: white;
    padding: 2px 4px;
    border: 1px solid #999;
    border-radius: 2px;
    letter-spacing: 1px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-area {
        padding: 0;
        margin: 0;
        background: white;
    }
    
    .label-preview {
        border: 2px solid #000;
        page-break-inside: avoid;
        margin: 2mm;
        box-shadow: none;
    }
    
    .label-preview::before {
        display: none;
    }
    
    .label-container {
        background: white;
        padding: 0;
        margin: 0;
        gap: 2mm;
    }
    
    .barcode-image {
        border: 1px solid #000;
    }
    
    .product-name {
        background: #f0f0f0;
        border: 1px solid #000;
    }
    
    .product-price {
        background: #e0e0e0;
        border: 2px solid #000;
        color: #000;
    }
    
    .barcode-number {
        background: white;
        border: 1px solid #000;
        color: #000;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .label-container {
        padding: 10px;
        gap: 5px;
    }
    
    .label-preview {
        margin: 4px;
    }
}

/* Animation for new labels */
.label-preview {
    animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover effects for preview */
.label-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

/* Special styles for different label types */
.label-preview.premium {
    border-color: #8B1538;
    background: linear-gradient(135deg, #fff, #f8f9fa);
}

.label-preview.premium::before {
    background: linear-gradient(90deg, #8B1538, #6d1028, #8B1538);
}

/* QR Code specific styles */
.qr-code {
    border: 2px solid #333;
    padding: 4px;
    background: white;
    border-radius: 4px;
}

/* Barcode type indicator */
.barcode-type {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.6em;
    background: #333;
    color: white;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: bold;
}

/* Enhanced borders for different sizes */
.label-size-30x20 {
    border-width: 2px;
}

.label-size-40x30 {
    border-width: 2px;
}

.label-size-50x30 {
    border-width: 3px;
}

.label-size-60x40 {
    border-width: 3px;
}

/* Loading animation */
.label-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #8B1538;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
