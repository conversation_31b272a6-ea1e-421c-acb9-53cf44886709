{% extends "base.html" %}

{% block title %}
{{ 'سلة المشتريات' if language == 'ar' else 'Shopping Cart' }} - {{ 'نظام نقاط البيع القطري' if language == 'ar' else 'Qatar POS System' }}
{% endblock %}

{% block extra_css %}
<style>
/* تصميم سلة المشتريات المستقلة */
.cart-container {
    min-height: calc(100vh - 120px);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.cart-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.cart-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1rem 1.5rem;
    margin: 0;
    font-weight: 600;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.product-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.product-info h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.product-price {
    color: #e74c3c;
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.product-stock {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.product-stock.low-stock {
    color: #e74c3c;
    font-weight: 600;
}

.add-to-cart-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40,167,69,0.3);
}

.cart-sidebar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.cart-items {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.cart-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.cart-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.cart-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    margin: 0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.summary-row:last-child {
    border-bottom: none;
    font-size: 1.2rem;
    font-weight: 700;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid rgba(255,255,255,0.3);
}

.search-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.search-box {
    position: relative;
}

.search-box input {
    padding-right: 3rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.2rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.checkout-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231,76,60,0.3);
}

.checkout-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-cart i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.25rem;
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    
    .cart-sidebar {
        position: static;
        margin-top: 2rem;
    }
    
    .filter-buttons {
        justify-content: center;
    }
}

/* تحسينات إضافية */
.badge-stock {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-stock.low {
    background: #dc3545;
}

.badge-stock.out {
    background: #6c757d;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>
{% endblock %}

{% block content %}
<div class="cart-container">
    <!-- رأس الصفحة -->
    <div class="cart-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-cart3 me-3"></i>
                        {{ 'سلة المشتريات' if language == 'ar' else 'Shopping Cart' }}
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">
                        {{ 'اختر المنتجات وأضفها إلى سلة المشتريات' if language == 'ar' else 'Select products and add them to your cart' }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{{ url_for('sales.pos') }}" class="btn btn-light">
                            <i class="bi bi-calculator"></i>
                            {{ 'نقاط البيع' if language == 'ar' else 'POS' }}
                        </a>
                        <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-light">
                            <i class="bi bi-house"></i>
                            {{ 'الرئيسية' if language == 'ar' else 'Home' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- قسم المنتجات -->
            <div class="col-lg-8">
                <!-- قسم البحث والفلترة -->
                <div class="search-section">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="search-box">
                                <input type="text" class="form-control" id="product_search"
                                       placeholder="{{ 'البحث عن منتج...' if language == 'ar' else 'Search for products...' }}">
                                <i class="bi bi-search search-icon"></i>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="barcode_search"
                                   placeholder="{{ 'مسح الباركود' if language == 'ar' else 'Scan barcode' }}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="category_filter">
                                <option value="">{{ 'جميع الفئات' if language == 'ar' else 'All Categories' }}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.get_display_name(language) }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <!-- أزرار الفلترة السريعة -->
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">
                            {{ 'الكل' if language == 'ar' else 'All' }}
                        </button>
                        <button class="filter-btn" data-filter="in-stock">
                            {{ 'متوفر' if language == 'ar' else 'In Stock' }}
                        </button>
                        <button class="filter-btn" data-filter="low-stock">
                            {{ 'مخزون منخفض' if language == 'ar' else 'Low Stock' }}
                        </button>
                        <button class="filter-btn" data-filter="featured">
                            {{ 'مميز' if language == 'ar' else 'Featured' }}
                        </button>
                    </div>
                </div>

                <!-- شبكة المنتجات -->
                <div class="cart-section">
                    <h5 class="section-header">
                        <i class="bi bi-grid3x3-gap me-2"></i>
                        {{ 'المنتجات المتاحة' if language == 'ar' else 'Available Products' }}
                        <span class="badge bg-light text-dark ms-2" id="products_count">0</span>
                    </h5>
                    
                    <div id="products_loading" class="loading-spinner" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ 'جاري التحميل...' if language == 'ar' else 'Loading...' }}</span>
                        </div>
                    </div>
                    
                    <div class="product-grid" id="products_grid">
                        <!-- المنتجات ستُحمل هنا بواسطة JavaScript -->
                    </div>
                    
                    <!-- ترقيم الصفحات -->
                    <div class="d-flex justify-content-center p-3">
                        <nav>
                            <ul class="pagination" id="products_pagination">
                                <!-- ترقيم الصفحات سيُحمل هنا -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- سلة المشتريات -->
            <div class="col-lg-4">
                <div class="cart-sidebar">
                    <!-- رأس السلة -->
                    <div class="section-header">
                        <i class="bi bi-cart-check me-2"></i>
                        {{ 'سلة المشتريات' if language == 'ar' else 'Shopping Cart' }}
                        <span class="badge bg-light text-dark ms-2" id="cart_count">0</span>
                    </div>
                    
                    <!-- عناصر السلة -->
                    <div class="cart-items" id="cart_items">
                        <div class="empty-cart">
                            <i class="bi bi-cart-x"></i>
                            <h6>{{ 'السلة فارغة' if language == 'ar' else 'Cart is empty' }}</h6>
                            <p>{{ 'اختر منتجات لإضافتها' if language == 'ar' else 'Select products to add' }}</p>
                        </div>
                    </div>
                    
                    <!-- ملخص السلة -->
                    <div class="cart-summary">
                        <div class="summary-row">
                            <span>{{ 'المجموع الفرعي:' if language == 'ar' else 'Subtotal:' }}</span>
                            <span id="cart_subtotal">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</span>
                        </div>
                        <div class="summary-row">
                            <span>{{ 'الخصم:' if language == 'ar' else 'Discount:' }}</span>
                            <span id="cart_discount">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</span>
                        </div>
                        <div class="summary-row">
                            <span>{{ 'الضريبة:' if language == 'ar' else 'Tax:' }}</span>
                            <span id="cart_tax">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</span>
                        </div>
                        <div class="summary-row">
                            <span>{{ 'الإجمالي:' if language == 'ar' else 'Total:' }}</span>
                            <span id="cart_total">{{ '0.00 ر.ق' if language == 'ar' else 'QAR 0.00' }}</span>
                        </div>
                        
                        <button class="checkout-btn" id="checkout_btn" disabled>
                            <i class="bi bi-credit-card me-2"></i>
                            {{ 'إتمام الشراء' if language == 'ar' else 'Checkout' }}
                        </button>
                        
                        <button class="btn btn-outline-light w-100 mt-2" id="clear_cart_btn">
                            <i class="bi bi-trash me-2"></i>
                            {{ 'مسح السلة' if language == 'ar' else 'Clear Cart' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إدارة سلة المشتريات
class CartManager {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('shopping_cart') || '[]');
        this.currentPage = 1;
        this.currentFilter = 'all';
        this.currentCategory = '';
        this.currentSearch = '';

        this.init();
    }

    init() {
        this.loadProducts();
        this.updateCartDisplay();
        this.bindEvents();
        this.loadCategories();
    }

    bindEvents() {
        // البحث
        document.getElementById('product_search').addEventListener('input', (e) => {
            this.currentSearch = e.target.value;
            this.currentPage = 1;
            this.loadProducts();
        });

        // البحث بالباركود
        document.getElementById('barcode_search').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchByBarcode(e.target.value);
                e.target.value = '';
            }
        });

        // فلترة الفئات
        document.getElementById('category_filter').addEventListener('change', (e) => {
            this.currentCategory = e.target.value;
            this.currentPage = 1;
            this.loadProducts();
        });

        // أزرار الفلترة
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.filter;
                this.currentPage = 1;
                this.loadProducts();
            });
        });

        // أزرار السلة
        document.getElementById('checkout_btn').addEventListener('click', () => {
            this.checkout();
        });

        document.getElementById('clear_cart_btn').addEventListener('click', () => {
            this.clearCart();
        });
    }

    async loadProducts() {
        const loading = document.getElementById('products_loading');
        const grid = document.getElementById('products_grid');

        loading.style.display = 'flex';

        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                search: this.currentSearch,
                category_id: this.currentCategory
            });

            const response = await fetch(`/cart/api/products?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderProducts(data.products);
                this.renderPagination(data.pagination);
                document.getElementById('products_count').textContent = data.pagination.total;
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            this.showError('خطأ في تحميل المنتجات');
        } finally {
            loading.style.display = 'none';
        }
    }

    renderProducts(products) {
        const grid = document.getElementById('products_grid');

        if (products.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-search display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">${'{{ "لا توجد منتجات" if language == "ar" else "No products found" }}'}</h5>
                </div>
            `;
            return;
        }

        grid.innerHTML = products.map(product => `
            <div class="product-card" onclick="cart.addToCart(${product.id})">
                <div class="badge-stock ${product.stock <= 0 ? 'out' : product.stock <= 10 ? 'low' : ''}">
                    ${product.stock <= 0 ? '{{ "نفد المخزون" if language == "ar" else "Out of Stock" }}' :
                      product.stock <= 10 ? '{{ "مخزون منخفض" if language == "ar" else "Low Stock" }}' :
                      '{{ "متوفر" if language == "ar" else "In Stock" }}'}
                </div>
                <img src="${product.image}" alt="${product.name}" class="product-image"
                     onerror="this.src='/static/images/no-image.png'">
                <div class="product-info">
                    <h6>${product.name}</h6>
                    <div class="product-price">${this.formatCurrency(product.price)}</div>
                    <div class="product-stock ${product.stock <= 10 ? 'low-stock' : ''}">
                        ${'{{ "المخزون:" if language == "ar" else "Stock:" }}'} ${product.stock} ${product.unit}
                    </div>
                    <button class="add-to-cart-btn" ${product.stock <= 0 ? 'disabled' : ''}>
                        <i class="bi bi-cart-plus me-2"></i>
                        ${'{{ "إضافة للسلة" if language == "ar" else "Add to Cart" }}'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    async addToCart(productId) {
        try {
            const response = await fetch(`/cart/api/product/${productId}`);
            const data = await response.json();

            if (data.success) {
                const product = data.product;

                // البحث عن المنتج في السلة
                const existingItem = this.cart.find(item => item.id === productId);

                if (existingItem) {
                    if (existingItem.quantity < product.stock) {
                        existingItem.quantity += 1;
                        this.showSuccess('تم زيادة الكمية');
                    } else {
                        this.showError('لا يوجد مخزون كافي');
                        return;
                    }
                } else {
                    if (product.stock > 0) {
                        this.cart.push({
                            id: product.id,
                            name: product.name,
                            price: product.price,
                            quantity: 1,
                            stock: product.stock,
                            image: product.image
                        });
                        this.showSuccess('تم إضافة المنتج للسلة');
                    } else {
                        this.showError('المنتج غير متوفر');
                        return;
                    }
                }

                this.saveCart();
                this.updateCartDisplay();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            this.showError('خطأ في إضافة المنتج');
        }
    }

    updateCartDisplay() {
        const container = document.getElementById('cart_items');
        const countElement = document.getElementById('cart_count');
        const checkoutBtn = document.getElementById('checkout_btn');

        countElement.textContent = this.cart.length;

        if (this.cart.length === 0) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="bi bi-cart-x"></i>
                    <h6>${'{{ "السلة فارغة" if language == "ar" else "Cart is empty" }}'}</h6>
                    <p>${'{{ "اختر منتجات لإضافتها" if language == "ar" else "Select products to add" }}'}</p>
                </div>
            `;
            checkoutBtn.disabled = true;
        } else {
            container.innerHTML = this.cart.map(item => `
                <div class="cart-item">
                    <div class="d-flex align-items-center">
                        <img src="${item.image}" alt="${item.name}"
                             style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;"
                             onerror="this.src='/static/images/no-image.png'">
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">${item.name}</h6>
                            <div class="text-muted small">${this.formatCurrency(item.price)}</div>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="cart.removeFromCart(${item.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="cart.updateQuantity(${item.id}, ${item.quantity - 1})">
                            <i class="bi bi-dash"></i>
                        </button>
                        <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="${item.stock}"
                               onchange="cart.updateQuantity(${item.id}, parseInt(this.value))">
                        <button class="quantity-btn" onclick="cart.updateQuantity(${item.id}, ${item.quantity + 1})">
                            <i class="bi bi-plus"></i>
                        </button>
                        <span class="ms-2 text-muted small">
                            ${this.formatCurrency(item.price * item.quantity)}
                        </span>
                    </div>
                </div>
            `).join('');
            checkoutBtn.disabled = false;
        }

        this.updateCartSummary();
    }

    updateCartSummary() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discount = 0;
        const tax = 0;
        const total = subtotal - discount + tax;

        document.getElementById('cart_subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('cart_discount').textContent = this.formatCurrency(discount);
        document.getElementById('cart_tax').textContent = this.formatCurrency(tax);
        document.getElementById('cart_total').textContent = this.formatCurrency(total);
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartDisplay();
        this.showSuccess('تم حذف المنتج من السلة');
    }

    updateQuantity(productId, quantity) {
        const item = this.cart.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeFromCart(productId);
            } else if (quantity <= item.stock) {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartDisplay();
            } else {
                this.showError('الكمية المطلوبة أكبر من المخزون المتاح');
            }
        }
    }

    clearCart() {
        if (this.cart.length === 0) return;

        if (confirm('{{ "هل أنت متأكد من مسح السلة؟" if language == "ar" else "Are you sure you want to clear the cart?" }}')) {
            this.cart = [];
            this.saveCart();
            this.updateCartDisplay();
            this.showSuccess('تم مسح السلة');
        }
    }

    checkout() {
        if (this.cart.length === 0) return;

        // تحويل إلى صفحة نقاط البيع مع بيانات السلة
        localStorage.setItem('pos_cart_data', JSON.stringify(this.cart));
        window.location.href = '/sales/pos';
    }

    saveCart() {
        localStorage.setItem('shopping_cart', JSON.stringify(this.cart));
    }

    formatCurrency(amount) {
        const formatted = parseFloat(amount || 0).toFixed(2);
        return '{{ language == "ar" }}' === 'true' ?
            `${formatted} ر.ق` : `QAR ${formatted}`;
    }

    showSuccess(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }

    showError(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    async searchByBarcode(barcode) {
        if (!barcode.trim()) return;

        try {
            const response = await fetch(`/cart/api/barcode/${encodeURIComponent(barcode)}`);
            const data = await response.json();

            if (data.success) {
                this.addToCart(data.product.id);
                this.showSuccess('تم العثور على المنتج وإضافته للسلة');
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            this.showError('خطأ في البحث بالباركود');
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('/cart/api/categories');
            const data = await response.json();

            if (data.success) {
                const select = document.getElementById('category_filter');

                data.categories.forEach(category => {
                    if (!select.querySelector(`option[value="${category.id}"]`)) {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('خطأ في تحميل الفئات:', error);
        }
    }

    renderPagination(pagination) {
        const container = document.getElementById('products_pagination');

        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '';

        if (pagination.has_prev) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="cart.goToPage(${pagination.page - 1})">
                    ${'{{ "السابق" if language == "ar" else "Previous" }}'}
                </a>
            </li>`;
        }

        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="cart.goToPage(${i})">${i}</a>
                </li>`;
            }
        }

        if (pagination.has_next) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="cart.goToPage(${pagination.page + 1})">
                    ${'{{ "التالي" if language == "ar" else "Next" }}'}
                </a>
            </li>`;
        }

        container.innerHTML = html;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadProducts();
    }
}

// تهيئة مدير السلة
const cart = new CartManager();
</script>
{% endblock %}
