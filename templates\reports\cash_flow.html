{% extends "base.html" %}

{% block title %}{{ 'تقرير التدفق النقدي' if language == 'ar' else 'Cash Flow Report' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-cash-stack me-2"></i>
                    {{ 'تقرير التدفق النقدي' if language == 'ar' else 'Cash Flow Report' }}
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i>
                        {{ 'طباعة' if language == 'ar' else 'Print' }}
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                        <i class="bi bi-file-earmark-excel me-1"></i>
                        {{ 'تصدير إكسل' if language == 'ar' else 'Export Excel' }}
                    </button>
                </div>
            </div>

            <!-- Period Filter -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-range me-2"></i>
                        {{ 'فترة التقرير' if language == 'ar' else 'Report Period' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">{{ 'نوع الفترة' if language == 'ar' else 'Period Type' }}</label>
                            <select name="period" class="form-select">
                                <option value="month" {% if period == 'month' %}selected{% endif %}>{{ 'شهري' if language == 'ar' else 'Monthly' }}</option>
                                <option value="quarter" {% if period == 'quarter' %}selected{% endif %}>{{ 'ربع سنوي' if language == 'ar' else 'Quarterly' }}</option>
                                <option value="year" {% if period == 'year' %}selected{% endif %}>{{ 'سنوي' if language == 'ar' else 'Yearly' }}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'السنة' if language == 'ar' else 'Year' }}</label>
                            <select name="year" class="form-select">
                                {% for y in range(2020, 2030) %}
                                <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{{ 'الشهر' if language == 'ar' else 'Month' }}</label>
                            <select name="month" class="form-select">
                                {% for m in range(1, 13) %}
                                <option value="{{ m }}" {% if m == month %}selected{% endif %}>
                                    {{ m }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    {{ 'عرض التقرير' if language == 'ar' else 'Generate Report' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report Period Info -->
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle me-2"></i>
                <strong>{{ 'فترة التقرير:' if language == 'ar' else 'Report Period:' }}</strong>
                {{ start_date.strftime('%Y-%m-%d') }} {{ 'إلى' if language == 'ar' else 'to' }} {{ end_date.strftime('%Y-%m-%d') }}
            </div>

            <!-- Cash Flow Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي التدفقات الداخلة' if language == 'ar' else 'Total Cash Inflows' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(total_inflows) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                    {% if cash_flow_growth != 0 %}
                                    <small class="{% if cash_flow_growth > 0 %}text-success{% else %}text-danger{% endif %}">
                                        <i class="bi bi-arrow-{% if cash_flow_growth > 0 %}up{% else %}down{% endif %}"></i>
                                        {{ "%.1f"|format(abs(cash_flow_growth)) }}%
                                    </small>
                                    {% endif %}
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-arrow-down-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'إجمالي التدفقات الخارجة' if language == 'ar' else 'Total Cash Outflows' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(total_outflows) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-arrow-up-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card {% if net_cash_flow >= 0 %}bg-primary{% else %}bg-warning{% endif %} text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'صافي التدفق النقدي' if language == 'ar' else 'Net Cash Flow' }}</h6>
                                    <h3 class="mb-0">{{ "%.0f"|format(net_cash_flow) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-{% if net_cash_flow >= 0 %}check-circle{% else %}exclamation-triangle{% endif %} fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">{{ 'نسبة التدفق' if language == 'ar' else 'Cash Flow Ratio' }}</h6>
                                    <h3 class="mb-0">{{ "%.1f"|format((net_cash_flow / total_inflows * 100) if total_inflows > 0 else 0) }}%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-percent fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Cash Inflows Breakdown -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-arrow-down-circle me-2"></i>
                                {{ 'التدفقات النقدية الداخلة' if language == 'ar' else 'Cash Inflows' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'طريقة الدفع' if language == 'ar' else 'Payment Method' }}</th>
                                            <th class="text-end">{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th class="text-end">{{ 'النسبة' if language == 'ar' else 'Percentage' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <i class="bi bi-cash me-2"></i>
                                                {{ 'نقدي' if language == 'ar' else 'Cash' }}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(cash_sales) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ "%.1f"|format(payment_methods.cash.percentage) }}%</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-credit-card me-2"></i>
                                                {{ 'بطاقة' if language == 'ar' else 'Card' }}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(card_sales) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ "%.1f"|format(payment_methods.card.percentage) }}%</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-bank me-2"></i>
                                                {{ 'تحويل بنكي' if language == 'ar' else 'Bank Transfer' }}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(bank_transfer_sales) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ "%.1f"|format(payment_methods.bank_transfer.percentage) }}%</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="bi bi-three-dots me-2"></i>
                                                {{ 'أخرى' if language == 'ar' else 'Other' }}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(other_sales) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ "%.1f"|format(payment_methods.other.percentage) }}%</td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="table-success">
                                        <tr>
                                            <th>{{ 'الإجمالي' if language == 'ar' else 'Total' }}</th>
                                            <th class="text-end">{{ "%.2f"|format(total_inflows) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</th>
                                            <th class="text-end">100.0%</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cash Outflows Breakdown -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-arrow-up-circle me-2"></i>
                                {{ 'التدفقات النقدية الخارجة' if language == 'ar' else 'Cash Outflows' }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ 'نوع المصروف' if language == 'ar' else 'Expense Type' }}</th>
                                            <th class="text-end">{{ 'المبلغ' if language == 'ar' else 'Amount' }}</th>
                                            <th class="text-end">{{ 'النسبة' if language == 'ar' else 'Percentage' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for expense_type, amount in estimated_outflows.items() %}
                                        <tr>
                                            <td>
                                                {% if expense_type == 'inventory_purchases' %}
                                                <i class="bi bi-box me-2"></i>{{ 'مشتريات المخزون' if language == 'ar' else 'Inventory Purchases' }}
                                                {% elif expense_type == 'rent' %}
                                                <i class="bi bi-house me-2"></i>{{ 'الإيجار' if language == 'ar' else 'Rent' }}
                                                {% elif expense_type == 'utilities' %}
                                                <i class="bi bi-lightning me-2"></i>{{ 'المرافق' if language == 'ar' else 'Utilities' }}
                                                {% elif expense_type == 'salaries' %}
                                                <i class="bi bi-people me-2"></i>{{ 'الرواتب' if language == 'ar' else 'Salaries' }}
                                                {% elif expense_type == 'marketing' %}
                                                <i class="bi bi-megaphone me-2"></i>{{ 'التسويق' if language == 'ar' else 'Marketing' }}
                                                {% elif expense_type == 'insurance' %}
                                                <i class="bi bi-shield me-2"></i>{{ 'التأمين' if language == 'ar' else 'Insurance' }}
                                                {% elif expense_type == 'maintenance' %}
                                                <i class="bi bi-tools me-2"></i>{{ 'الصيانة' if language == 'ar' else 'Maintenance' }}
                                                {% else %}
                                                <i class="bi bi-three-dots me-2"></i>{{ 'أخرى' if language == 'ar' else 'Other' }}
                                                {% endif %}
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(amount) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                            <td class="text-end">{{ "%.1f"|format((amount / total_outflows * 100) if total_outflows > 0 else 0) }}%</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-danger">
                                        <tr>
                                            <th>{{ 'الإجمالي' if language == 'ar' else 'Total' }}</th>
                                            <th class="text-end">{{ "%.2f"|format(total_outflows) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</th>
                                            <th class="text-end">100.0%</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cash Flow Summary -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calculator me-2"></i>
                        {{ 'ملخص التدفق النقدي' if language == 'ar' else 'Cash Flow Summary' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="cashFlowTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>{{ 'البند' if language == 'ar' else 'Item' }}</th>
                                    <th class="text-end">{{ 'المبلغ (ر.ق)' if language == 'ar' else 'Amount (QAR)' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-success">
                                    <td><strong>{{ 'إجمالي التدفقات الداخلة' if language == 'ar' else 'Total Cash Inflows' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(total_inflows) }}</strong></td>
                                </tr>
                                <tr class="table-danger">
                                    <td><strong>{{ 'إجمالي التدفقات الخارجة' if language == 'ar' else 'Total Cash Outflows' }}</strong></td>
                                    <td class="text-end"><strong>({{ "%.2f"|format(total_outflows) }})</strong></td>
                                </tr>
                                <tr class="table-{% if net_cash_flow >= 0 %}primary{% else %}warning{% endif %}">
                                    <td><strong>{{ 'صافي التدفق النقدي' if language == 'ar' else 'Net Cash Flow' }}</strong></td>
                                    <td class="text-end"><strong>{{ "%.2f"|format(net_cash_flow) }}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    {% if net_cash_flow < 0 %}
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>{{ 'تحذير:' if language == 'ar' else 'Warning:' }}</strong>
                        {{ 'التدفق النقدي سالب. يُنصح بمراجعة المصروفات وتحسين الإيرادات.' if language == 'ar' else 'Negative cash flow detected. Consider reviewing expenses and improving revenue.' }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    const table = document.getElementById('cashFlowTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Cash Flow"});
    XLSX.writeFile(wb, 'cash_flow_report.xlsx');
}

// Print styles
const printStyles = `
    @media print {
        .btn-group { display: none !important; }
        .card { border: 1px solid #000 !important; }
        .table { font-size: 12px; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
{% endblock %}
