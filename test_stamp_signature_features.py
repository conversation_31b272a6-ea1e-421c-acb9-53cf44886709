#!/usr/bin/env python3
"""
Test script for stamp and signature features in Qatar POS System
"""

from app import create_app
from models.setting import Setting
from extensions import db
import os

def test_stamp_signature_features():
    """Test all stamp and signature related features"""
    app = create_app()
    
    with app.app_context():
        print("🧪 اختبار ميزات الختم والتوقيع")
        print("=" * 50)
        
        # Test 1: Check if settings exist
        print("1️⃣ فحص وجود الإعدادات...")
        
        required_settings = [
            'company_stamp', 'stamp_width', 'stamp_height',
            'manager_signature', 'signature_width', 'signature_height',
            'manager_name', 'manager_title'
        ]
        
        missing_settings = []
        for setting_key in required_settings:
            setting = Setting.query.filter_by(key=setting_key).first()
            if not setting:
                missing_settings.append(setting_key)
            else:
                print(f"   ✅ {setting_key}: {setting.get_value()}")
        
        if missing_settings:
            print(f"   ❌ إعدادات مفقودة: {missing_settings}")
            return False
        
        # Test 2: Check file existence
        print("\n2️⃣ فحص وجود الملفات...")
        
        stamp_file = Setting.get_setting('company_stamp')
        signature_file = Setting.get_setting('manager_signature')
        
        if stamp_file:
            stamp_path = f"static/uploads/stamps/{stamp_file}"
            if os.path.exists(stamp_path):
                print(f"   ✅ ختم الشركة موجود: {stamp_path}")
            else:
                print(f"   ❌ ختم الشركة غير موجود: {stamp_path}")
        else:
            print("   ⚠️ لم يتم تحديد ختم للشركة")
        
        if signature_file:
            signature_path = f"static/uploads/signatures/{signature_file}"
            if os.path.exists(signature_path):
                print(f"   ✅ توقيع المسؤول موجود: {signature_path}")
            else:
                print(f"   ❌ توقيع المسؤول غير موجود: {signature_path}")
        else:
            print("   ⚠️ لم يتم تحديد توقيع للمسؤول")
        
        # Test 3: Check directories
        print("\n3️⃣ فحص المجلدات...")
        
        directories = [
            'static/uploads/stamps',
            'static/uploads/signatures'
        ]
        
        for directory in directories:
            if os.path.exists(directory):
                files_count = len([f for f in os.listdir(directory) if f.endswith(('.png', '.jpg', '.jpeg', '.gif'))])
                print(f"   ✅ {directory} موجود ({files_count} ملف)")
            else:
                print(f"   ❌ {directory} غير موجود")
        
        # Test 4: Test image processor functions
        print("\n4️⃣ اختبار دوال معالجة الصور...")
        
        try:
            from utils.image_processor import process_stamp_upload, process_signature_upload, delete_uploaded_image
            print("   ✅ تم استيراد دوال معالجة الصور بنجاح")
        except ImportError as e:
            print(f"   ❌ خطأ في استيراد دوال معالجة الصور: {e}")
            return False
        
        # Test 5: Check settings categories
        print("\n5️⃣ فحص فئات الإعدادات...")
        
        company_settings = Setting.get_category_settings('company')
        stamp_signature_settings = [s for s in company_settings if s.key in required_settings]
        
        print(f"   📊 إجمالي إعدادات الشركة: {len(company_settings)}")
        print(f"   🏷️ إعدادات الختم والتوقيع: {len(stamp_signature_settings)}")
        
        # Test 6: Display current configuration
        print("\n6️⃣ الإعدادات الحالية:")
        
        config = {
            'company_stamp': Setting.get_setting('company_stamp'),
            'stamp_dimensions': f"{Setting.get_setting('stamp_width')}x{Setting.get_setting('stamp_height')}",
            'manager_signature': Setting.get_setting('manager_signature'),
            'signature_dimensions': f"{Setting.get_setting('signature_width')}x{Setting.get_setting('signature_height')}",
            'manager_name': Setting.get_setting('manager_name'),
            'manager_title': Setting.get_setting('manager_title')
        }
        
        for key, value in config.items():
            print(f"   📋 {key}: {value or 'غير محدد'}")
        
        print("\n✅ جميع اختبارات الختم والتوقيع نجحت!")
        return True

def test_template_integration():
    """Test template integration"""
    print("\n🎨 اختبار تكامل القوالب...")
    
    # Check if company.html template exists and has the new sections
    template_path = "templates/settings/company.html"
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for stamp section
        if 'company_stamp' in content and 'ختم الشركة' in content:
            print("   ✅ قسم ختم الشركة موجود في القالب")
        else:
            print("   ❌ قسم ختم الشركة غير موجود في القالب")
        
        # Check for signature section
        if 'manager_signature' in content and 'توقيع المسؤول' in content:
            print("   ✅ قسم توقيع المسؤول موجود في القالب")
        else:
            print("   ❌ قسم توقيع المسؤول غير موجود في القالب")
        
        # Check for JavaScript functions
        js_functions = ['previewStamp', 'previewSignature', 'removeStamp', 'removeSignature']
        missing_functions = []
        
        for func in js_functions:
            if func in content:
                print(f"   ✅ دالة JavaScript {func} موجودة")
            else:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"   ❌ دوال JavaScript مفقودة: {missing_functions}")
        
    else:
        print(f"   ❌ ملف القالب غير موجود: {template_path}")

if __name__ == '__main__':
    print("🔬 اختبار شامل لميزات الختم والتوقيع")
    print("=" * 60)
    
    try:
        success = test_stamp_signature_features()
        test_template_integration()
        
        if success:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ ميزات الختم والتوقيع جاهزة للاستخدام")
            print("\n📝 للاستخدام:")
            print("   1. اذهب إلى إعدادات الشركة")
            print("   2. ارفع ختم الشركة في قسم 'ختم الشركة'")
            print("   3. ارفع توقيع المسؤول في قسم 'توقيع المسؤول'")
            print("   4. أدخل اسم ومنصب المسؤول")
            print("   5. احفظ الإعدادات")
        else:
            print("\n❌ بعض الاختبارات فشلت!")
            
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار: {e}")
