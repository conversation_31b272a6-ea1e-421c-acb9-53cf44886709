# دليل تحويل نظام نقاط البيع القطري إلى تطبيق سطح مكتب
# Qatar POS System Desktop Application Guide

## 📋 المحتويات - Contents

1. [متطلبات النظام](#متطلبات-النظام)
2. [خطوات التحويل](#خطوات-التحويل)
3. [بناء التطبيق](#بناء-التطبيق)
4. [التثبيت والتشغيل](#التثبيت-والتشغيل)
5. [استكشاف الأخطاء](#استكشاف-الأخطاء)
6. [التخصيص](#التخصيص)

---

## 🖥️ متطلبات النظام - System Requirements

### الحد الأدنى:
- **نظام التشغيل:** Windows 10 (64-bit)
- **المعالج:** Intel Core i3 أو AMD equivalent
- **الذاكرة:** 4 GB RAM
- **التخزين:** 1 GB مساحة فارغة
- **Python:** 3.8 أو أحدث (للتطوير فقط)

### الموصى به:
- **نظام التشغيل:** Windows 11 (64-bit)
- **المعالج:** Intel Core i5 أو AMD equivalent
- **الذاكرة:** 8 GB RAM
- **التخزين:** 2 GB مساحة فارغة
- **الشبكة:** اتصال إنترنت للتحديثات

---

## 🔧 خطوات التحويل - Conversion Steps

### 1. تحضير البيئة

```bash
# تثبيت المتطلبات
pip install -r requirements_desktop.txt

# التحقق من الملفات المطلوبة
python -c "import os; print('✅ جميع الملفات موجودة' if all(os.path.exists(f) for f in ['app.py', 'desktop_app.py', 'config.py']) else '❌ ملفات مفقودة')"
```

### 2. تشغيل التطبيق في وضع التطوير

```bash
# تشغيل التطبيق المكتبي
python desktop_app.py
```

### 3. اختبار الوظائف

- ✅ بدء وإيقاف الخادم
- ✅ فتح المتصفح تلقائياً
- ✅ عرض السجلات
- ✅ حفظ الإعدادات

---

## 🏗️ بناء التطبيق - Building the Application

### الطريقة الأولى: استخدام السكريپت التلقائي

```bash
# تشغيل سكريپت البناء
python build_exe.py
```

أو استخدام ملف batch:

```cmd
# في Windows
build.bat
```

### الطريقة الثانية: البناء اليدوي

```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف المواصفات
pyi-makespec --onefile --windowed --icon=static/images/logo.ico desktop_app.py

# تعديل ملف المواصفات (إضافة الملفات والمجلدات)
# راجع ملف qatar_pos.spec المُنشأ

# بناء التطبيق
pyinstaller qatar_pos.spec
```

### 3. النتائج المتوقعة

بعد البناء الناجح ستجد:

```
dist/
├── QatarPOS.exe          # الملف التنفيذي الرئيسي
├── templates/            # قوالب HTML
├── static/              # الملفات الثابتة
└── ...                  # ملفات أخرى مطلوبة
```

---

## 💾 التثبيت والتشغيل - Installation & Running

### التثبيت التلقائي

1. **تشغيل المثبت:**
   ```cmd
   install.bat
   ```

2. **اتباع التعليمات:**
   - سيتم نسخ الملفات إلى `C:\QatarPOS\`
   - إنشاء اختصارات على سطح المكتب
   - إضافة التطبيق لقائمة البداية

### التشغيل اليدوي

1. **نسخ الملفات:**
   - انسخ مجلد `dist` إلى الموقع المطلوب
   - أعد تسمية المجلد إلى `QatarPOS`

2. **تشغيل التطبيق:**
   - انقر مرتين على `QatarPOS.exe`
   - أو استخدم سطر الأوامر: `QatarPOS.exe`

### أول تشغيل

1. **بدء التطبيق:**
   - سيظهر نافذة التحكم
   - سيبدأ الخادم تلقائياً

2. **الوصول للنظام:**
   - انقر "فتح المتصفح"
   - أو اذهب إلى: `http://127.0.0.1:2626`

3. **تسجيل الدخول:**
   - المستخدم الافتراضي: `admin`
   - كلمة المرور الافتراضية: `admin123`

---

## 🔍 استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة وحلولها

#### 1. فشل في بدء الخادم

**المشكلة:** `خطأ في تشغيل الخادم: [Errno 10048]`

**الحل:**
```python
# تغيير المنفذ في desktop_config.py
DEFAULT_PORT = 2627  # بدلاً من 2626
```

#### 2. قاعدة البيانات لا تعمل

**المشكلة:** `خطأ في قاعدة البيانات`

**الحل:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
del qatar_pos.db
# إعادة تشغيل التطبيق
```

#### 3. الملفات الثابتة مفقودة

**المشكلة:** `404 Not Found` للصور والـ CSS

**الحل:**
- تأكد من وجود مجلد `static` في نفس مجلد التطبيق
- تحقق من ملف المواصفات `qatar_pos.spec`

#### 4. مشكلة في الخطوط العربية

**المشكلة:** النصوص العربية لا تظهر بشكل صحيح

**الحل:**
```python
# في desktop_app.py
self.root.option_add('*Font', 'Tahoma 10')
```

### سجل الأخطاء

يمكنك العثور على سجل مفصل للأخطاء في:
- **واجهة التطبيق:** تبويب "سجل النظام"
- **ملف السجل:** `logs/qatar_pos.log`

---

## 🎨 التخصيص - Customization

### تخصيص الواجهة

#### 1. تغيير الأيقونة

```python
# في desktop_app.py
try:
    self.root.iconbitmap("path/to/your/icon.ico")
except:
    pass
```

#### 2. تخصيص الألوان

```python
# في desktop_config.py
DESKTOP_SETTINGS = {
    'theme': 'dark',  # أو 'light'
    'primary_color': '#007bff',
    'secondary_color': '#6c757d',
}
```

#### 3. تغيير حجم النافذة

```python
# في desktop_app.py
self.root.geometry("1000x700")  # عرض x ارتفاع
```

### تخصيص الوظائف

#### 1. إضافة أزرار جديدة

```python
# في create_widgets()
custom_btn = ttk.Button(control_frame, text="وظيفة مخصصة", 
                       command=self.custom_function)
custom_btn.pack(side=tk.LEFT, padx=(0, 5))
```

#### 2. تخصيص رسائل السجل

```python
# في desktop_config.py
MESSAGES['ar']['custom_message'] = 'رسالة مخصصة'
```

### إعدادات متقدمة

#### 1. تغيير مسار قاعدة البيانات

```python
# في desktop_config.py
DATABASE_PATH = Path("D:/QatarPOS/data/qatar_pos.db")
```

#### 2. تفعيل وضع التطوير

```python
# في FlaskDesktopConfig
DEBUG = True
```

#### 3. تخصيص النسخ الاحتياطي

```python
# في desktop_config.py
AUTO_BACKUP = True
BACKUP_INTERVAL_HOURS = 12  # كل 12 ساعة
```

---

## 📦 التوزيع - Distribution

### إنشاء حزمة التوزيع

1. **ضغط الملفات:**
   ```bash
   # إنشاء ملف مضغوط يحتوي على:
   # - QatarPOS.exe
   # - install.bat
   # - uninstall.bat
   # - README.txt
   ```

2. **إنشاء مثبت متقدم:**
   - استخدم Inno Setup أو NSIS
   - أضف معالج التثبيت
   - تضمين متطلبات النظام

### نشر التحديثات

1. **نظام التحديث التلقائي:**
   ```python
   # في desktop_config.py
   CHECK_UPDATES = True
   UPDATE_URL = "https://your-server.com/updates"
   ```

2. **إشعارات التحديث:**
   - فحص التحديثات عند البدء
   - تنزيل وتثبيت تلقائي
   - إعادة تشغيل التطبيق

---

## 🔒 الأمان - Security

### حماية التطبيق

1. **تشفير قاعدة البيانات:**
   ```python
   # استخدام SQLCipher
   SQLALCHEMY_DATABASE_URI = "sqlite+pysqlcipher://:password@/path/to/database.db"
   ```

2. **حماية الملفات:**
   - تشفير الملفات الحساسة
   - استخدام كلمات مرور قوية
   - تفعيل المصادقة الثنائية

### النسخ الاحتياطي الآمن

```python
# نسخ احتياطي مشفر
import zipfile
import cryptography

def create_encrypted_backup():
    # إنشاء نسخة احتياطية مشفرة
    pass
```

---

## 📞 الدعم الفني - Support

### الحصول على المساعدة

- **الوثائق:** راجع هذا الدليل
- **السجلات:** فحص ملفات السجل
- **المجتمع:** منتديات المطورين
- **الدعم المباشر:** <EMAIL>

### الإبلاغ عن الأخطاء

عند الإبلاغ عن خطأ، يرجى تضمين:
1. وصف مفصل للمشكلة
2. خطوات إعادة إنتاج الخطأ
3. رسائل الخطأ من السجل
4. معلومات النظام (OS, RAM, etc.)
5. إصدار التطبيق

---

## 📈 التطوير المستقبلي - Future Development

### ميزات مخططة

- [ ] دعم أنظمة تشغيل أخرى (macOS, Linux)
- [ ] واجهة مستخدم محسنة
- [ ] تكامل مع أنظمة خارجية
- [ ] تقارير متقدمة
- [ ] دعم متعدد المتاجر
- [ ] تطبيق جوال مصاحب

### المساهمة في التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-repo/qatar-pos.git

# إنشاء فرع جديد
git checkout -b feature/new-feature

# تطوير الميزة
# ...

# إرسال طلب دمج
git push origin feature/new-feature
```

---

## 📄 الترخيص - License

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

---

## 🙏 شكر وتقدير - Acknowledgments

- فريق تطوير Flask
- مجتمع Python
- مطوري PyInstaller
- المساهمين في المشروع

---

**© 2024 Qatar POS System. جميع الحقوق محفوظة.**
