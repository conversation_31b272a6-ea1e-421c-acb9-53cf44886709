#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص قاعدة البيانات
Check Database Structure
"""

import sqlite3
from app import create_app
from extensions import db

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    print("🔍 فحص هيكل قاعدة البيانات...")
    
    app = create_app()
    
    with app.app_context():
        # Connect to database
        conn = sqlite3.connect('instance/database.db')
        cursor = conn.cursor()
        
        # Check users table structure
        print("\n👤 هيكل جدول المستخدمين:")
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"  📋 {column[1]} ({column[2]}) - {'NOT NULL' if column[3] else 'NULL'}")
        
        # Check if role_id column exists
        role_id_exists = any(col[1] == 'role_id' for col in columns)
        print(f"\n🔍 عمود role_id موجود: {'نعم' if role_id_exists else 'لا'}")
        
        # Check roles table
        print("\n👥 هيكل جدول الأدوار:")
        try:
            cursor.execute("PRAGMA table_info(roles)")
            roles_columns = cursor.fetchall()
            for column in roles_columns:
                print(f"  📋 {column[1]} ({column[2]}) - {'NOT NULL' if column[3] else 'NULL'}")
        except sqlite3.OperationalError as e:
            print(f"  ❌ خطأ: {e}")
        
        # Check permissions table
        print("\n🔐 هيكل جدول الصلاحيات:")
        try:
            cursor.execute("PRAGMA table_info(permissions)")
            permissions_columns = cursor.fetchall()
            for column in permissions_columns:
                print(f"  📋 {column[1]} ({column[2]}) - {'NOT NULL' if column[3] else 'NULL'}")
        except sqlite3.OperationalError as e:
            print(f"  ❌ خطأ: {e}")
        
        conn.close()
        
        return role_id_exists

def fix_users_table():
    """إصلاح جدول المستخدمين"""
    print("\n🔧 إصلاح جدول المستخدمين...")
    
    conn = sqlite3.connect('instance/database.db')
    cursor = conn.cursor()
    
    try:
        # Add role_id column if it doesn't exist
        cursor.execute("ALTER TABLE users ADD COLUMN role_id INTEGER")
        print("✅ تم إضافة عمود role_id")
        
        # Add foreign key constraint (SQLite doesn't support adding FK constraints to existing tables)
        # We'll handle this in the application logic
        
        conn.commit()
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("⚠️ عمود role_id موجود بالفعل")
        else:
            print(f"❌ خطأ: {e}")
    
    finally:
        conn.close()

if __name__ == '__main__':
    role_id_exists = check_database_structure()
    
    if not role_id_exists:
        fix_users_table()
        print("\n🔄 فحص الهيكل مرة أخرى...")
        check_database_structure()
    
    print("\n✅ انتهى فحص قاعدة البيانات")
