# 🛠️ استكشاف الأخطاء وإصلاحها | Troubleshooting

## 🇶🇦 نظام نقاط البيع القطري - حل المشاكل الشائعة

---

## 🚨 المشاكل الأكثر شيوعاً

### ❌ المشكلة: الرابط http://localhost:5000 لا يعمل

#### الحلول:

1. **تأكد من تشغيل الخادم**:
   ```bash
   python start_here.py
   ```

2. **جرب روابط بديلة**:
   - http://127.0.0.1:5000
   - http://0.0.0.0:5000

3. **تحقق من رسائل الخطأ**:
   - ابحث عن "Running on" في Terminal
   - تأكد من عدم وجود أخطاء حمراء

4. **تحقق من المنفذ**:
   ```bash
   # Windows
   netstat -an | findstr :5000
   
   # Linux/Mac
   lsof -i :5000
   ```

---

### ❌ المشكلة: "python is not recognized"

#### الحلول:

1. **تثبيت Python**:
   - تحميل من: https://python.org/downloads/
   - ✅ تأكد من تحديد "Add Python to PATH"

2. **إعادة تشغيل Command Prompt**

3. **اختبار التثبيت**:
   ```bash
   python --version
   ```

---

### ❌ المشكلة: "No module named 'flask'"

#### الحلول:

1. **تثبيت Flask**:
   ```bash
   pip install flask
   ```

2. **تحديث pip**:
   ```bash
   python -m pip install --upgrade pip
   pip install flask
   ```

3. **استخدام الإصلاح التلقائي**:
   ```bash
   python fix_system.py
   ```

---

### ❌ المشكلة: "Port 5000 is already in use"

#### الحلول:

1. **إغلاق البرامج الأخرى**:
   - أغلق متصفحات أخرى
   - أغلق برامج أخرى تستخدم المنفذ 5000

2. **قتل العملية**:
   ```bash
   # Windows
   netstat -ano | findstr :5000
   taskkill /PID [PID_NUMBER] /F
   
   # Linux/Mac
   lsof -ti:5000 | xargs kill -9
   ```

3. **استخدام منفذ آخر**:
   - عدل الملف وغير 5000 إلى 8000

---

### ❌ المشكلة: صفحة فارغة أو خطأ 404

#### الحلول:

1. **تأكد من الرابط الصحيح**:
   - http://localhost:5000 (وليس https)

2. **جرب صفحات أخرى**:
   - http://localhost:5000/test
   - http://localhost:5000/health

3. **أعد تشغيل الخادم**:
   ```bash
   # أوقف الخادم (Ctrl+C)
   # ثم شغله مرة أخرى
   python start_here.py
   ```

---

## 🔧 أدوات التشخيص

### 1. فحص النظام الشامل:
```bash
python check_system.py
```

### 2. إصلاح تلقائي:
```bash
python fix_system.py
```

### 3. اختبار جميع الملفات:
```bash
python run_all_tests.py
```

### 4. البدء التلقائي:
```bash
python start_here.py
```

---

## 🪟 مشاكل خاصة بـ Windows

### مشكلة: "Access is denied"
**الحل**: شغل Command Prompt كمدير (Run as Administrator)

### مشكلة: "pip is not recognized"
**الحل**: 
```bash
python -m pip install flask
```

### مشكلة: مشاكل الترميز
**الحل**: 
```bash
chcp 65001
python start_here.py
```

---

## 🐧 مشاكل خاصة بـ Linux/Mac

### مشكلة: "Permission denied"
**الحل**:
```bash
chmod +x start.sh
./start.sh
```

### مشكلة: Python 2 vs Python 3
**الحل**:
```bash
python3 start_here.py
```

### مشكلة: pip permissions
**الحل**:
```bash
pip3 install --user flask
```

---

## 📱 مشاكل المتصفح

### المشكلة: الصفحة لا تحمل
**الحلول**:
1. أعد تحميل الصفحة (F5)
2. امسح الكاش (Ctrl+F5)
3. جرب متصفح آخر
4. تأكد من عدم وجود VPN أو Proxy

### المشكلة: الخطوط العربية لا تظهر
**الحل**: تأكد من دعم المتصفح للخطوط العربية

---

## 🆘 إذا لم تنجح أي من الحلول

### خطوات الطوارئ:

1. **تحميل Python جديد**:
   - احذف Python القديم
   - حمل أحدث إصدار من python.org
   - تأكد من تحديد "Add to PATH"

2. **تثبيت يدوي**:
   ```bash
   python -m pip install --upgrade pip
   python -m pip install flask jinja2 werkzeug
   ```

3. **استخدام الملف الأبسط**:
   ```bash
   python test_server.py
   ```

4. **إنشاء ملف جديد**:
   ```python
   # احفظ هذا في ملف test.py
   from flask import Flask
   app = Flask(__name__)
   
   @app.route('/')
   def home():
       return "Qatar POS System Works!"
   
   if __name__ == '__main__':
       app.run(debug=True)
   ```

---

## 📞 طلب المساعدة

إذا استمرت المشاكل:

1. **اجمع المعلومات**:
   - نظام التشغيل
   - إصدار Python
   - رسالة الخطأ كاملة
   - الخطوات التي جربتها

2. **شارك المعلومات**:
   - لقطة شاشة للخطأ
   - نسخ ولصق رسالة الخطأ
   - نتيجة `python --version`

3. **جرب الحلول البديلة**:
   - استخدم Docker
   - جرب على جهاز آخر
   - استخدم خدمة سحابية

---

## ✅ علامات النجاح

عندما يعمل النظام بنجاح ستشاهد:

### في Terminal/Command Prompt:
```
🚀 Starting Qatar POS System...
* Running on http://127.0.0.1:5000
* Debug mode: on
```

### في المتصفح:
- شعار قطر 🇶🇦
- "نظام نقاط البيع القطري"
- "النظام يعمل بنجاح!"

---

**مبروك! النظام يعمل الآن! 🎉**
