/**
 * Qatar POS System - Sound Manager
 * Manages all sound effects and notifications
 */

class SoundManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.enabled = true;
        this.volume = 0.5;
        this.init();
    }

    async init() {
        try {
            // Initialize Web Audio Context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Load settings from localStorage
            this.loadSettings();
            
            // Generate sound effects
            this.generateSounds();
            
            console.log('Sound Manager initialized successfully');
        } catch (error) {
            console.warn('Sound Manager initialization failed:', error);
        }
    }

    loadSettings() {
        const soundsEnabled = localStorage.getItem('sounds_enabled');
        const soundVolume = localStorage.getItem('sound_volume');
        
        this.enabled = soundsEnabled !== null ? soundsEnabled === 'true' : true;
        this.volume = soundVolume !== null ? parseFloat(soundVolume) : 0.5;
    }

    saveSettings() {
        localStorage.setItem('sounds_enabled', this.enabled.toString());
        localStorage.setItem('sound_volume', this.volume.toString());
    }

    setEnabled(enabled) {
        this.enabled = enabled;
        this.saveSettings();
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.saveSettings();
    }

    generateSounds() {
        // Success sound (pleasant chime)
        this.sounds.success = this.createTone([523.25, 659.25, 783.99], 0.3, 'sine');
        
        // Error sound (warning beep)
        this.sounds.error = this.createTone([220, 220, 220], 0.2, 'square');
        
        // Sale complete sound (cash register)
        this.sounds.sale = this.createTone([440, 554.37, 659.25, 880], 0.4, 'sine');
        
        // Button click sound
        this.sounds.click = this.createTone([800], 0.1, 'sine');
        
        // Notification sound
        this.sounds.notification = this.createTone([523.25, 659.25], 0.3, 'sine');
        
        // Warning sound
        this.sounds.warning = this.createTone([349.23, 293.66], 0.4, 'triangle');
        
        // Payment received sound
        this.sounds.payment = this.createTone([659.25, 783.99, 987.77], 0.5, 'sine');
        
        // Low stock alert
        this.sounds.lowStock = this.createTone([293.66, 246.94, 220], 0.6, 'sawtooth');
    }

    createTone(frequencies, duration, waveType = 'sine') {
        if (!this.audioContext) return null;

        return () => {
            if (!this.enabled || this.volume === 0) return;

            const gainNode = this.audioContext.createGain();
            gainNode.connect(this.audioContext.destination);
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            frequencies.forEach((freq, index) => {
                const oscillator = this.audioContext.createOscillator();
                oscillator.type = waveType;
                oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime + (index * duration / frequencies.length));
                oscillator.connect(gainNode);
                oscillator.start(this.audioContext.currentTime + (index * duration / frequencies.length));
                oscillator.stop(this.audioContext.currentTime + duration);
            });
        };
    }

    play(soundName) {
        if (!this.enabled || !this.sounds[soundName]) return;
        
        try {
            // Resume audio context if suspended (required by some browsers)
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            this.sounds[soundName]();
        } catch (error) {
            console.warn('Error playing sound:', error);
        }
    }

    // Convenience methods for common sounds
    playSuccess() { this.play('success'); }
    playError() { this.play('error'); }
    playSale() { this.play('sale'); }
    playClick() { this.play('click'); }
    playNotification() { this.play('notification'); }
    playWarning() { this.play('warning'); }
    playPayment() { this.play('payment'); }
    playLowStock() { this.play('lowStock'); }

    // Test all sounds
    testSounds() {
        const soundNames = Object.keys(this.sounds);
        let index = 0;
        
        const playNext = () => {
            if (index < soundNames.length) {
                console.log(`Playing: ${soundNames[index]}`);
                this.play(soundNames[index]);
                index++;
                setTimeout(playNext, 800);
            }
        };
        
        playNext();
    }
}

// Initialize global sound manager
window.soundManager = new SoundManager();

// Auto-enable audio context on first user interaction
document.addEventListener('click', function enableAudio() {
    if (window.soundManager.audioContext && window.soundManager.audioContext.state === 'suspended') {
        window.soundManager.audioContext.resume();
    }
    document.removeEventListener('click', enableAudio);
}, { once: true });

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SoundManager;
}
