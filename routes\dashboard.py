"""
Dashboard routes for Qatar POS System
Main dashboard with summary statistics and quick access
"""

from flask import Blueprint, render_template, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, and_
from models.user import User
from models.product import Product
from models.sale import Sale, SaleItem
from models.customer import Customer
from models.supplier import Supplier
from extensions import db
from utils.helpers import get_user_language, format_currency

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    """Main dashboard"""
    language = get_user_language()
    
    # Get dashboard statistics
    stats = get_dashboard_stats()
    
    # Get recent sales
    recent_sales = Sale.query.order_by(Sale.created_at.desc()).limit(5).all()
    
    # Get low stock products
    low_stock_products = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).limit(10).all()
    
    # Get top selling products (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_sold')
    ).join(SaleItem).join(Sale).filter(
        Sale.created_at >= thirty_days_ago,
        Sale.status == 'completed'
    ).group_by(Product.id).order_by(
        func.sum(SaleItem.quantity).desc()
    ).limit(5).all()
    
    return render_template('dashboard/index.html',
                         stats=stats,
                         recent_sales=recent_sales,
                         low_stock_products=low_stock_products,
                         top_products=top_products,
                         language=language)

@dashboard_bp.route('/api/stats')
@login_required
def api_stats():
    """API endpoint for dashboard statistics"""
    stats = get_dashboard_stats()
    return jsonify(stats)

@dashboard_bp.route('/api/sales-chart')
@login_required
def api_sales_chart():
    """API endpoint for sales chart data"""
    from flask import request
    days = request.args.get('days', 7, type=int)
    
    # Get sales data for the last N days
    start_date = datetime.utcnow() - timedelta(days=days)
    
    sales_data = db.session.query(
        func.date(Sale.sale_date).label('date'),
        func.sum(Sale.total_amount).label('total'),
        func.count(Sale.id).label('count')
    ).filter(
        Sale.sale_date >= start_date,
        Sale.status == 'completed'
    ).group_by(func.date(Sale.sale_date)).all()
    
    # Format data for chart
    chart_data = {
        'labels': [],
        'sales': [],
        'transactions': []
    }
    
    for data in sales_data:
        chart_data['labels'].append(data.date.strftime('%Y-%m-%d'))
        chart_data['sales'].append(float(data.total))
        chart_data['transactions'].append(data.count)
    
    return jsonify(chart_data)

def get_dashboard_stats():
    """Get dashboard statistics"""
    today = datetime.utcnow().date()
    yesterday = today - timedelta(days=1)
    this_month_start = today.replace(day=1)
    last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)
    
    # Today's sales
    today_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).scalar() or 0
    
    # Yesterday's sales
    yesterday_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        func.date(Sale.sale_date) == yesterday,
        Sale.status == 'completed'
    ).scalar() or 0
    
    # This month's sales
    this_month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        Sale.sale_date >= this_month_start,
        Sale.status == 'completed'
    ).scalar() or 0
    
    # Last month's sales
    last_month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        and_(Sale.sale_date >= last_month_start,
             Sale.sale_date < this_month_start),
        Sale.status == 'completed'
    ).scalar() or 0
    
    # Today's transactions
    today_transactions = Sale.query.filter(
        func.date(Sale.sale_date) == today,
        Sale.status == 'completed'
    ).count()
    
    # Total products
    total_products = Product.query.filter_by(is_active=True).count()
    
    # Low stock products
    low_stock_count = Product.query.filter(
        Product.current_stock <= Product.minimum_stock,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    
    # Out of stock products
    out_of_stock_count = Product.query.filter(
        Product.current_stock <= 0,
        Product.is_active == True,
        Product.track_inventory == True
    ).count()
    
    # Total customers
    total_customers = Customer.query.filter_by(is_active=True).count()
    
    # Total suppliers
    total_suppliers = Supplier.query.filter_by(is_active=True).count()
    
    # Active users
    active_users = User.query.filter_by(is_active=True).count()
    
    # Calculate growth percentages
    sales_growth = 0
    if yesterday_sales > 0:
        sales_growth = ((today_sales - yesterday_sales) / yesterday_sales) * 100
    
    monthly_growth = 0
    if last_month_sales > 0:
        monthly_growth = ((this_month_sales - last_month_sales) / last_month_sales) * 100
    
    return {
        'today_sales': float(today_sales),
        'yesterday_sales': float(yesterday_sales),
        'this_month_sales': float(this_month_sales),
        'last_month_sales': float(last_month_sales),
        'today_transactions': today_transactions,
        'sales_growth': round(sales_growth, 1),
        'monthly_growth': round(monthly_growth, 1),
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'total_customers': total_customers,
        'total_suppliers': total_suppliers,
        'active_users': active_users
    }
