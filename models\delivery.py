"""
Delivery management models for Qatar POS System
Handles delivery orders, COD payments, and delivery tracking
"""

from datetime import datetime
from decimal import Decimal
from extensions import db

class DeliveryOrder(db.Model):
    """Delivery orders for COD and regular deliveries"""
    
    __tablename__ = 'delivery_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    
    # Delivery information
    delivery_number = db.Column(db.String(50), unique=True, nullable=False)
    delivery_type = db.Column(db.Enum('standard', 'express', 'same_day', 'scheduled',
                                     name='delivery_types'), 
                             nullable=False, default='standard')
    
    # Customer delivery details
    customer_name = db.Column(db.String(200), nullable=False)
    customer_phone = db.Column(db.String(20), nullable=False)
    customer_email = db.Column(db.String(120))
    
    # Delivery address
    delivery_address = db.Column(db.Text, nullable=False)
    delivery_area = db.Column(db.String(100))
    delivery_city = db.Column(db.String(100), default='الدوحة')
    delivery_zone = db.Column(db.String(50))  # Qatar delivery zones
    building_number = db.Column(db.String(20))
    floor_number = db.Column(db.String(10))
    apartment_number = db.Column(db.String(10))
    landmark = db.Column(db.String(200))
    
    # Delivery scheduling
    delivery_date = db.Column(db.Date)
    delivery_time_slot = db.Column(db.String(50))  # e.g., "09:00-12:00"
    estimated_delivery = db.Column(db.DateTime)
    actual_delivery = db.Column(db.DateTime)
    
    # Delivery status
    status = db.Column(db.Enum('pending', 'confirmed', 'picked_up', 'in_transit', 
                              'out_for_delivery', 'delivered', 'failed', 'cancelled',
                              name='delivery_statuses'),
                      nullable=False, default='pending')
    
    # COD information
    is_cod = db.Column(db.Boolean, default=False, nullable=False)
    cod_amount = db.Column(db.Numeric(10, 2), default=0)
    cod_collected = db.Column(db.Boolean, default=False)
    cod_collection_date = db.Column(db.DateTime)
    
    # Delivery charges
    delivery_fee = db.Column(db.Numeric(10, 2), default=0)
    cod_fee = db.Column(db.Numeric(10, 2), default=0)  # COD handling fee
    total_delivery_charges = db.Column(db.Numeric(10, 2), default=0)
    
    # Delivery company information
    delivery_company = db.Column(db.String(100))
    delivery_driver = db.Column(db.String(100))
    driver_phone = db.Column(db.String(20))
    tracking_number = db.Column(db.String(100))
    
    # Notes and special instructions
    delivery_notes = db.Column(db.Text)
    special_instructions = db.Column(db.Text)
    customer_notes = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    sale = db.relationship('Sale', backref='delivery_order', uselist=False)
    delivery_attempts = db.relationship('DeliveryAttempt', backref='delivery_order', 
                                       lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(DeliveryOrder, self).__init__(**kwargs)
        if not self.delivery_number:
            self.delivery_number = self.generate_delivery_number()
        self.calculate_delivery_charges()
    
    @staticmethod
    def generate_delivery_number():
        """Generate unique delivery number"""
        today = datetime.now()
        prefix = f"DEL{today.strftime('%Y%m%d')}"
        
        # Get last delivery number for today
        last_delivery = DeliveryOrder.query.filter(
            DeliveryOrder.delivery_number.like(f"{prefix}%")
        ).order_by(DeliveryOrder.delivery_number.desc()).first()
        
        if last_delivery:
            last_number = int(last_delivery.delivery_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:04d}"
    
    def calculate_delivery_charges(self):
        """Calculate delivery charges based on zone and type"""
        # Qatar delivery zones and fees
        zone_fees = {
            'doha_center': 10.00,      # وسط الدوحة
            'doha_suburbs': 15.00,     # ضواحي الدوحة
            'al_rayyan': 20.00,        # الريان
            'al_wakrah': 25.00,        # الوكرة
            'al_khor': 35.00,          # الخور
            'mesaieed': 40.00,         # مسيعيد
            'dukhan': 50.00,           # دخان
            'other': 30.00             # مناطق أخرى
        }
        
        # Base delivery fee
        base_fee = zone_fees.get(self.delivery_zone, zone_fees['other'])
        
        # Express delivery surcharge
        if self.delivery_type == 'express':
            base_fee = base_fee * 1.5
        elif self.delivery_type == 'same_day':
            base_fee = base_fee * 2.0

        self.delivery_fee = Decimal(str(base_fee))
        
        # COD handling fee (2% of order value, minimum 5 QAR)
        if self.is_cod and self.cod_amount:
            cod_fee = max(self.cod_amount * Decimal('0.02'), Decimal('5.00'))
            self.cod_fee = cod_fee
        else:
            self.cod_fee = Decimal('0.00')
        
        self.total_delivery_charges = self.delivery_fee + self.cod_fee
    
    def update_status(self, new_status, notes=None):
        """Update delivery status with timestamp"""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()
        
        # Record status change
        attempt = DeliveryAttempt(
            delivery_order_id=self.id,
            attempt_date=datetime.utcnow(),
            status=new_status,
            notes=notes or f"Status changed from {old_status} to {new_status}"
        )
        db.session.add(attempt)
        
        # Update specific timestamps
        if new_status == 'delivered':
            self.actual_delivery = datetime.utcnow()
            if self.is_cod:
                self.cod_collected = True
                self.cod_collection_date = datetime.utcnow()
    
    def get_delivery_address_formatted(self):
        """Get formatted delivery address"""
        address_parts = [self.delivery_address]
        
        if self.building_number:
            address_parts.append(f"مبنى رقم {self.building_number}")
        if self.floor_number:
            address_parts.append(f"الطابق {self.floor_number}")
        if self.apartment_number:
            address_parts.append(f"شقة {self.apartment_number}")
        if self.landmark:
            address_parts.append(f"بالقرب من {self.landmark}")
        
        address_parts.append(f"{self.delivery_city}, قطر")
        
        return "\n".join(address_parts)
    
    def to_dict(self, language='ar'):
        """Convert delivery order to dictionary"""
        return {
            'id': self.id,
            'delivery_number': self.delivery_number,
            'sale_id': self.sale_id,
            'customer_name': self.customer_name,
            'customer_phone': self.customer_phone,
            'delivery_address': self.get_delivery_address_formatted(),
            'delivery_type': self.delivery_type,
            'status': self.status,
            'is_cod': self.is_cod,
            'cod_amount': float(self.cod_amount) if self.cod_amount else 0,
            'delivery_fee': float(self.delivery_fee),
            'cod_fee': float(self.cod_fee),
            'total_charges': float(self.total_delivery_charges),
            'delivery_company': self.delivery_company,
            'tracking_number': self.tracking_number,
            'estimated_delivery': self.estimated_delivery.isoformat() if self.estimated_delivery else None,
            'actual_delivery': self.actual_delivery.isoformat() if self.actual_delivery else None,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<DeliveryOrder {self.delivery_number}>'


class DeliveryAttempt(db.Model):
    """Track delivery attempts and status changes"""
    
    __tablename__ = 'delivery_attempts'
    
    id = db.Column(db.Integer, primary_key=True)
    delivery_order_id = db.Column(db.Integer, db.ForeignKey('delivery_orders.id'), nullable=False)
    
    attempt_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    status = db.Column(db.String(50), nullable=False)
    notes = db.Column(db.Text)
    
    # Delivery person information
    delivery_person = db.Column(db.String(100))
    delivery_person_phone = db.Column(db.String(20))
    
    # Location information (if available)
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    
    def to_dict(self):
        """Convert delivery attempt to dictionary"""
        return {
            'id': self.id,
            'attempt_date': self.attempt_date.isoformat(),
            'status': self.status,
            'notes': self.notes,
            'delivery_person': self.delivery_person
        }
    
    def __repr__(self):
        return f'<DeliveryAttempt {self.id} - {self.status}>'


class DeliveryZone(db.Model):
    """Qatar delivery zones configuration"""
    
    __tablename__ = 'delivery_zones'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Zone information
    zone_code = db.Column(db.String(50), unique=True, nullable=False)
    zone_name_ar = db.Column(db.String(100), nullable=False)
    zone_name_en = db.Column(db.String(100), nullable=False)
    
    # Delivery settings
    base_fee = db.Column(db.Numeric(10, 2), nullable=False, default=10.00)
    express_multiplier = db.Column(db.Numeric(3, 2), default=1.5)
    same_day_multiplier = db.Column(db.Numeric(3, 2), default=2.0)
    
    # Service availability
    standard_delivery = db.Column(db.Boolean, default=True)
    express_delivery = db.Column(db.Boolean, default=True)
    same_day_delivery = db.Column(db.Boolean, default=False)
    cod_available = db.Column(db.Boolean, default=True)
    
    # Time slots
    delivery_time_slots = db.Column(db.Text)  # JSON string of available time slots
    
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    def get_name(self, language='ar'):
        """Get zone name in specified language"""
        return self.zone_name_ar if language == 'ar' else self.zone_name_en
    
    def get_time_slots(self):
        """Get available time slots"""
        import json
        if self.delivery_time_slots:
            return json.loads(self.delivery_time_slots)
        return [
            "09:00-12:00", "12:00-15:00", "15:00-18:00", "18:00-21:00"
        ]
    
    def calculate_fee(self, delivery_type='standard'):
        """Calculate delivery fee for this zone"""
        base = float(self.base_fee)
        
        if delivery_type == 'express':
            return base * float(self.express_multiplier)
        elif delivery_type == 'same_day':
            return base * float(self.same_day_multiplier)
        
        return base
    
    def to_dict(self, language='ar'):
        """Convert delivery zone to dictionary"""
        return {
            'id': self.id,
            'zone_code': self.zone_code,
            'zone_name': self.get_name(language),
            'base_fee': float(self.base_fee),
            'standard_delivery': self.standard_delivery,
            'express_delivery': self.express_delivery,
            'same_day_delivery': self.same_day_delivery,
            'cod_available': self.cod_available,
            'time_slots': self.get_time_slots()
        }
    
    def __repr__(self):
        return f'<DeliveryZone {self.zone_code}>'
