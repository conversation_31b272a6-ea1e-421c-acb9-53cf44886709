@echo off
chcp 65001 >nul
title Qatar POS System - Advanced Installer

:: تشغيل كمدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ========================================
    echo    تحتاج صلاحيات المدير للتثبيت
    echo    Administrator privileges required
    echo ========================================
    echo.
    echo جاري إعادة التشغيل كمدير...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

cls
echo ========================================
echo    Qatar POS System - Advanced Installer
echo    نظام نقاط البيع القطري - مثبت متقدم
echo ========================================
echo.

:: التحقق من وجود الملف
if not exist "dist\QatarPOS.exe" (
    echo ❌ خطأ: ملف QatarPOS.exe غير موجود
    echo ❌ Error: QatarPOS.exe not found
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف التطبيق
echo.

:: خيارات التثبيت
echo 🎯 خيارات التثبيت - Installation Options:
echo.
echo 1. تثبيت عادي (C:\QatarPOS)
echo    Standard installation (C:\QatarPOS)
echo.
echo 2. تثبيت مخصص (اختيار المسار)
echo    Custom installation (choose path)
echo.
echo 3. تثبيت محمول (في المجلد الحالي)
echo    Portable installation (current folder)
echo.
set /p install_type=اختر نوع التثبيت (1-3) / Choose installation type (1-3): 

if "%install_type%"=="1" (
    set INSTALL_DIR=C:\QatarPOS
    set INSTALL_TYPE=Standard
) else if "%install_type%"=="2" (
    echo.
    echo أدخل مسار التثبيت المطلوب:
    echo Enter desired installation path:
    set /p INSTALL_DIR=
    set INSTALL_TYPE=Custom
) else if "%install_type%"=="3" (
    set INSTALL_DIR=%~dp0QatarPOS_Portable
    set INSTALL_TYPE=Portable
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo ❌ Invalid choice
    pause
    exit /b 1
)

echo.
echo 📁 مسار التثبيت: %INSTALL_DIR%
echo 📁 Installation path: %INSTALL_DIR%
echo.

:: خيارات إضافية
echo 🔧 خيارات إضافية - Additional Options:
echo.
set /p create_desktop=إنشاء اختصار سطح المكتب؟ (Y/N) / Create desktop shortcut? (Y/N): 
set /p create_startmenu=إنشاء اختصار قائمة البداية؟ (Y/N) / Create start menu shortcut? (Y/N): 
set /p register_system=تسجيل في النظام؟ (Y/N) / Register in system? (Y/N): 

echo.
echo 🚀 بدء التثبيت...
echo 🚀 Starting installation...
echo.

:: إنشاء مجلد التثبيت
if exist "%INSTALL_DIR%" (
    echo ⚠️ المجلد موجود، سيتم الاستبدال
    rmdir /S /Q "%INSTALL_DIR%" >nul 2>&1
)

mkdir "%INSTALL_DIR%" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء مجلد التثبيت
    pause
    exit /b 1
)

:: نسخ الملفات
echo 📋 نسخ الملفات...
copy "dist\QatarPOS.exe" "%INSTALL_DIR%\" >nul 2>&1

:: إنشاء ملف التكوين
(
echo [QatarPOS Configuration]
echo InstallationType=%INSTALL_TYPE%
echo InstallationDate=%date% %time%
echo InstallationPath=%INSTALL_DIR%
echo Version=1.0.0
echo.
echo [Features]
echo DesktopShortcut=%create_desktop%
echo StartMenuShortcut=%create_startmenu%
echo SystemRegistration=%register_system%
) > "%INSTALL_DIR%\config.ini"

:: إنشاء أداة إلغاء التثبيت المتقدمة
(
echo @echo off
echo chcp 65001 ^>nul
echo title Qatar POS System - Advanced Uninstaller
echo.
echo ========================================
echo    Qatar POS System - Uninstaller
echo    نظام نقاط البيع القطري - إلغاء التثبيت
echo ========================================
echo.
echo هل أنت متأكد من إلغاء التثبيت؟
echo Are you sure you want to uninstall?
echo.
set /p confirm=اضغط Y للمتابعة / Press Y to continue: 
if /i "%%confirm%%"=="Y" ^(
    echo.
    echo 🗑️ جاري إلغاء التثبيت...
    echo.
    
    REM قراءة ملف التكوين
    for /f "tokens=2 delims==" %%%%a in ^('findstr "DesktopShortcut" config.ini'^) do set desktop=%%%%a
    for /f "tokens=2 delims==" %%%%a in ^('findstr "StartMenuShortcut" config.ini'^) do set startmenu=%%%%a
    for /f "tokens=2 delims==" %%%%a in ^('findstr "SystemRegistration" config.ini'^) do set registry=%%%%a
    
    REM حذف الاختصارات حسب التكوين
    if /i "%%desktop%%"=="Y" ^(
        echo حذف اختصار سطح المكتب...
        if exist "%%USERPROFILE%%\Desktop\Qatar POS.lnk" del "%%USERPROFILE%%\Desktop\Qatar POS.lnk"
    ^)
    
    if /i "%%startmenu%%"=="Y" ^(
        echo حذف اختصارات قائمة البداية...
        if exist "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS" rmdir /S /Q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\Qatar POS"
    ^)
    
    if /i "%%registry%%"=="Y" ^(
        echo حذف إدخالات النظام...
        reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /f ^>nul 2^>^&1
    ^)
    
    echo حذف ملفات التطبيق...
    cd /d "%%USERPROFILE%%"
    if exist "%INSTALL_DIR%" rmdir /S /Q "%INSTALL_DIR%"
    
    echo.
    echo ✅ تم إلغاء التثبيت بنجاح!
^) else ^(
    echo.
    echo ⏹️ تم إلغاء العملية
^)
echo.
pause
) > "%INSTALL_DIR%\Uninstall.bat"

:: إنشاء اختصار سطح المكتب
if /i "%create_desktop%"=="Y" (
    echo 🖥️ إنشاء اختصار سطح المكتب...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()" >nul 2>&1
    echo ✅ تم إنشاء اختصار سطح المكتب
)

:: إنشاء اختصار قائمة البداية
if /i "%create_startmenu%"=="Y" (
    echo 📋 إنشاء اختصارات قائمة البداية...
    set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Qatar POS
    mkdir "%START_MENU_DIR%" >nul 2>&1
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\QatarPOS.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Qatar POS System'; $Shortcut.Save()" >nul 2>&1
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Uninstall Qatar POS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Uninstall Qatar POS System'; $Shortcut.Save()" >nul 2>&1
    
    echo ✅ تم إنشاء اختصارات قائمة البداية
)

:: تسجيل في النظام
if /i "%register_system%"=="Y" (
    echo 📝 تسجيل في النظام...
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayName" /t REG_SZ /d "Qatar POS System" /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "Publisher" /t REG_SZ /d "Qatar POS System" /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\QatarPOS" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul 2>&1
    echo ✅ تم تسجيل التطبيق في النظام
)

:: إنشاء ملف سجل التثبيت
(
echo Qatar POS System - Advanced Installation Log
echo ===========================================
echo.
echo Installation Type: %INSTALL_TYPE%
echo Installation Date: %date% %time%
echo Installation Path: %INSTALL_DIR%
echo Version: 1.0.0
echo.
echo Options Selected:
echo - Desktop Shortcut: %create_desktop%
echo - Start Menu Shortcut: %create_startmenu%
echo - System Registration: %register_system%
echo.
echo Installation Status: SUCCESS
) > "%INSTALL_DIR%\Installation_Log.txt"

echo.
echo ========================================
echo 🎉 تم التثبيت بنجاح!
echo 🎉 Installation completed successfully!
echo ========================================
echo.
echo 📍 مسار التطبيق: %INSTALL_DIR%\QatarPOS.exe
echo 📍 Application path: %INSTALL_DIR%\QatarPOS.exe
echo.
echo 🗑️ لإلغاء التثبيت: %INSTALL_DIR%\Uninstall.bat
echo 🗑️ To uninstall: %INSTALL_DIR%\Uninstall.bat
echo.

set /p run_now=هل تريد تشغيل التطبيق الآن؟ (Y/N) / Run application now? (Y/N): 
if /i "%run_now%"=="Y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    start "" "%INSTALL_DIR%\QatarPOS.exe"
)

echo.
echo شكراً لاستخدام نظام نقاط البيع القطري!
echo Thank you for using Qatar POS System!
echo.
pause
