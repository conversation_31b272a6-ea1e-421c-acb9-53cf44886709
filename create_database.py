#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات من الصفر
Create Database from Scratch
"""

import os
from app import create_app
from extensions import db

def create_fresh_database():
    """إنشاء قاعدة بيانات جديدة"""
    print("🇶🇦 نظام نقاط البيع القطري - إنشاء قاعدة البيانات")
    print("=" * 60)
    
    # Remove existing database
    db_path = 'instance/database.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    # Create app and database
    app = create_app()
    
    with app.app_context():
        # Import all models to ensure they're registered
        print("📦 استيراد النماذج...")

        # Import core models first
        from models.role import Role, Permission
        print("  ✅ تم استيراد نماذج الأدوار والصلاحيات")

        from models.user import User
        print("  ✅ تم استيراد نموذج المستخدم")

        from models.category import Category
        print("  ✅ تم استيراد نموذج الفئات")

        from models.product import Product
        print("  ✅ تم استيراد نموذج المنتجات")

        from models.customer import Customer
        print("  ✅ تم استيراد نموذج العملاء")

        from models.supplier import Supplier
        print("  ✅ تم استيراد نموذج الموردين")

        from models.sale import Sale, SaleItem
        print("  ✅ تم استيراد نماذج المبيعات")

        from models.setting import Setting, SystemInfo
        print("  ✅ تم استيراد نماذج الإعدادات")

        # Try to import optional models
        try:
            from models.purchase_order import PurchaseOrder, PurchaseOrderItem
            print("  ✅ تم استيراد نماذج أوامر الشراء")
        except ImportError:
            print("  ⚠️ نماذج أوامر الشراء غير موجودة")

        try:
            from models.inventory_transaction import InventoryTransaction
            print("  ✅ تم استيراد نموذج معاملات المخزون")
        except ImportError:
            print("  ⚠️ نموذج معاملات المخزون غير موجود")
        
        print("📋 إنشاء جداول قاعدة البيانات...")
        
        # Create all tables
        db.create_all()
        
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح!")
        
        # Verify tables were created
        inspector = db.inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"📊 عدد الجداول المُنشأة: {len(tables)}")
        
        for table in sorted(tables):
            print(f"  📋 {table}")
        
        return True

if __name__ == '__main__':
    success = create_fresh_database()
    if success:
        print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
        print("💡 يمكنك الآن تشغيل setup_basic_data.py لإضافة البيانات الأساسية")
    else:
        print("\n❌ فشل في إنشاء قاعدة البيانات")
