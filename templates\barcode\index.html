{% extends "base.html" %}

{% block title %}{{ 'إدارة الباركود' if language == 'ar' else 'Barcode Management' }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3">
                    <i class="bi bi-upc-scan"></i>
                    {{ 'إدارة الباركود' if language == 'ar' else 'Barcode Management' }}
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('barcode.generate') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        {{ 'توليد باركود' if language == 'ar' else 'Generate Barcodes' }}
                    </a>
                    <a href="{{ url_for('barcode.print_labels') }}" class="btn btn-success">
                        <i class="bi bi-printer"></i>
                        {{ 'طباعة ملصقات' if language == 'ar' else 'Print Labels' }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_products }}</h4>
                            <p class="card-text">{{ 'إجمالي المنتجات' if language == 'ar' else 'Total Products' }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-box-seam fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ products_with_barcode }}</h4>
                            <p class="card-text">{{ 'منتجات بباركود' if language == 'ar' else 'Products with Barcode' }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ products_without_barcode }}</h4>
                            <p class="card-text">{{ 'منتجات بدون باركود' if language == 'ar' else 'Products without Barcode' }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Products without Barcodes -->
    {% if recent_products %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-circle text-warning"></i>
                        {{ 'منتجات حديثة بدون باركود' if language == 'ar' else 'Recent Products without Barcode' }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ 'رمز المنتج' if language == 'ar' else 'SKU' }}</th>
                                    <th>{{ 'اسم المنتج' if language == 'ar' else 'Product Name' }}</th>
                                    <th>{{ 'الفئة' if language == 'ar' else 'Category' }}</th>
                                    <th>{{ 'السعر' if language == 'ar' else 'Price' }}</th>
                                    <th>{{ 'تاريخ الإنشاء' if language == 'ar' else 'Created Date' }}</th>
                                    <th>{{ 'الإجراءات' if language == 'ar' else 'Actions' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td><code>{{ product.sku }}</code></td>
                                    <td>{{ product.get_name(language) }}</td>
                                    <td>{{ product.category.get_name(language) if product.category else '-' }}</td>
                                    <td>{{ "%.2f"|format(product.get_final_price()) }} {{ 'ر.ق' if language == 'ar' else 'QAR' }}</td>
                                    <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="generateSingleBarcode({{ product.id }})">
                                            <i class="bi bi-upc-scan"></i>
                                            {{ 'توليد باركود' if language == 'ar' else 'Generate' }}
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ url_for('barcode.generate') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            {{ 'توليد باركود لجميع المنتجات' if language == 'ar' else 'Generate Barcodes for All Products' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                {{ 'جميع المنتجات تحتوي على باركود!' if language == 'ar' else 'All products have barcodes!' }}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">{{ 'جاري توليد الباركود...' if language == 'ar' else 'Generating barcode...' }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateSingleBarcode(productId) {
    // Show loading modal
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    fetch('/barcode/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_ids: [productId],
            barcode_type: 'EAN13'
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        
        if (data.success) {
            // Show success message
            showAlert(data.message, 'success');
            
            // Reload page after 2 seconds
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        showAlert('{{ "خطأ في توليد الباركود" if language == "ar" else "Error generating barcode" }}', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
