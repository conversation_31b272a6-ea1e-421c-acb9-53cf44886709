#!/usr/bin/env python3
"""
Qatar POS System - Auto Fix
Automatically fix common issues
"""

import sys
import os
import subprocess
from pathlib import Path

def print_header():
    print("🇶🇦 نظام نقاط البيع القطري - إصلاح تلقائي")
    print("Qatar POS System - Auto Fix")
    print("=" * 50)

def install_flask():
    print("\n📦 Installing Flask...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask'])
        print("✅ Flask installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Flask")
        return False

def create_missing_files():
    print("\n📁 Creating missing files...")
    
    # Create basic app.py if missing
    if not os.path.exists('app.py'):
        app_content = '''"""
Qatar POS System - Main Application
"""

from flask import Flask

def create_app():
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'dev-secret-key'
    
    @app.route('/')
    def index():
        return "Qatar POS System is running!"
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
'''
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(app_content)
        print("✅ Created app.py")
    
    # Create basic config.py if missing
    if not os.path.exists('config.py'):
        config_content = '''"""
Configuration for Qatar POS System
"""

import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///qatarpos.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
'''
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ Created config.py")

def create_missing_directories():
    print("\n📂 Creating missing directories...")
    
    directories = [
        'templates',
        'static/css',
        'static/js',
        'static/images',
        'models',
        'routes',
        'utils',
        'uploads',
        'logs',
        'backups'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}/")

def create_env_file():
    print("\n⚙️ Creating .env file...")
    
    if not os.path.exists('.env'):
        env_content = '''# Qatar POS System - Environment Variables
SECRET_KEY=dev-secret-key-change-in-production
FLASK_ENV=development
FLASK_DEBUG=1
DATABASE_URL=sqlite:///qatarpos.db
DEFAULT_LANGUAGE=ar
DEFAULT_CURRENCY=QAR
DEFAULT_TIMEZONE=Asia/Qatar
'''
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ Created .env file")
    else:
        print("✅ .env file already exists")

def create_basic_templates():
    print("\n📄 Creating basic templates...")
    
    # Create templates directory
    Path('templates').mkdir(exist_ok=True)
    
    # Create basic index template
    if not os.path.exists('templates/index.html'):
        index_content = '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع القطري</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .container { max-width: 600px; margin: 0 auto; }
        h1 { color: #333; }
        .success { color: #28a745; font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇶🇦 نظام نقاط البيع القطري</h1>
        <h2>Qatar POS System</h2>
        <p class="success">✅ النظام يعمل بنجاح!</p>
        <p class="success">✅ System is working!</p>
    </div>
</body>
</html>'''
        with open('templates/index.html', 'w', encoding='utf-8') as f:
            f.write(index_content)
        print("✅ Created templates/index.html")

def test_system():
    print("\n🧪 Testing system...")
    
    try:
        # Test Flask import
        import flask
        print("✅ Flask import successful")
        
        # Test basic Flask app
        app = flask.Flask(__name__)
        
        @app.route('/test')
        def test():
            return "OK"
        
        with app.test_client() as client:
            response = client.get('/test')
            if response.status_code == 200:
                print("✅ Flask app test successful")
                return True
            else:
                print("❌ Flask app test failed")
                return False
                
    except ImportError:
        print("❌ Flask not available")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    print_header()
    
    print("🔧 Starting auto-fix process...")
    
    # Step 1: Install Flask
    if not install_flask():
        print("❌ Cannot proceed without Flask")
        return
    
    # Step 2: Create missing files
    create_missing_files()
    
    # Step 3: Create missing directories
    create_missing_directories()
    
    # Step 4: Create .env file
    create_env_file()
    
    # Step 5: Create basic templates
    create_basic_templates()
    
    # Step 6: Test system
    if test_system():
        print("\n" + "=" * 50)
        print("🎉 Auto-fix completed successfully!")
        print("🎉 الإصلاح التلقائي اكتمل بنجاح!")
        print("\n📋 Next steps:")
        print("1. Run: python test_server.py")
        print("2. Open: http://localhost:5000")
        print("3. Or run: python simple_run.py")
        print("=" * 50)
    else:
        print("\n❌ Auto-fix completed but system test failed")
        print("💡 Try running: python test_server.py")

if __name__ == '__main__':
    main()
