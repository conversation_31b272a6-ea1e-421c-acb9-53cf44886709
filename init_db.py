#!/usr/bin/env python3
"""
Database initialization script for Qatar POS System
Creates tables and adds sample data
"""

from app import create_app
from extensions import db
from models.user import User
from models.category import Category
from models.product import Product
from models.customer import Customer
from models.supplier import Supplier

def init_database():
    """Initialize database with tables and sample data"""
    app = create_app()
    
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Creating admin user...")
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name_ar='المدير',
                first_name_en='Admin',
                last_name_ar='العام',
                last_name_en='User',
                phone='+974-5555-0001',
                role='manager',
                employee_id='EMP001'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
        
        # Create sample seller
        seller_user = User.query.filter_by(username='seller').first()
        if not seller_user:
            print("Creating seller user...")
            seller_user = User(
                username='seller',
                email='<EMAIL>',
                first_name_ar='البائع',
                first_name_en='Sales',
                last_name_ar='الأول',
                last_name_en='Person',
                phone='+974-5555-0002',
                role='seller',
                employee_id='EMP002'
            )
            seller_user.set_password('seller123')
            db.session.add(seller_user)
        
        # Create sample accountant
        accountant_user = User.query.filter_by(username='accountant').first()
        if not accountant_user:
            print("Creating accountant user...")
            accountant_user = User(
                username='accountant',
                email='<EMAIL>',
                first_name_ar='المحاسب',
                first_name_en='Account',
                last_name_ar='الأول',
                last_name_en='Manager',
                phone='+974-5555-0003',
                role='accountant',
                employee_id='EMP003'
            )
            accountant_user.set_password('accountant123')
            db.session.add(accountant_user)
        
        # Create sample categories
        categories_data = [
            {
                'name_ar': 'المشروبات',
                'name_en': 'Beverages',
                'description_ar': 'جميع أنواع المشروبات',
                'description_en': 'All types of beverages'
            },
            {
                'name_ar': 'الوجبات الخفيفة',
                'name_en': 'Snacks',
                'description_ar': 'الوجبات الخفيفة والحلويات',
                'description_en': 'Light snacks and sweets'
            },
            {
                'name_ar': 'منتجات الألبان',
                'name_en': 'Dairy Products',
                'description_ar': 'الحليب والجبن واللبن',
                'description_en': 'Milk, cheese, and yogurt'
            },
            {
                'name_ar': 'الخضروات والفواكه',
                'name_en': 'Fruits & Vegetables',
                'description_ar': 'الخضروات والفواكه الطازجة',
                'description_en': 'Fresh fruits and vegetables'
            }
        ]
        
        for cat_data in categories_data:
            existing_cat = Category.query.filter_by(name_en=cat_data['name_en']).first()
            if not existing_cat:
                print(f"Creating category: {cat_data['name_en']}")
                category = Category(**cat_data)
                db.session.add(category)
        
        db.session.commit()
        
        # Create sample products
        beverages_cat = Category.query.filter_by(name_en='Beverages').first()
        snacks_cat = Category.query.filter_by(name_en='Snacks').first()
        dairy_cat = Category.query.filter_by(name_en='Dairy Products').first()
        
        if beverages_cat and snacks_cat and dairy_cat:
            products_data = [
                {
                    'sku': 'BEV001',
                    'barcode': '123456789001',
                    'name_ar': 'ماء معدني',
                    'name_en': 'Mineral Water',
                    'description_ar': 'ماء معدني طبيعي 500 مل',
                    'description_en': 'Natural mineral water 500ml',
                    'category_id': beverages_cat.id,
                    'cost_price': 0.5,
                    'selling_price': 1.0,
                    'current_stock': 100,
                    'minimum_stock': 20,
                    'unit_of_measure': 'bottle'
                },
                {
                    'sku': 'BEV002',
                    'barcode': '123456789002',
                    'name_ar': 'عصير برتقال',
                    'name_en': 'Orange Juice',
                    'description_ar': 'عصير برتقال طبيعي 250 مل',
                    'description_en': 'Natural orange juice 250ml',
                    'category_id': beverages_cat.id,
                    'cost_price': 1.5,
                    'selling_price': 3.0,
                    'current_stock': 50,
                    'minimum_stock': 10,
                    'unit_of_measure': 'bottle'
                },
                {
                    'sku': 'SNK001',
                    'barcode': '123456789003',
                    'name_ar': 'شيبس',
                    'name_en': 'Potato Chips',
                    'description_ar': 'شيبس بطاطس مقرمش',
                    'description_en': 'Crispy potato chips',
                    'category_id': snacks_cat.id,
                    'cost_price': 2.0,
                    'selling_price': 4.0,
                    'current_stock': 75,
                    'minimum_stock': 15,
                    'unit_of_measure': 'pack'
                },
                {
                    'sku': 'DAI001',
                    'barcode': '123456789004',
                    'name_ar': 'حليب طازج',
                    'name_en': 'Fresh Milk',
                    'description_ar': 'حليب طازج كامل الدسم 1 لتر',
                    'description_en': 'Fresh full-fat milk 1 liter',
                    'category_id': dairy_cat.id,
                    'cost_price': 3.0,
                    'selling_price': 5.0,
                    'current_stock': 30,
                    'minimum_stock': 5,
                    'unit_of_measure': 'liter'
                }
            ]
            
            for prod_data in products_data:
                existing_prod = Product.query.filter_by(sku=prod_data['sku']).first()
                if not existing_prod:
                    print(f"Creating product: {prod_data['name_en']}")
                    product = Product(**prod_data)
                    db.session.add(product)
        
        # Create sample customers
        customers_data = [
            {
                'customer_code': 'C000001',
                'customer_type': 'individual',
                'first_name_ar': 'أحمد',
                'first_name_en': 'Ahmed',
                'last_name_ar': 'محمد',
                'last_name_en': 'Mohammed',
                'phone': '+974-5555-1001',
                'email': '<EMAIL>',
                'address_ar': 'الدوحة، قطر',
                'address_en': 'Doha, Qatar',
                'city_ar': 'الدوحة',
                'city_en': 'Doha'
            },
            {
                'customer_code': 'C000002',
                'customer_type': 'company',
                'company_name_ar': 'شركة التجارة المتقدمة',
                'company_name_en': 'Advanced Trading Company',
                'contact_person_ar': 'سالم الكعبي',
                'contact_person_en': 'Salem Al-Kaabi',
                'phone': '+974-4444-2001',
                'email': '<EMAIL>',
                'address_ar': 'المنطقة الصناعية، الدوحة',
                'address_en': 'Industrial Area, Doha',
                'city_ar': 'الدوحة',
                'city_en': 'Doha',
                'commercial_registration': 'CR123456',
                'tax_number': 'TAX789012'
            }
        ]
        
        for cust_data in customers_data:
            existing_cust = Customer.query.filter_by(customer_code=cust_data['customer_code']).first()
            if not existing_cust:
                print(f"Creating customer: {cust_data.get('company_name_en', cust_data.get('first_name_en'))}")
                customer = Customer(**cust_data)
                db.session.add(customer)
        
        # Create sample suppliers
        suppliers_data = [
            {
                'supplier_code': 'S000001',
                'company_name_ar': 'شركة المواد الغذائية الخليجية',
                'company_name_en': 'Gulf Food Supplies Company',
                'contact_person_ar': 'خالد العلي',
                'contact_person_en': 'Khalid Al-Ali',
                'phone': '+974-4444-3001',
                'email': '<EMAIL>',
                'address_ar': 'منطقة الخور الصناعية',
                'address_en': 'Al Khor Industrial Area',
                'city_ar': 'الخور',
                'city_en': 'Al Khor',
                'commercial_registration': 'CR654321',
                'payment_terms': 'Net 30'
            }
        ]
        
        for supp_data in suppliers_data:
            existing_supp = Supplier.query.filter_by(supplier_code=supp_data['supplier_code']).first()
            if not existing_supp:
                print(f"Creating supplier: {supp_data['company_name_en']}")
                supplier = Supplier(**supp_data)
                db.session.add(supplier)
        
        db.session.commit()
        print("Database initialization completed successfully!")
        
        print("\n" + "="*50)
        print("DEFAULT LOGIN CREDENTIALS:")
        print("="*50)
        print("Manager:")
        print("  Username: admin")
        print("  Password: admin123")
        print("\nSeller:")
        print("  Username: seller")
        print("  Password: seller123")
        print("\nAccountant:")
        print("  Username: accountant")
        print("  Password: accountant123")
        print("="*50)

if __name__ == '__main__':
    init_database()
